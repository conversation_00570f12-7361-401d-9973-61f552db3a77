import logging
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from common.models.slave_server import SlaveServer
from common.models.device import Device
from common.schemas.status_schema import SlaveStatusReport
from common.schemas.device_schema import DeviceCreate

logger = logging.getLogger(__name__)

class StatusService:
    async def process_slave_report(self, db: AsyncSession, *, slave_id: int, report: SlaveStatusReport):
        """
        Processes a status report from a slave server, synchronizing device states.
        """
        # 1. Update slave server's last_seen timestamp
        slave = await db.get(SlaveServer, slave_id)
        if not slave:
            logger.error(f"Received report from a non-existent slave with ID {slave_id}. This should not happen.")
            return

        slave.last_seen = datetime.utcnow()
        slave.status = "online"
        db.add(slave)

        # 2. Get all existing devices for this slave from the DB
        stmt = select(Device).where(Device.slave_server_id == slave_id)
        result = await db.execute(stmt)
        db_devices = result.scalars().all()
        db_device_map = {d.device_id: d for d in db_devices}

        # 3. Process devices from the report
        reported_device_paths = set()
        for reported_device in report.devices:
            device_path = reported_device.device_path
            reported_device_paths.add(device_path)

            if device_path in db_device_map:
                # Device exists, update its info if necessary (e.g., status)
                db_device = db_device_map[device_path]
                if db_device.status == "offline":
                    db_device.status = "available" # It came back online
                    logger.info(f"Device {device_path} for slave {slave_id} is back online.")
                    db.add(db_device)
            else:
                # New device detected, create it in the DB
                if not device_path:
                    logger.warning(f"Slave {slave_id} reported a device with an empty device_path. Skipping creation. Full data: {reported_device}")
                    continue

                logger.info(f"New device {device_path} detected for slave {slave_id}. Creating new record.")
                new_device_schema = DeviceCreate(
                    device_id=device_path,
                    vendor_id=reported_device.vendor_id,
                    product_id=reported_device.model, # Assuming model is product_id
                    serial_number=None, # Not available in report
                    description=f"{reported_device.manufacturer} {reported_device.model}",
                    device_type="unknown",
                    status="available",
                    slave_server_id=slave_id
                )
                new_device = Device(**new_device_schema.model_dump())
                db.add(new_device)
        
        # 4. Mark devices that are in DB but not in the report as offline
        for device_path, db_device in db_device_map.items():
            if device_path not in reported_device_paths:
                if db_device.status != "offline":
                    logger.info(f"Device {device_path} for slave {slave_id} is now offline.")
                    db_device.status = "offline"
                    db.add(db_device)
        
        # 5. Commit all changes
        await db.commit()

status_service = StatusService() 