from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete

from common.models.policy import PolicyRule
from common.schemas.policy_schema import PolicyRuleCreate, PolicyRuleUpdate

class PolicyService:
    """
    Service layer for handling policy rule logic.
    """

    async def get(self, db: AsyncSession, *, id: int) -> Optional[PolicyRule]:
        """
        Get a policy rule by ID.
        """
        statement = select(PolicyRule).where(PolicyRule.id == id)
        result = await db.execute(statement)
        return result.scalars().first()

    async def get_multi(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> List[PolicyRule]:
        """
        Get multiple policy rules.
        """
        statement = select(PolicyRule).offset(skip).limit(limit)
        result = await db.execute(statement)
        return result.scalars().all()

    async def create(self, db: AsyncSession, *, obj_in: PolicyRuleCreate) -> PolicyRule:
        """
        Create a new policy rule.
        """
        # Pydantic v2 .model_dump() replaces .dict()
        obj_in_data = obj_in.model_dump()
        db_obj = PolicyRule(**obj_in_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update(
        self, db: AsyncSession, *, db_obj: PolicyRule, obj_in: PolicyRuleUpdate
    ) -> PolicyRule:
        """
        Update a policy rule.
        """
        # Pydantic v2 .model_dump() replaces .dict()
        update_data = obj_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def remove(self, db: AsyncSession, *, id: int) -> PolicyRule:
        """
        Delete a policy rule.
        """
        statement = delete(PolicyRule).where(PolicyRule.id == id).returning(PolicyRule)
        result = await db.execute(statement)
        await db.commit()
        return result.scalars().one()

policy_service = PolicyService()
