"""
迁移处理器测试

测试迁移处理器的功能，包括任务创建、状态跟踪和进度监控。
"""

import unittest
import logging
import tempfile
import shutil
import time
from datetime import datetime
from unittest.mock import patch, Mock, MagicMock

from ..migration_service import MigrationService
from ..migration_handler import MigrationHandler

# 禁用测试日志
logging.disable(logging.CRITICAL)

class TestMigrationHandler(unittest.TestCase):
    """迁移处理器测试用例"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 模拟迁移服务
        self.migration_service = MagicMock(spec=MigrationService)
        
        # 模拟状态回调注册
        self.migration_service.register_state_callback = Mock()
        
        # 模拟开始迁移方法
        self.migration_service.start_migration = Mock(return_value="test_migration_id")
        
        # 模拟获取迁移状态方法
        self.migration_service.get_migration_status = Mock(return_value={
            'id': 'test_migration_id',
            'state': 'initialized',
            'progress': 0,
            'error': None,
            'logs': [
                {
                    'timestamp': datetime.now().isoformat(),
                    'level': 'info',
                    'message': '迁移已初始化'
                }
            ],
            'checkpoints': {}
        })
        
        # 模拟停止迁移方法
        self.migration_service.stop_migration = Mock(return_value=True)
        
        # 创建迁移处理器
        self.migration_handler = MigrationHandler(self.migration_service)
        
        # 创建测试服务器信息
        self.source_server = {
            'id': 'source_server',
            'name': 'Source Server',
            'host': '*************',
            'port': 22,
            'api_url': 'https://*************:8443/api',
            'auth_type': 'basic',
            'username': 'test',
            'password': 'test123'
        }
        
        self.target_server = {
            'id': 'target_server',
            'name': 'Target Server',
            'host': '*************',
            'port': 22,
            'api_url': 'https://*************:8443/api',
            'auth_type': 'basic',
            'username': 'test',
            'password': 'test123'
        }
    
    def tearDown(self):
        """测试后清理"""
        # 删除临时目录
        shutil.rmtree(self.temp_dir)
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.migration_handler.migration_service, self.migration_service)
        self.assertEqual(len(self.migration_handler.tasks), 0)
        
        # 验证回调注册
        self.migration_service.register_state_callback.assert_called()
    
    def test_create_migration_task(self):
        """测试创建迁移任务"""
        # 创建迁移任务
        task_id = self.migration_handler.create_migration_task(
            self.source_server,
            self.target_server,
            'full'
        )
        
        # 验证任务ID
        self.assertEqual(task_id, 'test_migration_id')
        
        # 验证任务信息
        self.assertIn(task_id, self.migration_handler.tasks)
        task_info = self.migration_handler.tasks[task_id]
        self.assertEqual(task_info['id'], task_id)
        self.assertEqual(task_info['source'], self.source_server)
        self.assertEqual(task_info['target'], self.target_server)
        self.assertEqual(task_info['type'], 'full')
        self.assertEqual(task_info['status'], 'created')
        
        # 验证服务方法调用
        self.migration_service.start_migration.assert_called_once_with(
            self.source_server,
            self.target_server,
            {'task_type': 'full', 'data_types': ['system_config', 'user_data', 'service_data', 'logs']}
        )
    
    def test_get_task_status(self):
        """测试获取任务状态"""
        # 创建迁移任务
        task_id = self.migration_handler.create_migration_task(
            self.source_server,
            self.target_server,
            'full'
        )
        
        # 获取任务状态
        status = self.migration_handler.get_task_status(task_id)
        
        # 验证状态
        self.assertIsNotNone(status)
        if status:
            self.assertEqual(status['id'], task_id)
            self.assertEqual(status['status'], 'initialized')
            self.assertEqual(status['progress'], 0)
        
        # 验证服务方法调用
        self.migration_service.get_migration_status.assert_called_with('test_migration_id')
    
    def test_get_task_status_nonexistent(self):
        """测试获取不存在的任务状态"""
        # 获取不存在的任务状态
        status = self.migration_handler.get_task_status('nonexistent')
        
        # 验证状态
        self.assertIsNone(status)
    
    def test_stop_task(self):
        """测试停止任务"""
        # 创建迁移任务
        task_id = self.migration_handler.create_migration_task(
            self.source_server,
            self.target_server,
            'full'
        )
        
        # 停止任务
        result = self.migration_handler.stop_task(task_id)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证服务方法调用
        self.migration_service.stop_migration.assert_called_once_with('test_migration_id', False)
    
    def test_stop_task_nonexistent(self):
        """测试停止不存在的任务"""
        # 停止不存在的任务
        result = self.migration_handler.stop_task('nonexistent')
        
        # 验证结果
        self.assertFalse(result)
    
    def test_stop_task_force(self):
        """测试强制停止任务"""
        # 创建迁移任务
        task_id = self.migration_handler.create_migration_task(
            self.source_server,
            self.target_server,
            'full'
        )
        
        # 强制停止任务
        result = self.migration_handler.stop_task(task_id, True)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证服务方法调用
        self.migration_service.stop_migration.assert_called_once_with('test_migration_id', True)
    
    def test_list_tasks(self):
        """测试列出任务"""
        # 创建迁移任务
        task_id1 = self.migration_handler.create_migration_task(
            self.source_server,
            self.target_server,
            'full'
        )
        
        # 修改第一个任务的状态
        self.migration_handler.tasks[task_id1]['status'] = 'preparing'
        
        # 模拟获取状态
        self.migration_service.get_migration_status = Mock(return_value={
            'id': task_id1,
            'state': 'preparing',
            'progress': 10,
            'error': None
        })
        
        # 创建第二个迁移任务
        task_id2 = 'test_migration_id2'
        self.migration_service.start_migration.return_value = task_id2
        task_id2 = self.migration_handler.create_migration_task(
            self.source_server,
            self.target_server,
            'config_only'
        )
        
        # 修改第二个任务的状态
        self.migration_handler.tasks[task_id2]['status'] = 'completed'
        
        # 列出所有任务
        tasks = self.migration_handler.list_tasks()
        
        # 验证结果
        self.assertEqual(len(tasks), 2)
        
        # 列出指定状态的任务
        tasks_preparing = self.migration_handler.list_tasks('preparing')
        
        # 验证结果
        self.assertEqual(len(tasks_preparing), 1)
        self.assertEqual(tasks_preparing[0]['id'], task_id1)
    
    def test_get_task_logs(self):
        """测试获取任务日志"""
        # 创建迁移任务
        task_id = self.migration_handler.create_migration_task(
            self.source_server,
            self.target_server,
            'full'
        )
        
        # 模拟获取状态
        test_logs = [
            {
                'timestamp': datetime.now().isoformat(),
                'level': 'info',
                'message': '迁移已初始化'
            },
            {
                'timestamp': datetime.now().isoformat(),
                'level': 'info',
                'message': '正在准备迁移'
            }
        ]
        
        self.migration_service.get_migration_status = Mock(return_value={
            'id': task_id,
            'state': 'preparing',
            'progress': 10,
            'error': None,
            'logs': test_logs
        })
        
        # 获取任务日志
        logs = self.migration_handler.get_task_logs(task_id, limit=10)
        
        # 验证结果
        self.assertEqual(len(logs), 2)
        self.assertEqual(logs[0]['message'], '迁移已初始化')
        self.assertEqual(logs[1]['message'], '正在准备迁移')
    
    def test_get_task_logs_nonexistent(self):
        """测试获取不存在的任务日志"""
        # 获取不存在的任务日志
        logs = self.migration_handler.get_task_logs('nonexistent')
        
        # 验证结果
        self.assertEqual(len(logs), 0)
    
    def test_get_migration_speed(self):
        """测试获取迁移速度"""
        # 创建迁移任务
        task_id = self.migration_handler.create_migration_task(
            self.source_server,
            self.target_server,
            'full'
        )
        
        # 模拟获取状态
        current_time = datetime.now()
        start_time = current_time.replace(second=current_time.second - 10)  # 10秒前
        
        test_checkpoints = {
            'transfer_start': {
                'timestamp': start_time.isoformat(),
                'data': {
                    'timestamp': start_time.isoformat(),
                    'bytes_transferred': 0,
                    'total_bytes': 1024 * 1024  # 1MB
                }
            },
            'transfer_current': {
                'timestamp': current_time.isoformat(),
                'data': {
                    'timestamp': current_time.isoformat(),
                    'bytes_transferred': 512 * 1024,  # 512KB
                    'total_bytes': 1024 * 1024  # 1MB
                }
            }
        }
        
        self.migration_service.get_migration_status = Mock(return_value={
            'id': task_id,
            'state': 'transferring',
            'progress': 50,
            'error': None,
            'checkpoints': test_checkpoints
        })
        
        # 获取迁移速度
        speed_info = self.migration_handler.get_migration_speed(task_id)
        
        # 验证结果
        self.assertEqual(speed_info['data_processed'], 512 * 1024)
        self.assertEqual(speed_info['data_total'], 1024 * 1024)
        self.assertGreater(speed_info['current_speed'], 0)
        self.assertGreater(speed_info['average_speed'], 0)
        self.assertIsNotNone(speed_info['estimated_time_remaining'])
    
    def test_get_migration_speed_no_checkpoints(self):
        """测试获取没有检查点的迁移速度"""
        # 创建迁移任务
        task_id = self.migration_handler.create_migration_task(
            self.source_server,
            self.target_server,
            'full'
        )
        
        # 模拟获取状态（没有检查点）
        self.migration_service.get_migration_status = Mock(return_value={
            'id': task_id,
            'state': 'preparing',
            'progress': 10,
            'error': None,
            'checkpoints': {}
        })
        
        # 获取迁移速度
        speed_info = self.migration_handler.get_migration_speed(task_id)
        
        # 验证结果
        self.assertEqual(speed_info['current_speed'], 0)
        self.assertEqual(speed_info['average_speed'], 0)
        self.assertIsNone(speed_info['estimated_time_remaining'])
        self.assertEqual(speed_info['data_processed'], 0)
        self.assertEqual(speed_info['data_total'], 0)
    
    def test_get_detailed_status(self):
        """测试获取详细状态"""
        # 创建迁移任务
        task_id = self.migration_handler.create_migration_task(
            self.source_server,
            self.target_server,
            'full'
        )
        
        # 模拟获取基本状态
        self.migration_handler.get_task_status = Mock(return_value={
            'id': task_id,
            'status': 'transferring',
            'progress': 50
        })
        
        # 模拟获取速度
        self.migration_handler.get_migration_speed = Mock(return_value={
            'current_speed': 51200,  # 50KB/s
            'average_speed': 51200,
            'estimated_time_remaining': 10,
            'data_processed': 512 * 1024,
            'data_total': 1024 * 1024
        })
        
        # 模拟获取日志
        self.migration_handler.get_task_logs = Mock(return_value=[
            {
                'timestamp': datetime.now().isoformat(),
                'level': 'info',
                'message': '开始传输数据'
            }
        ])
        
        # 获取详细状态
        detailed_status = self.migration_handler.get_detailed_status(task_id)
        
        # 验证结果
        self.assertEqual(detailed_status['id'], task_id)
        self.assertEqual(detailed_status['status'], 'transferring')
        self.assertEqual(detailed_status['progress'], 50)
        self.assertIn('speed', detailed_status)
        self.assertIn('recent_logs', detailed_status)
        self.assertEqual(len(detailed_status['recent_logs']), 1)
    
    def test_handle_migration_state_change(self):
        """测试处理迁移状态变更"""
        # 模拟状态数据
        state_data = {
            'state': 'preparing',
            'message': '准备迁移'
        }
        
        # 模拟获取迁移状态
        self.migration_service.get_migration_status = Mock(return_value={
            'id': 'test_migration_id',
            'state': 'preparing',
            'progress': 10
        })
        
        # 模拟启动进度跟踪
        self.migration_handler._start_progress_tracking = Mock()
        
        # 处理状态变更
        self.migration_handler._handle_migration_state_change(state_data)
        
        # 验证进度跟踪启动
        self.migration_handler._start_progress_tracking.assert_called_once_with('test_migration_id')
    
    def test_progress_tracking_thread(self):
        """测试进度跟踪线程"""
        # 创建迁移任务
        task_id = self.migration_handler.create_migration_task(
            self.source_server,
            self.target_server,
            'full'
        )
        
        # 创建停止标志
        self.migration_handler.stop_flags[task_id] = Mock()
        self.migration_handler.stop_flags[task_id].is_set.side_effect = [False, True]  # 第二次调用时返回True
        
        # 模拟获取迁移状态
        self.migration_service.get_migration_status = Mock(return_value={
            'id': task_id,
            'state': 'preparing',
            'progress': 10,
            'error': None
        })
        
        # 启动进度跟踪线程
        self.migration_handler._progress_tracking_thread(task_id)
        
        # 验证任务状态更新
        self.assertEqual(self.migration_handler.tasks[task_id]['status'], 'preparing')
        self.assertEqual(self.migration_handler.tasks[task_id]['progress'], 10)


if __name__ == '__main__':
    unittest.main() 
