from nicegui import ui
from . import auth_client
from contextlib import contextmanager

@contextmanager
def main_layout():
    """
    创建主应用程序布局，参考dillon-admin-pro风格
    包含顶部导航栏、左侧菜单和主内容区域
    """
    # 设置主题色彩
    ui.colors(
        primary='#1976d2',     # 蓝色主色调
        secondary='#424242',   # 灰色辅助色
        accent='#82b1ff',      # 强调色
        positive='#4caf50',    # 成功色
        negative='#f44336',    # 错误色
        info='#2196f3',        # 信息色
        warning='#ff9800'      # 警告色
    )

    # 顶部导航栏
    with ui.header(elevated=True).classes('bg-white border-b border-gray-200 text-gray-800 shadow-sm'):
        with ui.row().classes('w-full items-center justify-between px-4'):
            # 左侧：Logo和标题
            with ui.row().classes('items-center gap-4'):
                ui.icon('account_tree').classes('text-3xl text-primary')
                ui.label('OmniLink 管理系统').classes('text-xl font-bold text-gray-800')
            
            # 右侧：工具栏
            with ui.row().classes('items-center gap-2'):
                # 通知按钮
                with ui.button(icon='notifications', flat=True, round=True).classes('text-gray-600 hover:text-primary'):
                    with ui.menu():
                        with ui.menu_item():
                            ui.label('暂无新通知').classes('text-gray-500')
                
                # 帮助按钮
                ui.button(icon='help_outline', flat=True, round=True).classes('text-gray-600 hover:text-primary')
                
                # 用户菜单
                with ui.button(icon='account_circle', flat=True, round=True).classes('text-gray-600 hover:text-primary'):
                    with ui.menu():
                        with ui.menu_item('个人资料', on_click=lambda: ui.navigate.to('/profile')):
                            ui.item_section().slot('prepend').content(ui.icon('person'))
                        with ui.menu_item('系统设置', on_click=lambda: ui.navigate.to('/settings')):
                            ui.item_section().slot('prepend').content(ui.icon('settings'))
                        ui.separator()
                        with ui.menu_item('退出登录', on_click=auth_client.logout):
                            ui.item_section().slot('prepend').content(ui.icon('logout'))

    # 左侧导航抽屉
    with ui.left_drawer(value=True, elevated=True).classes('bg-gray-50 border-r border-gray-200'):
        with ui.column().classes('w-full h-full'):
            # 导航菜单
            with ui.expansion('仪表板', icon='dashboard').classes('w-full'):
                with ui.list().classes('pl-4'):
                    ui.item('系统概览', on_click=lambda: ui.navigate.to('/home')).classes('hover:bg-blue-50')
                    ui.item('实时监控', on_click=lambda: ui.navigate.to('/monitor')).classes('hover:bg-blue-50')
            
            with ui.expansion('设备管理', icon='devices').classes('w-full'):
                with ui.list().classes('pl-4'):
                    ui.item('设备列表', on_click=lambda: ui.navigate.to('/devices')).classes('hover:bg-blue-50')
                    ui.item('设备分组', on_click=lambda: ui.navigate.to('/device-groups')).classes('hover:bg-blue-50')
                    ui.item('设备策略', on_click=lambda: ui.navigate.to('/device-policies')).classes('hover:bg-blue-50')
            
            with ui.expansion('从服务器', icon='dns').classes('w-full'):
                with ui.list().classes('pl-4'):
                    ui.item('服务器列表', on_click=lambda: ui.navigate.to('/slaves')).classes('hover:bg-blue-50')
                    ui.item('服务器监控', on_click=lambda: ui.navigate.to('/slave-monitor')).classes('hover:bg-blue-50')
                    ui.item('连接管理', on_click=lambda: ui.navigate.to('/connections')).classes('hover:bg-blue-50')
            
            with ui.expansion('用户管理', icon='people').classes('w-full'):
                with ui.list().classes('pl-4'):
                    ui.item('用户列表', on_click=lambda: ui.navigate.to('/users')).classes('hover:bg-blue-50')
                    ui.item('角色管理', on_click=lambda: ui.navigate.to('/roles')).classes('hover:bg-blue-50')
                    ui.item('权限管理', on_click=lambda: ui.navigate.to('/permissions')).classes('hover:bg-blue-50')
                    ui.item('组织架构', on_click=lambda: ui.navigate.to('/organizations')).classes('hover:bg-blue-50')
            
            with ui.expansion('系统管理', icon='settings').classes('w-full'):
                with ui.list().classes('pl-4'):
                    ui.item('系统配置', on_click=lambda: ui.navigate.to('/system-config')).classes('hover:bg-blue-50')
                    ui.item('审计日志', on_click=lambda: ui.navigate.to('/audit-logs')).classes('hover:bg-blue-50')
                    ui.item('备份恢复', on_click=lambda: ui.navigate.to('/backup')).classes('hover:bg-blue-50')
                    ui.item('系统信息', on_click=lambda: ui.navigate.to('/system-info')).classes('hover:bg-blue-50')

    # 主内容区域
    with ui.page_container().classes('bg-gray-100 min-h-screen'):
        with ui.column().classes('w-full p-6'):
            # 这里是页面内容区域
            yield 