# 数据库配置
DB_HOST=postgres
DB_PORT=5432
DB_USER=omnilink
DB_PASSWORD=omnilink_password
DB_NAME=omnilink_db

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# 应用配置
SECRET_KEY=your_very_secure_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# 服务配置
ENVIRONMENT=production  # development, testing, production
DEBUG=false
ALLOWED_HOSTS=localhost,127.0.0.1

# 主服务器配置
MAIN_SERVER_PORT=8000
USB_CONTROL_PORT=25001

# 从服务器配置
SLAVE_SERVER_NAME=slave1
SLAVE_SERVER_TOKEN=your_secure_slave_server_token
VIRTUALHERE_SERVER_PORT=7575
SLAVE_SERVER_PORT=5000

# 邮件配置 (可选)
MAIL_SERVER=smtp.example.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_mail_password
MAIL_DEFAULT_SENDER=<EMAIL>

# 安全配置
SESSION_TIMEOUT_MINUTES=60
MAX_LOGIN_ATTEMPTS=5
LOGIN_COOLDOWN_MINUTES=15

# 其他配置
UPLOAD_FOLDER=/app/data/uploads
TEMP_FOLDER=/app/data/temp
MAX_UPLOAD_SIZE=10485760  # 10MB 