#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主题生成脚本

从colors.json、typography.json和motion.json文件生成CSS和Python格式的主题定义
"""

import os
import json
import re
from pathlib import Path

# 定义文件路径
SCRIPT_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
PROJECT_ROOT = SCRIPT_DIR.parent.parent.parent  # 项目根目录
COLORS_JSON_PATH = SCRIPT_DIR.parent / "themes" / "colors.json"
TYPOGRAPHY_JSON_PATH = SCRIPT_DIR.parent / "themes" / "typography.json"
MOTION_JSON_PATH = SCRIPT_DIR.parent / "themes" / "motion.json"

WEB_CSS_PATH = PROJECT_ROOT / "websevs" / "ind" / "static" / "css" / "material-design.css"
QT_PYTHON_PATH = PROJECT_ROOT / "USER" / "src" / "app" / "views" / "material_style.py"

# 确保目录存在
def ensure_dir_exists(path):
    """确保目录存在，如果不存在则创建"""
    directory = os.path.dirname(path)
    if not os.path.exists(directory):
        os.makedirs(directory)

# 将驼峰命名转换为短横线命名（用于CSS变量）
def camel_to_kebab(name):
    """将驼峰命名转换为短横线命名"""
    return re.sub(r'(?<!^)(?=[A-Z])', '-', name).lower()

# 将驼峰命名转换为大写下划线命名（用于Python常量）
def camel_to_upper_snake(name):
    """将驼峰命名转换为大写下划线命名"""
    # 先转换为带下划线的格式
    s1 = re.sub(r'(?<!^)(?=[A-Z])', '_', name)
    # 全部转为大写
    return s1.upper()

# 生成CSS变量定义
def generate_css(colors_data, typography_data, motion_data):
    """生成CSS变量定义"""
    css_content = """/**
 * Material Design 3 CSS变量和基础样式
 * 为Web界面提供Material Design风格
 * 自动生成，请勿手动修改
 */

:root {
  /* ===== 核心颜色系统 ===== */
"""
    
    # 生成亮色模式颜色
    for key, value in colors_data["light"].items():
        if not key.startswith("elevation"):  # 阴影样式特殊处理
            css_content += f"  --md-{camel_to_kebab(key)}: {value};\n"
    
    # 添加阴影
    css_content += "\n  /* 阴影 */\n"
    for key, value in colors_data["light"].items():
        if key.startswith("elevation"):
            css_content += f"  --md-{camel_to_kebab(key)}: {value};\n"
    
    # 添加排版 - 字体系列
    css_content += "\n  /* 字体系列 */\n"
    for key, value in typography_data["fontFamily"].items():
        css_content += f"  --md-font-family-{camel_to_kebab(key)}: {value};\n"
    
    # 添加排版 - 文本样式
    css_content += "\n  /* 显示文本 */\n"
    for style, props in typography_data["display"].items():
        for prop_key, prop_value in props.items():
            css_content += f"  --md-display-{style}-{camel_to_kebab(prop_key)}: {prop_value};\n"
    
    css_content += "\n  /* 标题文本 */\n"
    for style, props in typography_data["headline"].items():
        for prop_key, prop_value in props.items():
            css_content += f"  --md-headline-{style}-{camel_to_kebab(prop_key)}: {prop_value};\n"
    
    css_content += "\n  /* 标题文本 */\n"
    for style, props in typography_data["title"].items():
        for prop_key, prop_value in props.items():
            css_content += f"  --md-title-{style}-{camel_to_kebab(prop_key)}: {prop_value};\n"
    
    css_content += "\n  /* 正文文本 */\n"
    for style, props in typography_data["body"].items():
        for prop_key, prop_value in props.items():
            css_content += f"  --md-body-{style}-{camel_to_kebab(prop_key)}: {prop_value};\n"
    
    css_content += "\n  /* 标签文本 */\n"
    for style, props in typography_data["label"].items():
        for prop_key, prop_value in props.items():
            css_content += f"  --md-label-{style}-{camel_to_kebab(prop_key)}: {prop_value};\n"
    
    # 添加动效 - 缓动曲线
    css_content += "\n  /* 缓动曲线 */\n"
    for key, value in motion_data["easing"].items():
        css_content += f"  --md-easing-{camel_to_kebab(key)}: {value};\n"
    
    # 添加动效 - 持续时间
    css_content += "\n  /* 持续时间 */\n"
    for key, value in motion_data["duration"].items():
        css_content += f"  --md-duration-{camel_to_kebab(key)}: {value};\n"
    
    # 添加间距
    css_content += "\n  /* 间距 */\n"
    for key, value in colors_data["spacing"].items():
        css_content += f"  --md-spacing-{key.lower()}: {value};\n"
    
    # 添加圆角
    css_content += "\n  /* 圆角半径 */\n"
    for key, value in colors_data["borderRadius"].items():
        css_content += f"  --md-radius-{key.lower()}: {value};\n"
    
    # 添加过渡
    css_content += "\n  /* 过渡 */\n"
    for key, value in colors_data["transition"].items():
        css_content += f"  --md-transition-{camel_to_kebab(key)}: {value};\n"
    
    # 添加深色模式
    css_content += """
}

/* ===== 深色模式 ===== */
@media (prefers-color-scheme: dark) {
  :root {
"""
    
    # 生成暗色模式颜色
    for key, value in colors_data["dark"].items():
        if not key.startswith("elevation"):  # 阴影样式特殊处理
            css_content += f"    --md-{camel_to_kebab(key)}: {value};\n"
    
    css_content += """  }
}

/* ===== 全局样式 ===== */

html, body {
  margin: 0;
  padding: 0;
  font-family: var(--md-font-family-primary, 'Roboto', 'Noto Sans SC', sans-serif);
  background-color: var(--md-background);
  color: var(--md-on-background);
  line-height: 1.5;
}

/* ===== 排版样式类 ===== */
"""

    # 添加排版样式类
    style_types = ["display", "headline", "title", "body", "label"]
    style_sizes = ["large", "medium", "small"]
    
    for type_name in style_types:
        for size in style_sizes:
            style_name = f"{type_name}-{size}"
            css_content += f"""
.md-{style_name} {{
  font-size: var(--md-{style_name}-size);
  font-weight: var(--md-{style_name}-weight);
  line-height: var(--md-{style_name}-line-height);
  letter-spacing: var(--md-{style_name}-letter-spacing);
}}
"""
    
    # 添加布局容器
    css_content += """
/* ===== 布局容器 ===== */

.md-container {
  width: 100%;
  padding-right: var(--md-spacing-medium);
  padding-left: var(--md-spacing-medium);
  margin-right: auto;
  margin-left: auto;
  max-width: 1200px;
}

.md-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-gap: var(--md-spacing-medium);
}

@media (max-width: 600px) {
  .md-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 601px) and (max-width: 960px) {
  .md-grid {
    grid-template-columns: repeat(8, 1fr);
  }
}

/* ===== 间距工具类 ===== */

.md-padding-tiny { padding: var(--md-spacing-tiny); }
.md-padding-small { padding: var(--md-spacing-small); }
.md-padding-medium { padding: var(--md-spacing-medium); }
.md-padding-large { padding: var(--md-spacing-large); }

.md-margin-tiny { margin: var(--md-spacing-tiny); }
.md-margin-small { margin: var(--md-spacing-small); }
.md-margin-medium { margin: var(--md-spacing-medium); }
.md-margin-large { margin: var(--md-spacing-large); }

/* ===== 高度工具类 ===== */

.md-elevation-1 { box-shadow: var(--md-elevation-level1); }
.md-elevation-2 { box-shadow: var(--md-elevation-level2); }
.md-elevation-3 { box-shadow: var(--md-elevation-level3); }
.md-elevation-4 { box-shadow: var(--md-elevation-level4); }
.md-elevation-5 { box-shadow: var(--md-elevation-level5); }

/* ===== 圆角工具类 ===== */

.md-radius-small { border-radius: var(--md-radius-small); }
.md-radius-medium { border-radius: var(--md-radius-medium); }
.md-radius-large { border-radius: var(--md-radius-large); }
.md-radius-extra-large { border-radius: var(--md-radius-extra-large); }
.md-radius-full { border-radius: var(--md-radius-full); }

/* ===== 颜色工具类 ===== */

.md-primary-bg { background-color: var(--md-primary); color: var(--md-on-primary); }
.md-on-primary-color { color: var(--md-on-primary); }
.md-primary-container-bg { background-color: var(--md-primary-container); color: var(--md-on-primary-container); }
.md-on-primary-container-color { color: var(--md-on-primary-container); }

.md-secondary-bg { background-color: var(--md-secondary); color: var(--md-on-secondary); }
.md-on-secondary-color { color: var(--md-on-secondary); }
.md-secondary-container-bg { background-color: var(--md-secondary-container); color: var(--md-on-secondary-container); }
.md-on-secondary-container-color { color: var(--md-on-secondary-container); }

.md-surface-bg { background-color: var(--md-surface); color: var(--md-on-surface); }
.md-on-surface-color { color: var(--md-on-surface); }
.md-surface-variant-bg { background-color: var(--md-surface-variant); color: var(--md-on-surface-variant); }
.md-on-surface-variant-color { color: var(--md-on-surface-variant); }

.md-error-bg { background-color: var(--md-error); color: var(--md-on-error); }
.md-on-error-color { color: var(--md-on-error); }
.md-error-container-bg { background-color: var(--md-error-container); color: var(--md-on-error-container); }
.md-on-error-container-color { color: var(--md-on-error-container); }

.md-outline-color { color: var(--md-outline); }
.md-outline-variant-color { color: var(--md-outline-variant); }

/* ===== 透明度工具类 ===== */

.md-opacity-disabled { opacity: 0.38; }
.md-opacity-medium { opacity: 0.6; }
.md-opacity-full { opacity: 1; }

/* ===== 动效工具类 ===== */

.md-transition-standard { transition: all var(--md-transition-standard); }
.md-transition-emphasized { transition: all var(--md-transition-emphasized); }

/* ===== 响应式隐藏工具类 ===== */

@media (max-width: 600px) {
  .md-hide-small { display: none !important; }
}

@media (min-width: 601px) and (max-width: 960px) {
  .md-hide-medium { display: none !important; }
}

@media (min-width: 961px) {
  .md-hide-large { display: none !important; }
}
"""
    
    return css_content

# 生成Python常量定义
def generate_python(colors_data, typography_data, motion_data):
    """生成Python常量定义"""
    py_content = """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
Material Design 样式模块

提供Material Design 3风格的颜色、字体和样式定义，用于创建符合MD规范的QT界面
自动生成的颜色定义部分，请勿手动修改
\"\"\"

from PyQt5.QtGui import QFont, QColor, QPalette
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, pyqtSignal, QObject
from enum import Enum
import json
import os

class ThemeMode(Enum):
    \"\"\"主题模式枚举\"\"\"
    LIGHT = "light"
    DARK = "dark"

class MaterialColors:
    \"\"\"Material Design 3 颜色系统\"\"\"
    
    # 当前主题模式
    CURRENT_THEME = ThemeMode.LIGHT
    
    # 颜色定义 - 亮色模式
    LIGHT = {
"""
    
    # 生成亮色模式颜色字典
    for key, value in colors_data["light"].items():
        py_content += f'        "{camel_to_upper_snake(key)}": "{value}",\n'
    
    py_content += """    }
    
    # 颜色定义 - 暗色模式
    DARK = {
"""
    
    # 生成暗色模式颜色字典
    for key, value in colors_data["dark"].items():
        py_content += f'        "{camel_to_upper_snake(key)}": "{value}",\n'
    
    py_content += """    }
    
    # 当前活动主题的颜色（默认为亮色模式）
"""
    
    # 生成颜色常量（默认使用亮色模式）
    for key in colors_data["light"].keys():
        const_name = camel_to_upper_snake(key)
        py_content += f'    {const_name} = LIGHT["{const_name}"]\n'
    
    py_content += """
    @classmethod
    def set_theme(cls, theme_mode=ThemeMode.LIGHT):
        \"\"\"
        设置主题模式并更新所有颜色常量
        
        Args:
            theme_mode: ThemeMode枚举值，指定主题模式
        \"\"\"
        cls.CURRENT_THEME = theme_mode
        theme_dict = cls.DARK if theme_mode == ThemeMode.DARK else cls.LIGHT
        
        # 更新所有颜色常量
"""

    # 生成更新颜色常量的代码
    for key in colors_data["light"].keys():
        const_name = camel_to_upper_snake(key)
        py_content += f'        cls.{const_name} = theme_dict["{const_name}"]\n'
    
    py_content += """
    @classmethod
    def get_rgb(cls, color_hex):
        \"\"\"
        将十六进制颜色转换为RGB元组
        
        Args:
            color_hex: 十六进制颜色字符串，例如"#RRGGBB"
            
        Returns:
            RGB元组 (r, g, b)
        \"\"\"
        color_hex = color_hex.lstrip('#')
        return tuple(int(color_hex[i:i+2], 16) for i in (0, 2, 4))
    
    @classmethod
    def get_qcolor(cls, color_hex):
        \"\"\"
        将十六进制颜色转换为QColor对象
        
        Args:
            color_hex: 十六进制颜色字符串，例如"#RRGGBB"
            
        Returns:
            QColor对象
        \"\"\"
        if color_hex.startswith("rgba"):
            # 解析rgba格式
            vals = color_hex.strip("rgba(").rstrip(")").split(",")
            r, g, b = int(vals[0]), int(vals[1]), int(vals[2])
            a = int(float(vals[3]) * 255)
            return QColor(r, g, b, a)
        else:
            # 解析十六进制格式
            color_hex = color_hex.lstrip('#')
            if len(color_hex) == 6:
                r, g, b = tuple(int(color_hex[i:i+2], 16) for i in (0, 2, 4))
                return QColor(r, g, b)
            else:
                return QColor(color_hex)

class MaterialFont:
    \"\"\"Material Design 3 字体系统\"\"\"
    
    # 基础字体系列
    PRIMARY_FONT_FAMILY = typography_data["fontFamily"]["primary"].split(", ")[0].replace("'", "").replace('"', '')
    MONOSPACE_FONT_FAMILY = typography_data["fontFamily"]["monospace"].split(", ")[0].replace("'", "").replace('"', '')
    
    # 字体定义
    FONTS = {
"""

    # 生成字体定义
    for category in ["display", "headline", "title", "body", "label"]:
        for size, props in typography_data[category].items():
            py_content += f'        "{category}_{size}": ' + "{\n"
            size_value = props["size"].replace("px", "")
            weight_value = props["weight"]
            letter_spacing = props["letterSpacing"].replace("px", "")
            
            py_weight = "QFont.Normal"
            if weight_value == 500:
                py_weight = "QFont.Medium"
            elif weight_value == 700:
                py_weight = "QFont.Bold"
            
            py_content += f'            "size": {size_value},\n'
            py_content += f'            "weight": {py_weight},\n'
            py_content += f'            "letter_spacing": {letter_spacing}\n'
            py_content += "        },\n"
    
    py_content += """    }
    
    @classmethod
    def get_font(cls, style_name):
        \"\"\"
        获取指定样式的QFont对象
        
        Args:
            style_name: 字体样式名称，例如"body_large"
            
        Returns:
            QFont对象
        \"\"\"
        font = QFont()
        
        # 设置字体系列
        font.setFamily(cls.PRIMARY_FONT_FAMILY)
        
        # 设置字体属性
        if style_name in cls.FONTS:
            style = cls.FONTS[style_name]
            font.setPointSize(style["size"])
            font.setWeight(style["weight"])
            font.setLetterSpacing(QFont.AbsoluteSpacing, style["letter_spacing"])
        
        return font

class MaterialMotion:
    \"\"\"Material Design 3 动效系统\"\"\"
    
    # 缓动曲线
"""

    # 生成缓动曲线定义
    for name, value in motion_data["easing"].items():
        const_name = camel_to_upper_snake(name)
        py_content += f'    EASING_{const_name} = "{value}"\n'
    
    py_content += "\n    # 持续时间\n"
    
    # 生成持续时间定义
    for name, value in motion_data["duration"].items():
        const_name = camel_to_upper_snake(name)
        duration_ms = value.replace("ms", "")
        py_content += f'    DURATION_{const_name} = {duration_ms}  # ms\n'
    
    py_content += """
    # 标准过渡类型
    TRANSITIONS = {
"""
    
    # 生成过渡类型定义
    for name, props in motion_data["transitions"].items():
        py_content += f'        "{name}": ' + "{\n"
        duration_key = props["duration"]
        easing_key = props["easing"]
        py_content += f'            "duration": "DURATION_{duration_key.upper()}",\n'
        py_content += f'            "easing": "EASING_{easing_key.upper()}"\n'
        py_content += "        },\n"
    
    py_content += """    }
    
    @classmethod
    def get_duration_ms(cls, duration_name):
        \"\"\"获取指定持续时间的毫秒值\"\"\"
        attr_name = f"DURATION_{duration_name.upper()}"
        if hasattr(cls, attr_name):
            return getattr(cls, attr_name)
        return 300  # 默认300毫秒
    
    @classmethod
    def get_easing_curve(cls, easing_name):
        \"\"\"获取指定缓动曲线的QEasingCurve\"\"\"
        from PyQt5.QtCore import QEasingCurve
        
        # 标准缓动曲线映射
        easing_map = {
            "standard": QEasingCurve.OutCubic,
            "accelerate": QEasingCurve.InCubic,
            "decelerate": QEasingCurve.OutCubic,
            "sharp": QEasingCurve.InOutCubic
        }
        
        return easing_map.get(easing_name, QEasingCurve.OutCubic)

class ThemeManager(QObject):
    \"\"\"Material Design 主题管理器\"\"\"
    
    # 主题变更信号
    theme_changed = pyqtSignal(ThemeMode)
    
    def __init__(self):
        super().__init__()
        self._theme_mode = ThemeMode.LIGHT
    
    def toggle_theme(self):
        \"\"\"切换主题模式\"\"\"
        if self._theme_mode == ThemeMode.LIGHT:
            self.set_theme_mode(ThemeMode.DARK)
        else:
            self.set_theme_mode(ThemeMode.LIGHT)
    
    def set_theme_mode(self, mode):
        \"\"\"设置主题模式\"\"\"
        if mode != self._theme_mode:
            self._theme_mode = mode
            MaterialColors.set_theme(mode)
            self.theme_changed.emit(mode)
    
    def get_theme_mode(self):
        \"\"\"获取当前主题模式\"\"\"
        return self._theme_mode
    
    def is_dark_mode(self):
        \"\"\"检查是否是暗色模式\"\"\"
        return self._theme_mode == ThemeMode.DARK
    
    def save_preference(self, settings):
        \"\"\"保存主题偏好\"\"\"
        settings.setValue("theme/mode", self._theme_mode.value)
    
    def load_preference(self, settings):
        \"\"\"加载主题偏好\"\"\"
        mode_str = settings.value("theme/mode", ThemeMode.LIGHT.value)
        mode = ThemeMode.DARK if mode_str == "dark" else ThemeMode.LIGHT
        self.set_theme_mode(mode)

class MaterialStyle:
    \"\"\"Material Design 3 样式定义\"\"\"
    
    # 形状 - 圆角半径
"""
    
    # 生成圆角半径定义
    for key, value in colors_data["borderRadius"].items():
        const_name = "BORDER_RADIUS_" + key.upper()
        value_num = value.replace("px", "").strip()
        py_content += f'    {const_name} = {value_num}\n'
    
    py_content += """
    # 间距
"""
    for key, value in colors_data["spacing"].items():
        const_name = "SPACING_" + key.upper()
        value_num = value.replace("px", "").strip()
        py_content += f'    {const_name} = {value_num}\n'
    
    py_content += """
    # 主题管理器实例
    theme_manager = ThemeManager()
    
    @classmethod
    def apply_style(cls, app):
        \"\"\"
        将Material Design样式应用到整个应用程序
        
        Args:
            app: QApplication实例
        \"\"\"
        # 设置应用程序调色板
        palette = QPalette()
        
        # 设置窗口背景色
        palette.setColor(QPalette.Window, MaterialColors.get_qcolor(MaterialColors.BACKGROUND))
        palette.setColor(QPalette.WindowText, MaterialColors.get_qcolor(MaterialColors.ON_BACKGROUND))
        
        # 设置基础控件颜色
        palette.setColor(QPalette.Base, MaterialColors.get_qcolor(MaterialColors.SURFACE))
        palette.setColor(QPalette.AlternateBase, MaterialColors.get_qcolor(MaterialColors.SURFACE_VARIANT))
        palette.setColor(QPalette.Text, MaterialColors.get_qcolor(MaterialColors.ON_SURFACE))
        
        # 设置按钮颜色
        palette.setColor(QPalette.Button, MaterialColors.get_qcolor(MaterialColors.PRIMARY))
        palette.setColor(QPalette.ButtonText, MaterialColors.get_qcolor(MaterialColors.ON_PRIMARY))
        
        # 设置高亮和链接颜色
        palette.setColor(QPalette.Highlight, MaterialColors.get_qcolor(MaterialColors.PRIMARY))
        palette.setColor(QPalette.HighlightedText, MaterialColors.get_qcolor(MaterialColors.ON_PRIMARY))
        palette.setColor(QPalette.Link, MaterialColors.get_qcolor(MaterialColors.PRIMARY))
        
        # 应用调色板
        app.setPalette(palette)
        
        # 设置应用程序字体
        app.setFont(MaterialFont.get_font("body_medium"))
        
        # 应用全局样式表（可根据当前主题模式动态生成）
        cls._apply_stylesheet(app)
        
        # 连接主题变更信号，动态更新样式
        cls.theme_manager.theme_changed.connect(lambda mode: cls._apply_stylesheet(app))
    
    @classmethod
    def _apply_stylesheet(cls, app):
        \"\"\"应用样式表，根据当前主题生成\"\"\"
        is_dark = cls.theme_manager.is_dark_mode()
        
        # 获取当前主题的颜色
        primary = MaterialColors.PRIMARY
        on_primary = MaterialColors.ON_PRIMARY
        surface = MaterialColors.SURFACE
        on_surface = MaterialColors.ON_SURFACE
        surface_variant = MaterialColors.SURFACE_VARIANT
        outline = MaterialColors.OUTLINE
        
        # 生成基于当前主题的样式表
        stylesheet = f\"\"\"
            QWidget {{
                font-family: '{MaterialFont.PRIMARY_FONT_FAMILY}';
                background-color: {surface};
                color: {on_surface};
            }}
            
            QPushButton {{
                border: none;
                border-radius: {cls.BORDER_RADIUS_MEDIUM}px;
                padding: 6px 16px;
                background-color: {primary};
                color: {on_primary};
                font-weight: 500;
            }}
            
            QPushButton:hover {{
                background-color: #1565C0;
            }}
            
            QPushButton:pressed {{
                background-color: #0D47A1;
            }}
            
            QPushButton:disabled {{
                opacity: 0.38;
            }}
            
            QLineEdit, QTextEdit, QPlainTextEdit {{
                border: 1px solid {outline};
                border-radius: {cls.BORDER_RADIUS_SMALL}px;
                padding: 8px;
                background-color: {surface};
                color: {on_surface};
            }}
            
            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
                border: 2px solid {primary};
            }}
            
            QComboBox {{
                border: 1px solid {outline};
                border-radius: {cls.BORDER_RADIUS_SMALL}px;
                padding: 6px 12px;
                background-color: {surface};
                color: {on_surface};
            }}
            
            QTabWidget::pane {{
                border: 1px solid {outline};
                border-radius: {cls.BORDER_RADIUS_SMALL}px;
            }}
            
            QTabBar::tab {{
                background-color: {surface_variant};
                padding: 8px 16px;
                margin-right: 2px;
            }}
            
            QTabBar::tab:selected {{
                background-color: {surface};
                border-bottom: 2px solid {primary};
            }}
        \"\"\"
        
        app.setStyleSheet(stylesheet)
"""
    
    return py_content

def main():
    """主函数"""
    # 加载颜色配置
    try:
        with open(COLORS_JSON_PATH, 'r', encoding='utf-8') as f:
            colors_data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"错误：加载colors.json失败 - {e}")
        return
    
    # 加载排版配置
    try:
        with open(TYPOGRAPHY_JSON_PATH, 'r', encoding='utf-8') as f:
            typography_data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"错误：加载typography.json失败 - {e}")
        # 使用默认排版配置
        typography_data = {
            "fontFamily": {
                "primary": "Roboto, 'Noto Sans SC', sans-serif",
                "monospace": "'Roboto Mono', 'Source Code Pro', monospace"
            },
            "display": {
                "large": {"size": "57px", "weight": 400, "lineHeight": "64px", "letterSpacing": "-0.25px"},
                "medium": {"size": "45px", "weight": 400, "lineHeight": "52px", "letterSpacing": "0px"},
                "small": {"size": "36px", "weight": 400, "lineHeight": "44px", "letterSpacing": "0px"}
            },
            "headline": {
                "large": {"size": "32px", "weight": 400, "lineHeight": "40px", "letterSpacing": "0px"},
                "medium": {"size": "28px", "weight": 400, "lineHeight": "36px", "letterSpacing": "0px"},
                "small": {"size": "24px", "weight": 400, "lineHeight": "32px", "letterSpacing": "0px"}
            },
            "title": {
                "large": {"size": "22px", "weight": 400, "lineHeight": "28px", "letterSpacing": "0px"},
                "medium": {"size": "16px", "weight": 500, "lineHeight": "24px", "letterSpacing": "0.15px"},
                "small": {"size": "14px", "weight": 500, "lineHeight": "20px", "letterSpacing": "0.1px"}
            },
            "body": {
                "large": {"size": "16px", "weight": 400, "lineHeight": "24px", "letterSpacing": "0.5px"},
                "medium": {"size": "14px", "weight": 400, "lineHeight": "20px", "letterSpacing": "0.25px"},
                "small": {"size": "12px", "weight": 400, "lineHeight": "16px", "letterSpacing": "0.4px"}
            },
            "label": {
                "large": {"size": "14px", "weight": 500, "lineHeight": "20px", "letterSpacing": "0.1px"},
                "medium": {"size": "12px", "weight": 500, "lineHeight": "16px", "letterSpacing": "0.5px"},
                "small": {"size": "11px", "weight": 500, "lineHeight": "16px", "letterSpacing": "0.5px"}
            }
        }
    
    # 加载动效配置
    try:
        with open(MOTION_JSON_PATH, 'r', encoding='utf-8') as f:
            motion_data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"错误：加载motion.json失败 - {e}")
        # 使用默认动效配置
        motion_data = {
            "easing": {
                "standard": "cubic-bezier(0.4, 0.0, 0.2, 1)",
                "accelerate": "cubic-bezier(0.4, 0.0, 1.0, 1.0)",
                "decelerate": "cubic-bezier(0.0, 0.0, 0.2, 1.0)",
                "sharp": "cubic-bezier(0.4, 0.0, 0.6, 1.0)"
            },
            "duration": {
                "medium2": "300ms",
                "medium3": "350ms",
                "medium4": "400ms",
                "long2": "500ms"
            },
            "transitions": {
                "standard": {"duration": "medium2", "easing": "standard"},
                "emphasized": {"duration": "long2", "easing": "standard"},
                "emphasizedDecelerate": {"duration": "medium3", "easing": "decelerate"},
                "emphasizedAccelerate": {"duration": "medium3", "easing": "accelerate"}
            }
        }
    
    # 生成CSS
    try:
        css_content = generate_css(colors_data, typography_data, motion_data)
        ensure_dir_exists(WEB_CSS_PATH)
        with open(WEB_CSS_PATH, 'w', encoding='utf-8') as f:
            f.write(css_content)
        print(f"CSS文件生成成功: {WEB_CSS_PATH}")
    except Exception as e:
        print(f"CSS文件生成失败: {e}")
    
    # 生成Python
    try:
        py_content = generate_python(colors_data, typography_data, motion_data)
        ensure_dir_exists(QT_PYTHON_PATH)
        with open(QT_PYTHON_PATH, 'w', encoding='utf-8') as f:
            f.write(py_content)
        print(f"Python文件生成成功: {QT_PYTHON_PATH}")
    except Exception as e:
        print(f"Python文件生成失败: {e}")

if __name__ == "__main__":
    main() 