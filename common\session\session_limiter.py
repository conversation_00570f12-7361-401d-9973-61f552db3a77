#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
会话限制管理模块

此模块提供会话限制管理功能，包括会话时长控制、用户并发限制、
设备并发限制和基于规则的动态限制策略。
"""

import logging
import time
import threading
import json
import os
from enum import Enum
from typing import Dict, List, Set, Optional, Any, Callable, Tuple, Union
from datetime import datetime, timedelta

from .session_manager import SessionManager, Session, SessionState, SessionEvent

logger = logging.getLogger(__name__)

class LimitType(Enum):
    """限制类型枚举"""
    TIME_LIMIT = "time_limit"              # 时间限制
    CONCURRENT_USER_LIMIT = "user_limit"   # 用户并发限制
    CONCURRENT_DEVICE_LIMIT = "device_limit"  # 设备并发限制
    DATA_TRANSFER_LIMIT = "data_limit"     # 数据传输限制
    IDLE_TIMEOUT = "idle_timeout"          # 空闲超时
    BANDWIDTH_LIMIT = "bandwidth_limit"    # 带宽限制
    CUSTOM_LIMIT = "custom_limit"          # 自定义限制

class LimitAction(Enum):
    """限制动作枚举"""
    NOTIFY = "notify"                # 仅通知
    SUSPEND = "suspend"              # 挂起会话
    TERMINATE = "terminate"          # 终止会话
    THROTTLE = "throttle"            # 限制带宽
    LOG_ONLY = "log_only"            # 仅记录日志

class LimitRule:
    """限制规则类"""
    
    def __init__(self, 
                limit_type: LimitType,
                threshold: Any,
                action: LimitAction,
                user_id: Optional[str] = None,
                device_id: Optional[str] = None,
                description: str = "",
                enabled: bool = True,
                notification_callback: Optional[Callable[[Dict[str, Any]], None]] = None):
        """
        初始化限制规则
        
        参数:
            limit_type: 限制类型
            threshold: 阈值 (根据限制类型不同而不同)
            action: 触发限制时的动作
            user_id: 应用于特定用户 (None表示所有用户)
            device_id: 应用于特定设备 (None表示所有设备)
            description: 规则描述
            enabled: 是否启用
            notification_callback: 通知回调函数
        """
        self.limit_type = limit_type
        self.threshold = threshold
        self.action = action
        self.user_id = user_id
        self.device_id = device_id
        self.description = description
        self.enabled = enabled
        self.notification_callback = notification_callback
        self.created_at = time.time()
        self.last_triggered = None
        self.trigger_count = 0
    
    def applies_to(self, session: Session) -> bool:
        """
        检查规则是否适用于会话
        
        参数:
            session: 会话对象
            
        返回:
            bool: 是否适用
        """
        if not self.enabled:
            return False
        
        # 检查用户限制
        if self.user_id and session.user_id != self.user_id:
            return False
        
        # 检查设备限制
        if self.device_id and session.device_uuid != self.device_id:
            return False
        
        return True
    
    def check_violation(self, session: Session, context: Dict[str, Any] = None) -> bool:
        """
        检查是否违反限制
        
        参数:
            session: 会话对象
            context: 上下文信息
            
        返回:
            bool: 是否违反限制
        """
        if not self.applies_to(session):
            return False
        
        context = context or {}
        current_time = time.time()
        
        if self.limit_type == LimitType.TIME_LIMIT:
            # 时间限制 (以秒为单位)
            session_duration = current_time - session.created_at
            return session_duration >= self.threshold
        
        elif self.limit_type == LimitType.IDLE_TIMEOUT:
            # 空闲超时 (以秒为单位)
            idle_time = current_time - session.last_activity
            return idle_time >= self.threshold
        
        elif self.limit_type == LimitType.DATA_TRANSFER_LIMIT:
            # 数据传输限制 (以字节为单位)
            total_transferred = session.metrics.total_data_sent + session.metrics.total_data_received
            return total_transferred >= self.threshold
        
        elif self.limit_type == LimitType.BANDWIDTH_LIMIT:
            # 带宽限制 (以字节/秒为单位)
            # 此检查需要context中提供current_bandwidth
            if 'current_bandwidth' not in context:
                return False
            return context['current_bandwidth'] >= self.threshold
        
        elif self.limit_type == LimitType.CUSTOM_LIMIT:
            # 自定义限制
            # 此检查需要context中提供custom_check_result
            if 'custom_check_result' not in context:
                return False
            return context['custom_check_result']
        
        # CONCURRENT_USER_LIMIT 和 CONCURRENT_DEVICE_LIMIT 由SessionLimiter直接处理
        return False
    
    def trigger(self, session: Session, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        触发限制动作
        
        参数:
            session: 会话对象
            context: 上下文信息
            
        返回:
            Dict[str, Any]: 触发结果
        """
        self.last_triggered = time.time()
        self.trigger_count += 1
        
        context = context or {}
        
        # 准备通知数据
        notification_data = {
            'rule_type': self.limit_type.value,
            'action': self.action.value,
            'session_id': session.session_id,
            'user_id': session.user_id,
            'device_id': session.device_uuid,
            'timestamp': self.last_triggered,
            'threshold': self.threshold,
            'trigger_count': self.trigger_count,
            'description': self.description,
            'context': context
        }
        
        # 执行通知回调
        if self.notification_callback:
            try:
                self.notification_callback(notification_data)
            except Exception as e:
                logger.error(f"执行通知回调失败: {str(e)}")
        
        # 返回限制结果
        return {
            'triggered': True,
            'action': self.action.value,
            'rule_type': self.limit_type.value,
            'session_id': session.session_id,
            'timestamp': self.last_triggered
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        返回:
            Dict[str, Any]: 限制规则字典
        """
        return {
            'limit_type': self.limit_type.value,
            'threshold': self.threshold,
            'action': self.action.value,
            'user_id': self.user_id,
            'device_id': self.device_id,
            'description': self.description,
            'enabled': self.enabled,
            'created_at': self.created_at,
            'last_triggered': self.last_triggered,
            'trigger_count': self.trigger_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LimitRule':
        """
        从字典创建限制规则
        
        参数:
            data: 限制规则字典
            
        返回:
            LimitRule: 限制规则对象
        """
        try:
            rule = cls(
                limit_type=LimitType(data['limit_type']),
                threshold=data['threshold'],
                action=LimitAction(data['action']),
                user_id=data.get('user_id'),
                device_id=data.get('device_id'),
                description=data.get('description', ''),
                enabled=data.get('enabled', True)
            )
            
            # 恢复状态
            rule.created_at = data.get('created_at', time.time())
            rule.last_triggered = data.get('last_triggered')
            rule.trigger_count = data.get('trigger_count', 0)
            
            return rule
        except Exception as e:
            logger.error(f"从字典创建限制规则失败: {str(e)}")
            # 返回默认规则
            return cls(
                limit_type=LimitType.TIME_LIMIT,
                threshold=3600,
                action=LimitAction.NOTIFY,
                description="从损坏的数据恢复的默认规则"
            )

class SessionLimiter:
    """会话限制管理器"""
    
    def __init__(self, session_manager: SessionManager,
                check_interval: int = 30,
                storage_path: Optional[str] = None,
                default_time_limit: int = 14400,  # 4小时
                default_user_concurrent_limit: int = 5,
                default_device_concurrent_limit: int = 3,
                default_idle_timeout: int = 1800,  # 30分钟
                notification_handlers: Optional[Dict[LimitAction, List[Callable[[Dict[str, Any]], None]]]] = None):
        """
        初始化会话限制管理器
        
        参数:
            session_manager: 会话管理器实例
            check_interval: 检查间隔(秒)
            storage_path: 存储路径
            default_time_limit: 默认会话时间限制(秒)
            default_user_concurrent_limit: 默认用户并发限制
            default_device_concurrent_limit: 默认设备并发限制
            default_idle_timeout: 默认空闲超时(秒)
            notification_handlers: 通知处理器
        """
        self.session_manager = session_manager
        self.check_interval = check_interval
        self.storage_path = storage_path or os.path.expanduser("~/.ky/session_limits.json")
        
        # 确保存储目录存在
        os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
        
        # 默认限制
        self.default_time_limit = default_time_limit
        self.default_user_concurrent_limit = default_user_concurrent_limit
        self.default_device_concurrent_limit = default_device_concurrent_limit
        self.default_idle_timeout = default_idle_timeout
        
        # 通知处理器
        self.notification_handlers = notification_handlers or {action: [] for action in LimitAction}
        
        # 限制规则
        self.global_rules: List[LimitRule] = []
        self.user_rules: Dict[str, List[LimitRule]] = {}  # user_id -> rules
        self.device_rules: Dict[str, List[LimitRule]] = {}  # device_id -> rules
        
        # 用户限制覆盖
        self.user_time_limits: Dict[str, int] = {}  # user_id -> limit in seconds
        self.user_concurrent_limits: Dict[str, int] = {}  # user_id -> limit count
        
        # 设备限制覆盖
        self.device_time_limits: Dict[str, int] = {}  # device_id -> limit in seconds
        self.device_concurrent_limits: Dict[str, int] = {}  # device_id -> limit count
        
        # 会话特定限制
        self.session_time_limits: Dict[str, int] = {}  # session_id -> limit in seconds
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 运行标志
        self.running = False
        self.check_thread = None
        
        # 注册会话事件处理
        self.session_manager.register_session_listener(self._handle_session_event)
        
        # 初始化默认规则
        self._init_default_rules()
        
        # 加载保存的规则
        self._load_rules()
        
        logger.info("会话限制管理器初始化完成")
    
    def _init_default_rules(self) -> None:
        """初始化默认规则"""
        # 添加全局会话时间限制规则
        self.add_global_rule(LimitRule(
            limit_type=LimitType.TIME_LIMIT,
            threshold=self.default_time_limit,
            action=LimitAction.NOTIFY,
            description="默认会话时间限制"
        ))
        
        # 添加全局空闲超时规则
        self.add_global_rule(LimitRule(
            limit_type=LimitType.IDLE_TIMEOUT,
            threshold=self.default_idle_timeout,
            action=LimitAction.SUSPEND,
            description="默认空闲超时"
        ))
    
    def start(self) -> None:
        """启动会话限制检查"""
        if self.running:
            logger.warning("会话限制检查已在运行")
            return
        
        self.running = True
        self.check_thread = threading.Thread(target=self._check_loop)
        self.check_thread.daemon = True
        self.check_thread.start()
        
        logger.info("会话限制检查已启动")
    
    def stop(self) -> None:
        """停止会话限制检查"""
        if not self.running:
            logger.warning("会话限制检查未运行")
            return
        
        self.running = False
        
        if self.check_thread and self.check_thread.is_alive():
            self.check_thread.join(timeout=5.0)
        
        logger.info("会话限制检查已停止")
    
    def add_global_rule(self, rule: LimitRule) -> None:
        """
        添加全局限制规则
        
        参数:
            rule: 限制规则
        """
        with self.lock:
            self.global_rules.append(rule)
        
        logger.info(f"已添加全局限制规则: {rule.limit_type.value} - {rule.description}")
        
        # 保存规则
        self._save_rules()
    
    def add_user_rule(self, user_id: str, rule: LimitRule) -> None:
        """
        添加用户限制规则
        
        参数:
            user_id: 用户ID
            rule: 限制规则
        """
        with self.lock:
            if user_id not in self.user_rules:
                self.user_rules[user_id] = []
            
            self.user_rules[user_id].append(rule)
        
        logger.info(f"已添加用户 {user_id} 的限制规则: {rule.limit_type.value} - {rule.description}")
        
        # 保存规则
        self._save_rules()
    
    def add_device_rule(self, device_id: str, rule: LimitRule) -> None:
        """
        添加设备限制规则
        
        参数:
            device_id: 设备ID
            rule: 限制规则
        """
        with self.lock:
            if device_id not in self.device_rules:
                self.device_rules[device_id] = []
            
            self.device_rules[device_id].append(rule)
        
        logger.info(f"已添加设备 {device_id} 的限制规则: {rule.limit_type.value} - {rule.description}")
        
        # 保存规则
        self._save_rules()
    
    def set_user_time_limit(self, user_id: str, limit: int) -> None:
        """
        设置用户会话时间限制
        
        参数:
            user_id: 用户ID
            limit: 时间限制(秒)
        """
        with self.lock:
            self.user_time_limits[user_id] = limit
        
        logger.info(f"已设置用户 {user_id} 的会话时间限制: {limit}秒")
        
        # 保存规则
        self._save_rules()
    
    def set_user_concurrent_limit(self, user_id: str, limit: int) -> None:
        """
        设置用户并发限制
        
        参数:
            user_id: 用户ID
            limit: 并发限制
        """
        with self.lock:
            self.user_concurrent_limits[user_id] = limit
        
        logger.info(f"已设置用户 {user_id} 的并发限制: {limit}")
        
        # 保存规则
        self._save_rules()
    
    def set_device_time_limit(self, device_id: str, limit: int) -> None:
        """
        设置设备会话时间限制
        
        参数:
            device_id: 设备ID
            limit: 时间限制(秒)
        """
        with self.lock:
            self.device_time_limits[device_id] = limit
        
        logger.info(f"已设置设备 {device_id} 的会话时间限制: {limit}秒")
        
        # 保存规则
        self._save_rules()
    
    def set_device_concurrent_limit(self, device_id: str, limit: int) -> None:
        """
        设置设备并发限制
        
        参数:
            device_id: 设备ID
            limit: 并发限制
        """
        with self.lock:
            self.device_concurrent_limits[device_id] = limit
        
        logger.info(f"已设置设备 {device_id} 的并发限制: {limit}")
        
        # 保存规则
        self._save_rules()
    
    def set_session_time_limit(self, session_id: str, limit: int) -> None:
        """
        设置会话时间限制
        
        参数:
            session_id: 会话ID
            limit: 时间限制(秒)
        """
        with self.lock:
            self.session_time_limits[session_id] = limit
        
        logger.info(f"已设置会话 {session_id} 的时间限制: {limit}秒")
    
    def get_user_concurrent_count(self, user_id: str) -> int:
        """
        获取用户当前活跃会话数
        
        参数:
            user_id: 用户ID
            
        返回:
            int: 活跃会话数
        """
        active_states = [SessionState.ACTIVE, SessionState.RECONNECTING]
        sessions = self.session_manager.get_user_sessions(user_id)
        
        return sum(1 for session in sessions if session.state in active_states)
    
    def get_device_concurrent_count(self, device_id: str) -> int:
        """
        获取设备当前活跃会话数
        
        参数:
            device_id: 设备ID
            
        返回:
            int: 活跃会话数
        """
        active_states = [SessionState.ACTIVE, SessionState.RECONNECTING]
        sessions = self.session_manager.get_device_sessions(device_id)
        
        return sum(1 for session in sessions if session.state in active_states)
    
    def can_create_session(self, user_id: str, device_id: str) -> Tuple[bool, str]:
        """
        检查是否可以创建新会话
        
        参数:
            user_id: 用户ID
            device_id: 设备ID
            
        返回:
            Tuple[bool, str]: (是否可以创建, 原因)
        """
        with self.lock:
            # 检查用户并发限制
            user_limit = self.user_concurrent_limits.get(user_id, self.default_user_concurrent_limit)
            user_count = self.get_user_concurrent_count(user_id)
            
            if user_count >= user_limit:
                return False, f"用户并发限制: {user_count}/{user_limit}"
            
            # 检查设备并发限制
            device_limit = self.device_concurrent_limits.get(device_id, self.default_device_concurrent_limit)
            device_count = self.get_device_concurrent_count(device_id)
            
            if device_count >= device_limit:
                return False, f"设备并发限制: {device_count}/{device_limit}"
            
            return True, "允许创建会话"
    
    def check_session_limits(self, session_id: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        检查会话是否超出限制
        
        参数:
            session_id: 会话ID
            context: 上下文信息
            
        返回:
            Dict[str, Any]: 检查结果
        """
        session = self.session_manager.get_session(session_id)
        if not session:
            return {'status': 'not_found', 'message': '会话不存在'}
        
        # 如果会话不是活跃状态，跳过检查
        if session.state != SessionState.ACTIVE:
            return {'status': 'not_active', 'message': f'会话当前状态: {session.state.value}'}
        
        # 执行检查
        with self.lock:
            # 检查会话特定时间限制
            if session_id in self.session_time_limits:
                time_limit = self.session_time_limits[session_id]
                session_duration = time.time() - session.created_at
                
                if session_duration >= time_limit:
                    return self._handle_limit_violation(
                        session, 
                        LimitType.TIME_LIMIT, 
                        time_limit, 
                        session_duration,
                        LimitAction.TERMINATE
                    )
            
            # 检查所有适用的规则
            applicable_rules = self._get_applicable_rules(session)
            
            for rule in applicable_rules:
                if rule.check_violation(session, context):
                    # 触发规则
                    result = rule.trigger(session, context)
                    
                    # 执行限制动作
                    action_result = self._execute_limit_action(rule.action, session, rule.to_dict())
                    
                    # 合并结果
                    result.update(action_result)
                    return result
        
        # 没有违反任何限制
        return {'status': 'within_limits', 'message': '会话在限制范围内'}
    
    def _handle_limit_violation(self, session: Session, limit_type: LimitType, 
                               threshold: Any, current_value: Any,
                               action: LimitAction) -> Dict[str, Any]:
        """
        处理限制违规
        
        参数:
            session: 会话对象
            limit_type: 限制类型
            threshold: 阈值
            current_value: 当前值
            action: 执行动作
            
        返回:
            Dict[str, Any]: 处理结果
        """
        logger.info(f"会话 {session.session_id} 违反了限制 {limit_type.value}: {current_value} > {threshold}")
        
        rule_dict = {
            'limit_type': limit_type.value,
            'threshold': threshold,
            'current_value': current_value,
            'action': action.value,
            'session_id': session.session_id,
            'user_id': session.user_id,
            'device_id': session.device_uuid
        }
        
        # 执行限制动作
        result = self._execute_limit_action(action, session, rule_dict)
        
        # 添加违规信息
        result.update({
            'status': 'limit_violated',
            'limit_type': limit_type.value,
            'threshold': threshold,
            'current_value': current_value
        })
        
        return result
    
    def _execute_limit_action(self, action: LimitAction, session: Session, 
                             context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行限制动作
        
        参数:
            action: 限制动作
            session: 会话对象
            context: 上下文信息
            
        返回:
            Dict[str, Any]: 执行结果
        """
        result = {
            'action': action.value,
            'action_timestamp': time.time()
        }
        
        try:
            # 发送通知
            self._send_notification(action, context)
            
            # 执行动作
            if action == LimitAction.TERMINATE:
                # 终止会话
                session.update_state(SessionState.DISCONNECTED, {
                    'reason': 'limit_violated',
                    'limit_type': context.get('limit_type'),
                    'threshold': context.get('threshold'),
                    'current_value': context.get('current_value')
                })
                
                result['action_result'] = 'terminated'
                
            elif action == LimitAction.SUSPEND:
                # 挂起会话
                session.update_state(SessionState.SUSPENDED, {
                    'reason': 'limit_violated',
                    'limit_type': context.get('limit_type'),
                    'threshold': context.get('threshold'),
                    'current_value': context.get('current_value')
                })
                
                result['action_result'] = 'suspended'
                
            elif action == LimitAction.THROTTLE:
                # 限制带宽 (实际动作在会话处理器中执行)
                result['action_result'] = 'throttled'
                result['throttle_level'] = context.get('throttle_level', 0.5)  # 默认限制到50%
                
            elif action == LimitAction.NOTIFY:
                # 仅通知 (已在上面发送通知)
                result['action_result'] = 'notified'
                
            elif action == LimitAction.LOG_ONLY:
                # 仅记录日志
                result['action_result'] = 'logged'
                
            else:
                # 未知动作
                result['action_result'] = 'unknown_action'
                
        except Exception as e:
            logger.error(f"执行限制动作失败: {str(e)}")
            result['error'] = str(e)
            result['action_result'] = 'failed'
        
        return result
    
    def _send_notification(self, action: LimitAction, context: Dict[str, Any]) -> None:
        """
        发送限制通知
        
        参数:
            action: 限制动作
            context: 上下文信息
        """
        # 获取通知处理器
        handlers = self.notification_handlers.get(action, [])
        
        # 没有处理器则跳过
        if not handlers:
            return
        
        # 准备通知数据
        notification_data = {
            'timestamp': time.time(),
            'action': action.value,
            'context': context
        }
        
        # 调用处理器
        for handler in handlers:
            try:
                handler(notification_data)
            except Exception as e:
                logger.error(f"通知处理器异常: {str(e)}")
    
    def _get_applicable_rules(self, session: Session) -> List[LimitRule]:
        """
        获取适用于会话的规则
        
        参数:
            session: 会话对象
            
        返回:
            List[LimitRule]: 规则列表
        """
        applicable_rules = []
        
        # 添加全局规则
        applicable_rules.extend(self.global_rules)
        
        # 添加用户规则
        if session.user_id in self.user_rules:
            applicable_rules.extend(self.user_rules[session.user_id])
        
        # 添加设备规则
        if session.device_uuid in self.device_rules:
            applicable_rules.extend(self.device_rules[session.device_uuid])
        
        # 过滤出适用的规则
        return [rule for rule in applicable_rules if rule.applies_to(session)]
    
    def _handle_session_event(self, session_id: str, event: SessionEvent, 
                             details: Dict[str, Any]) -> None:
        """
        处理会话事件
        
        参数:
            session_id: 会话ID
            event: 事件类型
            details: 事件详情
        """
        # 如果会话创建，检查是否需要为其设置限制
        if event == SessionEvent.CREATED:
            session = self.session_manager.get_session(session_id)
            if not session:
                return
            
            # 设置时间限制
            user_time_limit = self.user_time_limits.get(session.user_id)
            device_time_limit = self.device_time_limits.get(session.device_uuid)
            
            # 使用最小的限制
            if user_time_limit and device_time_limit:
                session_limit = min(user_time_limit, device_time_limit)
            elif user_time_limit:
                session_limit = user_time_limit
            elif device_time_limit:
                session_limit = device_time_limit
            else:
                session_limit = self.default_time_limit
            
            # 设置会话时间限制
            self.set_session_time_limit(session_id, session_limit)
    
    def _check_loop(self) -> None:
        """会话限制检查循环"""
        while self.running:
            try:
                self._check_all_sessions()
            except Exception as e:
                logger.error(f"会话限制检查异常: {str(e)}")
            
            # 等待下一次检查
            time.sleep(self.check_interval)
    
    def _check_all_sessions(self) -> None:
        """检查所有会话的限制"""
        # 获取所有活跃会话
        active_sessions = []
        for session in self.session_manager.sessions.values():
            if session.state == SessionState.ACTIVE:
                active_sessions.append(session)
        
        # 检查每个会话
        for session in active_sessions:
            try:
                # 执行限制检查
                result = self.check_session_limits(session.session_id)
                
                if result['status'] != 'within_limits':
                    logger.info(f"会话 {session.session_id} 限制检查结果: {result}")
            except Exception as e:
                logger.error(f"检查会话 {session.session_id} 限制时异常: {str(e)}")
    
    def _save_rules(self) -> None:
        """保存规则到文件"""
        try:
            data = {
                'global_rules': [rule.to_dict() for rule in self.global_rules],
                'user_rules': {uid: [rule.to_dict() for rule in rules] for uid, rules in self.user_rules.items()},
                'device_rules': {did: [rule.to_dict() for rule in rules] for did, rules in self.device_rules.items()},
                'user_time_limits': self.user_time_limits,
                'user_concurrent_limits': self.user_concurrent_limits,
                'device_time_limits': self.device_time_limits,
                'device_concurrent_limits': self.device_concurrent_limits,
                'default_time_limit': self.default_time_limit,
                'default_user_concurrent_limit': self.default_user_concurrent_limit,
                'default_device_concurrent_limit': self.default_device_concurrent_limit,
                'default_idle_timeout': self.default_idle_timeout,
                'last_updated': time.time()
            }
            
            # 确保目录存在
            os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
            
            # 保存到文件
            with open(self.storage_path, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"已保存会话限制规则到: {self.storage_path}")
            
        except Exception as e:
            logger.error(f"保存会话限制规则失败: {str(e)}")
    
    def _load_rules(self) -> None:
        """从文件加载规则"""
        if not os.path.exists(self.storage_path):
            logger.info(f"会话限制规则文件不存在: {self.storage_path}")
            return
        
        try:
            with open(self.storage_path, 'r') as f:
                data = json.load(f)
            
            with self.lock:
                # 加载默认设置
                self.default_time_limit = data.get('default_time_limit', self.default_time_limit)
                self.default_user_concurrent_limit = data.get('default_user_concurrent_limit', self.default_user_concurrent_limit)
                self.default_device_concurrent_limit = data.get('default_device_concurrent_limit', self.default_device_concurrent_limit)
                self.default_idle_timeout = data.get('default_idle_timeout', self.default_idle_timeout)
                
                # 加载全局规则
                self.global_rules = []
                for rule_data in data.get('global_rules', []):
                    try:
                        rule = LimitRule.from_dict(rule_data)
                        self.global_rules.append(rule)
                    except Exception as e:
                        logger.error(f"加载全局规则失败: {str(e)}")
                
                # 加载用户规则
                self.user_rules = {}
                for user_id, rules_data in data.get('user_rules', {}).items():
                    self.user_rules[user_id] = []
                    for rule_data in rules_data:
                        try:
                            rule = LimitRule.from_dict(rule_data)
                            self.user_rules[user_id].append(rule)
                        except Exception as e:
                            logger.error(f"加载用户 {user_id} 规则失败: {str(e)}")
                
                # 加载设备规则
                self.device_rules = {}
                for device_id, rules_data in data.get('device_rules', {}).items():
                    self.device_rules[device_id] = []
                    for rule_data in rules_data:
                        try:
                            rule = LimitRule.from_dict(rule_data)
                            self.device_rules[device_id].append(rule)
                        except Exception as e:
                            logger.error(f"加载设备 {device_id} 规则失败: {str(e)}")
                
                # 加载限制覆盖
                self.user_time_limits = data.get('user_time_limits', {})
                self.user_concurrent_limits = data.get('user_concurrent_limits', {})
                self.device_time_limits = data.get('device_time_limits', {})
                self.device_concurrent_limits = data.get('device_concurrent_limits', {})
            
            logger.info(f"已从 {self.storage_path} 加载会话限制规则")
            
        except Exception as e:
            logger.error(f"加载会话限制规则失败: {str(e)}")

def get_session_limiter(session_manager: Optional[SessionManager] = None, **kwargs) -> SessionLimiter:
    """
    获取会话限制管理器实例(单例模式)
    
    参数:
        session_manager: 会话管理器实例
        **kwargs: 其他初始化参数
        
    返回:
        SessionLimiter: 会话限制管理器实例
    """
    if not hasattr(get_session_limiter, "_instance") or get_session_limiter._instance is None:
        from .session_manager import get_session_manager
        session_manager = session_manager or get_session_manager()
        get_session_limiter._instance = SessionLimiter(session_manager, **kwargs)
        
        # 启动会话限制检查
        get_session_limiter._instance.start()
    
    return get_session_limiter._instance 
