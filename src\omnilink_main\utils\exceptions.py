# ky/src/utils/exceptions.py
class AppExceptionBase(Exception):
    """应用自定义异常的基类"""
    # 逻辑已规划，待具体实现

class NotFoundException(AppExceptionBase):
    """资源未找到异常"""
    # 逻辑已规划，待具体实现

class BadRequestException(AppExceptionBase):
    """错误请求异常"""
    # 逻辑已规划，待具体实现

class ConflictException(AppExceptionBase):
    """资源冲突异常 (例如，唯一键冲突)"""
    # 逻辑已规划，待具体实现

class AuthenticationException(AppExceptionBase):
    """认证失败异常"""
    # 逻辑已规划，待具体实现

class AuthorizationException(AppExceptionBase):
    """授权失败/权限不足异常"""
    # 逻辑已规划，待具体实现
