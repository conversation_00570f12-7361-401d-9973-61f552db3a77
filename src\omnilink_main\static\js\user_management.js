/**
 * 用户权限管理模块
 * 实现用户管理、角色管理和权限配置功能
 */

// 全局状态
const userManagement = {
  users: [],
  roles: [],
  permissions: [],
  permissionCategories: [],
  currentView: 'users',
  selectedItems: new Set(),
  pagination: {
    page: 1,
    perPage: 10,
    total: 0
  },
  filters: {
    search: '',
    status: 'all',
    role: 'all'
  },
  sort: {
    field: 'username',
    order: 'asc'
  },
  dialogs: {
    user: null,
    role: null
  },
  websocket: null,
  editingItem: null
};

/**
 * 初始化用户管理模块
 */
function initUserManagement() {
  // 初始化Material Design组件
  initMaterialComponents();
  
  // 初始化视图切换
  initViewToggle();
  
  // 初始化批量操作
  initBatchOperations();
  
  // 初始化事件监听器
  initEventListeners();
  
  // 初始化WebSocket连接
  initWebSocket();
  
  // 加载初始数据
  loadData();
}

/**
 * 初始化Material Design组件
 */
function initMaterialComponents() {
  // 初始化顶部应用栏
  const topAppBar = document.querySelector('.mdc-top-app-bar');
  if (topAppBar) {
    mdc.topAppBar.MDCTopAppBar.attachTo(topAppBar);
  }
  
  // 初始化抽屉
  const drawer = document.querySelector('.mdc-drawer');
  if (drawer) {
    mdc.drawer.MDCDrawer.attachTo(drawer);
  }
  
  // 初始化菜单
  const menuEl = document.getElementById('user-menu');
  if (menuEl) {
    userManagement.menus = {
      userMenu: new mdc.menu.MDCMenu(menuEl)
    };
  }
  
  // 初始化按钮
  document.querySelectorAll('.mdc-button').forEach(button => {
    mdc.ripple.MDCRipple.attachTo(button);
  });
  
  // 初始化图标按钮
  document.querySelectorAll('.mdc-icon-button').forEach(button => {
    mdc.ripple.MDCRipple.attachTo(button);
  });
  
  // 初始化对话框
  const userDialogEl = document.getElementById('user-dialog');
  if (userDialogEl) {
    userManagement.dialogs.user = new mdc.dialog.MDCDialog(userDialogEl);
  }
  
  const roleDialogEl = document.getElementById('role-dialog');
  if (roleDialogEl) {
    userManagement.dialogs.role = new mdc.dialog.MDCDialog(roleDialogEl);
  }
  
  // 初始化表单字段
  document.querySelectorAll('.mdc-text-field').forEach(textField => {
    mdc.textField.MDCTextField.attachTo(textField);
  });
  
  // 初始化选择器
  document.querySelectorAll('.mdc-select').forEach(select => {
    mdc.select.MDCSelect.attachTo(select);
  });
  
  // 初始化复选框
  document.querySelectorAll('.mdc-checkbox').forEach(checkbox => {
    mdc.checkbox.MDCCheckbox.attachTo(checkbox);
  });
  
  // 初始化数据表格
  const dataTable = document.querySelector('.mdc-data-table');
  if (dataTable) {
    mdc.dataTable.MDCDataTable.attachTo(dataTable);
  }
}

/**
 * 初始化视图切换
 */
function initViewToggle() {
  const viewButtons = document.querySelectorAll('.view-toggle-button');
  viewButtons.forEach(button => {
    button.addEventListener('click', function() {
      const view = this.dataset.view;
      
      // 更新按钮状态
      viewButtons.forEach(btn => {
        btn.classList.remove('mdc-button--raised');
        btn.classList.add('mdc-button--outlined');
      });
      this.classList.remove('mdc-button--outlined');
      this.classList.add('mdc-button--raised');
      
      // 更新视图
      document.querySelectorAll('.view-panel').forEach(panel => {
        panel.style.display = 'none';
      });
      document.getElementById(`${view}-view`).style.display = 'block';
      
      // 更新添加按钮文本
      const addButton = document.getElementById('add-item-button');
      if (addButton) {
        if (view === 'users') {
          addButton.querySelector('.mdc-button__label').textContent = '添加用户';
        } else if (view === 'roles') {
          addButton.querySelector('.mdc-button__label').textContent = '添加角色';
        } else {
          addButton.style.display = 'none';
          return;
        }
        addButton.style.display = 'flex';
      }
      
      // 更新当前视图
      userManagement.currentView = view;
      
      // 重新加载数据
      if (view === 'roles') {
        loadRolesData();
      } else if (view === 'permissions') {
        loadPermissionsData();
      }
    });
  });
  
  // 默认选择用户视图
  const usersViewButton = document.getElementById('view-toggle-users');
  if (usersViewButton) {
    usersViewButton.classList.add('mdc-button--raised');
    usersViewButton.classList.remove('mdc-button--outlined');
  }
}

/**
 * 初始化批量操作
 */
function initBatchOperations() {
  // 全选复选框
  const selectAllCheckbox = document.getElementById('select-all-users');
  if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', function() {
      const checkboxes = document.querySelectorAll('#user-table-body .mdc-checkbox__native-control');
      checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
        
        const userId = checkbox.dataset.id;
        if (this.checked) {
          userManagement.selectedItems.add(userId);
        } else {
          userManagement.selectedItems.delete(userId);
        }
      });
      
      updateBatchOperationsToolbar();
    });
  }
  
  // 批量操作按钮
  document.getElementById('batch-enable-button')?.addEventListener('click', () => batchUpdateStatus('active'));
  document.getElementById('batch-disable-button')?.addEventListener('click', () => batchUpdateStatus('inactive'));
  document.getElementById('batch-role-button')?.addEventListener('click', showBatchRoleAssignment);
  document.getElementById('batch-delete-button')?.addEventListener('click', confirmBatchDelete);
  document.getElementById('cancel-batch-button')?.addEventListener('click', clearSelection);
}

/**
 * 更新批量操作工具栏
 */
function updateBatchOperationsToolbar() {
  const toolbar = document.querySelector('.batch-operations-toolbar');
  const countElement = document.getElementById('selected-item-count');
  
  if (userManagement.selectedItems.size > 0) {
    if (toolbar) toolbar.style.display = 'block';
    if (countElement) countElement.textContent = userManagement.selectedItems.size;
  } else {
    if (toolbar) toolbar.style.display = 'none';
  }
}

/**
 * 批量更新用户状态
 * @param {string} status - 要设置的状态
 */
function batchUpdateStatus(status) {
  if (userManagement.selectedItems.size === 0) return;
  
  const userIds = Array.from(userManagement.selectedItems);
  
  // 发送批量更新请求
  fetch('/api/users/batch-update-status', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      userIds,
      status
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      showNotification('批量操作成功', `已成功${status === 'active' ? '启用' : '禁用'}${data.updatedCount}个用户`);
      clearSelection();
      loadUsersData();
    } else {
      showNotification('批量操作失败', data.message || '操作过程中出现错误', 'error');
    }
  })
  .catch(error => {
    console.error('批量更新用户状态错误:', error);
    showNotification('批量操作失败', '请求过程中出现错误', 'error');
  });
}

/**
 * 显示批量角色分配对话框
 */
function showBatchRoleAssignment() {
  // 这里只是一个示例，实际上需要创建一个对话框来选择角色
  console.log('批量角色分配');
}

/**
 * 确认批量删除
 */
function confirmBatchDelete() {
  if (userManagement.selectedItems.size === 0) return;
  
  if (confirm(`确定要删除选中的${userManagement.selectedItems.size}个用户吗？此操作不可撤销。`)) {
    const userIds = Array.from(userManagement.selectedItems);
    
    // 发送批量删除请求
    fetch('/api/users/batch-delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userIds
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showNotification('批量删除成功', `已成功删除${data.deletedCount}个用户`);
        clearSelection();
        loadUsersData();
      } else {
        showNotification('批量删除失败', data.message || '操作过程中出现错误', 'error');
      }
    })
    .catch(error => {
      console.error('批量删除用户错误:', error);
      showNotification('批量删除失败', '请求过程中出现错误', 'error');
    });
  }
}

/**
 * 清除选择
 */
function clearSelection() {
  userManagement.selectedItems.clear();
  
  const selectAllCheckbox = document.getElementById('select-all-users');
  if (selectAllCheckbox) {
    selectAllCheckbox.checked = false;
  }
  
  const checkboxes = document.querySelectorAll('#user-table-body .mdc-checkbox__native-control');
  checkboxes.forEach(checkbox => {
    checkbox.checked = false;
  });
  
  updateBatchOperationsToolbar();
}

/**
 * 初始化事件监听器
 */
function initEventListeners() {
  // 菜单按钮
  document.getElementById('menu-button')?.addEventListener('click', function() {
    const drawer = document.querySelector('.mdc-drawer');
    drawer.classList.toggle('mdc-drawer--open');
  });
  
  // 用户菜单按钮
  document.getElementById('user-menu-button')?.addEventListener('click', function() {
    userManagement.menus.userMenu.open = !userManagement.menus.userMenu.open;
  });
  
  // 主题切换按钮
  document.getElementById('theme-toggle')?.addEventListener('click', function() {
    const body = document.body;
    const currentTheme = body.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    
    body.setAttribute('data-theme', newTheme);
    
    // 更新图标
    this.textContent = newTheme === 'light' ? 'dark_mode' : 'light_mode';
    
    // 保存主题设置
    localStorage.setItem('theme', newTheme);
  });
  
  // 刷新按钮
  document.getElementById('refresh-button')?.addEventListener('click', function() {
    loadData();
  });
  
  // 添加用户/角色按钮
  document.getElementById('add-item-button')?.addEventListener('click', function() {
    if (userManagement.currentView === 'users') {
      showUserDialog();
    } else if (userManagement.currentView === 'roles') {
      showRoleDialog();
    }
  });
  
  // 搜索输入
  document.getElementById('user-search')?.addEventListener('input', function() {
    userManagement.filters.search = this.value;
    applyFilters();
  });
  
  // 过滤按钮
  document.getElementById('filter-button')?.addEventListener('click', function() {
    showFilterDialog();
  });
  
  // 排序按钮
  document.getElementById('sort-button')?.addEventListener('click', function() {
    showSortDialog();
  });
  
  // 保存用户按钮
  document.getElementById('save-user-button')?.addEventListener('click', function() {
    saveUser();
  });
  
  // 保存角色按钮
  document.getElementById('save-role-button')?.addEventListener('click', function() {
    saveRole();
  });
  
  // 密码可见性切换
  document.getElementById('password-visibility')?.addEventListener('click', function() {
    const passwordInput = document.getElementById('password');
    if (passwordInput) {
      const type = passwordInput.type === 'password' ? 'text' : 'password';
      passwordInput.type = type;
      this.textContent = type === 'password' ? 'visibility_off' : 'visibility';
    }
  });
}

/**
 * 初始化WebSocket连接
 */
function initWebSocket() {
  // 创建WebSocket客户端
  userManagement.websocket = new WebSocketClient('admin', {
    onConnected: () => {
      console.log('WebSocket连接已建立');
      // 订阅用户管理相关事件
      userManagement.websocket.sendMessage('subscribe', {
        topics: ['user_events', 'role_events', 'permission_events']
      });
    },
    onDisconnected: () => {
      console.log('WebSocket连接已断开');
    },
    onMessage: (message) => {
      handleWebSocketMessage(message);
    }
  });
  
  // 尝试连接
  userManagement.websocket.connect();
}

/**
 * 处理WebSocket消息
 * @param {Object} message - 消息对象
 */
function handleWebSocketMessage(message) {
  if (!message || !message.type) return;
  
  switch (message.type) {
    case 'user_created':
    case 'user_updated':
    case 'user_deleted':
      // 重新加载用户数据
      loadUsersData();
      break;
      
    case 'role_created':
    case 'role_updated':
    case 'role_deleted':
      // 重新加载角色数据
      loadRolesData();
      break;
      
    case 'permission_updated':
      // 重新加载权限数据
      loadPermissionsData();
      break;
      
    default:
      break;
  }
}

/**
 * 加载所有数据
 */
function loadData() {
  // 根据当前视图加载数据
  if (userManagement.currentView === 'users') {
    loadUsersData();
  } else if (userManagement.currentView === 'roles') {
    loadRolesData();
  } else if (userManagement.currentView === 'permissions') {
    loadPermissionsData();
  }
}

/**
 * 加载用户数据
 */
function loadUsersData() {
  fetch('/api/users?' + new URLSearchParams({
    page: userManagement.pagination.page,
    per_page: userManagement.pagination.perPage,
    search: userManagement.filters.search,
    status: userManagement.filters.status,
    role: userManagement.filters.role,
    sort_field: userManagement.sort.field,
    sort_order: userManagement.sort.order
  }))
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      userManagement.users = data.users;
      userManagement.pagination.total = data.total;
      
      renderUsersTable();
      updatePagination();
    } else {
      showNotification('加载用户数据失败', data.message || '请求过程中出现错误', 'error');
    }
  })
  .catch(error => {
    console.error('加载用户数据错误:', error);
    showNotification('加载用户数据失败', '请求过程中出现错误', 'error');
  });
}

/**
 * 渲染用户表格
 */
function renderUsersTable() {
  const tableBody = document.getElementById('user-table-body');
  if (!tableBody) return;
  
  // 清空表格
  tableBody.innerHTML = '';
  
  // 如果没有用户数据
  if (userManagement.users.length === 0) {
    const noDataRow = document.createElement('tr');
    noDataRow.className = 'mdc-data-table__row';
    noDataRow.innerHTML = `
      <td class="mdc-data-table__cell" colspan="6" style="text-align: center;">
        没有找到用户数据
      </td>
    `;
    tableBody.appendChild(noDataRow);
    return;
  }
  
  // 渲染用户行
  userManagement.users.forEach(user => {
    const row = document.createElement('tr');
    row.className = 'mdc-data-table__row';
    row.dataset.id = user.id;
    
    // 复选框单元格
    const checkboxCell = document.createElement('td');
    checkboxCell.className = 'mdc-data-table__cell mdc-data-table__cell--checkbox';
    checkboxCell.innerHTML = `
      <div class="mdc-checkbox">
        <input type="checkbox" class="mdc-checkbox__native-control" data-id="${user.id}" 
          ${userManagement.selectedItems.has(user.id) ? 'checked' : ''} />
        <div class="mdc-checkbox__background">
          <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
          </svg>
          <div class="mdc-checkbox__mixedmark"></div>
        </div>
      </div>
    `;
    
    // 用户名单元格
    const usernameCell = document.createElement('td');
    usernameCell.className = 'mdc-data-table__cell';
    usernameCell.innerHTML = `
      <div class="user-info">
        <span class="user-name">${escapeHtml(user.username)}</span>
        <span class="user-email">${escapeHtml(user.email)}</span>
      </div>
    `;
    
    // 状态单元格
    const statusCell = document.createElement('td');
    statusCell.className = 'mdc-data-table__cell';
    statusCell.innerHTML = `
      <span class="status-badge status-badge-${user.active ? 'success' : 'error'}">
        <span class="material-icons">${user.active ? 'check_circle' : 'cancel'}</span>
        <span>${user.active ? '已启用' : '已禁用'}</span>
      </span>
    `;
    
    // 角色单元格
    const roleCell = document.createElement('td');
    roleCell.className = 'mdc-data-table__cell';
    roleCell.innerHTML = user.roles && user.roles.length > 0
      ? user.roles.map(role => `<span class="role-pill">${escapeHtml(role.name)}</span>`).join('')
      : '<span class="role-pill role-none">无角色</span>';
    
    // 最后登录单元格
    const lastLoginCell = document.createElement('td');
    lastLoginCell.className = 'mdc-data-table__cell';
    lastLoginCell.innerHTML = user.lastLogin ? formatDate(user.lastLogin) : '从未登录';
    
    // 操作单元格
    const actionsCell = document.createElement('td');
    actionsCell.className = 'mdc-data-table__cell';
    actionsCell.innerHTML = `
      <div class="table-actions">
        <button class="mdc-icon-button material-icons edit-user-button" data-id="${user.id}" title="编辑用户">edit</button>
        <button class="mdc-icon-button material-icons ${user.active ? 'disable-user-button' : 'enable-user-button'}" 
          data-id="${user.id}" title="${user.active ? '禁用用户' : '启用用户'}">
          ${user.active ? 'block' : 'check_circle'}
        </button>
        <button class="mdc-icon-button material-icons delete-user-button" data-id="${user.id}" title="删除用户">delete</button>
      </div>
    `;
    
    // 添加所有单元格到行
    row.appendChild(checkboxCell);
    row.appendChild(usernameCell);
    row.appendChild(statusCell);
    row.appendChild(roleCell);
    row.appendChild(lastLoginCell);
    row.appendChild(actionsCell);
    
    // 添加行到表格
    tableBody.appendChild(row);
  });
  
  // 初始化新添加的Material Design组件
  initRowComponents();
}

/**
 * 初始化表格行组件
 */
function initRowComponents() {
  // 初始化复选框
  document.querySelectorAll('#user-table-body .mdc-checkbox').forEach(checkbox => {
    mdc.checkbox.MDCCheckbox.attachTo(checkbox);
  });
  
  // 初始化图标按钮
  document.querySelectorAll('#user-table-body .mdc-icon-button').forEach(button => {
    mdc.ripple.MDCRipple.attachTo(button);
  });
  
  // 添加复选框事件监听器
  document.querySelectorAll('#user-table-body .mdc-checkbox__native-control').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
      const userId = this.dataset.id;
      
      if (this.checked) {
        userManagement.selectedItems.add(userId);
      } else {
        userManagement.selectedItems.delete(userId);
      }
      
      updateBatchOperationsToolbar();
    });
  });
  
  // 添加编辑按钮事件监听器
  document.querySelectorAll('.edit-user-button').forEach(button => {
    button.addEventListener('click', function() {
      const userId = this.dataset.id;
      editUser(userId);
    });
  });
  
  // 添加启用/禁用按钮事件监听器
  document.querySelectorAll('.enable-user-button, .disable-user-button').forEach(button => {
    button.addEventListener('click', function() {
      const userId = this.dataset.id;
      const enable = this.classList.contains('enable-user-button');
      toggleUserStatus(userId, enable);
    });
  });
  
  // 添加删除按钮事件监听器
  document.querySelectorAll('.delete-user-button').forEach(button => {
    button.addEventListener('click', function() {
      const userId = this.dataset.id;
      confirmDeleteUser(userId);
    });
  });
}

/**
 * 加载角色数据
 */
function loadRolesData() {
  // 获取角色数据
  fetch('/api/roles')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        userManagement.roles = data.roles;
        renderRolesList();
      } else {
        showNotification('加载角色数据失败', data.message || '请求过程中出现错误', 'error');
      }
    })
    .catch(error => {
      console.error('加载角色数据错误:', error);
      showNotification('加载角色数据失败', '请求过程中出现错误', 'error');
    });
    
  // 获取角色继承关系数据
  fetch('/api/roles/inheritance')
    .then(response => response.json())
    .then(data => {
      if (data.success && window.rbacVisualizer) {
        window.rbacVisualizer.setData(
          userManagement.roles,
          data.inheritances
        );
      }
    })
    .catch(error => {
      console.error('加载角色继承关系数据错误:', error);
    });
}

/**
 * 渲染角色列表
 */
function renderRolesList() {
  const roleList = document.getElementById('role-list');
  if (!roleList) return;
  
  // 清空列表
  roleList.innerHTML = '';
  
  // 渲染角色项
  userManagement.roles.forEach(role => {
    const roleItem = document.createElement('div');
    roleItem.className = 'mdc-list-item';
    roleItem.dataset.id = role.id;
    roleItem.innerHTML = `
      <span class="mdc-list-item__ripple"></span>
      <span class="mdc-list-item__text">${escapeHtml(role.name)}</span>
      <button class="mdc-icon-button material-icons edit-role-button" data-id="${role.id}" title="编辑角色">edit</button>
      <button class="mdc-icon-button material-icons delete-role-button" data-id="${role.id}" title="删除角色">delete</button>
    `;
    
    // 添加点击事件
    roleItem.addEventListener('click', function(event) {
      if (event.target.tagName !== 'BUTTON') {
        showRoleDetails(role.id);
      }
    });
    
    roleList.appendChild(roleItem);
  });
  
  // 初始化按钮
  document.querySelectorAll('.edit-role-button').forEach(button => {
    button.addEventListener('click', function(event) {
      event.stopPropagation();
      const roleId = this.dataset.id;
      editRole(roleId);
    });
  });
  
  document.querySelectorAll('.delete-role-button').forEach(button => {
    button.addEventListener('click', function(event) {
      event.stopPropagation();
      const roleId = this.dataset.id;
      confirmDeleteRole(roleId);
    });
  });
}

/**
 * 加载权限数据
 */
function loadPermissionsData() {
  // 获取权限类别数据
  fetch('/api/permissions/categories')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        userManagement.permissionCategories = data.categories;
        renderPermissionCategories();
      } else {
        showNotification('加载权限类别数据失败', data.message || '请求过程中出现错误', 'error');
      }
    })
    .catch(error => {
      console.error('加载权限类别数据错误:', error);
      showNotification('加载权限类别数据失败', '请求过程中出现错误', 'error');
    });
}

/**
 * 显示用户对话框
 * @param {Object} user - 用户数据（可选，用于编辑）
 */
function showUserDialog(user = null) {
  // 设置对话框标题
  const dialogTitle = document.getElementById('user-dialog-title');
  if (dialogTitle) {
    dialogTitle.textContent = user ? '编辑用户' : '添加用户';
  }
  
  // 重置表单
  const userForm = document.getElementById('user-form');
  if (userForm) {
    userForm.reset();
  }
  
  // 如果是编辑，填充用户数据
  if (user) {
    document.getElementById('username')?.setAttribute('value', user.username);
    document.getElementById('email')?.setAttribute('value', user.email);
    
    // 密码字段在编辑时可选
    const passwordField = document.getElementById('password-field');
    if (passwordField) {
      const passwordLabel = passwordField.querySelector('.mdc-floating-label');
      if (passwordLabel) {
        passwordLabel.textContent = '密码（留空保持不变）';
      }
      document.getElementById('password')?.removeAttribute('required');
    }
    
    // 设置状态
    document.getElementById('user-active')?.checked = user.active;
    
    // 设置角色
    // 这里需要实现角色选择的逻辑
  }
  
  // 保存编辑的用户
  userManagement.editingItem = user;
  
  // 打开对话框
  userManagement.dialogs.user?.open();
}

/**
 * 保存用户
 */
function saveUser() {
  // 获取表单数据
  const username = document.getElementById('username')?.value;
  const email = document.getElementById('email')?.value;
  const password = document.getElementById('password')?.value;
  const active = document.getElementById('user-active')?.checked;
  
  // 验证数据
  if (!username || !email) {
    showNotification('保存失败', '用户名和邮箱为必填项', 'error');
    return;
  }
  
  if (!userManagement.editingItem && !password) {
    showNotification('保存失败', '新用户必须设置密码', 'error');
    return;
  }
  
  // 构建用户数据
  const userData = {
    username,
    email,
    active: active || false
  };
  
  // 如果密码不为空，添加到数据中
  if (password) {
    userData.password = password;
  }
  
  // 确定API端点和方法
  const endpoint = userManagement.editingItem 
    ? `/api/users/${userManagement.editingItem.id}` 
    : '/api/users';
  const method = userManagement.editingItem ? 'PUT' : 'POST';
  
  // 发送请求
  fetch(endpoint, {
    method,
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(userData)
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      showNotification(
        userManagement.editingItem ? '更新成功' : '创建成功',
        userManagement.editingItem ? '用户信息已更新' : '新用户已创建'
      );
      
      // 关闭对话框
      userManagement.dialogs.user?.close();
      
      // 重新加载用户数据
      loadUsersData();
      
      // 清除编辑状态
      userManagement.editingItem = null;
    } else {
      showNotification('保存失败', data.message || '请求过程中出现错误', 'error');
    }
  })
  .catch(error => {
    console.error('保存用户错误:', error);
    showNotification('保存失败', '请求过程中出现错误', 'error');
  });
}

/**
 * 编辑用户
 * @param {string} userId - 用户ID
 */
function editUser(userId) {
  // 查找用户
  const user = userManagement.users.find(u => u.id === userId);
  if (user) {
    showUserDialog(user);
  } else {
    // 从服务器获取用户数据
    fetch(`/api/users/${userId}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          showUserDialog(data.user);
        } else {
          showNotification('获取用户信息失败', data.message || '请求过程中出现错误', 'error');
        }
      })
      .catch(error => {
        console.error('获取用户信息错误:', error);
        showNotification('获取用户信息失败', '请求过程中出现错误', 'error');
      });
  }
}

/**
 * 切换用户状态
 * @param {string} userId - 用户ID
 * @param {boolean} enable - 是否启用
 */
function toggleUserStatus(userId, enable) {
  fetch(`/api/users/${userId}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      active: enable
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      showNotification(
        enable ? '用户已启用' : '用户已禁用',
        `用户状态已成功更新为${enable ? '启用' : '禁用'}`
      );
      
      // 重新加载用户数据
      loadUsersData();
    } else {
      showNotification('状态更新失败', data.message || '请求过程中出现错误', 'error');
    }
  })
  .catch(error => {
    console.error('更新用户状态错误:', error);
    showNotification('状态更新失败', '请求过程中出现错误', 'error');
  });
}

/**
 * 确认删除用户
 * @param {string} userId - 用户ID
 */
function confirmDeleteUser(userId) {
  if (confirm('确定要删除此用户吗？此操作不可撤销。')) {
    fetch(`/api/users/${userId}`, {
      method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showNotification('删除成功', '用户已成功删除');
        
        // 重新加载用户数据
        loadUsersData();
      } else {
        showNotification('删除失败', data.message || '请求过程中出现错误', 'error');
      }
    })
    .catch(error => {
      console.error('删除用户错误:', error);
      showNotification('删除失败', '请求过程中出现错误', 'error');
    });
  }
}

/**
 * 显示通知
 * @param {string} title - 通知标题
 * @param {string} message - 通知内容
 * @param {string} type - 通知类型 (info, success, warning, error)
 */
function showNotification(title, message, type = 'success') {
  if (typeof showNotification_global === 'function') {
    showNotification_global(title, message, type);
  } else {
    // 简单的回退通知
    alert(`${title}\n${message}`);
  }
}

/**
 * HTML转义
 * @param {string} str - 要转义的字符串
 * @returns {string} 转义后的字符串
 */
function escapeHtml(str) {
  if (!str) return '';
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

/**
 * 格式化日期
 * @param {string} dateStr - 日期字符串
 * @returns {string} 格式化后的日期
 */
function formatDate(dateStr) {
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 当文档加载完成后初始化
document.addEventListener('DOMContentLoaded', initUserManagement); 