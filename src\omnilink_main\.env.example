# ky/main_server/.env.example

# Core App Settings
# APP_NAME="OmniLink Main Server"
# DEBUG_MODE=False
# HOST="0.0.0.0"
# PORT=8000

# Main Server Specific Settings
SERVER_HOST="0.0.0.0"
SERVER_PORT=8888
DEBUG=False
SECRET_KEY=YOUR_VERY_STRONG_AND_UNIQUE_SECRET_KEY_HERE
ENCRYPTION_KEY=YOUR_SECURE_ENCRYPTION_KEY_32_BYTES_URL_SAFE_BASE64_ENCODED
TOTP_FERNET_KEY=YOUR_FERNET_KEY_FOR_TOTP_URL_SAFE_BASE64_ENCODED

# JWT Settings
JWT_SECRET_KEY=YOUR_JWT_SECRET_KEY_USUALLY_SAME_AS_SECRET_KEY_OR_LEAVE_BLANK_TO_USE_SECRET_KEY
JWT_ALGORITHM="HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440 # 24 hours
JWT_SERVER_TOKEN_EXPIRES_SECONDS=604800 # 7 days

# Server-specific JWT (if different from main JWT)
# SERVER_JWT_SECRET_KEY=
# SERVER_JWT_ALGORITHM=

# Database (Example for SQLite, adjust for PostgreSQL)
DB_PATH="ky_main.db"
# For PostgreSQL, you might need:
# DB_USER=your_db_user
# DB_PASSWORD=your_db_password
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=your_db_name

# Logging
LOG_LEVEL="INFO"
LOG_FILE="logs/ky_main.log" # Ensure 'logs' directory exists or is created
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# Security
USE_HTTPS=False
# CERT_FILE="certs/cert.pem" # Provide path if USE_HTTPS=True
# KEY_FILE="certs/key.pem"   # Provide path if USE_HTTPS=True
ALLOWED_ORIGINS='["*"]' # JSON-encoded list, e.g., '["http://localhost:3000","https://yourdomain.com"]'

# Slave Server
SLAVE_SYNC_DELAY=5
SLAVE_DISCOVERY_PORT=45678

# Authentication
TWO_FACTOR_ENABLED=True
SUPER_ADMIN_TWO_FACTOR_REQUIRED=True
VERIFICATION_CODE_EXPIRY=300
VERIFICATION_CODE_RESEND_DELAY=60

# Slave Server Secrets (JSON string format)
# Example: SLAVE_SERVER_SECRETS='{"slave_id_1": "secret1", "slave_id_2": "secret2"}'
SLAVE_SERVER_SECRETS={} 