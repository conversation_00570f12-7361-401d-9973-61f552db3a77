#!/usr/bin/env python3
"""
本地测试运行器
在没有Docker环境的情况下验证主从服务器的核心逻辑和功能
"""

import asyncio
import logging
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('local_test.log')
    ]
)
logger = logging.getLogger(__name__)

class LocalTestRunner:
    """本地测试运行器"""
    
    def __init__(self):
        self.test_results = []
        self.passed_tests = 0
        self.total_tests = 0
        
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """记录测试结果"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            logger.info(f"✅ {test_name}: PASSED {message}")
        else:
            logger.error(f"❌ {test_name}: FAILED {message}")
        
        self.test_results.append({
            "test_name": test_name,
            "passed": passed,
            "message": message,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    def test_project_structure(self):
        """测试项目结构完整性"""
        logger.info("🔍 测试项目结构...")
        
        required_files = [
            "docker-compose.yaml",
            "app.env",
            "src/omnilink_main/main.py",
            "src/omnilink_main/core/idle_session_manager.py",
            "src/omnilink_main/services/connection_control_service.py",
            "slave_server/main.py",
            "slave_server/core/config.py",
            "common/models/user.py",
            "common/models/device.py",
            "common/models/slave_server.py",
            "deployment/dockerfiles/Dockerfile.main",
            "deployment/dockerfiles/Dockerfile.slave"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            self.log_test_result(
                "项目结构检查", 
                False, 
                f"缺失文件: {', '.join(missing_files)}"
            )
        else:
            self.log_test_result("项目结构检查", True, "所有必要文件都存在")
    
    def test_import_dependencies(self):
        """测试Python依赖导入"""
        logger.info("🔍 测试Python依赖导入...")
        
        test_imports = [
            ("fastapi", "FastAPI"),
            ("sqlalchemy", "SQLAlchemy"),
            ("pydantic", "Pydantic"),
            ("asyncio", "异步IO"),
            ("psutil", "系统监控"),
            ("websockets", "WebSocket通信")
        ]
        
        failed_imports = []
        for module_name, description in test_imports:
            try:
                __import__(module_name)
                logger.debug(f"✓ {description} ({module_name}) 导入成功")
            except ImportError as e:
                failed_imports.append(f"{description} ({module_name}): {e}")
        
        if failed_imports:
            self.log_test_result(
                "依赖导入检查", 
                False, 
                f"导入失败: {'; '.join(failed_imports)}"
            )
        else:
            self.log_test_result("依赖导入检查", True, "所有依赖导入成功")
    
    def test_configuration_files(self):
        """测试配置文件"""
        logger.info("🔍 测试配置文件...")
        
        # 测试环境配置
        if os.path.exists("app.env"):
            try:
                with open("app.env", "r") as f:
                    env_content = f.read()
                
                required_vars = [
                    "POSTGRES_SERVER",
                    "POSTGRES_USER", 
                    "POSTGRES_PASSWORD",
                    "REDIS_HOST",
                    "SECRET_KEY"
                ]
                
                missing_vars = []
                for var in required_vars:
                    if var not in env_content:
                        missing_vars.append(var)
                
                if missing_vars:
                    self.log_test_result(
                        "环境配置检查", 
                        False, 
                        f"缺失环境变量: {', '.join(missing_vars)}"
                    )
                else:
                    self.log_test_result("环境配置检查", True, "环境配置完整")
                    
            except Exception as e:
                self.log_test_result("环境配置检查", False, f"读取配置文件失败: {e}")
        else:
            self.log_test_result("环境配置检查", False, "app.env文件不存在")
    
    async def test_core_services_logic(self):
        """测试核心服务逻辑"""
        logger.info("🔍 测试核心服务逻辑...")
        
        try:
            # 测试空闲会话管理器
            from src.omnilink_main.core.idle_session_manager import IdleSessionManager
            
            session_manager = IdleSessionManager(default_timeout_hours=1)
            
            # 模拟会话操作
            session_manager.start_session(device_id=1, user_id=1)
            session_info = session_manager.get_session_info(1)
            
            if session_info and session_info["device_id"] == 1:
                self.log_test_result("空闲会话管理器", True, "会话管理逻辑正常")
            else:
                self.log_test_result("空闲会话管理器", False, "会话管理逻辑异常")
                
            session_manager.end_session(1)
            
        except Exception as e:
            self.log_test_result("空闲会话管理器", False, f"异常: {e}")
        
        try:
            # 测试端口池
            from src.omnilink_main.utils.port_pool import get_port_pool
            
            port_pool = get_port_pool(start_port=10000, end_port=10100)
            
            # 测试端口分配
            port1 = port_pool.lease_port()
            port2 = port_pool.lease_port()
            
            if port1 and port2 and port1 != port2:
                self.log_test_result("端口池管理", True, f"端口分配正常: {port1}, {port2}")
                port_pool.release_port(port1)
                port_pool.release_port(port2)
            else:
                self.log_test_result("端口池管理", False, "端口分配异常")
                
        except Exception as e:
            self.log_test_result("端口池管理", False, f"异常: {e}")
    
    def test_websocket_message_format(self):
        """测试WebSocket消息格式"""
        logger.info("🔍 测试WebSocket消息格式...")
        
        try:
            # 测试心跳消息格式
            from slave_server.schemas import HeartbeatData
            
            heartbeat = HeartbeatData(
                slave_id="test-slave-001",
                timestamp=datetime.utcnow().isoformat(),
                status="online",
                device_count=5,
                load_average=0.5,
                memory_usage=45.2
            )
            
            heartbeat_dict = heartbeat.model_dump()
            
            required_fields = ["slave_id", "timestamp", "status", "device_count"]
            missing_fields = [f for f in required_fields if f not in heartbeat_dict]
            
            if not missing_fields:
                self.log_test_result("心跳消息格式", True, "消息格式正确")
            else:
                self.log_test_result("心跳消息格式", False, f"缺失字段: {missing_fields}")
                
        except Exception as e:
            self.log_test_result("心跳消息格式", False, f"异常: {e}")
    
    def test_database_models(self):
        """测试数据库模型"""
        logger.info("🔍 测试数据库模型...")
        
        try:
            # 测试模型导入
            from common.models.user import User
            from common.models.device import Device
            from common.models.slave_server import SlaveServer
            
            # 检查模型字段
            user_fields = User.__table__.columns.keys()
            device_fields = Device.__table__.columns.keys()
            slave_fields = SlaveServer.__table__.columns.keys()
            
            # 验证关键字段
            required_user_fields = ["id", "username", "email", "hashed_password"]
            required_device_fields = ["id", "device_id", "status", "slave_server_id"]
            required_slave_fields = ["id", "server_id", "api_key", "status"]
            
            missing_user = [f for f in required_user_fields if f not in user_fields]
            missing_device = [f for f in required_device_fields if f not in device_fields]
            missing_slave = [f for f in required_slave_fields if f not in slave_fields]
            
            if not (missing_user or missing_device or missing_slave):
                self.log_test_result("数据库模型", True, "所有模型字段完整")
            else:
                missing_info = []
                if missing_user:
                    missing_info.append(f"User: {missing_user}")
                if missing_device:
                    missing_info.append(f"Device: {missing_device}")
                if missing_slave:
                    missing_info.append(f"SlaveServer: {missing_slave}")
                
                self.log_test_result("数据库模型", False, f"缺失字段: {'; '.join(missing_info)}")
                
        except Exception as e:
            self.log_test_result("数据库模型", False, f"异常: {e}")
    
    def test_api_schemas(self):
        """测试API Schema"""
        logger.info("🔍 测试API Schema...")
        
        try:
            from common.schemas.device_schema import DeviceConnectionRequest, DeviceConnectionResponse
            from common.schemas.slave_server_schema import SlaveServerRegister
            
            # 测试设备连接请求
            conn_request = DeviceConnectionRequest(
                device_id=1,
                user_contact="<EMAIL>"
            )
            
            # 测试设备连接响应
            conn_response = DeviceConnectionResponse(
                device_id=1,
                device_name="Test Device",
                virtualhere_host="*************",
                virtualhere_port=7575
            )
            
            # 测试从服务器注册
            slave_register = SlaveServerRegister(
                server_id="test-slave-001",
                name="Test Slave",
                ip_address="*************",
                port=8001,
                status="online",
                version="1.0.0"
            )
            
            self.log_test_result("API Schema", True, "所有Schema验证通过")
            
        except Exception as e:
            self.log_test_result("API Schema", False, f"异常: {e}")
    
    def test_docker_configuration(self):
        """测试Docker配置"""
        logger.info("🔍 测试Docker配置...")
        
        # 检查Docker Compose文件
        if os.path.exists("docker-compose.yaml"):
            try:
                import yaml
                with open("docker-compose.yaml", "r") as f:
                    compose_config = yaml.safe_load(f)
                
                required_services = ["postgres-db", "redis", "main-server", "slave-server"]
                existing_services = list(compose_config.get("services", {}).keys())
                
                missing_services = [s for s in required_services if s not in existing_services]
                
                if not missing_services:
                    self.log_test_result("Docker Compose配置", True, f"包含所有必要服务: {existing_services}")
                else:
                    self.log_test_result("Docker Compose配置", False, f"缺失服务: {missing_services}")
                    
            except Exception as e:
                self.log_test_result("Docker Compose配置", False, f"解析失败: {e}")
        else:
            self.log_test_result("Docker Compose配置", False, "docker-compose.yaml不存在")
        
        # 检查Dockerfile
        dockerfiles = [
            "deployment/dockerfiles/Dockerfile.main",
            "deployment/dockerfiles/Dockerfile.slave"
        ]
        
        missing_dockerfiles = [df for df in dockerfiles if not os.path.exists(df)]
        
        if not missing_dockerfiles:
            self.log_test_result("Dockerfile配置", True, "所有Dockerfile存在")
        else:
            self.log_test_result("Dockerfile配置", False, f"缺失Dockerfile: {missing_dockerfiles}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始OmniLink本地功能测试...")
        
        print("=" * 80)
        print("OmniLink 主从服务器功能验证测试")
        print("=" * 80)
        
        # 执行所有测试
        test_methods = [
            self.test_project_structure,
            self.test_import_dependencies,
            self.test_configuration_files,
            self.test_core_services_logic,
            self.test_websocket_message_format,
            self.test_database_models,
            self.test_api_schemas,
            self.test_docker_configuration
        ]
        
        for test_method in test_methods:
            try:
                if asyncio.iscoroutinefunction(test_method):
                    await test_method()
                else:
                    test_method()
            except Exception as e:
                logger.error(f"测试方法 {test_method.__name__} 执行失败: {e}")
                self.log_test_result(test_method.__name__, False, f"执行异常: {e}")
            
            # 测试间隔
            await asyncio.sleep(0.5)
        
        # 输出测试结果
        self.print_test_summary()
        
        # 保存测试报告
        self.save_test_report()
    
    def print_test_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 80)
        print("测试结果总结")
        print("=" * 80)
        
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        print(f"总测试数: {self.total_tests}")
        print(f"通过测试: {self.passed_tests}")
        print(f"失败测试: {self.total_tests - self.passed_tests}")
        print(f"成功率: {success_rate:.1f}%")
        
        print("\n详细结果:")
        for result in self.test_results:
            status = "✅ PASS" if result["passed"] else "❌ FAIL"
            print(f"{status} {result['test_name']}: {result['message']}")
        
        print("\n" + "=" * 80)
        
        if success_rate >= 90:
            print("🎉 测试结果优秀！主从服务器功能验证通过。")
        elif success_rate >= 70:
            print("⚠️  测试结果良好，但有部分问题需要修复。")
        else:
            print("❌ 测试结果不理想，需要重大修复。")
        
        print("=" * 80)
    
    def save_test_report(self):
        """保存测试报告"""
        report = {
            "test_summary": {
                "total_tests": self.total_tests,
                "passed_tests": self.passed_tests,
                "failed_tests": self.total_tests - self.passed_tests,
                "success_rate": (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0,
                "timestamp": datetime.utcnow().isoformat()
            },
            "test_results": self.test_results
        }
        
        with open("test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info("测试报告已保存到 test_report.json")

async def main():
    """主函数"""
    runner = LocalTestRunner()
    await runner.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main()) 