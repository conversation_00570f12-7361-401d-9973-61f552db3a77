#!/bin/bash
set -e

# 打印欢迎信息
echo "================================"
echo "OmniLink 主服务器启动脚本"
echo "================================"
echo ""

# 等待PostgreSQL就绪
echo "等待PostgreSQL数据库就绪..."
until nc -z $DB_HOST $DB_PORT; do
  echo "PostgreSQL数据库尚未就绪 - 等待..."
  sleep 2
done
echo "PostgreSQL数据库已就绪!"

# 等待Redis就绪
echo "等待Redis服务就绪..."
until nc -z $REDIS_HOST $REDIS_PORT; do
  echo "Redis服务尚未就绪 - 等待..."
  sleep 2
done
echo "Redis服务已就绪!"

# 运行数据库迁移 (如果需要)
# 注意：请确认数据库迁移工具的实际模块路径
# echo "运行数据库迁移..."
# python -m src.omnilink_main.migration.migration_handler upgrade

# 启动主服务器
echo "启动OmniLink主服务器..."
exec uvicorn omnilink_main.main:app --host 0.0.0.0 --port 8000 --reload 