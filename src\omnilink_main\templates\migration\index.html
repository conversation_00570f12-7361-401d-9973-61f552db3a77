<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统数据迁移管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <style>
        .task-card {
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .task-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .progress-container {
            height: 25px;
            margin-top: 10px;
        }
        .task-status {
            font-weight: bold;
        }
        .task-status.success {
            color: #198754;
        }
        .task-status.error {
            color: #dc3545;
        }
        .task-status.running {
            color: #0d6efd;
        }
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .component-badge {
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .task-time {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">系统数据迁移管理</h1>
        
        <ul class="nav nav-tabs mb-4" id="migrationTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="export-tab" data-bs-toggle="tab" data-bs-target="#export" type="button" role="tab" aria-controls="export" aria-selected="true">数据导出</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import" type="button" role="tab" aria-controls="import" aria-selected="false">数据导入</button>
            </li>
        </ul>
        
        <div class="tab-content" id="migrationTabContent">
            <!-- 导出选项卡 -->
            <div class="tab-pane fade show active" id="export" role="tabpanel" aria-labelledby="export-tab">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">创建导出任务</div>
                            <div class="card-body">
                                <form id="exportForm">
                                    <div class="mb-3">
                                        <label class="form-label">选择要导出的组件</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="database" id="exportDatabase" checked>
                                            <label class="form-check-label" for="exportDatabase">
                                                数据库
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="configurations" id="exportConfigs" checked>
                                            <label class="form-check-label" for="exportConfigs">
                                                配置文件
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="devices" id="exportDevices" checked>
                                            <label class="form-check-label" for="exportDevices">
                                                设备信息
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="users" id="exportUsers" checked>
                                            <label class="form-check-label" for="exportUsers">
                                                用户数据
                                            </label>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100" id="startExportBtn">开始导出</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-8">
                        <h3>导出任务</h3>
                        <div id="exportTasks" class="mt-3">
                            <div class="alert alert-info">没有导出任务</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 导入选项卡 -->
            <div class="tab-pane fade" id="import" role="tabpanel" aria-labelledby="import-tab">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">导入数据</div>
                            <div class="card-body">
                                <form id="importForm">
                                    <div class="mb-3">
                                        <label for="importFile" class="form-label">选择导出文件</label>
                                        <input class="form-control" type="file" id="importFile" accept=".zip">
                                        <div class="form-text">选择之前导出的ZIP文件</div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="" id="confirmImport">
                                            <label class="form-check-label" for="confirmImport">
                                                我已备份当前数据，确认导入
                                            </label>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100" id="startImportBtn" disabled>开始导入</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-8">
                        <h3>导入任务</h3>
                        <div id="importTasks" class="mt-3">
                            <div class="alert alert-info">没有导入任务</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 导出表单处理
            const exportForm = document.getElementById('exportForm');
            const exportTasks = document.getElementById('exportTasks');
            
            exportForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // 获取选中的组件
                const components = [];
                if (document.getElementById('exportDatabase').checked) components.push('database');
                if (document.getElementById('exportConfigs').checked) components.push('configurations');
                if (document.getElementById('exportDevices').checked) components.push('devices');
                if (document.getElementById('exportUsers').checked) components.push('users');
                
                if (components.length === 0) {
                    alert('请至少选择一个要导出的组件');
                    return;
                }
                
                // 准备请求数据
                const requestData = {
                    components: components,
                    options: {
                        database: {
                            type: 'sqlite',
                            name: 'main_server'
                        },
                        config_dirs: ['config'],
                        devices_data: {}, // 这里应该从API获取
                        users_data: {}    // 这里应该从API获取
                    }
                };
                
                // 开始导出
                fetch('/api/migration/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const taskId = data.task_id;
                        addExportTask(taskId, data.status, components);
                        startPollingExportStatus(taskId);
                    } else {
                        alert('导出失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('导出请求出错:', error);
                    alert('导出请求出错');
                });
            });
            
            // 导入表单处理
            const importForm = document.getElementById('importForm');
            const importTasks = document.getElementById('importTasks');
            const confirmImport = document.getElementById('confirmImport');
            const startImportBtn = document.getElementById('startImportBtn');
            
            // 确认导入复选框
            confirmImport.addEventListener('change', function() {
                startImportBtn.disabled = !this.checked;
            });
            
            importForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const fileInput = document.getElementById('importFile');
                if (!fileInput.files || fileInput.files.length === 0) {
                    alert('请选择导入文件');
                    return;
                }
                
                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                formData.append('options', JSON.stringify({
                    database: {
                        type: 'sqlite',
                        name: 'main_server'
                    },
                    config_dir: 'config'
                }));
                
                // 开始导入
                fetch('/api/migration/import', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const taskId = data.task_id;
                        addImportTask(taskId, data.status);
                        startPollingImportStatus(taskId);
                    } else {
                        alert('导入失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('导入请求出错:', error);
                    alert('导入请求出错');
                });
            });
            
            // 添加导出任务到UI
            function addExportTask(taskId, status, components) {
                if (document.querySelector('.alert-info')) {
                    exportTasks.innerHTML = '';
                }
                
                const taskCard = document.createElement('div');
                taskCard.className = 'card task-card';
                taskCard.id = `export-task-${taskId}`;
                
                const componentBadges = components.map(c => 
                    `<span class="badge bg-secondary component-badge">${c}</span>`
                ).join('');
                
                taskCard.innerHTML = `
                    <div class="card-header">
                        <span>导出任务: ${taskId}</span>
                        <span class="task-status ${getStatusClass(status)}">${status}</span>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <strong>组件:</strong> ${componentBadges}
                        </div>
                        <div class="mb-2 task-time">
                            <span>开始时间: ${new Date().toLocaleString()}</span>
                        </div>
                        <div class="progress progress-container">
                            <div id="progress-${taskId}" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                        <div class="mt-3 d-none" id="download-${taskId}">
                            <a href="/api/migration/export/${taskId}/download" class="btn btn-success">
                                <i class="bi bi-download"></i> 下载导出文件
                            </a>
                        </div>
                    </div>
                `;
                
                exportTasks.prepend(taskCard);
            }
            
            // 添加导入任务到UI
            function addImportTask(taskId, status) {
                if (document.querySelector('.alert-info')) {
                    importTasks.innerHTML = '';
                }
                
                const taskCard = document.createElement('div');
                taskCard.className = 'card task-card';
                taskCard.id = `import-task-${taskId}`;
                
                taskCard.innerHTML = `
                    <div class="card-header">
                        <span>导入任务: ${taskId}</span>
                        <span class="task-status ${getStatusClass(status)}">${status}</span>
                    </div>
                    <div class="card-body">
                        <div class="mb-2 task-time">
                            <span>开始时间: ${new Date().toLocaleString()}</span>
                        </div>
                        <div class="progress progress-container">
                            <div id="progress-${taskId}" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                        <div id="import-results-${taskId}" class="mt-3">
                            <h5>导入结果</h5>
                            <div class="results-content">
                                <div class="placeholder">导入进行中...</div>
                            </div>
                        </div>
                    </div>
                `;
                
                importTasks.prepend(taskCard);
            }
            
            // 根据状态获取CSS类
            function getStatusClass(status) {
                if (status === '已完成') return 'success';
                if (status === '出错') return 'error';
                return 'running';
            }
            
            // 开始轮询导出任务状态
            function startPollingExportStatus(taskId) {
                const intervalId = setInterval(() => {
                    fetch(`/api/migration/export/${taskId}/status`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                updateExportTaskUI(taskId, data.task);
                                
                                // 如果任务完成或出错，停止轮询
                                if (data.task.status === '已完成' || data.task.status === '出错') {
                                    clearInterval(intervalId);
                                }
                            } else {
                                console.error('获取导出状态失败:', data.message);
                                clearInterval(intervalId);
                            }
                        })
                        .catch(error => {
                            console.error('轮询导出状态出错:', error);
                            clearInterval(intervalId);
                        });
                }, 1000);
            }
            
            // 开始轮询导入任务状态
            function startPollingImportStatus(taskId) {
                const intervalId = setInterval(() => {
                    fetch(`/api/migration/import/${taskId}/status`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                updateImportTaskUI(taskId, data.task);
                                
                                // 如果任务完成或出错，停止轮询
                                if (data.task.status === '已完成' || data.task.status === '出错') {
                                    clearInterval(intervalId);
                                }
                            } else {
                                console.error('获取导入状态失败:', data.message);
                                clearInterval(intervalId);
                            }
                        })
                        .catch(error => {
                            console.error('轮询导入状态出错:', error);
                            clearInterval(intervalId);
                        });
                }, 1000);
            }
            
            // 更新导出任务UI
            function updateExportTaskUI(taskId, task) {
                const taskElement = document.getElementById(`export-task-${taskId}`);
                if (!taskElement) return;
                
                // 更新状态
                const statusElement = taskElement.querySelector('.task-status');
                statusElement.textContent = task.status;
                statusElement.className = `task-status ${getStatusClass(task.status)}`;
                
                // 更新进度条
                const progressBar = document.getElementById(`progress-${taskId}`);
                progressBar.style.width = `${task.progress}%`;
                progressBar.setAttribute('aria-valuenow', task.progress);
                progressBar.textContent = `${task.progress}%`;
                
                // 如果完成，显示下载按钮
                if (task.status === '已完成') {
                    const downloadElement = document.getElementById(`download-${taskId}`);
                    downloadElement.classList.remove('d-none');
                    
                    // 添加结束时间
                    if (task.end_time) {
                        const timeElement = taskElement.querySelector('.task-time');
                        timeElement.innerHTML += `<br>结束时间: ${new Date(task.end_time).toLocaleString()}`;
                    }
                }
                
                // 如果出错，显示错误信息
                if (task.status === '出错' && task.error) {
                    const cardBody = taskElement.querySelector('.card-body');
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'alert alert-danger mt-3';
                    errorDiv.textContent = `错误: ${task.error}`;
                    cardBody.appendChild(errorDiv);
                }
            }
            
            // 更新导入任务UI
            function updateImportTaskUI(taskId, task) {
                const taskElement = document.getElementById(`import-task-${taskId}`);
                if (!taskElement) return;
                
                // 更新状态
                const statusElement = taskElement.querySelector('.task-status');
                statusElement.textContent = task.status;
                statusElement.className = `task-status ${getStatusClass(task.status)}`;
                
                // 更新进度条
                const progressBar = document.getElementById(`progress-${taskId}`);
                progressBar.style.width = `${task.progress}%`;
                progressBar.setAttribute('aria-valuenow', task.progress);
                progressBar.textContent = `${task.progress}%`;
                
                // 显示导入结果
                if (task.status === '已完成' || task.status === '出错') {
                    const resultsContent = taskElement.querySelector('.results-content');
                    let resultsHtml = '';
                    
                    if (task.status === '出错' && task.error) {
                        resultsHtml = `<div class="alert alert-danger">${task.error}</div>`;
                    } else if (task.results) {
                        resultsHtml = '<ul class="list-group">';
                        for (const [component, result] of Object.entries(task.results)) {
                            const icon = result.success ? '✓' : '✗';
                            const iconClass = result.success ? 'text-success' : 'text-danger';
                            resultsHtml += `
                                <li class="list-group-item">
                                    <span class="${iconClass}">${icon}</span>
                                    <strong>${component}:</strong> ${result.message}
                                </li>
                            `;
                        }
                        resultsHtml += '</ul>';
                    }
                    
                    resultsContent.innerHTML = resultsHtml;
                    
                    // 添加结束时间
                    if (task.end_time) {
                        const timeElement = taskElement.querySelector('.task-time');
                        timeElement.innerHTML += `<br>结束时间: ${new Date(task.end_time).toLocaleString()}`;
                    }
                }
            }
        });
    </script>
</body>
</html> 