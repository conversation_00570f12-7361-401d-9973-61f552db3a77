#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
异常检测API模块
提供异常检测规则的管理和检测结果的查询接口
"""

import json
import logging
import datetime
from typing import Dict, Any, List, Optional, Union
from fastapi import APIRouter, HTTPException, Depends, Query, Body, status
from pydantic import BaseModel, Field, validator

logger = logging.getLogger(__name__)

# 尝试导入相关服务和模型
try:
    from main_server.security.anomaly_detection.service import AnomalyRuleService
    from main_server.security.anomaly_detection.models import AnomalyDetectionRule, AnomalyEvent
except ImportError:
    logger.warning("异常检测服务模块不可用，使用模拟实现")
    
    # 创建模拟的服务和模型
    class AnomalyDetectionRule:
        def __init__(self, **kwargs):
            self.id = kwargs.get('id', 1)
            self.name = kwargs.get('name', 'test_rule')
            self.description = kwargs.get('description', '测试规则')
            self.event_type = kwargs.get('event_type', 'login_anomaly')
            self.parameters = kwargs.get('parameters', '{}')
            self.is_active = kwargs.get('is_active', True)
            self.severity = kwargs.get('severity', 'MEDIUM')
            self.notification_channels = kwargs.get('notification_channels', '["web_ui_alert"]')
            self.override_active = kwargs.get('override_active', False)
            self.override_description = kwargs.get('override_description', None)
            self.override_parameters = kwargs.get('override_parameters', None)
            self.override_start_time = kwargs.get('override_start_time', None)
            self.override_end_time = kwargs.get('override_end_time', None)
            self.grace_period_until = kwargs.get('grace_period_until', None)
            self.created_at = kwargs.get('created_at', datetime.datetime.now())
            self.updated_at = kwargs.get('updated_at', datetime.datetime.now())
            self.automated_response_action = kwargs.get('automated_response_action', None)
            self.is_suppressed = kwargs.get('is_suppressed', False)
    
    class AnomalyEvent:
        def __init__(self, **kwargs):
            self.id = kwargs.get('id', 1)
            self.rule_id = kwargs.get('rule_id', 1)
            self.event_type = kwargs.get('event_type', 'login_anomaly')
            self.severity = kwargs.get('severity', 'MEDIUM')
            self.detected_at = kwargs.get('detected_at', datetime.datetime.now())
            self.event_data = kwargs.get('event_data', '{}')
            self.is_confirmed = kwargs.get('is_confirmed', False)
            self.is_false_positive = kwargs.get('is_false_positive', False)
    
    class AnomalyRuleService:
        @staticmethod
        def create_rule(rule_data: Dict[str, Any]) -> AnomalyDetectionRule:
            return AnomalyDetectionRule(**rule_data)
        
        @staticmethod
        def list_rules(active_only: bool = False) -> List[AnomalyDetectionRule]:
            rules = [
                AnomalyDetectionRule(id=1, name='登录异常检测'),
                AnomalyDetectionRule(id=2, name='设备访问异常检测')
            ]
            if active_only:
                return [rule for rule in rules if rule.is_active]
            return rules
        
        @staticmethod
        def get_rule_by_id(rule_id: int) -> Optional[AnomalyDetectionRule]:
            if rule_id == 1:
                return AnomalyDetectionRule(id=1, name='登录异常检测')
            return None
        
        @staticmethod
        def update_rule(rule_id: int, rule_data: Dict[str, Any]) -> Optional[AnomalyDetectionRule]:
            rule = AnomalyRuleService.get_rule_by_id(rule_id)
            if rule:
                for key, value in rule_data.items():
                    setattr(rule, key, value)
                rule.updated_at = datetime.datetime.now()
            return rule
        
        @staticmethod
        def delete_rule(rule_id: int) -> bool:
            return rule_id == 1
        
        @staticmethod
        def get_events(rule_id: Optional[int] = None, limit: int = 100) -> List[AnomalyEvent]:
            events = [
                AnomalyEvent(id=1, rule_id=1, event_type='login_anomaly'),
                AnomalyEvent(id=2, rule_id=1, event_type='access_anomaly')
            ]
            if rule_id:
                events = [event for event in events if event.rule_id == rule_id]
            return events[:limit]

# Pydantic模型定义
class AnomalyRuleCreateRequest(BaseModel):
    """创建异常检测规则请求模型"""
    name: str = Field(..., min_length=1, max_length=255, description="规则名称")
    event_type: str = Field(..., description="事件类型")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="规则参数")
    description: Optional[str] = Field(None, max_length=1000, description="规则描述")
    is_active: bool = Field(True, description="是否激活")
    severity: str = Field("MEDIUM", description="严重程度")
    notification_channels: List[str] = Field(default_factory=lambda: ["web_ui_alert"], description="通知渠道")
    automated_response_action: Optional[str] = Field(None, description="自动响应动作")
    is_suppressed: bool = Field(False, description="是否抑制")
    
    @validator('severity')
    def validate_severity(cls, v):
        allowed_severities = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']
        if v.upper() not in allowed_severities:
            raise ValueError(f'严重程度必须是以下之一: {allowed_severities}')
        return v.upper()
    
    @validator('event_type')
    def validate_event_type(cls, v):
        allowed_types = ['login_anomaly', 'access_anomaly', 'usage_anomaly', 'network_anomaly', 'system_anomaly']
        if v not in allowed_types:
            raise ValueError(f'事件类型必须是以下之一: {allowed_types}')
        return v

class AnomalyRuleUpdateRequest(BaseModel):
    """更新异常检测规则请求模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    event_type: Optional[str] = Field(None)
    parameters: Optional[Dict[str, Any]] = Field(None)
    description: Optional[str] = Field(None, max_length=1000)
    is_active: Optional[bool] = Field(None)
    severity: Optional[str] = Field(None)
    notification_channels: Optional[List[str]] = Field(None)
    automated_response_action: Optional[str] = Field(None)
    is_suppressed: Optional[bool] = Field(None)

class AnomalyRuleResponse(BaseModel):
    """异常检测规则响应模型"""
    id: int
    name: str
    description: Optional[str]
    event_type: str
    parameters: Dict[str, Any]
    is_active: bool
    severity: str
    notification_channels: List[str]
    override_active: bool
    override_description: Optional[str]
    override_parameters: Optional[Dict[str, Any]]
    override_start_time: Optional[datetime.datetime]
    override_end_time: Optional[datetime.datetime]
    grace_period_until: Optional[datetime.datetime]
    created_at: datetime.datetime
    updated_at: datetime.datetime
    automated_response_action: Optional[str]
    is_suppressed: bool
    
    class Config:
        json_encoders = {
            datetime.datetime: lambda dt: dt.isoformat()
        }

class AnomalyEventResponse(BaseModel):
    """异常事件响应模型"""
    id: int
    rule_id: int
    event_type: str
    severity: str
    detected_at: datetime.datetime
    event_data: Dict[str, Any]
    is_confirmed: bool
    is_false_positive: bool
    
    class Config:
        json_encoders = {
            datetime.datetime: lambda dt: dt.isoformat()
        }

class RuleOverrideRequest(BaseModel):
    """规则覆盖请求模型"""
    description: str = Field(..., description="覆盖描述")
    override_parameters: Dict[str, Any] = Field(..., description="覆盖参数")
    start_time: Optional[datetime.datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime.datetime] = Field(None, description="结束时间")

# 创建路由器
anomaly_router = APIRouter(
    prefix="/security/anomaly-rules",
    tags=["Security - Anomaly Detection Rules"],
)

# 辅助函数
def rule_to_dict(rule: AnomalyDetectionRule) -> Dict[str, Any]:
    """
    将Peewee模型转换为可序列化的字典
    
    参数:
        rule: 异常检测规则模型实例
        
    返回:
        规则数据字典
    """
    data = {
        "id": rule.id,
        "name": rule.name,
        "description": rule.description,
        "event_type": rule.event_type,
        "parameters": {},
        "is_active": rule.is_active,
        "severity": rule.severity,
        "notification_channels": [],
        "override_active": rule.override_active,
        "override_description": rule.override_description,
        "override_parameters": {},
        "override_start_time": None,
        "override_end_time": None,
        "grace_period_until": None,
        "created_at": None,
        "updated_at": None,
        "automated_response_action": rule.automated_response_action,
        "is_suppressed": rule.is_suppressed
    }

    # 处理日期时间字段
    datetime_fields = [
        'override_start_time', 'override_end_time', 'grace_period_until',
        'created_at', 'updated_at'
    ]
    
    for field in datetime_fields:
        value = getattr(rule, field, None)
        if isinstance(value, datetime.datetime):
            data[field] = value.isoformat()

    # 处理JSON字符串字段
    json_fields = {
        'parameters': 'parameters',
        'notification_channels': 'notification_channels',
        'override_parameters': 'override_parameters'
    }
    
    for field, data_key in json_fields.items():
        try:
            value = getattr(rule, field, None)
            if isinstance(value, str) and value:
                parsed_value = json.loads(value)
                data[data_key] = parsed_value
            elif value is None:
                data[data_key] = {} if field != 'notification_channels' else []
        except json.JSONDecodeError:
            logger.warning(f"无法解析规则 {rule.id} 的 {field}: {value}")
            data[data_key] = {} if field != 'notification_channels' else []

    return data

def event_to_dict(event: AnomalyEvent) -> Dict[str, Any]:
    """
    将异常事件模型转换为可序列化的字典
    
    参数:
        event: 异常事件模型实例
        
    返回:
        事件数据字典
    """
    data = {
        "id": event.id,
        "rule_id": event.rule_id,
        "event_type": event.event_type,
        "severity": event.severity,
        "detected_at": event.detected_at.isoformat() if hasattr(event.detected_at, 'isoformat') else str(event.detected_at),
        "event_data": {},
        "is_confirmed": event.is_confirmed,
        "is_false_positive": event.is_false_positive
    }
    
    # 处理事件数据JSON字段
    try:
        if isinstance(event.event_data, str) and event.event_data:
            data["event_data"] = json.loads(event.event_data)
        elif event.event_data is None:
            data["event_data"] = {}
    except json.JSONDecodeError:
        logger.warning(f"无法解析事件 {event.id} 的事件数据: {event.event_data}")
        data["event_data"] = {}
    
    return data

# API端点实现
@anomaly_router.post("/", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def create_anomaly_rule(rule_data: AnomalyRuleCreateRequest):
    """
    创建新的异常检测规则
    
    参数:
        rule_data: 规则创建请求数据
        
    返回:
        创建的规则信息
        
    异常:
        HTTPException: 创建失败时抛出
    """
    try:
        # 转换为字典格式
        rule_dict = rule_data.dict()
        
        # 序列化JSON字段
        rule_dict['parameters'] = json.dumps(rule_dict['parameters'])
        rule_dict['notification_channels'] = json.dumps(rule_dict['notification_channels'])
        
        # 创建规则
        created_rule = AnomalyRuleService.create_rule(rule_dict)
        
        return rule_to_dict(created_rule)
        
    except ValueError as ve:
        logger.error(f"创建异常检测规则时参数错误: {ve}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ve))
    except Exception as e:
        logger.exception(f"创建异常检测规则时发生异常: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建规则时发生内部错误")

@anomaly_router.get("/", response_model=List[Dict[str, Any]])
async def list_anomaly_rules(active_only: Optional[bool] = Query(None, description="是否只返回激活的规则")):
    """
    获取异常检测规则列表
    
    参数:
        active_only: 是否只返回激活的规则
        
    返回:
        规则列表
        
    异常:
        HTTPException: 查询失败时抛出
    """
    try:
        if active_only is not None:
            rules = AnomalyRuleService.list_rules(active_only=active_only)
        else:
            rules = AnomalyRuleService.list_rules()
        
        return [rule_to_dict(rule) for rule in rules]
        
    except Exception as e:
        logger.exception(f"获取异常检测规则列表时发生异常: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取规则列表时发生内部错误")

@anomaly_router.get("/{rule_id}", response_model=Dict[str, Any])
async def get_anomaly_rule(rule_id: int):
    """
    获取指定ID的异常检测规则
    
    参数:
        rule_id: 规则ID
        
    返回:
        规则详细信息
        
    异常:
        HTTPException: 规则不存在时抛出404错误
    """
    try:
        rule = AnomalyRuleService.get_rule_by_id(rule_id)
        if not rule:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"ID为 {rule_id} 的规则不存在")
        
        return rule_to_dict(rule)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"获取规则 {rule_id} 时发生异常: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取规则时发生内部错误")

@anomaly_router.put("/{rule_id}", response_model=Dict[str, Any])
async def update_anomaly_rule(rule_id: int, rule_data: AnomalyRuleUpdateRequest):
    """
    更新现有的异常检测规则
    
    参数:
        rule_id: 规则ID
        rule_data: 规则更新数据
        
    返回:
        更新后的规则信息
        
    异常:
        HTTPException: 规则不存在或更新失败时抛出
    """
    try:
        # 只包含非None的字段
        rule_dict = {k: v for k, v in rule_data.dict().items() if v is not None}
        
        # 序列化JSON字段
        if 'parameters' in rule_dict:
            rule_dict['parameters'] = json.dumps(rule_dict['parameters'])
        if 'notification_channels' in rule_dict:
            rule_dict['notification_channels'] = json.dumps(rule_dict['notification_channels'])
        
        # 更新规则
        updated_rule = AnomalyRuleService.update_rule(rule_id, rule_dict)
        if not updated_rule:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"ID为 {rule_id} 的规则不存在")
        
        return rule_to_dict(updated_rule)
        
    except HTTPException:
        raise
    except ValueError as ve:
        logger.error(f"更新规则 {rule_id} 时参数错误: {ve}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ve))
    except Exception as e:
        logger.exception(f"更新规则 {rule_id} 时发生异常: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新规则时发生内部错误")

@anomaly_router.delete("/{rule_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_anomaly_rule(rule_id: int):
    """
    删除异常检测规则
    
    参数:
        rule_id: 规则ID
        
    异常:
        HTTPException: 规则不存在时抛出404错误
    """
    try:
        if not AnomalyRuleService.delete_rule(rule_id):
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"ID为 {rule_id} 的规则不存在")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"删除规则 {rule_id} 时发生异常: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除规则时发生内部错误")

@anomaly_router.post("/{rule_id}/activate-override", response_model=Dict[str, Any])
async def activate_rule_override(
    rule_id: int,
    override_data: RuleOverrideRequest
):
    """
    激活规则覆盖
    
    参数:
        rule_id: 规则ID
        override_data: 覆盖数据
        
    返回:
        更新后的规则信息
        
    异常:
        HTTPException: 规则不存在或操作失败时抛出
    """
    try:
        # 构造覆盖数据
        rule_dict = {
            'override_active': True,
            'override_description': override_data.description,
            'override_parameters': json.dumps(override_data.override_parameters),
            'override_start_time': override_data.start_time,
            'override_end_time': override_data.end_time
        }
        
        # 更新规则
        updated_rule = AnomalyRuleService.update_rule(rule_id, rule_dict)
        if not updated_rule:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"ID为 {rule_id} 的规则不存在")
        
        return rule_to_dict(updated_rule)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"激活规则 {rule_id} 覆盖时发生异常: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="激活规则覆盖时发生内部错误")

@anomaly_router.post("/{rule_id}/deactivate-override", response_model=Dict[str, Any])
async def deactivate_rule_override(rule_id: int):
    """
    停用规则覆盖
    
    参数:
        rule_id: 规则ID
        
    返回:
        更新后的规则信息
        
    异常:
        HTTPException: 规则不存在或操作失败时抛出
    """
    try:
        # 停用覆盖
        rule_dict = {
            'override_active': False,
            'override_description': None,
            'override_parameters': None,
            'override_start_time': None,
            'override_end_time': None
        }
        
        # 更新规则
        updated_rule = AnomalyRuleService.update_rule(rule_id, rule_dict)
        if not updated_rule:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"ID为 {rule_id} 的规则不存在")
        
        return rule_to_dict(updated_rule)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"停用规则 {rule_id} 覆盖时发生异常: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="停用规则覆盖时发生内部错误")

@anomaly_router.get("/{rule_id}/events", response_model=List[Dict[str, Any]])
async def get_rule_events(
    rule_id: int,
    limit: int = Query(100, ge=1, le=1000, description="返回的事件数量限制")
):
    """
    获取指定规则的异常事件列表
    
    参数:
        rule_id: 规则ID
        limit: 返回的事件数量限制
        
    返回:
        事件列表
        
    异常:
        HTTPException: 查询失败时抛出
    """
    try:
        events = AnomalyRuleService.get_events(rule_id=rule_id, limit=limit)
        return [event_to_dict(event) for event in events]
        
    except Exception as e:
        logger.exception(f"获取规则 {rule_id} 的事件列表时发生异常: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取事件列表时发生内部错误")

@anomaly_router.get("/events/recent", response_model=List[Dict[str, Any]])
async def get_recent_events(
    limit: int = Query(50, ge=1, le=1000, description="返回的事件数量限制")
):
    """
    获取最近的异常事件列表
    
    参数:
        limit: 返回的事件数量限制
        
    返回:
        最近的事件列表
        
    异常:
        HTTPException: 查询失败时抛出
    """
    try:
        events = AnomalyRuleService.get_events(limit=limit)
        return [event_to_dict(event) for event in events]
        
    except Exception as e:
        logger.exception(f"获取最近事件列表时发生异常: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取事件列表时发生内部错误")

# 健康检查端点
@anomaly_router.get("/health")
async def health_check():
    """异常检测API健康检查"""
    return {"status": "healthy", "service": "anomaly-detection-api"} 
