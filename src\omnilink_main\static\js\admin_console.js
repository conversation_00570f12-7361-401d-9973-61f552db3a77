/**
 * 管理控制台前端JavaScript
 * 用于主从服务器管理系统的Web界面交互逻辑
 */

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 帮助按钮点击事件
    const helpBtn = document.getElementById('helpBtn');
    if (helpBtn) {
        helpBtn.addEventListener('click', function() {
            var helpModal = new bootstrap.Modal(document.getElementById('helpModal'));
            helpModal.show();
        });
    }

    // 刷新按钮点击事件
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshData();
        });
    }

    // 初始加载数据
    refreshData();
    
    // 设置自动刷新（每60秒）
    setInterval(refreshData, 60000);

    // 初始化特定页面功能
    initPageSpecificFeatures();
});

/**
 * 根据当前页面路径初始化特定功能
 */
function initPageSpecificFeatures() {
    const currentPath = window.location.pathname;
    
    // 仪表盘页面
    if (currentPath.includes('index') || currentPath.endsWith('/admin_console/')) {
        initDashboard();
    }
    // 分组管理页面
    else if (currentPath.includes('group_management')) {
        initGroupManagement();
    }
    // 迁移向导页面
    else if (currentPath.includes('migration_wizard')) {
        initMigrationWizard();
    }
    // 告警设置页面
    else if (currentPath.includes('alerts')) {
        initAlerts();
    }
    // 健康监控页面
    else if (currentPath.includes('health_monitor')) {
        initHealthMonitor();
    }
    // 系统设置页面
    else if (currentPath.includes('system_settings')) {
        initSystemSettings();
    }
}

/**
 * 刷新页面数据
 */
function refreshData() {
    const currentPath = window.location.pathname;
    
    // 显示页面顶部加载提示
    showLoadingToast('正在刷新数据...');
    
    // 根据当前页面刷新相应数据
    if (currentPath.includes('index') || currentPath.endsWith('/admin_console/')) {
        refreshDashboardData();
    }
    else if (currentPath.includes('group_management')) {
        refreshGroupData();
    }
    else if (currentPath.includes('migration_wizard')) {
        refreshMigrationData();
    }
    else if (currentPath.includes('alerts')) {
        refreshAlertData();
    }
    else if (currentPath.includes('health_monitor')) {
        refreshHealthData();
    }
    else if (currentPath.includes('system_settings')) {
        refreshSystemSettings();
    }
}

/**
 * 显示加载提示消息
 * @param {string} message - 提示消息
 */
function showLoadingToast(message) {
    // 检查是否已存在toast容器，如果不存在则创建
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // 创建toast元素
    const toastId = 'loading-toast-' + Date.now();
    const toastHtml = `
    <div id="${toastId}" class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>`;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // 显示toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 2000 });
    toast.show();
    
    // 在关闭时移除DOM元素
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
    
    return toastId;
}

/**
 * 显示消息提示
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型 (success, danger, warning, info)
 * @param {number} duration - 显示时长(毫秒)
 */
function showToast(message, type = 'success', duration = 3000) {
    // 检查是否已存在toast容器，如果不存在则创建
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
    <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>`;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // 显示toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: duration });
    toast.show();
    
    // 在关闭时移除DOM元素
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 */
function handleApiError(error) {
    console.error('API错误:', error);
    showToast('操作失败: ' + (error.message || '未知错误'), 'danger');
}

/**
 * 格式化日期时间
 * @param {string|Date} dateTime - 日期时间字符串或Date对象
 * @param {boolean} includeTime - 是否包含时间部分
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDateTime(dateTime, includeTime = true) {
    if (!dateTime) return '-';
    
    const date = dateTime instanceof Date ? dateTime : new Date(dateTime);
    
    if (isNaN(date)) return '-';
    
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    };
    
    if (includeTime) {
        options.hour = '2-digit';
        options.minute = '2-digit';
        options.second = '2-digit';
    }
    
    return new Intl.DateTimeFormat('zh-CN', options).format(date);
}

/**
 * 格式化数字为带千分位的格式
 * @param {number} num - 要格式化的数字
 * @returns {string} 格式化后的数字字符串
 */
function formatNumber(num) {
    if (num === null || num === undefined) return '-';
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 创建健康状态指示器
 * @param {string} status - 健康状态 (healthy, warning, critical, unknown)
 * @returns {string} 健康状态指示器HTML
 */
function createHealthIndicator(status) {
    const statusClasses = {
        'healthy': 'success',
        'warning': 'warning',
        'critical': 'danger',
        'unknown': 'secondary'
    };
    
    const statusIcons = {
        'healthy': 'check-circle',
        'warning': 'exclamation-triangle',
        'critical': 'times-circle',
        'unknown': 'question-circle'
    };
    
    const statusText = {
        'healthy': '健康',
        'warning': '警告',
        'critical': '故障',
        'unknown': '未知'
    };
    
    const className = statusClasses[status] || 'secondary';
    const icon = statusIcons[status] || 'question-circle';
    const text = statusText[status] || '未知';
    
    return `<span class="health-status text-${className}">
              <i class="fas fa-${icon}"></i> ${text}
            </span>`;
}

// ========== 仪表盘页面 ==========
/**
 * 初始化仪表盘页面
 */
function initDashboard() {
    // 初始化图表
    initHealthTrendChart();
    initDeviceStatusChart();
    
    // 刷新仪表盘数据
    refreshDashboardData();
}

/**
 * 刷新仪表盘数据
 */
function refreshDashboardData() {
    // 获取状态卡片数据
    fetch('/api/admin_console/dashboard/stats')
        .then(response => response.json())
        .then(data => {
            updateDashboardStats(data);
        })
        .catch(handleApiError);
    
    // 获取最近事件
    fetch('/api/admin_console/dashboard/recent_events')
        .then(response => response.json())
        .then(data => {
            updateRecentEvents(data);
        })
        .catch(handleApiError);
    
    // 获取活跃告警
    fetch('/api/admin_console/dashboard/active_alerts')
        .then(response => response.json())
        .then(data => {
            updateActiveAlerts(data);
        })
        .catch(handleApiError);
        
    // 获取图表数据并更新
    fetch('/api/admin_console/dashboard/chart_data')
        .then(response => response.json())
        .then(data => {
            updateDashboardCharts(data);
        })
        .catch(handleApiError);
}

/**
 * 更新仪表盘统计数据
 * @param {Object} data - 统计数据
 */
function updateDashboardStats(data) {
    // 更新从服务器数量
    document.getElementById('slaveCount').textContent = formatNumber(data.slave_count || 0);
    document.getElementById('slaveOnline').textContent = formatNumber(data.slave_online || 0);
    
    // 更新设备数量
    document.getElementById('deviceCount').textContent = formatNumber(data.device_count || 0);
    document.getElementById('deviceActive').textContent = formatNumber(data.device_active || 0);
    
    // 更新用户数量
    document.getElementById('userCount').textContent = formatNumber(data.user_count || 0);
    document.getElementById('userActive').textContent = formatNumber(data.user_active || 0);
    
    // 更新告警数量
    document.getElementById('alertCount').textContent = formatNumber(data.alert_count || 0);
    document.getElementById('criticalAlerts').textContent = formatNumber(data.critical_alerts || 0);
}

/**
 * 更新最近事件列表
 * @param {Array} events - 事件数据
 */
function updateRecentEvents(events) {
    const container = document.getElementById('recentEvents');
    if (!container) return;
    
    if (!events || events.length === 0) {
        container.innerHTML = '<tr><td colspan="4" class="text-center">暂无事件</td></tr>';
        return;
    }
    
    let html = '';
    events.forEach(event => {
        const statusClass = {
            'success': 'success',
            'warning': 'warning',
            'error': 'danger',
            'info': 'info'
        }[event.status] || 'secondary';
        
        html += `
        <tr>
            <td>${formatDateTime(event.timestamp)}</td>
            <td>${event.type}</td>
            <td>${event.message}</td>
            <td><span class="badge bg-${statusClass}">${event.status}</span></td>
        </tr>`;
    });
    
    container.innerHTML = html;
}

/**
 * 更新活跃告警列表
 * @param {Array} alerts - 告警数据
 */
function updateActiveAlerts(alerts) {
    const container = document.getElementById('activeAlerts');
    if (!container) return;
    
    if (!alerts || alerts.length === 0) {
        container.innerHTML = '<div class="text-center p-3">当前无活跃告警</div>';
        return;
    }
    
    let html = '';
    alerts.forEach(alert => {
        const severityClass = {
            'critical': 'danger',
            'warning': 'warning',
            'info': 'info'
        }[alert.severity] || 'secondary';
        
        html += `
        <div class="alert alert-${severityClass} d-flex align-items-center" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <div>
                <div class="fw-bold">${alert.title}</div>
                <div>${alert.message}</div>
                <small class="text-muted">${formatDateTime(alert.timestamp)} · ${alert.source}</small>
            </div>
        </div>`;
    });
    
    container.innerHTML = html;
}

/**
 * 初始化健康趋势图表
 */
function initHealthTrendChart() {
    const ctx = document.getElementById('healthTrendChart');
    if (!ctx) return;
    
    // 创建空的图表实例，数据将在刷新时更新
    window.healthTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: '健康',
                    data: [],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: '警告',
                    data: [],
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: '故障',
                    data: [],
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    }
                }
            }
        }
    });
}

/**
 * 初始化设备状态图表
 */
function initDeviceStatusChart() {
    const ctx = document.getElementById('deviceStatusChart');
    if (!ctx) return;
    
    // 创建空的图表实例，数据将在刷新时更新
    window.deviceStatusChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['已连接', '已断开', '离线'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#6c757d'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '70%',
            plugins: {
                legend: {
                    position: 'right',
                }
            }
        }
    });
}

/**
 * 更新仪表盘图表
 * @param {Object} data - 图表数据
 */
function updateDashboardCharts(data) {
    // 更新健康趋势图表
    if (window.healthTrendChart && data.health_trend) {
        window.healthTrendChart.data.labels = data.health_trend.labels;
        window.healthTrendChart.data.datasets[0].data = data.health_trend.healthy;
        window.healthTrendChart.data.datasets[1].data = data.health_trend.warning;
        window.healthTrendChart.data.datasets[2].data = data.health_trend.critical;
        window.healthTrendChart.update();
    }
    
    // 更新设备状态图表
    if (window.deviceStatusChart && data.device_status) {
        window.deviceStatusChart.data.datasets[0].data = [
            data.device_status.connected || 0,
            data.device_status.disconnected || 0,
            data.device_status.offline || 0
        ];
        window.deviceStatusChart.update();
    }
}

// 其他页面的初始化和数据刷新函数可按照类似结构实现
// 如果需要特定页面的功能，可以进一步扩展这个文件

/**
 * 初始化分组管理页面
 */
function initGroupManagement() {
    // 初始化jsTree
    initGroupTree();
    
    // 注册事件处理
    initGroupEventHandlers();
    
    // 刷新分组数据
    refreshGroupData();
}

/**
 * 初始化分组树形结构
 */
function initGroupTree() {
    $('#groupTree').jstree({
        'core': {
            'themes': {
                'name': 'proton',
                'responsive': true
            },
            'data': [],
            'check_callback': true
        },
        'plugins': ['search', 'wholerow'],
        'search': {
            'case_insensitive': true,
            'show_only_matches': true
        }
    });
    
    // 处理点击节点事件
    $('#groupTree').on('select_node.jstree', function(e, data) {
        fetchGroupDetails(data.node.id);
    });
    
    // 绑定搜索输入
    let searchTimeout = false;
    $('#groupSearch').keyup(function() {
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }
        searchTimeout = setTimeout(function() {
            const v = $('#groupSearch').val();
            $('#groupTree').jstree(true).search(v);
        }, 250);
    });
    
    // 绑定展开全部和折叠全部按钮
    $('#expandAllBtn').click(function() {
        $('#groupTree').jstree('open_all');
    });
    $('#collapseAllBtn').click(function() {
        $('#groupTree').jstree('close_all');
    });
}

// 接下来可以根据需要添加其他页面的初始化和数据刷新函数 