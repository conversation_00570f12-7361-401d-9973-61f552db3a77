<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主服务器仪表盘 - Web管理服务</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .dashboard-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .dashboard-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1.5rem;
        }
        
        .dashboard-card h3 {
            margin-bottom: 1rem;
            color: var(--primary-color);
            border-bottom: 1px solid var(--gray-color);
            padding-bottom: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        
        .stat-box {
            text-align: center;
            padding: 1rem;
            background-color: var(--light-color);
            border-radius: var(--border-radius);
        }
        
        .stat-box .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-box .stat-label {
            font-size: 0.9rem;
            color: var(--gray-color);
        }
        
        .slave-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .slave-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem;
            border-bottom: 1px solid var(--light-color);
        }
        
        .slave-item:last-child {
            border-bottom: none;
        }
        
        .slave-name {
            font-weight: bold;
        }
        
        .slave-status {
            padding: 0.3rem 0.6rem;
            border-radius: 1rem;
            font-size: 0.8rem;
        }
        
        .status-online {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .status-offline {
            background-color: var(--danger-color);
            color: white;
        }
        
        .status-warning {
            background-color: var(--tertiary-color);
            color: var(--dark-color);
        }
        
        .chart-container {
            height: 250px;
            position: relative;
        }
    </style>
</head>
<body>
    <header>
        <h1>主服务器仪表盘</h1>
        <nav>
            <ul>
                <li><a href="{{ url_for('websevs.ind.index') }}">首页</a></li>
                <li><a href="{{ url_for('websevs.ind.dashboard') }}">仪表盘</a></li>
                <li><a href="{{ url_for('websevs.ind.devices') }}">设备管理</a></li>
                <li><a href="{{ url_for('websevs.ind.slaves') }}">从服务器</a></li>
                <li><a href="{{ url_for('websevs.ind.settings') }}">系统设置</a></li>
                <li><a href="{{ url_for('websevs.ind.monitoring') }}">系统监控</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section>
            <h2>系统概览</h2>
            <div class="dashboard-container">
                <div class="dashboard-card">
                    <h3>系统状态</h3>
                    <div class="stats-grid">
                        <div class="stat-box">
                            <div class="stat-value">5.2 天</div>
                            <div class="stat-label">运行时间</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">32%</div>
                            <div class="stat-label">CPU 使用率</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">2.1 GB</div>
                            <div class="stat-label">内存使用</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">45.6 GB</div>
                            <div class="stat-label">磁盘空间</div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <h3>设备统计</h3>
                    <div class="stats-grid">
                        <div class="stat-box">
                            <div class="stat-value">42</div>
                            <div class="stat-label">总设备数</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">28</div>
                            <div class="stat-label">在线设备</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">15</div>
                            <div class="stat-label">已共享</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">7</div>
                            <div class="stat-label">故障设备</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section>
            <h2>从服务器状态</h2>
            <div class="dashboard-container">
                <div class="dashboard-card">
                    <h3>从服务器列表</h3>
                    <ul class="slave-list">
                        <li class="slave-item">
                            <span class="slave-name">工作站A-1</span>
                            <span class="slave-status status-online">在线</span>
                        </li>
                        <li class="slave-item">
                            <span class="slave-name">工作站B-2</span>
                            <span class="slave-status status-online">在线</span>
                        </li>
                        <li class="slave-item">
                            <span class="slave-name">工作站C-3</span>
                            <span class="slave-status status-warning">延迟高</span>
                        </li>
                        <li class="slave-item">
                            <span class="slave-name">工作站D-4</span>
                            <span class="slave-status status-offline">离线</span>
                        </li>
                        <li class="slave-item">
                            <span class="slave-name">工作站E-5</span>
                            <span class="slave-status status-online">在线</span>
                        </li>
                    </ul>
                </div>

                <div class="dashboard-card">
                    <h3>设备分布</h3>
                    <div class="chart-container">
                        <canvas id="deviceDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <section>
            <h2>系统活动</h2>
            <div class="dashboard-card">
                <h3>最近活动</h3>
                <table>
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>用户</th>
                            <th>操作</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2023-07-15 14:23</td>
                            <td>admin</td>
                            <td>添加新设备 "打印机-HP4580"</td>
                            <td>成功</td>
                        </tr>
                        <tr>
                            <td>2023-07-15 13:45</td>
                            <td>user1</td>
                            <td>连接设备 "扫描仪-A125"</td>
                            <td>成功</td>
                        </tr>
                        <tr>
                            <td>2023-07-15 12:32</td>
                            <td>system</td>
                            <td>从服务器 "工作站D-4" 离线</td>
                            <td>警告</td>
                        </tr>
                        <tr>
                            <td>2023-07-15 10:05</td>
                            <td>admin</td>
                            <td>系统配置更新</td>
                            <td>成功</td>
                        </tr>
                        <tr>
                            <td>2023-07-15 09:17</td>
                            <td>user2</td>
                            <td>断开设备 "摄像头-C920"</td>
                            <td>成功</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
    </main>

    <footer>
        <p>&copy; 2023 Web管理服务系统 - 版本 1.0.0</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 设备分布图表
            const ctx = document.getElementById('deviceDistributionChart').getContext('2d');
            const deviceDistributionChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['工作站A-1', '工作站B-2', '工作站C-3', '工作站D-4', '工作站E-5'],
                    datasets: [{
                        label: '设备数量',
                        data: [12, 8, 5, 10, 7],
                        backgroundColor: [
                            '#4285f4',
                            '#34a853',
                            '#fbbc05',
                            '#ea4335',
                            '#9e9e9e'
                        ],
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>