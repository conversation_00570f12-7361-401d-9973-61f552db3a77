{% extends "admin_console/base.html" %}

{% block title %}设备详情 - {{ device.name or '未知设备' }}{% endblock %}

{% block content %}
<h1 class="mdc-typography--headline4">设备详情: <span id="deviceDetailName">{{ device.name or '未知设备' }}</span></h1>

<div class="mdc-card device-detail-card">
    <div class="mdc-card__primary-action" tabindex="0">
        <div class="mdc-card__media mdc-card__media--16-9 device-icon-container">
            <span id="deviceTypeIcon" class="material-symbols-outlined device-icon-large"></span>
        </div>
        <div class="device-detail-content">
            <h2 class="mdc-typography--headline6" id="deviceDetailNameHeader">{{ device.name or '未知设备' }} (ID: {{ device.device_id }})</h2>
            <p class="mdc-typography--subtitle2">类型: <span id="deviceDetailType">{{ device.device_type or '未知' }}</span></p>
            <p class="mdc-typography--body2">状态: <span id="deviceDetailStatus">{{ device.status }}</span></p>
            <p class="mdc-typography--body2">描述: <span id="deviceDetailDescription">{{ device.description or '无' }}</span></p>
            <p class="mdc-typography--body2">所属分组: <span id="deviceDetailGroup">{{ device.group.name if device.group else '未分组' }}</span></p>
            <p class="mdc-typography--body2">标签: <span id="deviceDetailTags">{{ device.tags | join(', ') if device.tags else '无' }}</span></p>
            <p class="mdc-typography--body2">所属从服务器: {{ device.slave_server_hostname or '未分配' }}</p>
            <p class="mdc-typography--body2">当前用户: {{ device.current_user_username or '无' }}</p>
            <p class="mdc-typography--body2">上次使用时间: {{ device.last_used | format_datetime if device.last_used else '从未' }}</p>
        </div>
    </div>
    <div class="mdc-card__actions">
        <button class="mdc-button mdc-card__action mdc-card__action--button" id="edit-device-button">
            <span class="mdc-button__ripple"></span>
            <span class="mdc-button__label">编辑</span>
        </button>
        <button class="mdc-button mdc-card__action mdc-card__action--button" id="view-logs-button">
            <span class="mdc-button__ripple"></span>
            <span class="mdc-button__label">查看日志</span>
        </button>
        <!-- Add more actions like enable/disable, force release etc. -->
    </div>
</div>

<!-- 编辑设备对话框 (Modal) -->
<div class="mdc-dialog" id="edit-device-dialog">
    <div class="mdc-dialog__container">
        <div class="mdc-dialog__surface"
            role="alertdialog"
            aria-modal="true"
            aria-labelledby="edit-dialog-title"
            aria-describedby="edit-dialog-content">
            
            <h2 class="mdc-dialog__title" id="edit-dialog-title">编辑设备信息</h2>
            
            <div class="mdc-dialog__content" id="edit-dialog-content">
                <form id="edit-device-form">
                    <input type="hidden" id="editDeviceId" name="device_id" value="{{ device.id }}">
                    {% if csrf_token %}
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    {% endif %}
                    
                    <div class="mdc-text-field mdc-text-field--outlined form-field" data-mdc-auto-init="MDCTextField">
                        <input class="mdc-text-field__input" id="editDeviceName" name="name" type="text" value="{{ device.name or '' }}">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="editDeviceName" class="mdc-floating-label">设备名称</label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>

                    <div class="mdc-text-field mdc-text-field--outlined form-field" data-mdc-auto-init="MDCTextField">
                        <input class="mdc-text-field__input" id="editDeviceDescription" name="description" type="text" value="{{ device.description or '' }}">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="editDeviceDescription" class="mdc-floating-label">设备描述</label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                    
                    <!-- Group Assignment -->
                    <div class="mdc-select mdc-select--outlined form-field" id="editDeviceGroupSelect" data-mdc-auto-init="MDCSelect">
                        <input type="hidden" name="group_id" value="{{ device.group_id or '' }}">
                        <div class="mdc-select__anchor"
                             role="button"
                             aria-haspopup="listbox"
                             aria-expanded="false"
                             aria-labelledby="group-label-edit">
                            <span class="mdc-select__ripple"></span>
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__notch">
                                    <span id="group-label-edit" class="mdc-floating-label">所属分组</span>
                                </span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                            <span class="mdc-select__selected-text-container">
                                <span id="selected-group-text-edit" class="mdc-select__selected-text"></span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg
                                    class="mdc-select__dropdown-icon-graphic"
                                    viewBox="7 10 10 5" focusable="false">
                                <polygon
                                    class="mdc-select__dropdown-icon-inactive"
                                    stroke="none"
                                    fill-rule="evenodd"
                                    points="7 10 12 15 17 10">
                                </polygon>
                                <polygon
                                    class="mdc-select__dropdown-icon-active"
                                    stroke="none"
                                    fill-rule="evenodd"
                                    points="7 15 12 10 17 15">
                                </polygon>
                                </svg>
                            </span>
                        </div>

                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-list" role="listbox" aria-label="Device group selection edit">
                                <li class="mdc-list-item" data-value="" aria-selected="{% if not device.group_id %}true{% else %}false{% endif %}" role="option">
                                    <span class="mdc-list-item__ripple"></span>
                                    <span class="mdc-list-item__text">-- 未分组 --</span>
                                </li>
                                {% for group in available_groups %}
                                <li class="mdc-list-item {% if device.group_id == group.id %}mdc-list-item--selected{% endif %}" data-value="{{ group.id }}" aria-selected="{% if device.group_id == group.id %}true{% else %}false{% endif %}" role="option">
                                    <span class="mdc-list-item__ripple"></span>
                                    <span class="mdc-list-item__text">{{ group.name }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>

                    <!-- Tags Input -->
                     <div class="mdc-text-field mdc-text-field--outlined form-field" data-mdc-auto-init="MDCTextField">
                        <input class="mdc-text-field__input" id="editDeviceTags" name="tags" type="text" value="{{ device.tags | join(', ') if device.tags else '' }}">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="editDeviceTags" class="mdc-floating-label">标签 (逗号分隔)</label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>

                    <!-- Is Enabled Switch -->
                    <div class="mdc-form-field">
                        <div class="mdc-switch mdc-switch--unselected" id="editDeviceEnabledSwitch" data-mdc-auto-init="MDCSwitch">
                            <div class="mdc-switch__track"></div>
                            <div class="mdc-switch__handle-track">
                                <div class="mdc-switch__handle">
                                    <div class="mdc-switch__shadow">
                                        <div class="mdc-elevation-overlay"></div>
                                    </div>
                                    <div class="mdc-switch__ripple"></div>
                                </div>
                            </div>
                        </div>
                        <input type="checkbox" id="editDeviceEnabled" name="is_enabled" class="mdc-switch__native-control" role="switch" {% if device.is_enabled %}checked{% endif %}>
                        <label for="editDeviceEnabled">启用设备</label>
                    </div>

                </form>
            </div>
            
            <div class="mdc-dialog__actions">
                <button type="button" class="mdc-button mdc-dialog__button" data-mdc-dialog-action="cancel">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">取消</span>
                </button>
                <button type="button" class="mdc-button mdc-dialog__button" id="save-device-changes-button" data-mdc-dialog-action="accept_initial_focus">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">保存更改</span>
                </button>
            </div>
        </div>
    </div>
    <div class="mdc-dialog__scrim"></div>
</div>

<!-- Snackbar for notifications -->
<aside class="mdc-snackbar" id="main-snackbar">
  <div class="mdc-snackbar__surface" role="status" aria-relevant="additions text">
    <div class="mdc-snackbar__label" aria-atomic="false"></div>
    <div class="mdc-snackbar__actions" aria-atomic="true">
      <button type="button" class="mdc-icon-button mdc-snackbar__dismiss mdc-icon-button--on-primary" title="关闭">
        <i class="material-icons mdc-icon-button__icon">close</i>
      </button>
    </div>
  </div>
</aside>

{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize MDC Components
        window.mdc.autoInit();

        const deviceId = "{{ device.id }}"; // Used for WebSocket and potentially other places
        const currentDeviceIdFromTemplate = "{{ device.id }}"; // Used specifically for edit form context

        // --- Plan 1: Device Icon ---
        const deviceType = "{{ device.device_type | default('generic_device') | escapejs }}";
        const iconElement = document.getElementById('deviceTypeIcon');
        if (iconElement) {
            const iconMap = {
                'printer': 'print',
                'scanner': 'scanner',
                'storage': 'inventory_2',
                'webcam': 'photo_camera',
                'network_device': 'router',
                'usb_hub': 'usb',
                'keyboard': 'keyboard',
                'mouse': 'mouse_outline',
                'monitor': 'desktop_windows',
                'audio_device': 'speaker',
                'generic_device': 'devices_other',
                'unknown': 'help_outline' // For genuinely unknown types
            };
            let iconName = iconMap[deviceType.toLowerCase()] || iconMap['unknown'];
            iconElement.textContent = iconName;
        }

        // --- Snackbar Initialization ---
        const snackbarElement = document.getElementById('main-snackbar');
        const snackbar = snackbarElement ? new mdc.snackbar.MDCSnackbar(snackbarElement) : null;

        function showSnackbar(message) {
            if (snackbar) {
                snackbar.labelText = message;
                snackbar.open();
            } else {
                console.warn('Snackbar component not found. Message:', message);
                alert(message); // Fallback
            }
        }
        
        // --- Edit Device Dialog Logic ---
        const editDeviceDialogElement = document.getElementById('edit-device-dialog');
        let editDeviceDialog;
        if (editDeviceDialogElement) {
            editDeviceDialog = new mdc.dialog.MDCDialog(editDeviceDialogElement);

            const editButton = document.getElementById('edit-device-button');
            if (editButton) {
                editButton.addEventListener('click', () => {
                    // Populate form with current device data (or ensure it's up-to-date if page isn't reloaded)
                    document.getElementById('editDeviceId').value = currentDeviceIdFromTemplate; // Use the one from template for this specific dialog instance
                    document.getElementById('editDeviceName').value = "{{ device.name or '' | escapejs }}";
                    document.getElementById('editDeviceDescription').value = "{{ device.description or '' | escapejs }}";
                    document.getElementById('editDeviceTags').value = "{{ device.tags | join(', ') if device.tags else '' | escapejs }}";
                    
                    const groupSelectElement = document.getElementById('editDeviceGroupSelect');
                    if (groupSelectElement && groupSelectElement.MDCSelect) {
                         groupSelectElement.MDCSelect.value = "{{ device.group_id or '' }}";
                    }

                    const enabledSwitchElement = document.getElementById('editDeviceEnabledSwitch');
                    if (enabledSwitchElement && enabledSwitchElement.MDCSwitch) {
                         enabledSwitchElement.MDCSwitch.checked = {{ 'true' if device.is_enabled else 'false' }};
                    }
                    editDeviceDialog.open();
                });
            }

            const saveDeviceChangesButton = document.getElementById('save-device-changes-button');
            if (saveDeviceChangesButton) {
                 saveDeviceChangesButton.addEventListener('click', async () => {
                    const form = document.getElementById('edit-device-form');
                    const formData = new FormData(form);
                    const currentEditDeviceId = formData.get('device_id'); // This should be the one from the hidden input
                    
                    const data = {};
                    formData.forEach((value, key) => {
                        if (key === 'is_enabled') {
                            data[key] = document.getElementById('editDeviceEnabled').checked;
                        } else if (key === 'group_id') {
                            data[key] = value ? parseInt(value, 10) : null;
                        } else if (key === 'tags') {
                            data[key] = value ? value.split(',').map(tag => tag.trim()).filter(tag => tag !== '') : [];
                        } else if (key !== 'csrf_token') { // Exclude csrf_token from JSON if it was part of FormData for other reasons
                            data[key] = value;
                        }
                    });

                    if (!currentEditDeviceId) {
                        console.error('Device ID is missing in form. Cannot save changes.');
                        showSnackbar('设备ID丢失，无法保存更改。');
                        return;
                    }
                    
                    const headers = {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    };
                    const csrfTokenInput = form.querySelector('input[name="csrf_token"]');
                    if (csrfTokenInput && csrfTokenInput.value) {
                        headers['X-CSRFToken'] = csrfTokenInput.value;
                    }

                    try {
                        const response = await fetch(`/api/admin/devices/${currentEditDeviceId}`, {
                            method: 'PUT',
                            headers: headers,
                            body: JSON.stringify(data)
                        });

                        const result = await response.json();

                        if (response.ok && result.success) {
                            showSnackbar(result.message || '设备信息已成功更新！');
                            if(editDeviceDialog) editDeviceDialog.close();
                            updateDeviceDisplay(result.device || data, currentEditDeviceId); // Pass ID for title updates
                        } else {
                            showSnackbar(result.message || '更新设备信息失败: ' + (result.errors ? JSON.stringify(result.errors) : ''));
                            console.error('Failed to update device:', result);
                        }
                    } catch (error) {
                        console.error('Error updating device:', error);
                        showSnackbar('更新设备信息时发生网络错误。');
                    }
                 });
            }
        } else {
            console.error("Edit device dialog element not found.");
        }

        function updateDeviceDisplay(updatedDeviceData, updatedDeviceId) {
            if (!updatedDeviceData) return;
            
            const nameForTitle = updatedDeviceData.name || '未知设备';

            // Update main title
            const mainTitleSpan = document.getElementById('deviceDetailName');
            if (mainTitleSpan) mainTitleSpan.textContent = nameForTitle;
            
            // Update header with name and ID
            const deviceDetailNameHeader = document.getElementById('deviceDetailNameHeader');
            if (deviceDetailNameHeader) deviceDetailNameHeader.textContent = `${nameForTitle} (ID: ${updatedDeviceId})`;

            const deviceTypeDisplay = document.getElementById('deviceDetailType');
            if (deviceTypeDisplay && typeof updatedDeviceData.device_type !== 'undefined') { // Check for undefined explicitly
                deviceTypeDisplay.textContent = updatedDeviceData.device_type || '未知';
            }
            
            const descriptionDisplay = document.getElementById('deviceDetailDescription');
            if (descriptionDisplay && typeof updatedDeviceData.description !== 'undefined') {
                descriptionDisplay.textContent = updatedDeviceData.description || '无';
            }

            const groupDisplay = document.getElementById('deviceDetailGroup');
            if (groupDisplay) {
                if (updatedDeviceData.group && updatedDeviceData.group.name) {
                     groupDisplay.textContent = updatedDeviceData.group.name;
                } else if (typeof updatedDeviceData.group_name !== 'undefined') { // Check for undefined
                     groupDisplay.textContent = updatedDeviceData.group_name || '未分组';
                } else if (typeof updatedDeviceData.group_id !== 'undefined' && updatedDeviceData.group_id !== null) {
                    const groupOption = document.querySelector(`#editDeviceGroupSelect .mdc-list-item[data-value="${updatedDeviceData.group_id}"] .mdc-list-item__text`);
                    groupDisplay.textContent = groupOption ? groupOption.textContent : '未分组';
                } else {
                     groupDisplay.textContent = '未分组';
                }
            }

            const tagsDisplay = document.getElementById('deviceDetailTags');
            if (tagsDisplay && typeof updatedDeviceData.tags !== 'undefined') {
                tagsDisplay.textContent = Array.isArray(updatedDeviceData.tags) ? updatedDeviceData.tags.join(', ') : (updatedDeviceData.tags || '无');
            }

            // Update icon if type changed
            const typeIcon = document.getElementById('deviceTypeIcon');
            if (typeIcon && typeof updatedDeviceData.device_type !== 'undefined') {
                const iconMap = {
                    'printer': 'print', 
                    'scanner': 'scanner', 
                    'storage': 'inventory_2', 
                    'webcam': 'photo_camera', 
                    'network_device': 'router', 
                    'usb_hub': 'usb', 
                    'keyboard': 'keyboard', 
                    'mouse': 'mouse_outline',
                    'monitor': 'desktop_windows', 
                    'audio_device': 'speaker', 
                    'generic_device': 'devices_other', 
                    'unknown': 'help_outline'
                };
                typeIcon.textContent = iconMap[(updatedDeviceData.device_type || 'unknown').toLowerCase()] || iconMap['unknown'];
            }
        }

        // --- View Logs Button ---
        const viewLogsButton = document.getElementById('view-logs-button');
        if (viewLogsButton) {
            viewLogsButton.addEventListener('click', () => {
                 // const deviceId is already defined from template at the top of the script block
                 window.location.href = `/admin/logs?filter=device_id:${deviceId}`;
            });
        }

        // --- WebSocket Connection for Live Status (existing code, adapted for current context) ---
        function setupWebSocketConnection() {
            // const deviceId is already defined
            let reconnectAttempts = 0;
            let socket = null;
            
            async function getToken() {
                try {
                    const response = await fetch('/api/auth/token', { /* ... as before ... */ method: 'GET', headers: {'Content-Type': 'application/json'}, credentials: 'include'});
                    if (!response.ok) throw new Error('获取认证令牌失败 (' + response.status + ')');
                    const data = await response.json();
                    return data.access_token;
                } catch (error) {
                    console.error('获取令牌出错:', error);
                    showSnackbar('无法获取认证信息，设备状态将不会自动更新');
                    return null;
                }
            }
            
            async function connectWebSocket() {
                try {
                    const token = await getToken();
                    if (!token) return;
                    if (!('WebSocket' in window)) {
                        console.error('浏览器不支持WebSocket');
                        showSnackbar('您的浏览器不支持WebSocket，设备状态将不会自动更新');
                        return;
                    }
                    
                    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    const wsUrl = `${wsProtocol}//${window.location.host}/ws/client`;
                    socket = new WebSocket(wsUrl);
                    
                    socket.onopen = function() {
                        console.log('WebSocket连接已建立 (详情页)');
                        reconnectAttempts = 0;
                        socket.send(JSON.stringify({ type: 'authenticate', data: { token: token } }));
                        setTimeout(() => { // Give time for auth
                            socket.send(JSON.stringify({ type: 'subscribe', subscription: { device_id: deviceId } }));
                            console.log(`已订阅设备 ${deviceId} 的状态更新 (详情页)`);
                        }, 500);
                    };
                    
                    socket.onmessage = function(event) {
                        try {
                            const message = JSON.parse(event.data);
                            if (message.type === 'device_status_update' && message.data.device_id === deviceId) {
                                updateDeviceStatusDisplay(message.data); // Note: different function from save update
                            } else if (message.type === 'auth_result') { /* ... */ }
                            else if (message.type === 'subscription_success') { /* ... */ }
                        } catch (error) { console.error('处理WebSocket消息时出错 (详情页):', error); }
                    };
                    
                    socket.onclose = function(event) {
                        console.log('WebSocket连接已关闭 (详情页):', event.code, event.reason);
                        if (reconnectAttempts < 5) {
                            reconnectAttempts++;
                            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
                            setTimeout(connectWebSocket, delay);
                        } else { showSnackbar('无法连接到服务器进行实时更新。'); }
                    };
                    socket.onerror = function(error) { console.error('WebSocket错误 (详情页):', error); };
                } catch (error) { console.error('建立WebSocket连接时出错 (详情页):', error); }
            }
            
            // This function updates status based on WebSocket messages
            function updateDeviceStatusDisplay(update) {
                const statusElement = document.getElementById('deviceDetailStatus'); // Main status display
                // const statusElement = document.getElementById('device-status-' + update.device_id); // If using dynamic ID
                if (statusElement) {
                    statusElement.textContent = update.status_text || update.status || '未知'; // Prefer status_text if available
                    // Add/remove classes for styling if needed
                    // statusElement.className = 'mdc-typography--body2 status-' + (update.status || 'unknown').toLowerCase();
                }
                // Potentially update other live fields like current_user_username, last_used etc.
                const currentUserElement = document.querySelector('.device-detail-content p:nth-child(5)'); // Fragile selector
                if (currentUserElement && typeof update.current_user_username !== 'undefined') {
                    currentUserElement.innerHTML = `当前用户: ${update.current_user_username || '无'}`;
                }

                showSnackbar(`设备 ${update.device_name || deviceId} 状态已更新为: ${update.status_text || update.status}`);
            }
            
            connectWebSocket();
            window.addEventListener('beforeunload', () => {
                if (socket && socket.readyState === WebSocket.OPEN) socket.close();
            });
        }
        
        setupWebSocketConnection();
    });
</script>
{% endblock %} 