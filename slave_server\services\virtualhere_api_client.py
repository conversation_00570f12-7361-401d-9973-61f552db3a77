import asyncio
import logging

logger = logging.getLogger(__name__)

class VirtualHereApiClient:
    """
    A simple async client to interact with the VirtualHere server's TCP API.
    This allows querying the state of shared devices and listening for events.
    """
    def __init__(self, host: str, port: int):
        self.host = host
        self.port = port
        self.reader: asyncio.StreamReader | None = None
        self.writer: asyncio.StreamWriter | None = None

    async def connect(self):
        """Establishes a connection to the VirtualHere server."""
        try:
            self.reader, self.writer = await asyncio.open_connection(self.host, self.port)
            logger.info(f"Successfully connected to VirtualHere instance at {self.host}:{self.port}")
            return True
        except ConnectionRefusedError:
            logger.error(f"Connection refused to VirtualHere instance at {self.host}:{self.port}. Is it running?")
            return False
        except Exception as e:
            logger.error(f"Error connecting to VirtualHere at {self.host}:{self.port}: {e}", exc_info=True)
            return False

    async def disconnect(self):
        """Closes the connection."""
        if self.writer:
            self.writer.close()
            await self.writer.wait_closed()
            logger.info(f"Disconnected from VirtualHere instance at {self.host}:{self.port}")
        self.reader = None
        self.writer = None

    async def send_command(self, command: str) -> str | None:
        """
        Sends a command to the VirtualHere server and returns the response.
        Note: VirtualHere's scripting API is often line-based.
        """
        if not self.writer or not self.reader:
            logger.error("Cannot send command, not connected.")
            return None
        
        try:
            self.writer.write(f"{command}\n".encode('utf-8'))
            await self.writer.drain()
            
            response = await self.reader.readline()
            return response.decode('utf-8').strip()
        except Exception as e:
            logger.error(f"Error sending command '{command}' or reading response: {e}", exc_info=True)
            await self.disconnect() # Disconnect on error to be safe
            return None

    async def list_devices_raw(self) -> str | None:
        """
        Sends the 'LIST' command to get a raw string of devices from VirtualHere.
        This is a primary command for device discovery.
        """
        if not self.writer or not self.reader:
            logger.error("Cannot list devices, not connected.")
            return None
        
        try:
            self.writer.write("LIST\n".encode('utf-8'))
            await self.writer.drain()
            
            # The response to LIST can be multi-line, ending with an "OK" or "OK."
            response_lines = []
            while True:
                line_bytes = await self.reader.readline()
                if not line_bytes:
                    # Connection closed prematurely
                    logger.warning("Connection closed while waiting for LIST response.")
                    return "\\n".join(response_lines)
                
                line = line_bytes.decode('utf-8').strip()
                if line.startswith("OK"):
                    break
                response_lines.append(line)
            
            return "\\n".join(response_lines)
        except Exception as e:
            logger.error(f"Error sending LIST command or reading response: {e}", exc_info=True)
            await self.disconnect()
            return None

    async def get_client_list(self) -> list[str]:
        """
        Retrieves the list of connected clients for a shared device.
        This is a hypothetical command, actual command might differ.
        Example command: 'LIST CLIENTS'
        """
        response = await self.send_command("LIST CLIENTS")
        if response and response.startswith("OK"):
            # Assuming response is like "OK,client1_ip,client2_ip,..."
            parts = response.split(',')
            return parts[1:]
        return []

    async def listen_for_events(self, callback):
        """
        Continuously listens for events from the server.
        """
        if not self.reader:
            logger.error("Cannot listen for events, not connected.")
            return

        while True:
            try:
                event_data = await self.reader.readline()
                if not event_data:
                    logger.warning(f"Connection to VirtualHere at {self.host}:{self.port} lost. Reconnecting...")
                    await self.disconnect()
                    break # Exit loop to allow for reconnection logic
                
                event = event_data.decode('utf-8').strip()
                if event:
                    await callback(event)
            except asyncio.CancelledError:
                logger.info("Event listener cancelled.")
                break
            except Exception as e:
                logger.error(f"Error in event listener: {e}", exc_info=True)
                await self.disconnect()
                break
        
        logger.info("Event listener stopped.")

if __name__ == '__main__':
    # Example Usage (for testing)
    async def handle_event(event_str):
        print(f"Received Event: {event_str}")

    async def main():
        # Replace with the actual port of a running VH instance
        vh_client = VirtualHereApiClient('127.0.0.1', 7576) 
        if await vh_client.connect():
            # Start listening for events in the background
            listener_task = asyncio.create_task(vh_client.listen_for_events(handle_event))
            
            # You can send commands in the foreground
            await asyncio.sleep(2)
            print("Sending a test command...")
            clients = await vh_client.get_client_list()
            print(f"Connected clients: {clients}")
            
            # Keep running
            await asyncio.sleep(30)
            listener_task.cancel()
            await vh_client.disconnect()

    asyncio.run(main()) 