from sqlalchemy import (
    <PERSON><PERSON><PERSON>, Integer, String, TIMESTAMP, <PERSON><PERSON>an, ForeignKey, UniqueConstraint
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func
from typing import Optional, Dict
from enum import Enum, auto
from dataclasses import dataclass, field
from common.database.base_class import Base

class DeviceStatus(Enum):
    """设备状态枚举"""
    AVAILABLE = "available"
    IN_USE = "in_use"
    OFFLINE = "offline"
    ERROR = "error"
    UNKNOWN = "unknown"

class DeviceType(Enum):
    """设备类型枚举"""
    STORAGE = "storage"
    CAMERA = "camera"
    HID = "hid"         # Human Interface Device (Keyboard, Mouse)
    PRINTER = "printer"
    AUDIO = "audio"
    OTHER = "other"

@dataclass
class DeviceInfo:
    """设备信息数据类，用于表示一个已发现的USB设备"""
    id: str  # Unique identifier (e.g., vendor_id:product_id:serial)
    name: str
    hub_path: Optional[str] = None # Path within the USB hub
    vendor_id: Optional[str] = None
    product_id: Optional[str] = None
    serial_number: Optional[str] = None
    manufacturer: Optional[str] = None
    product: Optional[str] = None
    device_type: DeviceType = DeviceType.OTHER
    status: DeviceStatus = DeviceStatus.UNKNOWN
    properties: Dict[str, str] = field(default_factory=dict)

class Device(Base):
    __tablename__ = 'devices'
    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String(255), nullable=False)
    vendor_id = Column(String(10))
    product_id = Column(String(10))
    serial_number = Column(String(100))
    description = Column(String)
    device_type = Column(String(50))
    status = Column(String(20), nullable=False)
    is_shared = Column(Boolean, default=False)
    assigned_port: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, index=True)
    slave_server_id = Column(Integer, ForeignKey('slave_servers.id'))
    current_user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    connected_at = Column(TIMESTAMP, nullable=True)
    created_at = Column(TIMESTAMP, server_default=func.now())
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now())
    
    slave_server = relationship('SlaveServer', back_populates='devices')
    current_user = relationship('User', back_populates='devices')
    
    __table_args__ = (UniqueConstraint('slave_server_id', 'device_id', name='_slave_device_uc'),)
