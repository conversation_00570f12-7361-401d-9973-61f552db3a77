# ky/config/sod_rules.yaml
# 职责分离 (Segregation of Duties - SoD) 规则定义

# 本文件定义了系统中互斥的权限组合，以防止单一用户拥有可能导致潜在风险或滥用的权限。
# RBAC策略服务 (rbac_policy_service.py) 将加载并使用这些规则。
#
# 规则格式说明:
# sod_rules:
#   - name: "规则的唯一人类可读名称"
#     description: "对规则目的的简要描述"
#     conflicting_permissions:
#       - "permission.string.1"  # 权限字符串1, 对应 initial_data.py 中的权限定义
#       - "permission.string.2"  # 权限字符串2
#       # 可以包含两个或多个互斥的权限
#     # 可选:
#     # severity: "high" | "medium" | "low"  # 冲突的严重级别，默认为 high
#     # enabled: true | false                  # 是否启用此规则，默认为 true

sod_rules:
  - name: "用户管理与角色管理分离"
    description: "禁止同一用户同时拥有创建/删除用户和创建/修改角色的权限，以防止单一管理员控制所有账户和权限体系。"
    conflicting_permissions:
      - "admin.user.create"       # 假设权限: 创建用户
      - "admin.user.delete"       # 假设权限: 删除用户
      - "admin.role.create"       # 假设权限: 创建角色
      - "admin.role.edit"         # 假设权限: 修改角色权限
    severity: "high"
    enabled: true

  - name: "从服务器管理与主服务器核心配置分离"
    description: "禁止同一用户同时管理从服务器（添加、删除、配置）和修改主服务器核心系统配置（如数据库、网络、全局安全设置）。"
    conflicting_permissions:
      - "admin.slaveserver.manage" # 假设权限: 管理从服务器 (增删改查、启停)
      - "admin.mainserver.config.core" # 假设权限: 修改主服务器核心配置
    severity: "high"
    enabled: true

  - name: "审计日志管理与敏感数据操作分离"
    description: "禁止同一用户同时拥有管理（特别是删除或修改）审计日志和执行高风险敏感数据操作（如批量删除用户数据）的权限。"
    conflicting_permissions:
      - "admin.auditlog.manage"   # 假设权限: 管理审计日志 (查看、归档、删除)
      - "admin.data.delete.bulk"  # 假设权限: 批量删除用户数据
    severity: "high"
    enabled: true

  - name: "设备共享策略管理与强制断开操作员分离"
    description: "考虑将全局设备共享策略的制定者与日常执行强制断开设备连接的操作员分离，以避免策略制定者滥用强制断开权限。"
    conflicting_permissions:
      - "admin.deviceshare.policy.edit" # 假设权限: 编辑全局设备共享策略
      - "admin.device.force_disconnect.all" # 假设权限: 对所有共享设备执行强制断开
    severity: "medium"
    enabled: true

  - name: "财务凭证创建与财务审批分离 (示例性)"
    description: "示例：如果系统未来涉及财务模块，创建支付凭证的权限不应与审批支付凭证的权限授予同一人。"
    conflicting_permissions:
      - "finance.payment.create"
      - "finance.payment.approve"
    severity: "high"
    enabled: false # 默认禁用，因为当前系统无此模块

# 注意: 上述权限字符串 (如 'admin.user.create') 均为假设，
# 实际应与 ky/src/auth/permissions/initial_data.py 中定义的权限字符串保持一致。
# 在 rbac_policy_service.py 实现时，需要确保能正确解析和匹配这些权限。 