#!/usr/bin/env python3
"""
动态筛选组

提供基于筛选条件的动态设备分组功能，支持自动更新
"""

import os
import json
import logging
import uuid
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set, Callable, Union

from .filter_engine import FilterGroup, DeviceFilterEngine

logger = logging.getLogger(__name__)

class DynamicFilterGroup:
    """动态筛选组类"""
    
    def __init__(self, 
                 group_id: str,
                 name: str,
                 filter_group: FilterGroup,
                 auto_update: bool = True,
                 update_interval: int = 60,  # 更新间隔（秒）
                 created_at: Optional[datetime] = None,
                 updated_at: Optional[datetime] = None,
                 created_by: Optional[str] = None,
                 description: Optional[str] = None,
                 icon: Optional[str] = None,
                 color: Optional[str] = None,
                 metadata: Optional[Dict[str, Any]] = None):
        """
        初始化动态筛选组
        
        参数:
            group_id: 组ID
            name: 组名称
            filter_group: 筛选条件组
            auto_update: 是否自动更新
            update_interval: 更新间隔（秒）
            created_at: 创建时间
            updated_at: 更新时间
            created_by: 创建者
            description: 描述
            icon: 图标
            color: 颜色
            metadata: 元数据
        """
        self.group_id = group_id
        self.name = name
        self.filter_group = filter_group
        self.auto_update = auto_update
        self.update_interval = update_interval
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
        self.created_by = created_by
        self.description = description
        self.icon = icon
        self.color = color
        self.metadata = metadata or {}
        
        # 缓存的设备ID集合
        self.cached_device_ids: Set[str] = set()
        self.last_update: Optional[datetime] = None
        self.update_lock = threading.RLock()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        返回:
            Dict[str, Any]: 组字典
        """
        return {
            "group_id": self.group_id,
            "name": self.name,
            "filter_group": self.filter_group.to_dict(),
            "auto_update": self.auto_update,
            "update_interval": self.update_interval,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "created_by": self.created_by,
            "description": self.description,
            "icon": self.icon,
            "color": self.color,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DynamicFilterGroup':
        """
        从字典创建组
        
        参数:
            data: 组字典
            
        返回:
            DynamicFilterGroup: 组对象
        """
        # 解析时间
        created_at = None
        if data.get("created_at"):
            try:
                created_at = datetime.fromisoformat(data["created_at"])
            except (ValueError, TypeError):
                created_at = datetime.now()
        
        updated_at = None
        if data.get("updated_at"):
            try:
                updated_at = datetime.fromisoformat(data["updated_at"])
            except (ValueError, TypeError):
                updated_at = datetime.now()
        
        # 解析筛选条件组
        filter_group = FilterGroup.from_dict(data["filter_group"])
        
        return cls(
            group_id=data["group_id"],
            name=data["name"],
            filter_group=filter_group,
            auto_update=data.get("auto_update", True),
            update_interval=data.get("update_interval", 60),
            created_at=created_at,
            updated_at=updated_at,
            created_by=data.get("created_by"),
            description=data.get("description"),
            icon=data.get("icon"),
            color=data.get("color"),
            metadata=data.get("metadata", {})
        )
    
    def update_devices(self, filter_engine: DeviceFilterEngine) -> Set[str]:
        """
        更新组内设备
        
        参数:
            filter_engine: 筛选引擎
            
        返回:
            Set[str]: 更新后的设备ID集合
        """
        with self.update_lock:
            # 执行筛选
            matched_devices = filter_engine.filter_devices(self.filter_group)
            
            # 获取设备ID集合
            device_ids = {device.get("id") for device in matched_devices if device.get("id")}
            
            # 更新缓存
            self.cached_device_ids = device_ids
            self.last_update = datetime.now()
            
            return device_ids
    
    def needs_update(self) -> bool:
        """
        检查是否需要更新
        
        返回:
            bool: 是否需要更新
        """
        if not self.auto_update:
            return False
        
        if not self.last_update:
            return True
        
        time_since_update = datetime.now() - self.last_update
        return time_since_update.total_seconds() >= self.update_interval
    
    def get_device_ids(self, filter_engine: DeviceFilterEngine) -> Set[str]:
        """
        获取组内设备ID集合
        
        参数:
            filter_engine: 筛选引擎
            
        返回:
            Set[str]: 设备ID集合
        """
        # 如果需要更新，执行更新
        if self.needs_update():
            return self.update_devices(filter_engine)
        
        # 返回缓存的设备ID集合
        return self.cached_device_ids
    
    def force_update(self, filter_engine: DeviceFilterEngine) -> Set[str]:
        """
        强制更新组内设备
        
        参数:
            filter_engine: 筛选引擎
            
        返回:
            Set[str]: 更新后的设备ID集合
        """
        return self.update_devices(filter_engine)

class DynamicGroupManager:
    """动态筛选组管理器类"""
    
    def __init__(self, 
                 groups_dir: Optional[str] = None, 
                 filter_engine: Optional[DeviceFilterEngine] = None):
        """
        初始化动态筛选组管理器
        
        参数:
            groups_dir: 组目录路径
            filter_engine: 筛选引擎
        """
        self.groups_dir = groups_dir
        self.filter_engine = filter_engine
        self.groups: Dict[str, DynamicFilterGroup] = {}
        self.lock = threading.RLock()
        
        # 更新线程
        self.update_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # 如果目录存在，加载组
        if self.groups_dir and os.path.isdir(self.groups_dir):
            self._load_groups()
        
        # 启动自动更新线程
        self._start_update_thread()
    
    def _load_groups(self) -> None:
        """从目录加载组"""
        try:
            with self.lock:
                self.groups = {}
                
                # 遍历组文件
                for filename in os.listdir(self.groups_dir):
                    if filename.endswith(".json"):
                        filepath = os.path.join(self.groups_dir, filename)
                        try:
                            with open(filepath, 'r', encoding='utf-8') as f:
                                group_data = json.load(f)
                                group = DynamicFilterGroup.from_dict(group_data)
                                self.groups[group.group_id] = group
                        except Exception as e:
                            logger.error(f"加载动态筛选组文件 {filepath} 失败: {e}")
                
                logger.info(f"已从 {self.groups_dir} 加载 {len(self.groups)} 个动态筛选组")
        except Exception as e:
            logger.error(f"加载动态筛选组目录失败: {e}")
    
    def _save_group(self, group: DynamicFilterGroup) -> bool:
        """
        保存组到文件
        
        参数:
            group: 组对象
            
        返回:
            bool: 是否成功保存
        """
        if not self.groups_dir:
            return False
            
        try:
            # 确保目录存在
            os.makedirs(self.groups_dir, exist_ok=True)
            
            # 组文件路径
            filepath = os.path.join(self.groups_dir, f"{group.group_id}.json")
            
            # 先写入临时文件，然后重命名
            temp_filepath = f"{filepath}.tmp"
            with open(temp_filepath, 'w', encoding='utf-8') as f:
                json.dump(group.to_dict(), f, indent=2, ensure_ascii=False)
                
            os.replace(temp_filepath, filepath)
            logger.debug(f"已保存动态筛选组 {group.name} 到 {filepath}")
            return True
        except Exception as e:
            logger.error(f"保存动态筛选组 {group.name} 失败: {e}")
            return False
    
    def _start_update_thread(self) -> None:
        """启动自动更新线程"""
        if self.update_thread and self.update_thread.is_alive():
            return
        
        self.stop_event.clear()
        self.update_thread = threading.Thread(
            target=self._update_loop,
            daemon=True,
            name="DynamicGroupUpdateThread"
        )
        self.update_thread.start()
        logger.debug("已启动动态筛选组自动更新线程")
    
    def _update_loop(self) -> None:
        """自动更新循环"""
        while not self.stop_event.is_set():
            try:
                # 更新需要更新的组
                self.update_all_groups(only_if_needed=True)
                
                # 等待10秒
                self.stop_event.wait(10)
            except Exception as e:
                logger.error(f"动态筛选组自动更新线程异常: {e}")
                # 等待5秒后重试
                self.stop_event.wait(5)
    
    def stop(self) -> None:
        """停止管理器，停止自动更新线程"""
        self.stop_event.set()
        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=2)
        logger.debug("已停止动态筛选组管理器")
    
    def add_group(self, group: DynamicFilterGroup) -> bool:
        """
        添加组
        
        参数:
            group: 组对象
            
        返回:
            bool: 是否成功添加
        """
        with self.lock:
            # 检查是否已存在
            if group.group_id in self.groups:
                logger.warning(f"动态筛选组 {group.group_id} 已存在")
                return False
            
            # 添加组
            self.groups[group.group_id] = group
            
            # 如果有筛选引擎，立即更新设备
            if self.filter_engine:
                group.update_devices(self.filter_engine)
            
            # 保存到文件
            if self.groups_dir:
                self._save_group(group)
            
            logger.info(f"已添加动态筛选组 {group.name} (ID: {group.group_id})")
            return True
    
    def update_group(self, group: DynamicFilterGroup) -> bool:
        """
        更新组
        
        参数:
            group: 组对象
            
        返回:
            bool: 是否成功更新
        """
        with self.lock:
            # 检查是否存在
            if group.group_id not in self.groups:
                logger.warning(f"动态筛选组 {group.group_id} 不存在，无法更新")
                return False
            
            # 设置更新时间
            group.updated_at = datetime.now()
            
            # 更新组
            self.groups[group.group_id] = group
            
            # 如果有筛选引擎，立即更新设备
            if self.filter_engine:
                group.update_devices(self.filter_engine)
            
            # 保存到文件
            if self.groups_dir:
                self._save_group(group)
            
            logger.info(f"已更新动态筛选组 {group.name} (ID: {group.group_id})")
            return True
    
    def delete_group(self, group_id: str) -> bool:
        """
        删除组
        
        参数:
            group_id: 组ID
            
        返回:
            bool: 是否成功删除
        """
        with self.lock:
            # 检查是否存在
            if group_id not in self.groups:
                logger.warning(f"动态筛选组 {group_id} 不存在，无法删除")
                return False
            
            # 获取组名称用于日志
            group_name = self.groups[group_id].name
            
            # 删除组
            del self.groups[group_id]
            
            # 删除文件
            if self.groups_dir:
                filepath = os.path.join(self.groups_dir, f"{group_id}.json")
                if os.path.exists(filepath):
                    try:
                        os.remove(filepath)
                    except Exception as e:
                        logger.error(f"删除动态筛选组文件 {filepath} 失败: {e}")
            
            logger.info(f"已删除动态筛选组 {group_name} (ID: {group_id})")
            return True
    
    def get_group(self, group_id: str) -> Optional[DynamicFilterGroup]:
        """
        获取组
        
        参数:
            group_id: 组ID
            
        返回:
            Optional[DynamicFilterGroup]: 组对象，不存在则返回None
        """
        with self.lock:
            return self.groups.get(group_id)
    
    def get_groups(self) -> List[DynamicFilterGroup]:
        """
        获取所有组
        
        返回:
            List[DynamicFilterGroup]: 组列表
        """
        with self.lock:
            return list(self.groups.values())
    
    def create_group(self,
                     name: str,
                     filter_group: FilterGroup,
                     auto_update: bool = True,
                     update_interval: int = 60,
                     created_by: Optional[str] = None,
                     description: Optional[str] = None,
                     icon: Optional[str] = None,
                     color: Optional[str] = None,
                     metadata: Optional[Dict[str, Any]] = None) -> DynamicFilterGroup:
        """
        创建新组
        
        参数:
            name: 组名称
            filter_group: 筛选条件组
            auto_update: 是否自动更新
            update_interval: 更新间隔（秒）
            created_by: 创建者
            description: 描述
            icon: 图标
            color: 颜色
            metadata: 元数据
            
        返回:
            DynamicFilterGroup: 新创建的组对象
        """
        # 生成组ID
        group_id = str(uuid.uuid4())
        
        # 创建组
        group = DynamicFilterGroup(
            group_id=group_id,
            name=name,
            filter_group=filter_group,
            auto_update=auto_update,
            update_interval=update_interval,
            created_by=created_by,
            description=description,
            icon=icon,
            color=color,
            metadata=metadata
        )
        
        # 添加组
        with self.lock:
            self.add_group(group)
        
        return group
    
    def update_all_groups(self, only_if_needed: bool = False) -> None:
        """
        更新所有组
        
        参数:
            only_if_needed: 是否仅更新需要更新的组
        """
        if not self.filter_engine:
            logger.warning("未设置筛选引擎，无法更新动态筛选组")
            return
        
        with self.lock:
            for group in self.groups.values():
                if not only_if_needed or group.needs_update():
                    try:
                        group.update_devices(self.filter_engine)
                    except Exception as e:
                        logger.error(f"更新动态筛选组 {group.name} 失败: {e}")
    
    def set_filter_engine(self, filter_engine: DeviceFilterEngine) -> None:
        """
        设置筛选引擎
        
        参数:
            filter_engine: 筛选引擎
        """
        with self.lock:
            self.filter_engine = filter_engine
    
    def get_devices_in_group(self, group_id: str) -> Set[str]:
        """
        获取组内设备ID集合
        
        参数:
            group_id: 组ID
            
        返回:
            Set[str]: 设备ID集合，组不存在则返回空集合
        """
        if not self.filter_engine:
            logger.warning("未设置筛选引擎，无法获取动态筛选组设备")
            return set()
        
        with self.lock:
            group = self.get_group(group_id)
            if not group:
                return set()
            
            return group.get_device_ids(self.filter_engine)
    
    def is_device_in_group(self, group_id: str, device_id: str) -> bool:
        """
        检查设备是否在组内
        
        参数:
            group_id: 组ID
            device_id: 设备ID
            
        返回:
            bool: 设备是否在组内
        """
        devices = self.get_devices_in_group(group_id)
        return device_id in devices 