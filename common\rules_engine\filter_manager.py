"""
设备过滤条件管理模块
此模块提供设备过滤条件的保存、加载、更新和删除功能。
"""
import os
import json
import uuid
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Set, Callable

import aiofiles
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from common.models.policy import PolicyRule
from .filter_visualizer import FilterVisualizer
from .models import FilterCondition, FilterGroup, FilterLogic, FilterOperator, FilterConditionSchema, FilterGroupSchema
from .filter_engine import DeviceFilterEngine

import threading
import logging


logger = logging.getLogger(__name__)


class FilterManager:
    """
    过滤管理器类，负责管理和应用过滤规则
    
    该类提供了一个高级接口来管理设备过滤规则，包括过滤规则的存储、创建、更新、删除和应用。
    它封装了底层的过滤引擎，提供更友好的API和额外的功能。
    """

    FILTER_DIR = os.path.join(os.path.dirname(__file__), "saved_filters")
    
    def __init__(self, filter_engine: Optional[DeviceFilterEngine] = None):
        """
        初始化过滤管理器
        
        参数:
            filter_engine: 可选的过滤引擎实例，如果不提供会创建一个新的
        """
        # 确保保存过滤条件的目录存在
        os.makedirs(self.FILTER_DIR, exist_ok=True)
        
        # 过滤引擎实例
        self.filter_engine = filter_engine or DeviceFilterEngine()
        
        # 命名过滤规则存储，键为规则名称，值为过滤条件组
        self._named_filters: Dict[str, FilterGroup] = {}
        
        # 过滤规则的最近使用时间记录
        self._filter_usage: Dict[str, datetime] = {}
        
        # 过滤规则的使用计数
        self._filter_counts: Dict[str, int] = {}
        
        # 线程锁，用于线程安全操作
        self._lock = threading.RLock()
    
    @staticmethod
    def _generate_filter_id() -> str:
        """生成唯一的过滤条件ID"""
        return str(uuid.uuid4())
    
    def _get_filter_path(self, filter_id: str) -> str:
        """获取过滤条件文件路径"""
        return os.path.join(self.FILTER_DIR, f"{filter_id}.json")
    
    async def save_filter(self, 
                          filter_data: Dict[str, Any], 
                          name: str, 
                          description: str = "",
                          user_id: Optional[str] = None,
                          tags: List[str] = None) -> Dict[str, Any]:
        """
        保存过滤条件到文件中
        
        Args:
            filter_data: 过滤条件数据
            name: 过滤条件名称
            description: 过滤条件描述
            user_id: 用户ID
            tags: 过滤条件标签列表
        
        Returns:
            包含过滤条件元数据的字典
        """
        # 生成过滤条件ID
        filter_id = self._generate_filter_id()
        
        # 创建元数据
        now = datetime.now().isoformat()
        metadata = {
            "id": filter_id,
            "name": name,
            "description": description,
            "created_at": now,
            "updated_at": now,
            "user_id": user_id,
            "tags": tags or [],
            "filter_data": filter_data
        }
        
        # 生成过滤条件的可视化摘要
        try:
            visualizer = FilterVisualizer(filter_data)
            metadata["summary"] = visualizer.generate_filter_summary()
        except Exception as e:
            metadata["summary"] = f"无法生成摘要: {str(e)}"
        
        # 保存到文件
        filter_path = self._get_filter_path(filter_id)
        async with aiofiles.open(filter_path, "w", encoding="utf-8") as f:
            await f.write(json.dumps(metadata, ensure_ascii=False, indent=2))
        
        return metadata
    
    async def load_filter(self, filter_id: str) -> Optional[Dict[str, Any]]:
        """
        通过ID加载过滤条件
        
        Args:
            filter_id: 过滤条件ID
        
        Returns:
            过滤条件数据，如果不存在则返回None
        """
        filter_path = self._get_filter_path(filter_id)
        
        if not os.path.exists(filter_path):
            return None
        
        try:
            async with aiofiles.open(filter_path, "r", encoding="utf-8") as f:
                content = await f.read()
                return json.loads(content)
        except Exception as e:
            print(f"加载过滤条件出错: {str(e)}")
            return None
    
    async def update_filter(self, 
                           filter_id: str, 
                           filter_data: Optional[Dict[str, Any]] = None,
                           name: Optional[str] = None, 
                           description: Optional[str] = None,
                           tags: Optional[List[str]] = None) -> Optional[Dict[str, Any]]:
        """
        更新已存在的过滤条件
        
        Args:
            filter_id: 过滤条件ID
            filter_data: 新的过滤条件数据
            name: 新的过滤条件名称
            description: 新的过滤条件描述
            tags: 新的标签列表
        
        Returns:
            更新后的过滤条件数据，如果不存在则返回None
        """
        # 加载现有过滤条件
        existing_filter = await self.load_filter(filter_id)
        if not existing_filter:
            return None
        
        # 更新字段
        if filter_data is not None:
            existing_filter["filter_data"] = filter_data
            try:
                visualizer = FilterVisualizer(filter_data)
                existing_filter["summary"] = visualizer.generate_filter_summary()
            except Exception as e:
                existing_filter["summary"] = f"无法生成摘要: {str(e)}"
        
        if name is not None:
            existing_filter["name"] = name
        
        if description is not None:
            existing_filter["description"] = description
        
        if tags is not None:
            existing_filter["tags"] = tags
        
        # 更新时间戳
        existing_filter["updated_at"] = datetime.now().isoformat()
        
        # 保存回文件
        filter_path = self._get_filter_path(filter_id)
        async with aiofiles.open(filter_path, "w", encoding="utf-8") as f:
            await f.write(json.dumps(existing_filter, ensure_ascii=False, indent=2))
        
        return existing_filter
    
    async def delete_filter(self, filter_id: str) -> bool:
        """
        删除过滤条件
        
        Args:
            filter_id: 过滤条件ID
        
        Returns:
            是否成功删除
        """
        filter_path = self._get_filter_path(filter_id)
        
        if not os.path.exists(filter_path):
            return False
        
        try:
            os.remove(filter_path)
            return True
        except Exception as e:
            print(f"删除过滤条件出错: {str(e)}")
            return False
    
    async def list_filters(self, 
                          user_id: Optional[str] = None, 
                          tags: Optional[List[str]] = None,
                          search_term: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        列出保存的过滤条件
        
        Args:
            user_id: 按用户ID筛选
            tags: 按标签筛选
            search_term: 按名称或描述搜索
        
        Returns:
            符合条件的过滤条件列表
        """
        results = []
        
        # 遍历保存的过滤条件文件
        for filename in os.listdir(self.FILTER_DIR):
            if not filename.endswith(".json"):
                continue
            
            filter_path = os.path.join(self.FILTER_DIR, filename)
            try:
                async with aiofiles.open(filter_path, "r", encoding="utf-8") as f:
                    content = await f.read()
                    filter_data = json.loads(content)
                    
                    # 应用过滤条件
                    if user_id and filter_data.get("user_id") != user_id:
                        continue
                    
                    if tags:
                        filter_tags = set(filter_data.get("tags", []))
                        if not all(tag in filter_tags for tag in tags):
                            continue
                    
                    if search_term:
                        search_term = search_term.lower()
                        name = filter_data.get("name", "").lower()
                        desc = filter_data.get("description", "").lower()
                        summary = filter_data.get("summary", "").lower()
                        
                        if (search_term not in name and 
                            search_term not in desc and 
                            search_term not in summary):
                            continue
                    
                    # 从结果中移除过滤条件数据本身，只返回元数据
                    metadata = {k: v for k, v in filter_data.items() if k != "filter_data"}
                    results.append(metadata)
            except Exception as e:
                print(f"读取过滤条件文件 {filename} 出错: {str(e)}")
        
        # 按更新时间排序，最新的排在前面
        results.sort(key=lambda x: x.get("updated_at", ""), reverse=True)
        return results
    
    async def get_filter_data(self, filter_id: str) -> Optional[Dict[str, Any]]:
        """
        获取过滤条件的数据部分
        
        Args:
            filter_id: 过滤条件ID
        
        Returns:
            过滤条件数据，如果不存在则返回None
        """
        filter_info = await self.load_filter(filter_id)
        if not filter_info:
            return None
        
        return filter_info.get("filter_data")
    
    async def export_filter(self, filter_id: str) -> Optional[Dict[str, Any]]:
        """
        导出过滤条件为完整的JSON格式，包括元数据和过滤条件数据
        
        Args:
            filter_id: 过滤条件ID
        
        Returns:
            完整的过滤条件数据，如果不存在则返回None
        """
        return await self.load_filter(filter_id)
    
    async def import_filter(self, 
                           filter_data: Dict[str, Any], 
                           preserve_id: bool = False) -> Optional[Dict[str, Any]]:
        """
        导入过滤条件
        
        Args:
            filter_data: 完整的过滤条件数据，包括元数据
            preserve_id: 是否保留原ID，默认生成新ID
        
        Returns:
            导入后的过滤条件元数据
        """
        if not filter_data or not isinstance(filter_data, dict):
            return None
        
        required_fields = ["name", "filter_data"]
        if not all(field in filter_data for field in required_fields):
            return None
        
        # 决定是否使用新ID
        if not preserve_id or "id" not in filter_data:
            filter_id = self._generate_filter_id()
            filter_data["id"] = filter_id
        else:
            filter_id = filter_data["id"]
        
        # 更新时间戳
        now = datetime.now().isoformat()
        filter_data["imported_at"] = now
        filter_data["updated_at"] = now
        
        # 如果没有创建时间，添加一个
        if "created_at" not in filter_data:
            filter_data["created_at"] = now
        
        # 保存到文件
        filter_path = self._get_filter_path(filter_id)
        
        try:
            # 重新生成摘要
            visualizer = FilterVisualizer(filter_data["filter_data"])
            filter_data["summary"] = visualizer.generate_filter_summary()
        except Exception as e:
            filter_data["summary"] = f"无法生成摘要: {str(e)}"
        
        async with aiofiles.open(filter_path, "w", encoding="utf-8") as f:
            await f.write(json.dumps(filter_data, ensure_ascii=False, indent=2))
        
        return filter_data
    
    async def clone_filter(self, 
                          filter_id: str, 
                          new_name: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        克隆现有的过滤条件
        
        Args:
            filter_id: 要克隆的过滤条件ID
            new_name: 新的过滤条件名称，默认为"[原名称] - 副本"
        
        Returns:
            克隆后的过滤条件元数据
        """
        # 加载现有过滤条件
        existing_filter = await self.load_filter(filter_id)
        if not existing_filter:
            return None
        
        # 创建克隆
        clone_data = existing_filter.copy()
        
        # 生成新ID
        clone_data["id"] = self._generate_filter_id()
        
        # 更新名称
        if new_name:
            clone_data["name"] = new_name
        else:
            clone_data["name"] = f"{existing_filter['name']} - 副本"
        
        # 更新时间戳
        now = datetime.now().isoformat()
        clone_data["created_at"] = now
        clone_data["updated_at"] = now
        clone_data["cloned_from"] = filter_id
        
        # 保存到文件
        filter_path = self._get_filter_path(clone_data["id"])
        async with aiofiles.open(filter_path, "w", encoding="utf-8") as f:
            await f.write(json.dumps(clone_data, ensure_ascii=False, indent=2))
        
        return clone_data
    
    async def save_filter_to_db(self, filter_id: str, db: AsyncSession) -> bool:
        """
        将过滤条件保存到数据库
        
        Args:
            filter_id: 过滤条件ID
            db: 数据库会话
        
        Returns:
            是否成功保存
        """
        # 这里实现保存到数据库的逻辑
        # 注意: 此方法需要根据实际的数据库模型和架构进行适配
        filter_data = await self.load_filter(filter_id)
        if not filter_data:
            return False
        
        try:
            # 这里假设有对应的数据库模型和API
            # db_filter = FilterModel(
            #     id=filter_data["id"],
            #     name=filter_data["name"],
            #     description=filter_data["description"],
            #     user_id=filter_data.get("user_id"),
            #     filter_json=json.dumps(filter_data["filter_data"]),
            #     created_at=datetime.fromisoformat(filter_data["created_at"]),
            #     updated_at=datetime.fromisoformat(filter_data["updated_at"])
            # )
            # db.add(db_filter)
            # await db.commit()
            
            # 由于没有具体数据库模型，此处返回True模拟成功
            return True
        except Exception as e:
            print(f"保存过滤条件到数据库出错: {str(e)}")
            # await db.rollback()
            return False
    
    async def get_visualization(self, 
                              filter_id: str, 
                              format: str = "mermaid") -> Optional[str]:
        """
        获取指定过滤条件的可视化表示
        
        Args:
            filter_id: 过滤条件ID
            format: 可视化格式，支持 'mermaid', 'd3', 'graphviz', 'html'
        
        Returns:
            可视化表示内容，如果不存在或格式不支持则返回None
        """
        filter_data = await self.get_filter_data(filter_id)
        if not filter_data:
            return None
        
        try:
            visualizer = FilterVisualizer(filter_data)
            
            if format.lower() == "mermaid":
                return visualizer.generate_mermaid_diagram()
            elif format.lower() == "d3":
                return json.dumps(visualizer.generate_d3_json(), ensure_ascii=False)
            elif format.lower() == "graphviz":
                return visualizer.generate_graphviz_dot()
            elif format.lower() == "html":
                return visualizer.generate_html_visualization()
            elif format.lower() == "summary":
                return visualizer.generate_filter_summary()
            else:
                return None
        except Exception as e:
            print(f"生成过滤条件可视化出错: {str(e)}")
            return None

    def create_filter(self, name: str, filter_group: FilterGroup) -> bool:
        """
        创建命名过滤规则
        
        参数:
            name: 过滤规则名称
            filter_group: 过滤条件组
            
        返回:
            bool: 创建是否成功
        """
        with self._lock:
            # 检查规则名称是否已存在
            if name in self._named_filters:
                logger.warning(f"创建过滤规则失败，规则名称已存在: {name}")
                return False
            
            # 保存过滤规则
            self._named_filters[name] = filter_group
            self._filter_usage[name] = datetime.now()
            self._filter_counts[name] = 0
            
            logger.info(f"成功创建过滤规则: {name}")
            return True
    
    def update_filter_group(self, name: str, filter_group: FilterGroup) -> bool:
        """
        更新命名过滤规则
        
        参数:
            name: 过滤规则名称
            filter_group: 过滤条件组
            
        返回:
            bool: 更新是否成功
        """
        with self._lock:
            # 检查规则名称是否存在
            if name not in self._named_filters:
                logger.warning(f"更新过滤规则失败，规则名称不存在: {name}")
                return False
            
            # 更新过滤规则
            self._named_filters[name] = filter_group
            self._filter_usage[name] = datetime.now()
            
            logger.info(f"成功更新过滤规则: {name}")
            return True
    
    def delete_filter_group(self, name: str) -> bool:
        """
        删除命名过滤规则
        
        参数:
            name: 过滤规则名称
            
        返回:
            bool: 删除是否成功
        """
        with self._lock:
            # 检查规则名称是否存在
            if name not in self._named_filters:
                logger.warning(f"删除过滤规则失败，规则名称不存在: {name}")
                return False
            
            # 删除过滤规则
            del self._named_filters[name]
            del self._filter_usage[name]
            del self._filter_counts[name]
            
            logger.info(f"成功删除过滤规则: {name}")
            return True
    
    def get_filter_group(self, name: str) -> Optional[FilterGroup]:
        """
        获取命名过滤规则
        
        参数:
            name: 过滤规则名称
            
        返回:
            Optional[FilterGroup]: 过滤条件组，如果不存在则返回None
        """
        with self._lock:
            # 检查规则名称是否存在
            if name not in self._named_filters:
                logger.warning(f"获取过滤规则失败，规则名称不存在: {name}")
                return None
            
            # 更新使用时间
            self._filter_usage[name] = datetime.now()
            
            return self._named_filters[name]
    
    def list_filter_groups(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有命名过滤规则
        
        返回:
            Dict[str, Dict[str, Any]]: 规则名称到规则信息的映射
        """
        with self._lock:
            result = {}
            for name, filter_group in self._named_filters.items():
                result[name] = {
                    "filter": filter_group.to_dict(),
                    "last_used": self._filter_usage[name].isoformat(),
                    "usage_count": self._filter_counts[name]
                }
            return result
    
    def apply_filter(self, name: str, include_subtree: bool = True) -> List[Dict[str, Any]]:
        """
        应用命名过滤规则
        
        参数:
            name: 过滤规则名称
            include_subtree: 是否包含子设备树
            
        返回:
            List[Dict[str, Any]]: 符合过滤条件的设备列表
        """
        with self._lock:
            # 获取过滤规则
            filter_group = self.get_filter_group(name)
            if filter_group is None:
                return []
            
            # 更新使用计数
            self._filter_counts[name] += 1
            
            # 应用过滤规则
            return self.filter_devices(filter_group, include_subtree)
    
    def filter_devices(self, filter_group: FilterGroup, include_subtree: bool = True) -> List[Dict[str, Any]]:
        """
        使用过滤条件组过滤设备
        
        参数:
            filter_group: 过滤条件组
            include_subtree: 是否包含子设备树
            
        返回:
            List[Dict[str, Any]]: 符合过滤条件的设备列表
        """
        return self.filter_engine.filter_devices_with_group(filter_group, include_subtree)
    
    def filter_devices_with_condition(self, condition: FilterCondition, include_subtree: bool = True) -> List[Dict[str, Any]]:
        """
        使用单个过滤条件过滤设备
        
        参数:
            condition: 过滤条件
            include_subtree: 是否包含子设备树
            
        返回:
            List[Dict[str, Any]]: 符合过滤条件的设备列表
        """
        return self.filter_engine.filter_devices_with_condition(condition, include_subtree)
    
    def add_device(self, device_id: str, device_data: Dict[str, Any]) -> bool:
        """
        添加设备到过滤引擎
        
        参数:
            device_id: 设备ID
            device_data: 设备数据
            
        返回:
            bool: 添加是否成功
        """
        return self.filter_engine.add_device(device_id, device_data)
    
    def update_device(self, device_id: str, device_data: Dict[str, Any]) -> bool:
        """
        更新设备数据
        
        参数:
            device_id: 设备ID
            device_data: 设备数据
            
        返回:
            bool: 更新是否成功
        """
        return self.filter_engine.update_device(device_id, device_data)
    
    def remove_device(self, device_id: str) -> bool:
        """
        从过滤引擎中移除设备
        
        参数:
            device_id: 设备ID
            
        返回:
            bool: 移除是否成功
        """
        return self.filter_engine.remove_device(device_id)
    
    def get_device(self, device_id: str) -> Optional[Dict[str, Any]]:
        """
        获取设备数据
        
        参数:
            device_id: 设备ID
            
        返回:
            Optional[Dict[str, Any]]: 设备数据，如果不存在则返回None
        """
        return self.filter_engine.get_device(device_id)
    
    def clear_cache(self) -> None:
        """清除过滤引擎的缓存"""
        self.filter_engine.clear_cache()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        返回:
            Dict[str, Any]: 缓存统计信息
        """
        return self.filter_engine.get_cache_stats()
    
    def export_filters(self) -> Dict[str, Any]:
        """
        导出所有过滤规则
        
        返回:
            Dict[str, Any]: 包含所有过滤规则的字典
        """
        with self._lock:
            filters = {}
            for name, filter_group in self._named_filters.items():
                filters[name] = filter_group.to_dict()
            return {
                "filters": filters,
                "metadata": {
                    "export_time": datetime.now().isoformat(),
                    "count": len(filters)
                }
            }
    
    def import_filters(self, data: Dict[str, Any]) -> Dict[str, bool]:
        """
        导入过滤规则
        
        参数:
            data: 包含过滤规则的字典
            
        返回:
            Dict[str, bool]: 规则名称到导入成功状态的映射
        """
        results = {}
        filters = data.get("filters", {})
        
        for name, filter_data in filters.items():
            try:
                filter_group = FilterGroup.from_dict(filter_data)
                if name in self._named_filters:
                    # 更新现有规则
                    success = self.update_filter_group(name, filter_group)
                else:
                    # 创建新规则
                    success = self.create_filter(name, filter_group)
                results[name] = success
            except Exception as e:
                logger.error(f"导入过滤规则'{name}'失败: {str(e)}")
                results[name] = False
        
        return results
    
    def create_simple_filter(self, name: str, field: str, operator: Union[FilterOperator, str], 
                            value: Any = None, logic: Union[FilterLogic, str] = FilterLogic.AND) -> bool:
        """
        创建简单的命名过滤规则（只包含一个条件）
        
        参数:
            name: 过滤规则名称
            field: 字段名
            operator: 操作符
            value: 条件值
            logic: 逻辑操作符
            
        返回:
            bool: 创建是否成功
        """
        try:
            # 创建过滤条件
            condition = FilterCondition(field, operator, value)
            
            # 创建过滤条件组
            filter_group = FilterGroup(logic)
            filter_group.add_condition(condition)
            
            # 创建过滤规则
            return self.create_filter(name, filter_group)
        except Exception as e:
            logger.error(f"创建简单过滤规则失败: {str(e)}")
            return False
    
    def add_condition_to_filter(self, name: str, field: str, operator: Union[FilterOperator, str], 
                               value: Any = None) -> bool:
        """
        向现有过滤规则添加条件
        
        参数:
            name: 过滤规则名称
            field: 字段名
            operator: 操作符
            value: 条件值
            
        返回:
            bool: 添加是否成功
        """
        with self._lock:
            # 获取现有过滤规则
            filter_group = self.get_filter_group(name)
            if filter_group is None:
                return False
            
            try:
                # 创建过滤条件
                condition = FilterCondition(field, operator, value)
                
                # 添加到现有规则
                filter_group.add_condition(condition)
                
                # 更新过滤规则
                return self.update_filter_group(name, filter_group)
            except Exception as e:
                logger.error(f"向过滤规则添加条件失败: {str(e)}")
                return False
    
    def get_filter_usage_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        获取过滤规则使用统计信息
        
        返回:
            Dict[str, Dict[str, Any]]: 规则名称到使用统计的映射
        """
        with self._lock:
            stats = {}
            for name in self._named_filters.keys():
                stats[name] = {
                    "last_used": self._filter_usage[name],
                    "usage_count": self._filter_counts[name]
                }
            return stats
    
    def get_most_used_filters(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取使用最多的过滤规则
        
        参数:
            limit: 返回结果的最大数量
            
        返回:
            List[Dict[str, Any]]: 过滤规则使用统计列表，按使用次数降序排列
        """
        with self._lock:
            # 按使用次数排序
            sorted_filters = sorted(
                self._filter_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )
            
            # 限制结果数量
            sorted_filters = sorted_filters[:limit]
            
            # 构建结果
            result = []
            for name, count in sorted_filters:
                result.append({
                    "name": name,
                    "usage_count": count,
                    "last_used": self._filter_usage[name].isoformat(),
                    "filter": self._named_filters[name].to_dict()
                })
            
            return result
    
    def get_recently_used_filters(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近使用的过滤规则
        
        参数:
            limit: 返回结果的最大数量
            
        返回:
            List[Dict[str, Any]]: 过滤规则使用统计列表，按最近使用时间降序排列
        """
        with self._lock:
            # 按最近使用时间排序
            sorted_filters = sorted(
                self._filter_usage.items(),
                key=lambda x: x[1],
                reverse=True
            )
            
            # 限制结果数量
            sorted_filters = sorted_filters[:limit]
            
            # 构建结果
            result = []
            for name, last_used in sorted_filters:
                result.append({
                    "name": name,
                    "usage_count": self._filter_counts[name],
                    "last_used": last_used.isoformat(),
                    "filter": self._named_filters[name].to_dict()
                })
            
            return result 