<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>审计日志报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { width: 90%; margin: 20px auto; background-color: #fff; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; border-bottom: 2px solid #3498db; padding-bottom: 10px; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; font-size: 0.9em; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        th { background-color: #f2f2f2; color: #555; font-weight: bold; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .report-meta { margin-bottom: 20px; padding: 10px; background-color: #ecf0f1; border-left: 5px solid #3498db; font-size: 0.9em; }
        .report-meta p { margin: 5px 0; }
        .footer { text-align: center; margin-top: 30px; padding-top: 10px; border-top: 1px solid #eee; font-size: 0.8em; color: #777; }
        .no-data { text-align: center; color: #777; padding: 20px; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <h1>审计日志报告</h1>
        
        <div class="report-meta">
            <p><strong>报告生成时间:</strong> {{ generation_time }}</p>
            <p><strong>筛选条件:</strong></p>
            <ul>
                {% for key, value in filters.items() if value %}
                    <li><strong>{{ key }}:</strong> {{ value }}</li>
                {% else %}
                    <li>无特定筛选条件</li>
                {% endfor %}
            </ul>
        </div>

        {% if audit_logs %}
            <table>
                <thead>
                    <tr>
                        <th>时间戳</th>
                        <th>用户</th>
                        <th>源IP</th>
                        <th>事件类型</th>
                        <th>操作对象</th>
                        <th>结果</th>
                        <th>详情</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in audit_logs %}
                    <tr>
                        <td>{{ log.timestamp_str }}</td>
                        <td>{{ log.user_identity if log.user_identity else 'N/A' }}</td>
                        <td>{{ log.source_ip if log.source_ip else 'N/A' }}</td>
                        <td>{{ log.event_type if log.event_type else 'N/A' }}</td>
                        <td>{{ log.target_resource if log.target_resource else 'N/A' }}</td>
                        <td>{{ log.outcome if log.outcome else 'N/A' }}</td>
                        <td>
                            {% if log.details and log.details is mapping %}
                                <ul>
                                {% for k, v in log.details.items() %}
                                    <li><strong>{{ k }}:</strong> {{ v }}</li>
                                {% endfor %}
                                </ul>
                            {% elif log.details %}
                                {{ log.details }}
                            {% else %}
                                无
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <p class="no-data">在选定条件下没有找到审计日志记录。</p>
        {% endif %}

        <div class="footer">
            <p>OmniLink 全联通系统</p>
        </div>
    </div>
</body>
</html> 