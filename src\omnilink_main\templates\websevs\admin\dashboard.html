<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员仪表盘 - Web管理服务</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .dashboard-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .dashboard-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1.5rem;
        }
        
        .dashboard-card h3 {
            margin-bottom: 1rem;
            color: var(--primary-color);
            border-bottom: 1px solid var(--gray-color);
            padding-bottom: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        
        .stat-box {
            text-align: center;
            padding: 1rem;
            background-color: var(--light-color);
            border-radius: var(--border-radius);
        }
        
        .stat-box .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-box .stat-label {
            font-size: 0.9rem;
            color: var(--gray-color);
        }
        
        .user-list, .role-list {
            list-style: none;
            padding: 0;
        }
        
        .user-item, .role-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem 0;
            border-bottom: 1px solid var(--light-color);
        }
        
        .user-item:last-child, .role-item:last-child {
            border-bottom: none;
        }
        
        .user-info, .role-info {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-weight: bold;
        }
        
        .user-details, .role-details {
            display: flex;
            flex-direction: column;
        }
        
        .user-name, .role-name {
            font-weight: bold;
        }
        
        .user-email, .role-description {
            font-size: 0.8rem;
            color: var(--gray-color);
        }
        
        .user-status, .role-status {
            padding: 0.3rem 0.6rem;
            border-radius: 1rem;
            font-size: 0.8rem;
        }
        
        .status-active {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .status-inactive {
            background-color: var(--danger-color);
            color: white;
        }
        
        .chart-container {
            height: 250px;
            position: relative;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn {
            padding: 0.4rem 0.8rem;
            border-radius: var(--border-radius);
            border: none;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-warning {
            background-color: var(--tertiary-color);
            color: var(--dark-color);
        }
    </style>
</head>
<body>
    <header>
        <h1>管理员仪表盘</h1>
        <nav>
            <ul>
                <li><a href="{{ url_for('websevs.admin.index') }}">首页</a></li>
                <li><a href="{{ url_for('websevs.admin.users') }}">用户管理</a></li>
                <li><a href="{{ url_for('websevs.admin.roles') }}">角色管理</a></li>
                <li><a href="{{ url_for('websevs.admin.permissions') }}">权限管理</a></li>
                <li><a href="{{ url_for('websevs.admin.logs') }}">系统日志</a></li>
                <li><a href="{{ url_for('websevs.admin.system') }}">系统配置</a></li>
                <li><a href="{{ url_for('websevs.admin.backup') }}">备份管理</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section>
            <h2>系统概览</h2>
            <div class="dashboard-container">
                <div class="dashboard-card">
                    <h3>用户统计</h3>
                    <div class="stats-grid">
                        <div class="stat-box">
                            <div class="stat-value">32</div>
                            <div class="stat-label">总用户数</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">28</div>
                            <div class="stat-label">活跃用户</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">5</div>
                            <div class="stat-label">管理员</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">4</div>
                            <div class="stat-label">禁用账户</div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <h3>系统统计</h3>
                    <div class="stats-grid">
                        <div class="stat-box">
                            <div class="stat-value">8</div>
                            <div class="stat-label">角色数量</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">24</div>
                            <div class="stat-label">权限数量</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">156</div>
                            <div class="stat-label">今日操作</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">3</div>
                            <div class="stat-label">待审核</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section>
            <h2>用户管理</h2>
            <div class="dashboard-container">
                <div class="dashboard-card">
                    <h3>最近活跃用户</h3>
                    <ul class="user-list">
                        <li class="user-item">
                            <div class="user-info">
                                <div class="user-avatar">AD</div>
                                <div class="user-details">
                                    <div class="user-name">管理员</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                            <div class="user-status status-active">活跃</div>
                        </li>
                        <li class="user-item">
                            <div class="user-info">
                                <div class="user-avatar">ZS</div>
                                <div class="user-details">
                                    <div class="user-name">张三</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                            <div class="user-status status-active">活跃</div>
                        </li>
                        <li class="user-item">
                            <div class="user-info">
                                <div class="user-avatar">LS</div>
                                <div class="user-details">
                                    <div class="user-name">李四</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                            <div class="user-status status-active">活跃</div>
                        </li>
                        <li class="user-item">
                            <div class="user-info">
                                <div class="user-avatar">WW</div>
                                <div class="user-details">
                                    <div class="user-name">王五</div>
                                    <div class="user-email"><EMAIL></div>
                                </div>
                            </div>
                            <div class="user-status status-inactive">禁用</div>
                        </li>
                    </ul>
                    
                    <div style="text-align: center; margin-top: 1rem;">
                        <a href="{{ url_for('websevs.admin.users') }}" class="btn btn-primary">查看所有用户</a>
                    </div>
                </div>

                <div class="dashboard-card">
                    <h3>角色管理</h3>
                    <ul class="role-list">
                        <li class="role-item">
                            <div class="role-info">
                                <div class="role-details">
                                    <div class="role-name">超级管理员</div>
                                    <div class="role-description">拥有系统所有权限</div>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-warning">编辑</button>
                                <button class="btn btn-danger">删除</button>
                            </div>
                        </li>
                        <li class="role-item">
                            <div class="role-info">
                                <div class="role-details">
                                    <div class="role-name">普通管理员</div>
                                    <div class="role-description">拥有部分系统管理权限</div>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-warning">编辑</button>
                                <button class="btn btn-danger">删除</button>
                            </div>
                        </li>
                        <li class="role-item">
                            <div class="role-info">
                                <div class="role-details">
                                    <div class="role-name">设备管理员</div>
                                    <div class="role-description">管理设备共享与访问</div>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-warning">编辑</button>
                                <button class="btn btn-danger">删除</button>
                            </div>
                        </li>
                        <li class="role-item">
                            <div class="role-info">
                                <div class="role-details">
                                    <div class="role-name">普通用户</div>
                                    <div class="role-description">使用共享设备</div>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-warning">编辑</button>
                                <button class="btn btn-danger">删除</button>
                            </div>
                        </li>
                    </ul>
                    
                    <div style="text-align: center; margin-top: 1rem;">
                        <a href="{{ url_for('websevs.admin.roles') }}" class="btn btn-primary">查看所有角色</a>
                    </div>
                </div>
            </div>
        </section>

        <section>
            <h2>系统活动</h2>
            <div class="dashboard-card">
                <div class="chart-container">
                    <canvas id="activityChart"></canvas>
                </div>
            </div>
        </section>

        <section>
            <h2>最近系统日志</h2>
            <div class="dashboard-card">
                <table>
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>用户</th>
                            <th>操作</th>
                            <th>IP地址</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2023-07-15 14:23</td>
                            <td>admin</td>
                            <td>创建用户 "赵六"</td>
                            <td>*************</td>
                            <td>成功</td>
                        </tr>
                        <tr>
                            <td>2023-07-15 13:45</td>
                            <td>zhangsan</td>
                            <td>修改个人信息</td>
                            <td>*************</td>
                            <td>成功</td>
                        </tr>
                        <tr>
                            <td>2023-07-15 12:32</td>
                            <td>lisi</td>
                            <td>尝试访问未授权页面</td>
                            <td>*************</td>
                            <td>拒绝</td>
                        </tr>
                        <tr>
                            <td>2023-07-15 10:05</td>
                            <td>admin</td>
                            <td>系统配置更新</td>
                            <td>*************</td>
                            <td>成功</td>
                        </tr>
                        <tr>
                            <td>2023-07-15 09:17</td>
                            <td>unknown</td>
                            <td>多次登录失败</td>
                            <td>192.168.1.200</td>
                            <td>警告</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="text-align: center; margin-top: 1rem;">
                    <a href="{{ url_for('websevs.admin.logs') }}" class="btn btn-primary">查看所有日志</a>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <p>&copy; 2023 Web管理服务系统 - 版本 1.0.0</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 用户活动图表
            const ctx = document.getElementById('activityChart').getContext('2d');
            const activityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00'],
                    datasets: [{
                        label: '登录次数',
                        data: [5, 3, 1, 2, 8, 12, 10, 15, 12, 8, 6, 4],
                        borderColor: '#4285f4',
                        backgroundColor: 'rgba(66, 133, 244, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '操作次数',
                        data: [10, 8, 3, 5, 15, 20, 18, 25, 20, 15, 12, 8],
                        borderColor: '#34a853',
                        backgroundColor: 'rgba(52, 168, 83, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        title: {
                            display: true,
                            text: '今日系统活动'
                        }
                    }
                }
            });
        });
    </script>
</body>
</html> 