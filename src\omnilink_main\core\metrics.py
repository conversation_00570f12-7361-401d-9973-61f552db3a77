"""
模拟的度量模块
"""

from prometheus_client import Gauge, Counter

# Websocket Metrics
WEBSOCKET_ACTIVE_CONNECTIONS = Gauge(
    'websocket_active_connections',
    'Number of active WebSocket connections'
)

WEBSOCKET_MESSAGES_SENT_TOTAL = Counter(
    'websocket_messages_sent_total',
    'Total number of messages sent over WebSockets'
)

WEBSOCKET_MESSAGES_RECEIVED_TOTAL = Counter(
    'websocket_messages_received_total',
    'Total number of messages received over WebSockets'
)

WEBSOCKET_ERRORS_TOTAL = Counter(
    'websocket_errors_total',
    'Total number of errors encountered in WebSocket communications'
)

WEBSOCKET_ACTIVE_USERS = Gauge(
    'websocket_active_users',
    'Number of unique users with active WebSocket connections'
)

# 模拟的度量指标变量
websocket_connections_total = 0
websocket_connections_active = 0
websocket_messages_sent = 0
websocket_messages_received = 0
websocket_errors = 0 
