"""
主服务器 - 配置工具模块

提供配置的加载、保存和验证功能
"""
import os
import json
import logging
from typing import Dict, Any, Optional

# 配置日志
logger = logging.getLogger('main_server.config')

# 默认配置
DEFAULT_CONFIG = {
    "server": {
        "host": "0.0.0.0",
        "api_port": 8080,
        "ws_port": 8765,
        "discovery_port": 45678
    },
    "database": {
        "type": "sqlite",
        "path": "data/main_server.db"
    }
}

def load_config(config_path: str) -> Dict[str, Any]:
    """
    从文件加载配置
    
    参数:
        config_path: 配置文件路径
        
    返回:
        Dict: 配置字典
    
    异常:
        FileNotFoundError: 如果配置文件不存在
        json.JSONDecodeError: 如果配置文件不是有效的JSON
    """
    # 检查配置文件是否存在
    if not os.path.exists(config_path):
        logger.warning(f"配置文件 {config_path} 不存在，创建默认配置")
        # 创建配置目录
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        # 保存默认配置
        save_config(config_path, DEFAULT_CONFIG)
        return DEFAULT_CONFIG
    
    # 读取配置文件
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 验证并补充缺失的配置
        config = validate_config(config)
        
        logger.info(f"已加载配置文件: {config_path}")
        return config
    except json.JSONDecodeError as e:
        logger.error(f"配置文件 {config_path} 不是有效的JSON: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"加载配置文件 {config_path} 失败: {str(e)}")
        raise

def save_config(config_path: str, config: Dict[str, Any]) -> bool:
    """
    保存配置到文件
    
    参数:
        config_path: 配置文件路径
        config: 配置字典
        
    返回:
        bool: 保存成功返回True，失败返回False
    """
    try:
        # 创建配置目录
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        # 写入配置文件
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
            
        logger.info(f"已保存配置到: {config_path}")
        return True
    except Exception as e:
        logger.error(f"保存配置到 {config_path} 失败: {str(e)}")
        return False

def validate_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证并补充缺失的配置
    
    参数:
        config: 待验证的配置字典
        
    返回:
        Dict: 验证后的配置字典
    """
    validated_config = {}
    
    # 递归合并配置
    def merge_config(default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                # 递归合并子字典
                result[key] = merge_config(result[key], value)
            else:
                # 使用用户配置覆盖默认配置
                result[key] = value
        return result
    
    # 合并默认配置和用户配置
    validated_config = merge_config(DEFAULT_CONFIG, config)
    
    # 记录缺失的配置项
    missing_keys = []
    for key in DEFAULT_CONFIG:
        if key not in config:
            missing_keys.append(key)
    
    if missing_keys:
        logger.warning(f"配置中缺少以下项，已使用默认值: {', '.join(missing_keys)}")
    
    return validated_config

def get_config_value(config: Dict[str, Any], key_path: str, default: Any = None) -> Any:
    """
    从配置中获取指定路径的值
    
    参数:
        config: 配置字典
        key_path: 键路径，使用点分隔，如 'server.host'
        default: 如果键不存在时返回的默认值
        
    返回:
        Any: 配置值，如果不存在则返回默认值
    """
    keys = key_path.split('.')
    current = config
    
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default
    
    return current 
