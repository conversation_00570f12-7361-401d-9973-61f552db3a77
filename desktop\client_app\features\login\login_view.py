import sys
from PyQt5.QtWidgets import (
    <PERSON><PERSON><PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLineEdit, QMessageBox, QFormLayout, QLabel, QCheckBox
)
from PyQt5.QtCore import Qt
from desktop.client_app.core.settings import settings

class LoginView(QWidget):
    """
    Provides the UI for the login window.
    """
    def __init__(self, controller=None):
        super().__init__()
        self.controller = controller
        self.init_ui()

    def set_controller(self, controller):
        """Sets the controller for this view."""
        self.controller = controller

    def init_ui(self):
        """Initializes the UI components."""
        self.setWindowTitle("登录 - OmniLink")
        self.setGeometry(300, 300, 400, 320) # Increased height for new controls

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        form_layout = QFormLayout()
        form_layout.setSpacing(10)
        form_layout.setLabelAlignment(Qt.AlignRight)

        self.server_address_input = QLineEdit(self)
        self.server_address_input.setPlaceholderText("例如: http://127.0.0.1:8000")
        form_layout.addRow(QLabel("服务器地址:"), self.server_address_input)
        
        self.username_input = QLineEdit(self)
        self.username_input.setPlaceholderText("请输入用户名")
        form_layout.addRow(QLabel("用户名:"), self.username_input)

        self.password_input = QLineEdit(self)
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("请输入密码")
        form_layout.addRow(QLabel("密码:"), self.password_input)
        
        # --- New Controls ---
        # Checkbox layout
        options_layout = QHBoxLayout()
        self.remember_username_checkbox = QCheckBox("记住用户", self)
        self.remember_password_checkbox = QCheckBox("记住密码", self)
        options_layout.addWidget(self.remember_username_checkbox)
        options_layout.addWidget(self.remember_password_checkbox)
        options_layout.addStretch(1) # Pushes checkboxes to the left

        # Buttons for Register and Change Password
        self.register_button = QPushButton("注册账号", self)
        self.change_password_button = QPushButton("修改密码", self)
        # Make them look like links
        self.register_button.setFlat(True)
        self.change_password_button.setFlat(True)
        options_layout.addWidget(self.register_button)
        options_layout.addWidget(self.change_password_button)
        
        self.login_button = QPushButton("登 录", self)
        self.login_button.setMinimumHeight(40)
        
        # --- Layout Assembly ---
        main_layout.addLayout(form_layout)
        main_layout.addLayout(options_layout) # Add the new options layout
        main_layout.addStretch(1)
        main_layout.addWidget(self.login_button)
        
        self.setLayout(main_layout)

        # --- Connections ---
        self.login_button.clicked.connect(self.on_login_button_click)
        self.register_button.clicked.connect(self.on_register_click)
        self.change_password_button.clicked.connect(self.on_change_password_click)
        self.remember_username_checkbox.toggled.connect(self.on_remember_username_toggled)

    def on_remember_username_toggled(self, checked):
        """
        If 'Remember User' is unchecked, also uncheck 'Remember Password'
        and disable it.
        """
        if not checked:
            self.remember_password_checkbox.setChecked(False)
            self.remember_password_checkbox.setEnabled(False)
        else:
            self.remember_password_checkbox.setEnabled(True)

    def on_login_button_click(self):
        """Handler for the login button click event."""
        if self.controller:
            self.controller.handle_login()

    def on_register_click(self):
        """Handler for the register button click event."""
        if self.controller:
            self.controller.handle_register()

    def on_change_password_click(self):
        """Handler for the change password button click event."""
        if self.controller:
            self.controller.handle_change_password()

    def get_credentials(self):
        """Returns the credentials entered by the user."""
        return (
            self.server_address_input.text().strip(),
            self.username_input.text().strip(),
            self.password_input.text().strip()
        )
    
    def get_remember_me_options(self):
        """Returns the state of the 'remember me' checkboxes."""
        return (
            self.remember_username_checkbox.isChecked(),
            self.remember_password_checkbox.isChecked()
        )

    def show_error_message(self, message):
        """Displays an error message box."""
        QMessageBox.warning(self, "登录失败", message)
        
    def show_info_message(self, title, message):
        """Displays an informational message box."""
        QMessageBox.information(self, title, message)

    def on_login_success(self):
        """
        Closes the login window upon successful login.
        """
        self.close()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    # A mock controller for testing the view standalone
    class MockController:
        def handle_login(self):
            print("Login clicked")
            creds = login_view.get_credentials()
            print(creds)
            opts = login_view.get_remember_me_options()
            print(f"Remember User: {opts[0]}, Remember Pass: {opts[1]}")
            # login_view.show_error_message("A test error.")
        
        def handle_register(self):
            print("Register clicked")
            login_view.show_info_message("注册", "注册功能开发中...")

        def handle_change_password(self):
            print("Change password clicked")
            login_view.show_info_message("修改密码", "修改密码功能开发中...")


    controller = MockController()
    login_view = LoginView(controller=controller)
    login_view.set_controller(controller) # Also need to set it for the view
    
    # Initial state
    login_view.remember_password_checkbox.setEnabled(False)

    login_view.show()
    sys.exit(app.exec_())