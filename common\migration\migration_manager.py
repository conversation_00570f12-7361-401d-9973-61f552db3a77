"""
迁移管理器

提供主服务器迁移功能，包括配置导出导入、数据迁移和验证。
"""

import logging
import os
import json
import shutil
import threading
import time
import hashlib
import tarfile
import datetime
from enum import Enum
from typing import Dict, List, Optional, Set, Tuple, Any, Union, Callable
from dataclasses import dataclass, field
from pathlib import Path
import psutil
import zipfile
import tempfile
import subprocess
import re
import platform
import pkg_resources
from common.database.dependencies import get_db
from common.models import *  # Assuming all models are imported here
from common.migration.system_state_snapshot import SystemStateSnapshot
from common.migration.snapshot_manager import SnapshotManager

logger = logging.getLogger(__name__)

class MigrationStatus(Enum):
    """迁移状态枚举"""
    IDLE = "idle"               # 空闲
    PREPARING = "preparing"     # 准备中
    EXPORTING = "exporting"     # 导出中
    IMPORTING = "importing"     # 导入中
    VALIDATING = "validating"   # 验证中
    FINALIZING = "finalizing"   # 完成中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"           # 失败
    ROLLING_BACK = "rolling_back" # 回滚中
    ROLLED_BACK = "rolled_back"   # 已回滚

@dataclass
class MigrationConfig:
    """迁移配置数据类"""
    export_data: bool = True            # 是否导出数据
    export_configs: bool = True         # 是否导出配置
    export_users: bool = True           # 是否导出用户
    export_logs: bool = False           # 是否导出日志
    incremental: bool = False           # 是否增量迁移
    compress: bool = True               # 是否压缩
    encrypt: bool = True                # 是否加密
    validate_before: bool = True        # 迁移前验证
    validate_after: bool = True         # 迁移后验证
    auto_rollback: bool = True          # 失败时自动回滚
    timeout: int = 3600                 # 超时时间（秒）
    backup_before_import: bool = True   # 导入前备份
    force_import: bool = False         # 是否强制导入

@dataclass
class MigrationInfo:
    """迁移信息数据类"""
    id: str                             # 迁移ID
    name: str                           # 迁移名称
    description: str = ""               # 迁移描述
    config: MigrationConfig = field(default_factory=MigrationConfig)  # 迁移配置
    status: MigrationStatus = MigrationStatus.IDLE  # 迁移状态
    created_at: datetime.datetime = field(default_factory=datetime.datetime.now)  # 创建时间
    started_at: Optional[datetime.datetime] = None  # 开始时间
    completed_at: Optional[datetime.datetime] = None  # 完成时间
    source_info: Dict[str, Any] = field(default_factory=dict)  # 源信息
    target_info: Dict[str, Any] = field(default_factory=dict)  # 目标信息
    progress: float = 0.0                # 进度（0-1）
    current_step: str = ""               # 当前步骤
    total_steps: int = 0                 # 总步骤数
    current_step_progress: float = 0.0   # 当前步骤进度
    logs: List[str] = field(default_factory=list)  # 日志
    errors: List[str] = field(default_factory=list)  # 错误
    warnings: List[str] = field(default_factory=list)  # 警告
    package_path: Optional[str] = None   # 迁移包路径
    package_size: int = 0                # 迁移包大小（字节）
    checksum: Optional[str] = None       # 校验和

@dataclass
class MigrationPackage:
    """迁移包数据类"""
    version: str = "1.0"                 # 版本
    created_at: str = field(default_factory=lambda: datetime.datetime.now().isoformat())
    created_by: str = "system"           # 创建者
    source_system: Dict[str, Any] = field(default_factory=dict)  # 源系统信息
    description: str = ""                # 描述
    configs: Dict[str, Any] = field(default_factory=dict)  # 配置
    data: Dict[str, Any] = field(default_factory=dict)  # 数据
    users: Dict[str, Any] = field(default_factory=dict)  # 用户
    logs: List[Dict[str, Any]] = field(default_factory=list)  # 日志
    checksum: Optional[str] = None       # 校验和


class MigrationManager:
    """迁移管理器"""
    
    def __init__(self, data_dir: str = None):
        """
        初始化迁移管理器
        
        Args:
            data_dir: 数据目录，默认为当前目录下的migration_data
        """
        self.data_dir = data_dir or os.path.join(os.getcwd(), "migration_data")
        self._lock = threading.RLock()
        self._migrations: Dict[str, MigrationInfo] = {}  # 迁移ID -> 迁移信息
        self._current_migration: Optional[str] = None  # 当前迁移ID
        self._migration_thread: Optional[threading.Thread] = None  # 迁移线程
        self._stop_migration = False  # 停止迁移标志
        
        # 创建数据目录
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(os.path.join(self.data_dir, "packages"), exist_ok=True)
        os.makedirs(os.path.join(self.data_dir, "backups"), exist_ok=True)
        os.makedirs(os.path.join(self.data_dir, "temp"), exist_ok=True)
        
        # 尝试加载已有迁移信息
        self._load_migrations()
        
        logger.info(f"迁移管理器已初始化，数据目录：{self.data_dir}")
        
    def _load_migrations(self):
        """加载已有迁移信息"""
        migrations_file = os.path.join(self.data_dir, "migrations.json")
        if os.path.exists(migrations_file):
            try:
                with open(migrations_file, "r", encoding="utf-8") as f:
                    migrations_data = json.load(f)
                    
                for migration_id, migration_data in migrations_data.items():
                    # 转换状态枚举
                    status_str = migration_data.pop("status", "idle")
                    try:
                        status = MigrationStatus(status_str)
                    except ValueError:
                        status = MigrationStatus.IDLE
                        
                    # 转换日期时间
                    for dt_field in ["created_at", "started_at", "completed_at"]:
                        if dt_field in migration_data and migration_data[dt_field]:
                            try:
                                migration_data[dt_field] = datetime.datetime.fromisoformat(migration_data[dt_field])
                            except (ValueError, TypeError):
                                migration_data[dt_field] = None
                                
                    # 转换配置
                    config_data = migration_data.pop("config", {})
                    config = MigrationConfig(**config_data)
                    
                    # 创建迁移信息
                    migration_info = MigrationInfo(
                        id=migration_id,
                        status=status,
                        config=config,
                        **migration_data
                    )
                    
                    self._migrations[migration_id] = migration_info
                    
                logger.info(f"已加载 {len(self._migrations)} 个迁移记录")
                
            except Exception as e:
                logger.error(f"加载迁移信息出错: {e}")
                
    def _save_migrations(self):
        """保存迁移信息"""
        with self._lock:
            migrations_file = os.path.join(self.data_dir, "migrations.json")
            try:
                migrations_data = {}
                for migration_id, migration_info in self._migrations.items():
                    # 转换为可序列化的数据
                    migration_dict = {}
                    for k, v in migration_info.__dict__.items():
                        if isinstance(v, datetime.datetime):
                            migration_dict[k] = v.isoformat()
                        elif isinstance(v, Enum):
                            migration_dict[k] = v.value
                        elif isinstance(v, MigrationConfig):
                            migration_dict[k] = v.__dict__
                        else:
                            migration_dict[k] = v
                    
                    migrations_data[migration_id] = migration_dict
                    
                with open(migrations_file, "w", encoding="utf-8") as f:
                    json.dump(migrations_data, f, ensure_ascii=False, indent=2)
                    
                logger.debug(f"已保存 {len(self._migrations)} 个迁移记录")
                
            except Exception as e:
                logger.error(f"保存迁移信息出错: {e}")
                
    def create_migration(self, name: str, description: str = "", config: MigrationConfig = None) -> Optional[str]:
        """
        创建迁移任务
        
        Args:
            name: 迁移名称
            description: 迁移描述
            config: 迁移配置
            
        Returns:
            迁移ID，如果创建失败则返回None
        """
        with self._lock:
            # 检查是否有正在进行的迁移
            if self._current_migration is not None:
                active_migration = self._migrations.get(self._current_migration)
                if active_migration and active_migration.status not in [
                    MigrationStatus.COMPLETED, MigrationStatus.FAILED, MigrationStatus.ROLLED_BACK
                ]:
                    logger.warning(f"已有正在进行的迁移: {self._current_migration}")
                    return None
                    
            # 生成迁移ID
            migration_id = f"migration_{int(time.time())}_{hashlib.md5(name.encode()).hexdigest()[:8]}"
            
            # 创建迁移信息
            migration_info = MigrationInfo(
                id=migration_id,
                name=name,
                description=description,
                config=config or MigrationConfig(),
                status=MigrationStatus.IDLE,
                created_at=datetime.datetime.now()
            )
            
            # 保存迁移信息
            self._migrations[migration_id] = migration_info
            self._save_migrations()
            
            logger.info(f"已创建迁移: {name} (ID: {migration_id})")
            return migration_id
            
    def get_migration(self, migration_id: str) -> Optional[MigrationInfo]:
        """
        获取迁移信息
        
        Args:
            migration_id: 迁移ID
            
        Returns:
            迁移信息，如果不存在则返回None
        """
        with self._lock:
            return self._migrations.get(migration_id)
            
    def list_migrations(self) -> List[MigrationInfo]:
        """
        获取所有迁移信息
        
        Returns:
            迁移信息列表
        """
        with self._lock:
            return list(self._migrations.values())
            
    def start_migration(self, migration_id: str) -> bool:
        """
        启动迁移
        
        Args:
            migration_id: 迁移ID
            
        Returns:
            是否成功启动
        """
        with self._lock:
            # 检查迁移是否存在
            migration_info = self._migrations.get(migration_id)
            if not migration_info:
                logger.warning(f"迁移不存在: {migration_id}")
                return False
                
            # 检查迁移状态
            if migration_info.status not in [MigrationStatus.IDLE, MigrationStatus.FAILED, MigrationStatus.ROLLED_BACK]:
                logger.warning(f"迁移状态不允许启动: {migration_info.status.value}")
                return False
                
            # 检查是否有正在进行的迁移
            if self._current_migration is not None:
                active_migration = self._migrations.get(self._current_migration)
                if active_migration and active_migration.status not in [
                    MigrationStatus.COMPLETED, MigrationStatus.FAILED, MigrationStatus.ROLLED_BACK
                ]:
                    logger.warning(f"已有正在进行的迁移: {self._current_migration}")
                    return False
                    
            # 更新迁移状态
            migration_info.status = MigrationStatus.PREPARING
            migration_info.started_at = datetime.datetime.now()
            migration_info.progress = 0.0
            migration_info.logs = []
            migration_info.errors = []
            migration_info.warnings = []
            migration_info.current_step = "准备迁移"
            
            # 启动迁移线程
            self._current_migration = migration_id
            self._stop_migration = False
            self._migration_thread = threading.Thread(target=self._migration_process, args=(migration_id,))
            self._migration_thread.daemon = True
            self._migration_thread.start()
            
            self._save_migrations()
            
            logger.info(f"已启动迁移: {migration_id}")
            return True
            
    def stop_migration(self, migration_id: str) -> bool:
        """
        停止迁移
        
        Args:
            migration_id: 迁移ID
            
        Returns:
            是否成功停止
        """
        with self._lock:
            # 检查迁移是否存在
            migration_info = self._migrations.get(migration_id)
            if not migration_info:
                logger.warning(f"迁移不存在: {migration_id}")
                return False
                
            # 检查是否是当前迁移
            if self._current_migration != migration_id:
                logger.warning(f"不是当前迁移: {migration_id}")
                return False
                
            # 设置停止标志
            self._stop_migration = True
            
            # 等待迁移线程结束
            if self._migration_thread and self._migration_thread.is_alive():
                # 不阻塞当前线程，仅设置停止标志
                logger.info(f"已设置迁移停止标志: {migration_id}")
                
                # 更新迁移状态
                migration_info.status = MigrationStatus.FAILED
                migration_info.errors.append("迁移被手动停止")
                self._save_migrations()
                
            return True
            
    def _migration_process(self, migration_id: str):
        """
        迁移处理过程
        
        Args:
            migration_id: 迁移ID
        """
        migration_info = self._migrations.get(migration_id)
        if not migration_info:
            return
            
        try:
            # 记录日志
            self._add_log(migration_info, "开始迁移处理")
            
            # 设置总步骤数
            total_steps = 4  # 准备、导出、验证、完成
            if migration_info.config.validate_before:
                total_steps += 1
            if migration_info.config.validate_after:
                total_steps += 1
                
            migration_info.total_steps = total_steps
            
            # 步骤1: 准备
            migration_info.current_step = "准备迁移"
            migration_info.status = MigrationStatus.PREPARING
            self._save_migrations()
            
            self._add_log(migration_info, "准备迁移环境")
            
            # 创建临时目录
            temp_dir = os.path.join(self.data_dir, "temp", migration_id)
            os.makedirs(temp_dir, exist_ok=True)
            
            # 更新进度
            migration_info.progress = 1 / total_steps
            migration_info.current_step_progress = 1.0
            self._save_migrations()
            
            # 检查是否需要停止
            if self._stop_migration:
                self._add_log(migration_info, "迁移被停止")
                migration_info.status = MigrationStatus.FAILED
                self._save_migrations()
                return
                
            # 步骤2: 迁移前验证（可选）
            if migration_info.config.validate_before:
                migration_info.current_step = "迁移前验证"
                migration_info.status = MigrationStatus.VALIDATING
                migration_info.current_step_progress = 0.0
                self._save_migrations()
                
                self._add_log(migration_info, "执行迁移前验证")
                
                # 实现迁移前验证逻辑
                validation_errors = []
                
                # 系统状态检查
                self._add_log(migration_info, "检查系统状态")
                try:
                    # 检查系统负载
                    cpu_usage = psutil.cpu_percent(interval=1)
                    if cpu_usage > 80:  # CPU使用率过高
                        warning = f"CPU使用率较高 ({cpu_usage}%)，可能影响迁移性能"
                        self._add_log(migration_info, warning)
                        migration_info.warnings.append(warning)
                        
                    # 检查内存使用情况
                    memory = psutil.virtual_memory()
                    if memory.percent > 85:  # 内存使用率过高
                        warning = f"内存使用率较高 ({memory.percent}%)，可能影响迁移性能"
                        self._add_log(migration_info, warning)
                        migration_info.warnings.append(warning)
                        
                    # 检查关键进程
                    required_processes = ["postgres", "redis-server"]
                    for process_name in required_processes:
                        found = False
                        for proc in psutil.process_iter(['name']):
                            if process_name in proc.info['name'].lower():
                                found = True
                                break
                        if not found:
                            error = f"关键进程未运行: {process_name}"
                            self._add_log(migration_info, error)
                            validation_errors.append(error)
                except Exception as e:
                    error = f"系统状态检查失败: {e}"
                    self._add_log(migration_info, error)
                    validation_errors.append(error)
                
                # 获取系统版本
                self._add_log(migration_info, "检查系统版本")
                try:
                    system_version = self._get_current_system_version()
                    self._add_log(migration_info, f"当前系统版本: {system_version}")
                    
                    # 保存系统版本信息到源信息中
                    migration_info.source_info["system_version"] = system_version
                    
                    # 检查版本格式
                    import re
                    version_pattern = re.compile(r'^\d+\.\d+\.\d+(-[\w\.]+)?$')
                    if not version_pattern.match(system_version):
                        warning = f"系统版本格式不标准: {system_version}"
                        self._add_log(migration_info, warning)
                        migration_info.warnings.append(warning)
                except Exception as e:
                    error = f"系统版本检查失败: {e}"
                    self._add_log(migration_info, error)
                    validation_errors.append(error)
                
                # 磁盘空间验证
                self._add_log(migration_info, "检查磁盘空间")
                try:
                    # 确保有足够的空间用于备份
                    required_space = 5 * 1024 * 1024 * 1024  # 假设至少需要5GB空间
                    
                    # 检查数据目录所在磁盘的可用空间
                    data_dir_usage = shutil.disk_usage(self.data_dir)
                    if data_dir_usage.free < required_space:
                        error = f"磁盘空间不足。可用: {data_dir_usage.free / (1024*1024*1024):.2f} GB, 建议: 5 GB"
                        self._add_log(migration_info, error)
                        validation_errors.append(error)
                        
                    # 检查临时目录所在磁盘的可用空间
                    temp_dir_usage = shutil.disk_usage(temp_dir)
                    if temp_dir_usage.free < required_space:
                        error = f"临时目录磁盘空间不足。可用: {temp_dir_usage.free / (1024*1024*1024):.2f} GB, 建议: 5 GB"
                        self._add_log(migration_info, error)
                        validation_errors.append(error)
                except Exception as e:
                    error = f"磁盘空间检查失败: {e}"
                    self._add_log(migration_info, error)
                    validation_errors.append(error)
                    
                # 配置一致性检查
                self._add_log(migration_info, "检查配置一致性")
                try:
                    # 检查配置文件是否存在
                    config_paths = [
                        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config", "system.json"),
                        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config", "database.json")
                    ]
                    
                    for config_path in config_paths:
                        if not os.path.exists(config_path):
                            warning = f"配置文件不存在: {config_path}"
                            self._add_log(migration_info, warning)
                            migration_info.warnings.append(warning)
                        else:
                            try:
                                # 尝试解析配置文件，确保格式正确
                                with open(config_path, 'r', encoding='utf-8') as f:
                                    json.load(f)
                            except json.JSONDecodeError:
                                error = f"配置文件格式错误: {config_path}"
                                self._add_log(migration_info, error)
                                validation_errors.append(error)
                except Exception as e:
                    error = f"配置一致性检查失败: {e}"
                    self._add_log(migration_info, error)
                    validation_errors.append(error)
                    
                # 检查数据库连接
                self._add_log(migration_info, "检查数据库连接")
                try:
                    # 获取数据库配置
                    db_config = self._get_database_config()
                    if db_config:
                        try:
                            # 生成连接字符串
                            conn_str = self._get_postgres_connection_string(db_config)
                            
                            # 尝试连接数据库
                            import psycopg2
                            conn = psycopg2.connect(conn_str)
                            
                            # 检查数据库版本
                            cursor = conn.cursor()
                            cursor.execute("SELECT version();")
                            db_version = cursor.fetchone()[0]
                            self._add_log(migration_info, f"数据库版本: {db_version}")
                            
                            # 保存数据库版本信息到源信息中
                            migration_info.source_info["database_version"] = db_version
                            
                            cursor.close()
                            conn.close()
                        except Exception as e:
                            error = f"数据库连接失败: {e}"
                            self._add_log(migration_info, error)
                            validation_errors.append(error)
                    else:
                        warning = "未找到数据库配置"
                        self._add_log(migration_info, warning)
                        migration_info.warnings.append(warning)
                except ImportError:
                    warning = "psycopg2模块未安装，跳过数据库连接检查"
                    self._add_log(migration_info, warning)
                    migration_info.warnings.append(warning)
                except Exception as e:
                    error = f"数据库连接检查失败: {e}"
                    self._add_log(migration_info, error)
                    validation_errors.append(error)
                
                # 检查验证结果
                if validation_errors:
                    error_message = f"迁移前验证失败，发现 {len(validation_errors)} 个错误：\n" + "\n".join(validation_errors)
                    self._add_log(migration_info, error_message)
                    migration_info.status = MigrationStatus.FAILED
                    migration_info.errors.extend(validation_errors)
                    self._save_migrations()
                    return
                else:
                    self._add_log(migration_info, "迁移前验证通过")
                
                # 更新进度
                migration_info.progress = 2 / total_steps
                migration_info.current_step_progress = 1.0
                self._save_migrations()
                
                # 检查是否需要停止
                if self._stop_migration:
                    self._add_log(migration_info, "迁移被停止")
                    migration_info.status = MigrationStatus.FAILED
                    self._save_migrations()
                    return
                    
            # 步骤3: 导出
            migration_info.current_step = "导出配置和数据"
            migration_info.status = MigrationStatus.EXPORTING
            migration_info.current_step_progress = 0.0
            self._save_migrations()
            
            self._add_log(migration_info, "导出配置和数据")
            
            # 调用导出方法
            package_path = self._export_package(migration_info, temp_dir)
            if not package_path:
                self._add_log(migration_info, "导出失败")
                migration_info.status = MigrationStatus.FAILED
                self._save_migrations()
                return
                
            migration_info.package_path = package_path
            
            # 计算包大小和校验和
            migration_info.package_size = os.path.getsize(package_path)
            with open(package_path, "rb") as f:
                migration_info.checksum = hashlib.sha256(f.read()).hexdigest()
                
            # 更新进度
            current_step = 2 if migration_info.config.validate_before else 1
            migration_info.progress = (current_step + 1) / total_steps
            migration_info.current_step_progress = 1.0
            self._save_migrations()
            
            # 检查是否需要停止
            if self._stop_migration:
                self._add_log(migration_info, "迁移被停止")
                migration_info.status = MigrationStatus.FAILED
                self._save_migrations()
                return
                
            # 步骤4: 验证导出包（可选）
            next_step = 3 if migration_info.config.validate_before else 2
            if migration_info.config.validate_after:
                migration_info.current_step = "验证迁移包"
                migration_info.status = MigrationStatus.VALIDATING
                migration_info.current_step_progress = 0.0
                self._save_migrations()
                
                self._add_log(migration_info, "验证迁移包")
                
                # 调用已实现的validate_package方法
                valid, warnings = self.validate_package(package_path)
                if not valid:
                    errors = warnings
                    error_message = f"迁移包验证失败，发现 {len(errors)} 个错误：\n" + "\n".join(errors)
                    self._add_log(migration_info, error_message)
                    migration_info.status = MigrationStatus.FAILED
                    migration_info.errors.extend(errors)
                    self._save_migrations()
                    return
                
                # 更新进度
                migration_info.progress = (next_step + 1) / total_steps
                migration_info.current_step_progress = 1.0
                self._save_migrations()
                
                # 检查是否需要停止
                if self._stop_migration:
                    self._add_log(migration_info, "迁移被停止")
                    migration_info.status = MigrationStatus.FAILED
                    self._save_migrations()
                    return
                    
            # 步骤5: 完成
            migration_info.current_step = "完成迁移"
            migration_info.status = MigrationStatus.FINALIZING
            migration_info.current_step_progress = 0.0
            self._save_migrations()
            
            self._add_log(migration_info, "迁移处理完成")
            
            # 移动迁移包到包目录
            package_name = os.path.basename(package_path)
            final_package_path = os.path.join(self.data_dir, "packages", package_name)
            shutil.move(package_path, final_package_path)
            migration_info.package_path = final_package_path
            
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
            
            # 更新状态和进度
            migration_info.status = MigrationStatus.COMPLETED
            migration_info.progress = 1.0
            migration_info.current_step_progress = 1.0
            migration_info.completed_at = datetime.datetime.now()
            self._save_migrations()
            
            self._add_log(migration_info, f"迁移成功完成，迁移包：{final_package_path}")
            
        except Exception as e:
            logger.error(f"迁移处理出错: {e}", exc_info=True)
            
            self._add_log(migration_info, f"迁移处理出错: {e}")
            migration_info.errors.append(str(e))
            migration_info.status = MigrationStatus.FAILED
            self._save_migrations()
            
        finally:
            # 清理
            self._current_migration = None
            
    def _export_package(self, migration_info: MigrationInfo, temp_dir: str) -> Optional[str]:
        """
        导出迁移包
        
        Args:
            migration_info: 迁移信息
            temp_dir: 临时目录
            
        Returns:
            迁移包路径，如果导出失败则返回None
        """
        try:
            # 创建迁移包数据
            package = MigrationPackage(
                created_by="system",
                description=migration_info.description,
                source_system={
                    "version": self._get_current_system_version(),  # 获取实际系统版本
                    "name": "主服务器",
                    "timestamp": datetime.datetime.now().isoformat()
                }
            )
            
            # 导出配置
            if migration_info.config.export_configs:
                self._add_log(migration_info, "导出系统配置")
                package.configs = self._export_configs()
                
            # 导出数据
            if migration_info.config.export_data:
                self._add_log(migration_info, "导出系统数据")
                package.data = self._export_data()
                
            # 导出用户
            if migration_info.config.export_users:
                self._add_log(migration_info, "导出用户数据")
                package.users = self._export_users()
                
            # 导出日志
            if migration_info.config.export_logs:
                self._add_log(migration_info, "导出系统日志")
                package.logs = self._export_logs()
                
            # 计算包校验和
            package_dict = {
                "version": package.version,
                "created_at": package.created_at,
                "created_by": package.created_by,
                "source_system": package.source_system,
                "description": package.description,
                "configs": package.configs,
                "data": package.data,
                "users": package.users,
                "logs": package.logs
            }
            package_json = json.dumps(package_dict, ensure_ascii=False)
            package.checksum = hashlib.sha256(package_json.encode()).hexdigest()
            package_dict["checksum"] = package.checksum
            
            # 将包保存到临时文件
            package_file = os.path.join(temp_dir, "package.json")
            with open(package_file, "w", encoding="utf-8") as f:
                json.dump(package_dict, f, ensure_ascii=False, indent=2)
                
            # 创建压缩包
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            package_name = f"migration_package_{timestamp}.tar.gz"
            package_path = os.path.join(temp_dir, package_name)
            
            with tarfile.open(package_path, "w:gz") as tar:
                tar.add(package_file, arcname="package.json")
                
                # 添加其他必要文件
                # 1. 添加配置文件
                if migration_info.config.export_configs:
                    config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                         "config")
                    if os.path.exists(config_dir):
                        config_temp_dir = os.path.join(temp_dir, "configs")
                        os.makedirs(config_temp_dir, exist_ok=True)
                        
                        # 复制主要配置文件（排除敏感信息）
                        safe_config_files = ["app_config.json", "logging.json", "network.json", "service.json"]
                        for file_name in safe_config_files:
                            src_path = os.path.join(config_dir, file_name)
                            if os.path.exists(src_path):
                                dst_path = os.path.join(config_temp_dir, file_name)
                                shutil.copy2(src_path, dst_path)
                                self._add_log(migration_info, f"添加配置文件: {file_name}")
                        
                        # 将临时配置目录添加到压缩包
                        for root, dirs, files in os.walk(config_temp_dir):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arcname = os.path.relpath(file_path, temp_dir)
                                tar.add(file_path, arcname=arcname)
                
                # 2. 添加系统信息文件
                system_info_file = os.path.join(temp_dir, "system_info.json")
                system_info = {
                    "version": self._get_current_system_version(),
                    "export_time": datetime.datetime.now().isoformat(),
                    "python_version": platform.python_version(),
                    "platform": platform.platform(),
                    "architecture": platform.architecture(),
                    "hostname": platform.node(),
                    "migration_manager_version": "1.0.0"
                }
                
                with open(system_info_file, "w", encoding="utf-8") as f:
                    json.dump(system_info, f, ensure_ascii=False, indent=2)
                tar.add(system_info_file, arcname="system_info.json")
                self._add_log(migration_info, "添加系统信息文件")
                
                # 3. 添加依赖列表文件
                try:
                    requirements_file = os.path.join(temp_dir, "requirements.txt")
                    requirements_content = []
                    
                    # 尝试从项目根目录获取requirements.txt
                    project_requirements_paths = [
                        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 
                                   "requirements.txt"),
                        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                   "requirements.txt")
                    ]
                    
                    requirements_found = False
                    for req_path in project_requirements_paths:
                        if os.path.exists(req_path):
                            with open(req_path, "r", encoding="utf-8") as f:
                                requirements_content.extend(f.readlines())
                            requirements_found = True
                            break
                    
                    # 如果没有找到requirements.txt，生成基本的依赖列表
                    if not requirements_found:
                        try:
                            installed_packages = [d for d in pkg_resources.working_set]
                            for package in installed_packages:
                                if any(keyword in package.project_name.lower() 
                                      for keyword in ['fastapi', 'uvicorn', 'sqlalchemy', 'redis', 'celery']):
                                    requirements_content.append(f"{package.project_name}=={package.version}\n")
                        except ImportError:
                            pass
                    
                    # 添加基本的系统依赖
                    if not requirements_content:
                        requirements_content = [
                            "fastapi>=0.68.0\n",
                            "uvicorn>=0.15.0\n", 
                            "sqlalchemy>=1.4.0\n",
                            "psycopg2-binary>=2.9.0\n",
                            "redis>=3.5.0\n",
                            "celery>=5.2.0\n"
                        ]
                    
                    with open(requirements_file, "w", encoding="utf-8") as f:
                        f.writelines(requirements_content)
                    tar.add(requirements_file, arcname="requirements.txt")
                    self._add_log(migration_info, "添加依赖文件")
                    
                except Exception as e:
                    self._add_log(migration_info, f"添加依赖文件失败: {e}")
                
                # 4. 添加数据库架构文件
                if migration_info.config.export_data:
                    try:
                        schema_file = os.path.join(temp_dir, "database_schema.sql")
                        db_config = self._get_database_config()
                        
                        if db_config:
                            import psycopg2
                            conn_str = self._get_postgres_connection_string(db_config)
                            conn = psycopg2.connect(conn_str)
                            cursor = conn.cursor()
                            
                            # 获取数据库架构
                            cursor.execute("""
                                SELECT 
                                    schemaname,
                                    tablename,
                                    pg_get_tabledef(schemaname||'.'||tablename) as table_def
                                FROM pg_tables 
                                WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
                                ORDER BY schemaname, tablename;
                            """)
                            
                            schema_content = ["-- Database Schema Export\n"]
                            schema_content.append(f"-- Generated at: {datetime.datetime.now().isoformat()}\n\n")
                            
                            tables = cursor.fetchall()
                            for schema_name, table_name, table_def in tables:
                                schema_content.append(f"-- Table: {schema_name}.{table_name}\n")
                                if table_def:
                                    schema_content.append(f"{table_def};\n\n")
                            
                            cursor.close()
                            conn.close()
                            
                            with open(schema_file, "w", encoding="utf-8") as f:
                                f.writelines(schema_content)
                            tar.add(schema_file, arcname="database_schema.sql")
                            self._add_log(migration_info, "添加数据库架构文件")
                            
                    except Exception as e:
                        self._add_log(migration_info, f"添加数据库架构文件失败: {e}")
                
                # 5. 添加README文件
                readme_file = os.path.join(temp_dir, "README.md")
                readme_content = f"""# OmniLink迁移包

## 包信息
- 创建时间: {package.created_at}
- 创建者: {package.created_by}
- 源系统版本: {package.source_system.get('version', 'Unknown')}
- 描述: {package.description}

## 包内容
- package.json: 主迁移数据文件
- system_info.json: 系统信息
- requirements.txt: Python依赖列表
{'- configs/: 配置文件目录' if migration_info.config.export_configs else ''}
{'- database_schema.sql: 数据库架构' if migration_info.config.export_data else ''}

## 导入说明
1. 确保目标系统满足requirements.txt中的依赖要求
2. 使用迁移管理器的import_package方法导入此包
3. 检查system_info.json确认兼容性

## 注意事项
- 此迁移包包含敏感配置信息，请妥善保管
- 导入前请备份目标系统数据
- 确保目标系统版本兼容性

生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
                
                with open(readme_file, "w", encoding="utf-8") as f:
                    f.write(readme_content)
                tar.add(readme_file, arcname="README.md")
                self._add_log(migration_info, "添加README文件")
                
                # 6. 添加日志文件样本（如果导出日志）
                if migration_info.config.export_logs and package.logs:
                    logs_sample_file = os.path.join(temp_dir, "logs_sample.json")
                    # 导出最近100条日志作为样本
                    sample_logs = package.logs[-100:] if len(package.logs) > 100 else package.logs
                    
                    with open(logs_sample_file, "w", encoding="utf-8") as f:
                        json.dump(sample_logs, f, ensure_ascii=False, indent=2)
                    tar.add(logs_sample_file, arcname="logs_sample.json")
                    self._add_log(migration_info, "添加日志样本文件")
                
                self._add_log(migration_info, f"迁移包创建完成: {package_name}")
            
            return package_path
            
        except Exception as e:
            logger.error(f"导出迁移包失败: {e}", exc_info=True)
            self._add_log(migration_info, f"导出迁移包失败: {e}")
            return None
            
    def _export_configs(self) -> Dict[str, Any]:
        """
        导出系统配置
        
        Returns:
            配置数据
        """
        configs = {
            "timestamp": datetime.datetime.now().isoformat(),
            "system": {
                "name": "主服务器",
                "version": self._get_current_system_version()
            },
            "settings": {},
            "network": {},
            "security": {},
            "services": {}
        }
        
        try:
            # 1. 获取系统配置
            config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config")
            if os.path.exists(config_dir):
                # 系统设置
                app_config_path = os.path.join(config_dir, "app_config.json")
                if os.path.exists(app_config_path):
                    with open(app_config_path, "r", encoding="utf-8") as f:
                        app_config = json.load(f)
                        # 移除敏感信息
                        if "secret_key" in app_config:
                            app_config["secret_key"] = "[REDACTED]"
                        if "database" in app_config and "password" in app_config["database"]:
                            app_config["database"]["password"] = "[REDACTED]"
                        configs["settings"] = app_config
                
                # 网络配置
                network_config_path = os.path.join(config_dir, "network.json")
                if os.path.exists(network_config_path):
                    with open(network_config_path, "r", encoding="utf-8") as f:
                        network_config = json.load(f)
                        configs["network"] = network_config
                
                # 安全配置
                security_config_path = os.path.join(config_dir, "security.json")
                if os.path.exists(security_config_path):
                    with open(security_config_path, "r", encoding="utf-8") as f:
                        security_config = json.load(f)
                        # 移除密钥和证书内容
                        if "certificates" in security_config:
                            for cert in security_config["certificates"]:
                                if "private_key" in cert:
                                    cert["private_key"] = "[REDACTED]"
                                if "certificate" in cert:
                                    cert["certificate"] = "[REDACTED PATH]"
                        configs["security"] = security_config
                
                # 服务配置
                service_config_path = os.path.join(config_dir, "service.json")
                if os.path.exists(service_config_path):
                    with open(service_config_path, "r", encoding="utf-8") as f:
                        service_config = json.load(f)
                        configs["services"] = service_config
            
            # 2. 获取环境变量（仅导出非敏感环境变量）
            safe_env_prefixes = ["KY_", "APP_", "SERVICE_", "LOG_"]
            sensitive_keys = ["PASSWORD", "SECRET", "KEY", "TOKEN", "CREDENTIAL"]
            env_vars = {}
            
            for key, value in os.environ.items():
                # 仅导出指定前缀的环境变量，且排除敏感信息
                if any(key.startswith(prefix) for prefix in safe_env_prefixes):
                    if any(sensitive in key.upper() for sensitive in sensitive_keys):
                        env_vars[key] = "[REDACTED]"
                    else:
                        env_vars[key] = value
            
            configs["environment"] = env_vars
            
            # 3. 获取系统信息
            import platform
            system_info = {
                "platform": platform.platform(),
                "python": platform.python_version(),
                "architecture": platform.architecture()[0],
                "hostname": platform.node()
            }
            configs["system_info"] = system_info
            
            logger.info("系统配置导出完成")
            return configs
            
        except Exception as e:
            logger.error(f"导出系统配置时出错: {e}", exc_info=True)
            return {
                "timestamp": datetime.datetime.now().isoformat(),
                "system": {
                    "name": "主服务器",
                    "version": self._get_current_system_version(),
                    "error": str(e)
                }
            }
        
    def _export_data(self) -> Dict[str, Any]:
        """
        导出系统数据
        
        Returns:
            系统数据
        """
        data = {
            "timestamp": datetime.datetime.now().isoformat(),
            "tables": {},
            "files": {},
            "statistics": {}
        }
        
        try:
            # 1. 获取数据库信息和配置
            db_config = self._get_database_config()
            if not db_config:
                logger.error("无法获取数据库配置")
                data["error"] = "无法获取数据库配置"
                return data
            
            # 数据库类型支持
            db_type = db_config.get("type", "").lower()
            
            # 2. 根据数据库类型导出表结构和数据统计
            if db_type == "postgresql":
                # 连接PostgreSQL数据库
                conn_str = self._get_postgres_connection_string(db_config)
                
                try:
                    import psycopg2
                    conn = psycopg2.connect(conn_str)
                    cursor = conn.cursor()
                    
                    # 获取所有表
                    cursor.execute("""
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = 'public'
                    """)
                    tables = cursor.fetchall()
                    
                    # 获取每个表的统计信息
                    data["tables"] = {}
                    for table in tables:
                        table_name = table[0]
                        # 获取表结构
                        cursor.execute(f"""
                            SELECT column_name, data_type, character_maximum_length 
                            FROM information_schema.columns 
                            WHERE table_name = '{table_name}'
                        """)
                        columns = cursor.fetchall()
                        
                        # 获取行数
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM \"{table_name}\"")
                            count = cursor.fetchone()[0]
                        except:
                            count = -1  # 表示无法获取
                            
                        # 统计表大小
                        cursor.execute(f"""
                            SELECT pg_size_pretty(pg_total_relation_size('{table_name}')),
                                  pg_total_relation_size('{table_name}')
                        """)
                        size_info = cursor.fetchone()
                        readable_size = size_info[0]
                        size_bytes = size_info[1]
                        
                        # 构建表信息
                        data["tables"][table_name] = {
                            "columns": [{"name": col[0], "type": col[1], "max_length": col[2]} for col in columns],
                            "row_count": count,
                            "size": {
                                "formatted": readable_size,
                                "bytes": size_bytes
                            }
                        }
                    
                    # 获取数据库总体统计
                    cursor.execute("""
                        SELECT pg_size_pretty(pg_database_size(current_database())),
                              pg_database_size(current_database())
                    """)
                    db_size_info = cursor.fetchone()
                    data["statistics"]["database"] = {
                        "size": {
                            "formatted": db_size_info[0],
                            "bytes": db_size_info[1]
                        },
                        "tables_count": len(tables)
                    }
                    
                    cursor.close()
                    conn.close()
                    
                except Exception as e:
                    logger.error(f"导出PostgreSQL数据时出错: {e}", exc_info=True)
                    data["error"] = f"导出PostgreSQL数据时出错: {str(e)}"
            
            elif db_type == "sqlite":
                # 处理SQLite数据库
                db_path = db_config.get("path", "")
                if not os.path.exists(db_path):
                    logger.error(f"SQLite数据库文件不存在: {db_path}")
                    data["error"] = f"SQLite数据库文件不存在: {db_path}"
                    return data
                    
                try:
                    import sqlite3
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    # 获取所有表
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()
                    
                    # 获取每个表的信息
                    data["tables"] = {}
                    for table in tables:
                        table_name = table[0]
                        if table_name.startswith("sqlite_"):
                            continue  # 跳过内部表
                            
                        # 获取表结构
                        cursor.execute(f"PRAGMA table_info({table_name})")
                        columns_info = cursor.fetchall()
                        
                        # 获取行数
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM '{table_name}'")
                            count = cursor.fetchone()[0]
                        except:
                            count = -1  # 表示无法获取
                            
                        # 构建表信息
                        data["tables"][table_name] = {
                            "columns": [{"name": col[1], "type": col[2]} for col in columns_info],
                            "row_count": count
                        }
                    
                    # 获取数据库文件大小
                    db_size_bytes = os.path.getsize(db_path)
                    db_size_formatted = self._format_size(db_size_bytes)
                    
                    data["statistics"]["database"] = {
                        "size": {
                            "formatted": db_size_formatted,
                            "bytes": db_size_bytes
                        },
                        "tables_count": len([t for t in tables if not t[0].startswith("sqlite_")])
                    }
                    
                    cursor.close()
                    conn.close()
                    
                except Exception as e:
                    logger.error(f"导出SQLite数据时出错: {e}", exc_info=True)
                    data["error"] = f"导出SQLite数据时出错: {str(e)}"
            
            # 3. 收集关键的文件结构和大小
            data_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            for dir_name in ["data", "logs", "uploads", "exports"]:
                dir_path = os.path.join(data_dir, dir_name)
                if os.path.exists(dir_path) and os.path.isdir(dir_path):
                    data["files"][dir_name] = self._collect_directory_stats(dir_path)
            
            return data
            
        except Exception as e:
            logger.error(f"导出系统数据时出错: {e}", exc_info=True)
            return {
                "timestamp": datetime.datetime.now().isoformat(),
                "error": str(e)
            }
            
    def _collect_directory_stats(self, directory: str) -> Dict[str, Any]:
        """
        收集目录统计信息
        
        Args:
            directory: 目录路径
            
        Returns:
            目录统计信息
        """
        stats = {
            "path": directory,
            "total_size": 0,
            "file_count": 0,
            "dir_count": 0,
            "largest_files": []
        }
        
        try:
            if not os.path.exists(directory):
                return stats
                
            # 统计文件数量和总大小
            largest_files = []
            for root, dirs, files in os.walk(directory):
                stats["dir_count"] += len(dirs)
                stats["file_count"] += len(files)
                
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        file_size = os.path.getsize(file_path)
                        stats["total_size"] += file_size
                        
                        # 记录最大的文件
                        largest_files.append((file_path, file_size))
                        largest_files.sort(key=lambda x: x[1], reverse=True)
                        if len(largest_files) > 10:
                            largest_files.pop()
                    except:
                        pass
                        
            # 格式化最大的文件
            stats["largest_files"] = [
                {
                    "path": os.path.relpath(path, directory),
                    "size_bytes": size,
                    "size_formatted": self._format_size(size)
                }
                for path, size in largest_files
            ]
            
            # 格式化总大小
            stats["total_size_formatted"] = self._format_size(stats["total_size"])
            
            return stats
            
        except Exception as e:
            logger.error(f"收集目录统计信息时出错: {e}", exc_info=True)
            return {"error": str(e)}
            
    def _get_database_config(self) -> Optional[dict]:
        """
        获取数据库配置信息
        
        返回:
            Optional[dict]: 数据库配置信息，如果配置不存在则返回None
        """
        try:
            # 首先检查配置文件路径
            config_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                     "config", "database.json")
            
            # 检查文件是否存在
            if not os.path.exists(config_file):
                # 尝试从环境变量获取配置
                config_path_env = os.environ.get('KY_CONFIG_PATH')
                if config_path_env:
                    config_file = os.path.join(config_path_env, "database.json")
                
            # 如果还是找不到配置文件，尝试在E:\key\config\keys\路径下查找
            if not os.path.exists(config_file):
                key_config_file = os.path.join(r"E:\key\config\keys", "sqlpsd.json")
                if os.path.exists(key_config_file):
                    with open(key_config_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
            
            # 如果找到了配置文件，读取配置
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
                    
            logger.warning("未找到数据库配置文件")
            return None
            
        except Exception as e:
            logger.error(f"获取数据库配置时出错: {e}")
            return None
        
    def _get_postgres_connection_string(self, db_config: dict) -> str:
        """
        生成PostgreSQL连接字符串
        
        参数:
            db_config: 数据库配置信息，包含host, port, dbname, user, password
            
        返回:
            str: PostgreSQL连接字符串
        """
        host = db_config.get("host", "localhost")
        port = db_config.get("port", 5432)
        dbname = db_config.get("dbname", "postgres")
        user = db_config.get("user", "postgres")
        password = db_config.get("password", "")
        
        # 构建连接字符串
        conn_str = f"host={host} port={port} dbname={dbname} user={user}"
        
        # 如果有密码，添加密码
        if password:
            conn_str += f" password={password}"
            
        return conn_str
        
    def _export_users(self) -> Dict[str, Any]:
        """
        导出用户数据
        
        Returns:
            用户数据
        """
        users_data = {
            "timestamp": datetime.datetime.now().isoformat(),
            "users": [],
            "roles": [],
            "permissions": [],
            "groups": [],
            "statistics": {
                "total_users": 0,
                "total_roles": 0,
                "total_permissions": 0,
                "total_groups": 0,
                "active_users": 0,
                "inactive_users": 0
            }
        }
        
        try:
            # 获取数据库配置
            db_config = self._get_database_config()
            if not db_config:
                logger.error("无法获取数据库配置")
                users_data["error"] = "无法获取数据库配置"
                return users_data
                
            # 根据数据库类型获取用户数据
            db_type = db_config.get("type", "").lower()
            
            if db_type == "postgresql":
                # 连接PostgreSQL数据库
                conn_str = self._get_postgres_connection_string(db_config)
                
                try:
                    import psycopg2
                    conn = psycopg2.connect(conn_str)
                    cursor = conn.cursor()
                    
                    # 1. 获取用户列表 (不包括敏感信息如密码)
                    try:
                        cursor.execute("""
                            SELECT id, username, email, first_name, last_name, 
                                  is_active, is_superuser, created_at, last_login,
                                  department, organization
                            FROM users
                        """)
                        user_rows = cursor.fetchall()
                        
                        for row in user_rows:
                            user_data = {
                                "id": row[0],
                                "username": row[1],
                                "email": row[2],
                                "first_name": row[3],
                                "last_name": row[4],
                                "is_active": row[5],
                                "is_superuser": row[6],
                                "created_at": row[7].isoformat() if row[7] else None,
                                "last_login": row[8].isoformat() if row[8] else None,
                                "department": row[9],
                                "organization": row[10]
                            }
                            users_data["users"].append(user_data)
                            
                            # 更新统计信息
                            users_data["statistics"]["total_users"] += 1
                            if user_data["is_active"]:
                                users_data["statistics"]["active_users"] += 1
                            else:
                                users_data["statistics"]["inactive_users"] += 1
                    except Exception as e:
                        logger.warning(f"获取用户列表时出错: {e}")
                        users_data["errors"] = users_data.get("errors", []) + [f"获取用户列表时出错: {str(e)}"]
                    
                    # 2. 获取角色列表
                    try:
                        cursor.execute("""
                            SELECT id, name, description, is_system_role, created_at
                            FROM roles
                        """)
                        role_rows = cursor.fetchall()
                        
                        for row in role_rows:
                            role_data = {
                                "id": row[0],
                                "name": row[1],
                                "description": row[2],
                                "is_system_role": row[3],
                                "created_at": row[4].isoformat() if row[4] else None
                            }
                            users_data["roles"].append(role_data)
                            users_data["statistics"]["total_roles"] += 1
                    except Exception as e:
                        logger.warning(f"获取角色列表时出错: {e}")
                        users_data["errors"] = users_data.get("errors", []) + [f"获取角色列表时出错: {str(e)}"]
                    
                    # 3. 获取权限列表
                    try:
                        cursor.execute("""
                            SELECT id, name, description, resource_type, action, is_system_permission
                            FROM permissions
                        """)
                        permission_rows = cursor.fetchall()
                        
                        for row in permission_rows:
                            permission_data = {
                                "id": row[0],
                                "name": row[1],
                                "description": row[2],
                                "resource_type": row[3],
                                "action": row[4],
                                "is_system_permission": row[5]
                            }
                            users_data["permissions"].append(permission_data)
                            users_data["statistics"]["total_permissions"] += 1
                    except Exception as e:
                        logger.warning(f"获取权限列表时出错: {e}")
                        users_data["errors"] = users_data.get("errors", []) + [f"获取权限列表时出错: {str(e)}"]
                    
                    # 4. 获取用户组列表
                    try:
                        cursor.execute("""
                            SELECT id, name, description, created_at
                            FROM groups
                        """)
                        group_rows = cursor.fetchall()
                        
                        for row in group_rows:
                            group_data = {
                                "id": row[0],
                                "name": row[1],
                                "description": row[2],
                                "created_at": row[3].isoformat() if row[3] else None
                            }
                            users_data["groups"].append(group_data)
                            users_data["statistics"]["total_groups"] += 1
                    except Exception as e:
                        logger.warning(f"获取用户组列表时出错: {e}")
                        users_data["errors"] = users_data.get("errors", []) + [f"获取用户组列表时出错: {str(e)}"]
                    
                    # 5. 获取用户-角色关系
                    try:
                        cursor.execute("""
                            SELECT user_id, role_id FROM user_roles
                        """)
                        user_role_rows = cursor.fetchall()
                        
                        user_roles = {}
                        for row in user_role_rows:
                            user_id = row[0]
                            role_id = row[1]
                            if user_id not in user_roles:
                                user_roles[user_id] = []
                            user_roles[user_id].append(role_id)
                        
                        # 添加到用户数据中
                        for user in users_data["users"]:
                            user_id = user["id"]
                            if user_id in user_roles:
                                user["roles"] = user_roles[user_id]
                    except Exception as e:
                        logger.warning(f"获取用户-角色关系时出错: {e}")
                        users_data["errors"] = users_data.get("errors", []) + [f"获取用户-角色关系时出错: {str(e)}"]
                    
                    # 6. 获取角色-权限关系
                    try:
                        cursor.execute("""
                            SELECT role_id, permission_id FROM role_permissions
                        """)
                        role_permission_rows = cursor.fetchall()
                        
                        role_permissions = {}
                        for row in role_permission_rows:
                            role_id = row[0]
                            permission_id = row[1]
                            if role_id not in role_permissions:
                                role_permissions[role_id] = []
                            role_permissions[role_id].append(permission_id)
                        
                        # 添加到角色数据中
                        for role in users_data["roles"]:
                            role_id = role["id"]
                            if role_id in role_permissions:
                                role["permissions"] = role_permissions[role_id]
                    except Exception as e:
                        logger.warning(f"获取角色-权限关系时出错: {e}")
                        users_data["errors"] = users_data.get("errors", []) + [f"获取角色-权限关系时出错: {str(e)}"]
                    
                    cursor.close()
                    conn.close()
                    
                except Exception as e:
                    logger.error(f"导出PostgreSQL用户数据时出错: {e}", exc_info=True)
                    users_data["error"] = f"导出PostgreSQL用户数据时出错: {str(e)}"
            
            elif db_type == "sqlite":
                # 处理SQLite数据库
                db_path = db_config.get("path", "")
                if not os.path.exists(db_path):
                    logger.error(f"SQLite数据库文件不存在: {db_path}")
                    users_data["error"] = f"SQLite数据库文件不存在: {db_path}"
                    return users_data
                    
                try:
                    import sqlite3
                    conn = sqlite3.connect(db_path)
                    conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
                    cursor = conn.cursor()
                    
                    # 1. 获取用户列表
                    try:
                        cursor.execute("""
                            SELECT id, username, email, first_name, last_name, 
                                  is_active, is_superuser, created_at, last_login,
                                  department, organization
                            FROM users
                        """)
                        user_rows = cursor.fetchall()
                        
                        for row in user_rows:
                            user_data = {
                                "id": row["id"],
                                "username": row["username"],
                                "email": row["email"],
                                "first_name": row["first_name"],
                                "last_name": row["last_name"],
                                "is_active": bool(row["is_active"]),
                                "is_superuser": bool(row["is_superuser"]),
                                "created_at": row["created_at"],
                                "last_login": row["last_login"],
                                "department": row["department"],
                                "organization": row["organization"]
                            }
                            users_data["users"].append(user_data)
                            
                            # 更新统计信息
                            users_data["statistics"]["total_users"] += 1
                            if user_data["is_active"]:
                                users_data["statistics"]["active_users"] += 1
                            else:
                                users_data["statistics"]["inactive_users"] += 1
                    except Exception as e:
                        logger.warning(f"获取用户列表时出错: {e}")
                        users_data["errors"] = users_data.get("errors", []) + [f"获取用户列表时出错: {str(e)}"]
                    
                    # 类似地处理角色、权限、用户组和关系
                    # (代码结构与PostgreSQL部分类似，只是使用SQLite的API)
                    
                    cursor.close()
                    conn.close()
                    
                except Exception as e:
                    logger.error(f"导出SQLite用户数据时出错: {e}", exc_info=True)
                    users_data["error"] = f"导出SQLite用户数据时出错: {str(e)}"
            
            return users_data
            
        except Exception as e:
            logger.error(f"导出用户数据时出错: {e}", exc_info=True)
            return {
                "timestamp": datetime.datetime.now().isoformat(),
                "error": str(e),
                "users": [],
                "roles": [],
                "permissions": []
            }
        
    def _export_logs(self) -> List[Dict[str, Any]]:
        """
        导出系统日志
        
        Returns:
            日志数据
        """
        logs_data = []
        
        try:
            # 获取数据库配置
            db_config = self._get_database_config()
            if not db_config:
                logger.error("无法获取数据库配置")
                return [{
                    "error": "无法获取数据库配置",
                    "timestamp": datetime.datetime.now().isoformat()
                }]
            
            # 根据数据库类型导出日志
            db_type = db_config.get("type", "").lower()
            
            # 定义日志类型和限制
            log_types = ["system", "security", "audit", "error", "access"]
            log_limit = 10000  # 每类日志最多导出条数
            days_limit = 30    # 最多导出多少天的日志
            
            # 计算日期限制
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_limit)
            cutoff_date_str = cutoff_date.isoformat()
            
            if db_type == "postgresql":
                # 连接PostgreSQL数据库
                conn_str = self._get_postgres_connection_string(db_config)
                
                try:
                    import psycopg2
                    conn = psycopg2.connect(conn_str)
                    cursor = conn.cursor()
                    
                    # 获取系统日志表
                    log_tables = {
                        "system": "system_logs",
                        "security": "security_logs",
                        "audit": "audit_logs",
                        "error": "error_logs",
                        "access": "access_logs"
                    }
                    
                    # 确保这些表存在
                    cursor.execute("""
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = 'public'
                    """)
                    existing_tables = [row[0] for row in cursor.fetchall()]
                    
                    for log_type, table_name in log_tables.items():
                        if table_name in existing_tables:
                            try:
                                # 查询最近的日志，按时间戳降序排序
                                cursor.execute(f"""
                                    SELECT id, timestamp, level, message, source, details
                                    FROM {table_name}
                                    WHERE timestamp >= %s
                                    ORDER BY timestamp DESC
                                    LIMIT %s
                                """, (cutoff_date_str, log_limit))
                                
                                log_entries = []
                                for row in cursor.fetchall():
                                    log_entry = {
                                        "id": row[0],
                                        "timestamp": row[1].isoformat() if isinstance(row[1], datetime.datetime) else row[1],
                                        "level": row[2],
                                        "message": row[3],
                                        "source": row[4],
                                        "type": log_type
                                    }
                                    
                                    # 尝试解析details为JSON，如果是字符串
                                    if row[5]:
                                        if isinstance(row[5], str):
                                            try:
                                                log_entry["details"] = json.loads(row[5])
                                            except:
                                                log_entry["details"] = row[5]
                                        else:
                                            log_entry["details"] = row[5]
                                    
                                    log_entries.append(log_entry)
                                
                                # 添加到总日志集合
                                log_summary = {
                                    "type": log_type,
                                    "count": len(log_entries),
                                    "oldest": log_entries[-1]["timestamp"] if log_entries else None,
                                    "newest": log_entries[0]["timestamp"] if log_entries else None,
                                    "entries": log_entries
                                }
                                logs_data.append(log_summary)
                                
                                logger.info(f"导出了 {len(log_entries)} 条 {log_type} 日志")
                                
                            except Exception as e:
                                logger.error(f"导出 {log_type} 日志出错: {e}", exc_info=True)
                                logs_data.append({
                                    "type": log_type,
                                    "error": str(e),
                                    "timestamp": datetime.datetime.now().isoformat()
                                })
                    
                    cursor.close()
                    conn.close()
                    
                except Exception as e:
                    logger.error(f"导出PostgreSQL日志时出错: {e}", exc_info=True)
                    return [{
                        "error": f"导出PostgreSQL日志时出错: {str(e)}",
                        "timestamp": datetime.datetime.now().isoformat()
                    }]
            
            elif db_type == "sqlite":
                # 处理SQLite数据库
                db_path = db_config.get("path", "")
                if not os.path.exists(db_path):
                    logger.error(f"SQLite数据库文件不存在: {db_path}")
                    return [{
                        "error": f"SQLite数据库文件不存在: {db_path}",
                        "timestamp": datetime.datetime.now().isoformat()
                    }]
                    
                try:
                    import sqlite3
                    conn = sqlite3.connect(db_path)
                    conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
                    cursor = conn.cursor()
                    
                    # 获取系统日志表
                    log_tables = {
                        "system": "system_logs",
                        "security": "security_logs",
                        "audit": "audit_logs",
                        "error": "error_logs",
                        "access": "access_logs"
                    }
                    
                    # 获取数据库中的所有表
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    existing_tables = [row[0] for row in cursor.fetchall()]
                    
                    for log_type, table_name in log_tables.items():
                        if table_name in existing_tables:
                            try:
                                # 查询最近的日志，按时间戳降序排序
                                cursor.execute(f"""
                                    SELECT id, timestamp, level, message, source, details
                                    FROM {table_name}
                                    WHERE timestamp >= ?
                                    ORDER BY timestamp DESC
                                    LIMIT ?
                                """, (cutoff_date_str, log_limit))
                                
                                log_entries = []
                                for row in cursor.fetchall():
                                    log_entry = {
                                        "id": row["id"],
                                        "timestamp": row["timestamp"],
                                        "level": row["level"],
                                        "message": row["message"],
                                        "source": row["source"],
                                        "type": log_type
                                    }
                                    
                                    # 尝试解析details为JSON，如果是字符串
                                    if row["details"]:
                                        try:
                                            log_entry["details"] = json.loads(row["details"])
                                        except:
                                            log_entry["details"] = row["details"]
                                    
                                    log_entries.append(log_entry)
                                
                                # 添加到总日志集合
                                log_summary = {
                                    "type": log_type,
                                    "count": len(log_entries),
                                    "oldest": log_entries[-1]["timestamp"] if log_entries else None,
                                    "newest": log_entries[0]["timestamp"] if log_entries else None,
                                    "entries": log_entries
                                }
                                logs_data.append(log_summary)
                                
                                logger.info(f"导出了 {len(log_entries)} 条 {log_type} 日志")
                                
                            except Exception as e:
                                logger.error(f"导出 {log_type} 日志出错: {e}", exc_info=True)
                                logs_data.append({
                                    "type": log_type,
                                    "error": str(e),
                                    "timestamp": datetime.datetime.now().isoformat()
                                })
                    
                    cursor.close()
                    conn.close()
                    
                except Exception as e:
                    logger.error(f"导出SQLite日志时出错: {e}", exc_info=True)
                    return [{
                        "error": f"导出SQLite日志时出错: {str(e)}",
                        "timestamp": datetime.datetime.now().isoformat()
                    }]
            
            else:
                # 不支持的数据库类型
                logger.warning(f"不支持的数据库类型: {db_type}")
                return [{
                    "error": f"不支持的数据库类型: {db_type}",
                    "timestamp": datetime.datetime.now().isoformat()
                }]
            
            # 添加文件日志信息
            try:
                # 查找系统日志文件
                log_files = []
                common_log_paths = [
                    "/var/log/syslog",
                    "/var/log/messages",
                    "/var/log/auth.log",
                    "/var/log/secure",
                    "/var/log/kern.log",
                    "C:\\Windows\\System32\\winevt\\Logs\\System.evtx",
                    "C:\\Windows\\System32\\winevt\\Logs\\Application.evtx",
                    "C:\\Windows\\System32\\winevt\\Logs\\Security.evtx",
                    "./logs/app.log",
                    "./logs/error.log",
                    "./logs/access.log"
                ]
                
                for log_path in common_log_paths:
                    if os.path.exists(log_path):
                        try:
                            stat_info = os.stat(log_path)
                            log_file_info = {
                                "path": log_path,
                                "size": stat_info.st_size,
                                "modified": datetime.datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                                "created": datetime.datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
                                "readable": os.access(log_path, os.R_OK)
                            }
                            log_files.append(log_file_info)
                        except Exception as e:
                            logger.warning(f"无法读取日志文件 {log_path}: {e}")
                
                if log_files:
                    logs_data.append({
                        "type": "file_logs",
                        "count": len(log_files),
                        "files": log_files,
                        "note": "文件系统日志信息"
                    })
                    
            except Exception as e:
                logger.error(f"收集文件日志信息时出错: {e}", exc_info=True)
                logs_data.append({
                    "type": "file_logs",
                    "error": str(e),
                    "timestamp": datetime.datetime.now().isoformat()
                })
                
            # 添加应用程序日志信息
            try:
                app_log_dirs = [
                    "./logs",
                    "../logs",
                    "/opt/omnilink/logs",
                    "C:\\OmniLink\\logs"
                ]
                
                app_logs_info = []
                for log_dir in app_log_dirs:
                    if os.path.exists(log_dir):
                        try:
                            for file_name in os.listdir(log_dir):
                                file_path = os.path.join(log_dir, file_name)
                                if os.path.isfile(file_path) and file_name.endswith(('.log', '.txt')):
                                    stat_info = os.stat(file_path)
                                    app_log_info = {
                                        "name": file_name,
                                        "path": file_path,
                                        "size": stat_info.st_size,
                                        "modified": datetime.datetime.fromtimestamp(stat_info.st_mtime).isoformat()
                                    }
                                    app_logs_info.append(app_log_info)
                        except Exception as e:
                            logger.warning(f"无法读取应用日志目录 {log_dir}: {e}")
                
                if app_logs_info:
                    logs_data.append({
                        "type": "application_logs",
                        "count": len(app_logs_info),
                        "files": app_logs_info,
                        "note": "应用程序日志文件"
                    })
                    
            except Exception as e:
                logger.error(f"收集应用程序日志信息时出错: {e}", exc_info=True)
            
            # 如果没有找到任何日志，返回基本信息
            if not logs_data:
                logs_data = [{
                    "type": "system_info",
                    "message": "未找到任何日志数据或日志表",
                    "timestamp": datetime.datetime.now().isoformat(),
                    "note": "这可能是因为日志表尚未创建或日志功能未启用"
                }]
            
            logger.info(f"日志导出完成，总共导出 {len(logs_data)} 类日志数据")
            
        except Exception as e:
            logger.error(f"导出日志时发生错误: {e}", exc_info=True)
            return [{
                "error": f"导出日志时发生错误: {str(e)}",
                "timestamp": datetime.datetime.now().isoformat()
            }]
        
        return logs_data
        
    def _add_log(self, migration_info: MigrationInfo, message: str):
        """
        添加迁移日志
        
        Args:
            migration_info: 迁移信息
            message: 日志消息
        """
        log_entry = f"[{datetime.datetime.now().isoformat()}] {message}"
        logger.info(f"迁移 {migration_info.id}: {message}")
        migration_info.logs.append(log_entry)
        self._save_migrations()
        
    def import_package(self, migration_id: str, package_path: str) -> bool:
        """
        导入迁移包
        
        Args:
            migration_id: 迁移ID
            package_path: 迁移包路径
            
        Returns:
            是否成功导入
        """
        logger.info(f"开始导入迁移包: {package_path}")
        
        # 获取迁移信息
        migration_info = self.get_migration(migration_id)
        if not migration_info:
            logger.error(f"未找到迁移ID: {migration_id}")
            return False
            
        # 更新迁移状态
        migration_info.status = MigrationStatus.IMPORTING
        migration_info.current_step = "准备导入"
        migration_info.progress = 0.0
        migration_info.current_step_progress = 0.0
        self._save_migrations()
        
        try:
            # 检查迁移包是否存在
            if not os.path.exists(package_path):
                self._add_log(migration_info, f"迁移包不存在: {package_path}")
                migration_info.errors.append(f"迁移包不存在: {package_path}")
                migration_info.status = MigrationStatus.FAILED
                self._save_migrations()
                return False
                
            # 1. 验证迁移包
            migration_info.current_step = "验证迁移包"
            self._save_migrations()
            
            is_valid, errors = self.validate_package(package_path)
            if not is_valid:
                error_msg = "迁移包验证失败: " + "; ".join(errors)
                self._add_log(migration_info, error_msg)
                migration_info.errors.append(error_msg)
                migration_info.status = MigrationStatus.FAILED
                self._save_migrations()
                return False
                
            self._add_log(migration_info, "迁移包验证成功")
            migration_info.progress = 0.2
            migration_info.current_step_progress = 1.0
            self._save_migrations()
            
            # 2. 创建临时目录解压迁移包
            migration_info.current_step = "解压迁移包"
            migration_info.current_step_progress = 0.0
            self._save_migrations()
            
            # 创建临时目录
            import tempfile
            temp_dir = tempfile.mkdtemp(prefix="migration_")
            
            try:
                # 检查文件类型并解压
                if package_path.endswith(".zip"):
                    with zipfile.ZipFile(package_path, "r") as zip_ref:
                        zip_ref.extractall(temp_dir)
                        
                elif package_path.endswith((".tar", ".tar.gz", ".tgz")):
                    with tarfile.open(package_path, "r:*") as tar_ref:
                        tar_ref.extractall(temp_dir)
                        
                elif package_path.endswith(".json"):
                    # 单个JSON文件，复制到临时目录
                    import shutil
                    shutil.copy(package_path, os.path.join(temp_dir, "package.json"))
                    
                else:
                    self._add_log(migration_info, f"不支持的迁移包格式: {os.path.basename(package_path)}")
                    migration_info.errors.append(f"不支持的迁移包格式: {os.path.basename(package_path)}")
                    migration_info.status = MigrationStatus.FAILED
                    self._save_migrations()
                    return False
                    
                self._add_log(migration_info, "迁移包解压成功")
                migration_info.progress = 0.3
                migration_info.current_step_progress = 1.0
                self._save_migrations()
                
                # 3. 加载迁移包数据
                migration_info.current_step = "加载迁移包数据"
                migration_info.current_step_progress = 0.0
                self._save_migrations()
                
                # 检查包JSON文件
                package_file = os.path.join(temp_dir, "package.json")
                if not os.path.exists(package_file):
                    self._add_log(migration_info, "迁移包缺少package.json文件")
                    migration_info.errors.append("迁移包缺少package.json文件")
                    migration_info.status = MigrationStatus.FAILED
                    self._save_migrations()
                    return False
                    
                # 加载包数据
                with open(package_file, "r", encoding="utf-8") as f:
                    package_data = json.load(f)
                    
                # 验证基本结构
                required_keys = ["version", "created_at", "source_system"]
                for key in required_keys:
                    if key not in package_data:
                        self._add_log(migration_info, f"迁移包缺少必要字段: {key}")
                        migration_info.errors.append(f"迁移包缺少必要字段: {key}")
                        migration_info.status = MigrationStatus.FAILED
                        self._save_migrations()
                        return False
                        
                # 版本兼容性检查
                current_version = self._get_current_system_version()
                package_version = package_data["source_system"]["version"]
                
                if not self._is_system_version_compatible(current_version, package_version):
                    self._add_log(migration_info, f"迁移包版本不兼容: 当前系统版本 {current_version}, 迁移包版本 {package_version}")
                    migration_info.errors.append(f"迁移包版本不兼容: 当前系统版本 {current_version}, 迁移包版本 {package_version}")
                    
                    # 如果未启用强制导入，则失败
                    if not migration_info.config.force_import:
                        migration_info.status = MigrationStatus.FAILED
                        self._save_migrations()
                        return False
                    else:
                        self._add_log(migration_info, "版本不兼容但强制继续导入")
                
                self._add_log(migration_info, "迁移包数据加载成功")
                migration_info.progress = 0.4
                migration_info.current_step_progress = 1.0
                self._save_migrations()
                
                # 4. 导入前备份(如果配置了备份)
                if migration_info.config.backup_before_import:
                    migration_info.current_step = "导入前备份"
                    migration_info.current_step_progress = 0.0
                    self._save_migrations()
                    
                    # 调用快照管理器创建备份
                    try:
                        from common.migration.snapshot_manager import SnapshotManager
                        snapshot_mgr = SnapshotManager()
                        backup_name = f"pre_migration_{migration_id}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        backup_desc = f"迁移 {migration_id} 前的自动备份"
                        
                        snapshot_id = snapshot_mgr.create_snapshot(backup_name, backup_desc)
                        if snapshot_id:
                            if snapshot_mgr.start_snapshot(snapshot_id):
                                self._add_log(migration_info, f"已创建迁移前备份，快照ID: {snapshot_id}")
                                # 等待快照完成
                                import time
                                max_wait_time = 300  # 最多等待5分钟
                                start_time = time.time()
                                while True:
                                    snapshot_info = snapshot_mgr.get_snapshot(snapshot_id)
                                    if not snapshot_info:
                                        break
                                    
                                    if snapshot_info.status == "completed":
                                        self._add_log(migration_info, "迁移前备份完成")
                                        break
                                    elif snapshot_info.status == "failed":
                                        self._add_log(migration_info, "迁移前备份失败，但将继续导入")
                                        break
                                    
                                    if time.time() - start_time > max_wait_time:
                                        self._add_log(migration_info, "迁移前备份超时，但将继续导入")
                                        break
                                        
                                    time.sleep(2)
                            else:
                                self._add_log(migration_info, "迁移前备份创建失败，但将继续导入")
                        else:
                            self._add_log(migration_info, "迁移前备份创建失败，但将继续导入")
                    except Exception as e:
                        self._add_log(migration_info, f"迁移前备份出错: {e}，但将继续导入")
                    
                    migration_info.progress = 0.5
                    migration_info.current_step_progress = 1.0
                    self._save_migrations()
                
                # 5. 执行导入操作
                migration_info.current_step = "执行导入"
                migration_info.current_step_progress = 0.0
                self._save_migrations()
                
                # 导入配置文件(如果存在)
                configs_dir = os.path.join(temp_dir, "configs")
                if os.path.exists(configs_dir) and migration_info.config.import_configs:
                    self._add_log(migration_info, "导入配置文件")
                    self._import_configs(migration_info, configs_dir)
                
                # 导入系统数据(如果存在)
                data_dir = os.path.join(temp_dir, "data")
                if os.path.exists(data_dir) and migration_info.config.import_data:
                    self._add_log(migration_info, "导入系统数据")
                    self._import_data(migration_info, data_dir, package_data)
                
                # 导入用户数据(如果存在)
                if "users" in package_data and migration_info.config.import_users:
                    self._add_log(migration_info, "导入用户数据")
                    self._import_users(migration_info, package_data["users"])
                
                migration_info.progress = 0.9
                migration_info.current_step_progress = 1.0
                self._save_migrations()
                
                # 6. 完成导入
                migration_info.current_step = "完成导入"
                migration_info.current_step_progress = 0.0
                self._save_migrations()
                
                # 记录导入信息
                migration_info.completed_at = datetime.datetime.now()
                migration_info.status = MigrationStatus.COMPLETED
                migration_info.progress = 1.0
                migration_info.current_step_progress = 1.0
                self._add_log(migration_info, f"迁移包导入完成: {package_path}")
                self._save_migrations()
                
                return True
                
            finally:
                # 清理临时目录
                try:
                    import shutil
                    shutil.rmtree(temp_dir)
                except:
                    logger.warning(f"无法清理临时目录: {temp_dir}")
                    
        except Exception as e:
            logger.error(f"导入迁移包时出错: {e}", exc_info=True)
            self._add_log(migration_info, f"导入迁移包时出错: {e}")
            migration_info.errors.append(str(e))
            migration_info.status = MigrationStatus.FAILED
            self._save_migrations()
            return False
            
    def _import_configs(self, migration_info, configs_dir):
        """
        导入配置文件
        
        Args:
            migration_info: 迁移信息
            configs_dir: 配置目录
        """
        try:
            # 确定系统配置目录
            system_config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config")
            if not os.path.exists(system_config_dir):
                os.makedirs(system_config_dir, exist_ok=True)
                
            # 配置文件白名单(仅导入这些文件)
            config_whitelist = [
                "app_config.json",
                "network.json",
                "service.json",
                "logging.json",
                "monitoring.json",
                "discovery.json"
            ]
            
            # 复制配置文件
            for root, _, files in os.walk(configs_dir):
                for file in files:
                    if file in config_whitelist:
                        src_path = os.path.join(root, file)
                        # 确定目标路径，保持相对路径结构
                        rel_path = os.path.relpath(os.path.join(root, file), configs_dir)
                        dest_path = os.path.join(system_config_dir, rel_path)
                        
                        # 确保目标目录存在
                        os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                        
                        # 如果是敏感配置文件，做特殊处理
                        if file == "app_config.json":
                            # 保留原有的敏感信息（如数据库密码）
                            if os.path.exists(dest_path):
                                with open(dest_path, "r", encoding="utf-8") as f:
                                    current_config = json.load(f)
                                
                                with open(src_path, "r", encoding="utf-8") as f:
                                    new_config = json.load(f)
                                
                                # 保留敏感信息
                                if "database" in current_config and "database" in new_config:
                                    if "password" in current_config["database"]:
                                        new_config["database"]["password"] = current_config["database"]["password"]
                                    
                                if "secret_key" in current_config:
                                    new_config["secret_key"] = current_config["secret_key"]
                                
                                # 保存合并后的配置
                                with open(dest_path, "w", encoding="utf-8") as f:
                                    json.dump(new_config, f, ensure_ascii=False, indent=2)
                                    
                                self._add_log(migration_info, f"合并并导入配置文件: {file}")
                            else:
                                # 如果目标文件不存在，直接复制，但处理敏感字段
                                with open(src_path, "r", encoding="utf-8") as f:
                                    config = json.load(f)
                                
                                # 处理敏感字段
                                if "database" in config and "password" in config["database"] and config["database"]["password"] == "[REDACTED]":
                                    # 使用默认密码
                                    config["database"]["password"] = "password"
                                
                                if "secret_key" in config and config["secret_key"] == "[REDACTED]":
                                    # 生成新密钥
                                    import secrets
                                    config["secret_key"] = secrets.token_hex(32)
                                
                                # 保存处理后的配置
                                with open(dest_path, "w", encoding="utf-8") as f:
                                    json.dump(config, f, ensure_ascii=False, indent=2)
                                    
                                self._add_log(migration_info, f"导入配置文件(处理了敏感字段): {file}")
                        else:
                            # 非敏感配置文件直接复制
                            import shutil
                            shutil.copy2(src_path, dest_path)
                            self._add_log(migration_info, f"导入配置文件: {file}")
            
        except Exception as e:
            self._add_log(migration_info, f"导入配置文件时出错: {e}")
            logger.error(f"导入配置文件时出错: {e}", exc_info=True)
            
    def _import_data(self, migration_info, data_dir, package_data):
        """
        导入系统数据
        
        Args:
            migration_info: 迁移信息
            data_dir: 数据目录
            package_data: 包数据
        """
        try:
            if "data" not in package_data:
                self._add_log(migration_info, "包中没有数据部分，跳过数据导入")
                return
                
            # 导入数据的实现取决于具体的应用需求
            # 这里只实现了数据文件的复制
            
            # 数据文件目录
            system_data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "data")
            if not os.path.exists(system_data_dir):
                os.makedirs(system_data_dir, exist_ok=True)
                
            if os.path.exists(data_dir):
                # 仅复制部分数据文件，排除数据库文件
                for root, _, files in os.walk(data_dir):
                    for file in files:
                        if file.endswith((".json", ".xml", ".csv")):
                            src_path = os.path.join(root, file)
                            # 保持相对路径结构
                            rel_path = os.path.relpath(os.path.join(root, file), data_dir)
                            dest_path = os.path.join(system_data_dir, rel_path)
                            
                            # 确保目标目录存在
                            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                            
                            # 复制文件
                            import shutil
                            shutil.copy2(src_path, dest_path)
                            self._add_log(migration_info, f"导入数据文件: {rel_path}")
                            
            # 注意：不导入数据库文件，因为这通常需要专门的过程
            # 数据库导入应该根据具体应用和数据库类型实现
            
        except Exception as e:
            self._add_log(migration_info, f"导入系统数据时出错: {e}")
            logger.error(f"导入系统数据时出错: {e}", exc_info=True)
            
    def _import_users(self, migration_info, users_data):
        """
        导入用户数据
        
        Args:
            migration_info: 迁移信息
            users_data: 用户数据
        """
        try:
            if not users_data:
                self._add_log(migration_info, "没有用户数据，跳过用户导入")
                return
                
            # 导入用户数据需要访问数据库
            # 这里只提供基本框架，实际操作取决于具体应用和数据库设计
            
            # 获取数据库配置
            db_config = self._get_database_config()
            if not db_config:
                self._add_log(migration_info, "无法获取数据库配置，跳过用户导入")
                return
                
            db_type = db_config.get("type", "").lower()
            
            # 根据数据库类型执行用户导入
            if db_type == "postgresql":
                # PostgreSQL导入
                self._add_log(migration_info, "注意: 仅导入非敏感的用户信息(不包括密码)")
                self._add_log(migration_info, "完整的用户数据导入需要管理员手动操作")
                
            elif db_type == "sqlite":
                # SQLite导入
                self._add_log(migration_info, "注意: 仅导入非敏感的用户信息(不包括密码)")
                self._add_log(migration_info, "完整的用户数据导入需要管理员手动操作")
                
            else:
                self._add_log(migration_info, f"不支持的数据库类型: {db_type}，跳过用户导入")
                
        except Exception as e:
            self._add_log(migration_info, f"导入用户数据时出错: {e}")
            logger.error(f"导入用户数据时出错: {e}", exc_info=True)
        
    def validate_package(self, package_path: str) -> Tuple[bool, List[str]]:
        """
        验证迁移包
        
        参数:
            package_path: 迁移包路径
            
        返回:
            Tuple[bool, List[str]]: (是否有效, 警告/错误列表)
        """
        warnings = []
        
        if not os.path.exists(package_path):
            return False, ["迁移包文件不存在"]
            
        if not os.path.isfile(package_path):
            return False, ["指定的路径不是文件"]
            
        # 检查文件扩展名
        if not package_path.endswith('.zip'):
            return False, ["迁移包必须是zip格式"]
            
        # 检查文件大小
        file_size = os.path.getsize(package_path)
        if file_size == 0:
            return False, ["迁移包文件大小为0"]
            
        # 记录文件大小
        size_mb = file_size / (1024 * 1024)
        if size_mb > 1000:  # 大于1GB
            warnings.append(f"迁移包文件较大 ({size_mb:.2f} MB)，可能需要较长时间处理")
            
        # 验证zip文件完整性
        try:
            with zipfile.ZipFile(package_path, 'r') as zip_file:
                # 测试压缩包完整性
                test_result = zip_file.testzip()
                if test_result is not None:
                    return False, [f"压缩包损坏，第一个损坏文件: {test_result}"]
                    
                # 检查必要文件是否存在
                required_files = ['manifest.json']
                for required_file in required_files:
                    if required_file not in zip_file.namelist():
                        return False, [f"缺少必要文件: {required_file}"]
                        
                # 读取并验证manifest文件
                try:
                    manifest_data = json.loads(zip_file.read('manifest.json').decode('utf-8'))
                    
                    # 检查必要字段
                    required_fields = ['version', 'created_at', 'source_system']
                    for field in required_fields:
                        if field not in manifest_data:
                            return False, [f"manifest缺少必要字段: {field}"]
                            
                    # 检查版本兼容性
                    package_version = manifest_data.get('version', '0.0.0')
                    current_version = self._get_current_system_version()
                    
                    if not self._is_version_compatible(package_version):
                        warnings.append(f"包版本 ({package_version}) 可能与当前系统版本 ({current_version}) 不兼容")
                        
                    # 检查创建时间
                    try:
                        created_at = datetime.datetime.fromisoformat(manifest_data.get('created_at', ''))
                        now = datetime.datetime.now()
                        if created_at > now:
                            warnings.append(f"包创建时间 ({created_at.isoformat()}) 在未来")
                    except (ValueError, TypeError):
                        warnings.append("包创建时间格式无效")
                        
                    # 检查源系统信息
                    source_system = manifest_data.get('source_system', {})
                    if not source_system:
                        warnings.append("包缺少源系统信息")
                    
                    # 检查数据一致性
                    if 'checksum' in manifest_data:
                        manifest_checksum = manifest_data['checksum']
                        # 计算实际校验和（排除manifest中的checksum字段）
                        manifest_copy = manifest_data.copy()
                        manifest_copy.pop('checksum', None)
                        calculated_checksum = hashlib.sha256(json.dumps(manifest_copy, sort_keys=True).encode()).hexdigest()
                        
                        if manifest_checksum != calculated_checksum:
                            warnings.append("manifest校验和不匹配，数据可能已被修改")
                            
                except json.JSONDecodeError:
                    return False, ["manifest.json不是有效的JSON格式"]
                except Exception as e:
                    return False, [f"验证manifest.json时出错: {str(e)}"]
                    
                # 检查数据文件
                if 'data.json' in zip_file.namelist():
                    try:
                        data = json.loads(zip_file.read('data.json').decode('utf-8'))
                        # 检查数据结构
                        if not isinstance(data, dict):
                            warnings.append("data.json格式不正确，应为JSON对象")
                    except json.JSONDecodeError:
                        return False, ["data.json不是有效的JSON格式"]
                    except Exception as e:
                        warnings.append(f"读取data.json时出错: {str(e)}")
                        
                # 检查配置文件
                if 'configs.json' in zip_file.namelist():
                    try:
                        configs = json.loads(zip_file.read('configs.json').decode('utf-8'))
                        # 检查配置结构
                        if not isinstance(configs, dict):
                            warnings.append("configs.json格式不正确，应为JSON对象")
                    except json.JSONDecodeError:
                        return False, ["configs.json不是有效的JSON格式"]
                    except Exception as e:
                        warnings.append(f"读取configs.json时出错: {str(e)}")
                        
                # 检查用户数据
                if 'users.json' in zip_file.namelist():
                    try:
                        users = json.loads(zip_file.read('users.json').decode('utf-8'))
                        # 检查用户数据结构
                        if not isinstance(users, dict):
                            warnings.append("users.json格式不正确，应为JSON对象")
                    except json.JSONDecodeError:
                        return False, ["users.json不是有效的JSON格式"]
                    except Exception as e:
                        warnings.append(f"读取users.json时出错: {str(e)}")
                        
                # 检查日志数据
                if 'logs.json' in zip_file.namelist():
                    try:
                        logs = json.loads(zip_file.read('logs.json').decode('utf-8'))
                        # 检查日志数据结构
                        if not isinstance(logs, list):
                            warnings.append("logs.json格式不正确，应为JSON数组")
                    except json.JSONDecodeError:
                        return False, ["logs.json不是有效的JSON格式"]
                    except Exception as e:
                        warnings.append(f"读取logs.json时出错: {str(e)}")
                        
        except zipfile.BadZipFile:
            return False, ["文件不是有效的zip格式"]
        except Exception as e:
            return False, [f"验证迁移包时出错: {str(e)}"]
            
        # 如果没有严重错误，但有警告，仍然返回有效
        return True, warnings
        
    def _is_version_compatible(self, version: str) -> bool:
        """
        检查版本是否兼容
        
        参数:
            version: 要检查的版本号
            
        返回:
            bool: 版本是否兼容
        """
        # 获取当前系统版本
        current_version = self._get_current_system_version()
        
        # 尝试解析版本号
        try:
            # 首先处理可能的版本后缀（如"-beta.1"）
            current_main_version = current_version.split('-')[0]
            target_main_version = version.split('-')[0]
            
            # 解析版本号为数字列表
            current_parts = [int(part) for part in current_main_version.split('.')]
            target_parts = [int(part) for part in target_main_version.split('.')]
            
            # 确保版本号部分至少有3个元素（主版本、次版本、补丁版本）
            while len(current_parts) < 3:
                current_parts.append(0)
            while len(target_parts) < 3:
                target_parts.append(0)
                
            # 检查主版本号
            if current_parts[0] != target_parts[0]:
                logger.warning(f"主版本号不匹配: 当前 {current_parts[0]}, 目标 {target_parts[0]}")
                return False
                
            # 检查次版本号
            if current_parts[1] < target_parts[1]:
                logger.warning(f"当前系统版本 ({current_main_version}) 低于迁移包版本 ({target_main_version})")
                return False
                
            # 如果次版本号相同，检查补丁版本
            if current_parts[1] == target_parts[1] and current_parts[2] < target_parts[2]:
                logger.warning(f"当前补丁版本 ({current_parts[2]}) 低于迁移包补丁版本 ({target_parts[2]})")
                # 补丁版本不匹配可以视为兼容，但需要记录警告
                
            return True
            
        except (ValueError, IndexError) as e:
            logger.error(f"版本号格式错误: {e}")
            # 版本号格式错误时，谨慎起见返回不兼容
            return False
        
    def _calculate_package_checksum(self, package_path: str, checksum_type: str = "sha256") -> str:
        """
        计算迁移包的校验和
        
        Args:
            package_path: 迁移包路径
            checksum_type: 校验和类型，默认为sha256
            
        Returns:
            校验和字符串
        """
        hash_algorithm = hashlib.sha256()
        
        with open(package_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_algorithm.update(chunk)
                
        return hash_algorithm.hexdigest()
        
    def _get_current_system_version(self) -> str:
        """
        获取实际系统版本
        
        Returns:
            系统版本字符串
        """
        try:
            # 方法1：从项目根目录的VERSION文件读取
            version_file_paths = [
                os.path.join(self.data_dir, "..", "..", "..", "VERSION"),
                os.path.join(self.data_dir, "..", "VERSION"),
                os.path.join(self.data_dir, "VERSION"),
                os.path.join(os.path.dirname(__file__), "..", "..", "..", "VERSION"),
                os.path.join(os.path.dirname(__file__), "..", "..", "VERSION")
            ]
            
            for version_file in version_file_paths:
                try:
                    if os.path.exists(version_file):
                        with open(version_file, 'r', encoding='utf-8') as f:
                            version = f.read().strip()
                            if version:
                                logger.info(f"从VERSION文件获取系统版本: {version}")
                                return version
                except Exception as e:
                    logger.debug(f"读取VERSION文件失败 {version_file}: {e}")
                    continue
            
            # 方法2：从项目配置文件读取
            try:
                config_paths = [
                    os.path.join(self.data_dir, "..", "config", "system.json"),
                    os.path.join(self.data_dir, "config", "system.json"),
                    os.path.join(os.path.dirname(__file__), "..", "..", "config", "system.json")
                ]
                
                for config_path in config_paths:
                    if os.path.exists(config_path):
                        import json
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                            if 'version' in config:
                                version = config['version']
                                logger.info(f"从配置文件获取系统版本: {version}")
                                return version
            except Exception as e:
                logger.debug(f"从配置文件读取版本失败: {e}")
            
            # 方法3：从Python包信息读取
            try:
                import pkg_resources
                try:
                    version = pkg_resources.get_distribution("omnilink").version
                    logger.info(f"从包信息获取系统版本: {version}")
                    return version
                except pkg_resources.DistributionNotFound:
                    logger.debug("未找到omnilink包信息")
            except ImportError:
                logger.debug("pkg_resources模块不可用")
            
            # 方法4：从Git信息读取
            try:
                import subprocess
                
                # 获取Git根目录
                git_root_paths = [
                    os.path.join(self.data_dir, "..", "..", ".."),
                    os.path.join(self.data_dir, "..", ".."),
                    os.path.join(os.path.dirname(__file__), "..", "..", "..")
                ]
                
                for git_root in git_root_paths:
                    if os.path.exists(os.path.join(git_root, ".git")):
                        try:
                            # 获取最新标签作为版本
                            result = subprocess.run(
                                ["git", "describe", "--tags", "--abbrev=0"],
                                cwd=git_root,
                                capture_output=True,
                                text=True,
                                timeout=10
                            )
                            if result.returncode == 0:
                                version = result.stdout.strip()
                                if version:
                                    logger.info(f"从Git标签获取系统版本: {version}")
                                    return version
                        except (subprocess.TimeoutExpired, subprocess.SubprocessError, FileNotFoundError) as e:
                            logger.debug(f"Git命令执行失败: {e}")
                            
                        try:
                            # 获取提交哈希作为版本
                            result = subprocess.run(
                                ["git", "rev-parse", "--short", "HEAD"],
                                cwd=git_root,
                                capture_output=True,
                                text=True,
                                timeout=10
                            )
                            if result.returncode == 0:
                                commit_hash = result.stdout.strip()
                                if commit_hash:
                                    version = f"git-{commit_hash}"
                                    logger.info(f"从Git提交获取系统版本: {version}")
                                    return version
                        except (subprocess.TimeoutExpired, subprocess.SubprocessError, FileNotFoundError) as e:
                            logger.debug(f"Git命令执行失败: {e}")
                        break
            except Exception as e:
                logger.debug(f"Git版本获取失败: {e}")
            
            # 方法5：从模块元数据读取
            try:
                # 尝试从当前模块路径推断版本
                current_dir = os.path.dirname(__file__)
                
                # 检查是否在开发环境中
                if "ky" in current_dir:
                    # 生成基于时间戳的开发版本
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%Y%m%d%H%M")
                    version = f"dev-{timestamp}"
                    logger.info(f"生成开发版本号: {version}")
                    return version
            except Exception as e:
                logger.debug(f"模块版本推断失败: {e}")
            
            # 默认版本
            default_version = "1.0.0-unknown"
            logger.warning(f"无法获取系统版本，使用默认版本: {default_version}")
            return default_version
            
        except Exception as e:
            logger.error(f"获取系统版本时出错: {e}", exc_info=True)
            return "1.0.0-error"
        
    def _is_system_version_compatible(self, current_version: str, package_version: str) -> bool:
        """
        检查系统版本是否兼容
        
        Args:
            current_version: 当前系统版本
            package_version: 迁移包的系统版本
            
        Returns:
            是否兼容
        """
        # 简单的版本比较，可以根据实际需求进行扩展
        # 例如，可以检查主版本号是否相同，或者当前版本是否高于迁移包版本
        
        # 将版本号分解为各个部分
        current_parts = [int(x) for x in current_version.split('.')]
        package_parts = [int(x) for x in package_version.split('.')]
        
        # 确保列表长度相同以便比较
        while len(current_parts) < len(package_parts):
            current_parts.append(0)
        while len(package_parts) < len(current_parts):
            package_parts.append(0)
            
        # 检查当前版本是否大于或等于迁移包版本
        # 通常只要主版本号相同，就认为是兼容的
        return current_parts[0] == package_parts[0] 
