#!/usr/bin/env python3
"""
Windows环境Docker部署脚本
在Windows Docker Desktop环境下构建和启动OmniLink主从服务器
"""

import subprocess
import time
import json
import logging
import sys
import os
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('windows_docker_deploy.log')
    ]
)
logger = logging.getLogger(__name__)

class WindowsDockerDeployer:
    """Windows Docker部署器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.deployment_results = []
        
    def run_command(self, command: str, timeout: int = 300, shell: bool = True) -> dict:
        """运行命令并返回结果"""
        try:
            logger.info(f"执行命令: {command}")
            
            # 在Windows环境下使用PowerShell
            if shell and not command.startswith("powershell"):
                command = f'powershell -Command "{command}"'
            
            result = subprocess.run(
                command,
                shell=shell,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.project_root
            )
            
            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout.strip(),
                "stderr": result.stderr.strip(),
                "command": command
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "命令执行超时",
                "command": command
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": command
            }
    
    def check_docker_environment(self) -> bool:
        """检查Docker环境"""
        logger.info("🔍 检查Docker Desktop环境...")
        
        # 检查Docker Desktop是否运行
        docker_check = self.run_command("docker version", timeout=30)
        
        if docker_check["success"]:
            logger.info(f"✅ Docker Desktop运行正常")
            logger.info(f"Docker版本信息:\n{docker_check['stdout']}")
            
            # 检查Docker Compose
            compose_check = self.run_command("docker-compose --version", timeout=30)
            if compose_check["success"]:
                logger.info(f"✅ Docker Compose可用: {compose_check['stdout']}")
                self.deployment_results.append({
                    "step": "Docker环境检查",
                    "status": "SUCCESS",
                    "message": "Docker Desktop和Compose都可用"
                })
                return True
            else:
                logger.error("❌ Docker Compose不可用")
                self.deployment_results.append({
                    "step": "Docker环境检查",
                    "status": "FAILED",
                    "message": "Docker Compose不可用"
                })
                return False
        else:
            logger.error("❌ Docker Desktop不可用或未启动")
            logger.error(f"错误信息: {docker_check.get('stderr', '')}")
            self.deployment_results.append({
                "step": "Docker环境检查",
                "status": "FAILED",
                "message": "Docker Desktop不可用"
            })
            return False
    
    def validate_configuration(self) -> bool:
        """验证配置文件"""
        logger.info("🔍 验证配置文件...")
        
        required_files = [
            "docker-compose.yaml",
            "app.env",
            "deployment/dockerfiles/Dockerfile.main",
            "deployment/dockerfiles/Dockerfile.slave"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            logger.error(f"❌ 缺失配置文件: {', '.join(missing_files)}")
            self.deployment_results.append({
                "step": "配置验证",
                "status": "FAILED",
                "message": f"缺失文件: {', '.join(missing_files)}"
            })
            return False
        
        # 验证Docker Compose语法
        compose_check = self.run_command("docker-compose config", timeout=60)
        if compose_check["success"]:
            logger.info("✅ Docker Compose配置语法正确")
            self.deployment_results.append({
                "step": "配置验证",
                "status": "SUCCESS",
                "message": "所有配置文件有效"
            })
            return True
        else:
            logger.error(f"❌ Docker Compose配置错误: {compose_check.get('stderr', '')}")
            self.deployment_results.append({
                "step": "配置验证",
                "status": "FAILED",
                "message": f"Compose配置错误: {compose_check.get('stderr', '')}"
            })
            return False
    
    def build_images(self) -> bool:
        """构建Docker镜像"""
        logger.info("🔨 构建Docker镜像...")
        
        # 清理旧容器和镜像
        logger.info("清理旧容器...")
        self.run_command("docker-compose down --remove-orphans", timeout=120)
        
        # 构建所有镜像
        logger.info("构建所有服务镜像...")
        build_result = self.run_command("docker-compose build --no-cache", timeout=900)
        
        if build_result["success"]:
            logger.info("✅ Docker镜像构建成功")
            
            # 列出构建的镜像
            images_result = self.run_command("docker images | findstr omnilink", timeout=30)
            if images_result["success"]:
                logger.info(f"构建的镜像:\n{images_result['stdout']}")
            
            self.deployment_results.append({
                "step": "镜像构建",
                "status": "SUCCESS",
                "message": "所有镜像构建成功"
            })
            return True
        else:
            logger.error(f"❌ Docker镜像构建失败: {build_result.get('stderr', '')}")
            self.deployment_results.append({
                "step": "镜像构建",
                "status": "FAILED",
                "message": f"构建失败: {build_result.get('stderr', '')}"
            })
            return False
    
    def start_services(self) -> bool:
        """启动服务"""
        logger.info("🚀 启动Docker服务...")
        
        # 分阶段启动服务
        logger.info("启动数据库和Redis...")
        db_result = self.run_command("docker-compose up -d postgres-db redis", timeout=180)
        
        if not db_result["success"]:
            logger.error(f"❌ 数据库服务启动失败: {db_result.get('stderr', '')}")
            self.deployment_results.append({
                "step": "服务启动",
                "status": "FAILED",
                "message": "数据库服务启动失败"
            })
            return False
        
        # 等待数据库启动
        logger.info("等待数据库服务就绪...")
        time.sleep(30)
        
        # 启动主服务器
        logger.info("启动主服务器...")
        main_result = self.run_command("docker-compose up -d main-server", timeout=180)
        
        if not main_result["success"]:
            logger.error(f"❌ 主服务器启动失败: {main_result.get('stderr', '')}")
            self.deployment_results.append({
                "step": "服务启动",
                "status": "FAILED",
                "message": "主服务器启动失败"
            })
            return False
        
        # 等待主服务器启动
        time.sleep(20)
        
        # 启动从服务器
        logger.info("启动从服务器...")
        slave_result = self.run_command("docker-compose up -d slave-server", timeout=180)
        
        if slave_result["success"]:
            logger.info("✅ 所有服务启动成功")
            self.deployment_results.append({
                "step": "服务启动",
                "status": "SUCCESS",
                "message": "所有服务启动成功"
            })
            return True
        else:
            logger.error(f"❌ 从服务器启动失败: {slave_result.get('stderr', '')}")
            self.deployment_results.append({
                "step": "服务启动",
                "status": "FAILED",
                "message": "从服务器启动失败"
            })
            return False
    
    def verify_services(self) -> bool:
        """验证服务状态"""
        logger.info("🔍 验证服务状态...")
        
        # 检查容器状态
        ps_result = self.run_command("docker-compose ps", timeout=30)
        if ps_result["success"]:
            logger.info(f"容器状态:\n{ps_result['stdout']}")
        
        # 检查主服务器健康
        logger.info("检查主服务器健康状态...")
        main_health = self.run_command(
            "curl -f http://localhost:8000/health",
            timeout=30
        )
        
        main_status = "SUCCESS" if main_health["success"] else "FAILED"
        
        # 检查从服务器健康
        logger.info("检查从服务器健康状态...")
        slave_health = self.run_command(
            'curl -f http://localhost:8001/health -H "Authorization: Bearer dev-api-key-12345"',
            timeout=30
        )
        
        slave_status = "SUCCESS" if slave_health["success"] else "FAILED"
        
        # 检查API文档可访问性
        logger.info("检查API文档...")
        docs_check = self.run_command("curl -f http://localhost:8000/docs", timeout=30)
        docs_status = "SUCCESS" if docs_check["success"] else "FAILED"
        
        overall_success = all([
            main_health["success"],
            slave_health["success"],
            docs_check["success"]
        ])
        
        if overall_success:
            logger.info("✅ 所有服务验证通过")
            self.deployment_results.append({
                "step": "服务验证",
                "status": "SUCCESS",
                "message": f"主服务器: {main_status}, 从服务器: {slave_status}, API文档: {docs_status}"
            })
        else:
            logger.error("❌ 服务验证失败")
            self.deployment_results.append({
                "step": "服务验证",
                "status": "FAILED",
                "message": f"主服务器: {main_status}, 从服务器: {slave_status}, API文档: {docs_status}"
            })
        
        return overall_success
    
    def test_communication(self) -> bool:
        """测试主从服务器通信"""
        logger.info("🔗 测试主从服务器通信...")
        
        # 测试从服务器设备端点
        devices_test = self.run_command(
            'curl -f http://localhost:8001/devices -H "Authorization: Bearer dev-api-key-12345"',
            timeout=30
        )
        
        if devices_test["success"]:
            logger.info("✅ 从服务器设备端点可访问")
            logger.info(f"设备列表响应: {devices_test['stdout']}")
            
            # 测试主服务器从服务器列表
            slaves_test = self.run_command(
                'curl -f http://localhost:8000/api/v1/slaves -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxLCJleHAiOjE3MzUwNzI4MDB9.dummy"',
                timeout=30
            )
            
            communication_success = devices_test["success"]
            
            self.deployment_results.append({
                "step": "通信测试",
                "status": "SUCCESS" if communication_success else "FAILED",
                "message": "主从服务器通信正常" if communication_success else "通信测试失败"
            })
            
            return communication_success
        else:
            logger.error(f"❌ 从服务器设备端点不可访问: {devices_test.get('stderr', '')}")
            self.deployment_results.append({
                "step": "通信测试",
                "status": "FAILED",
                "message": "从服务器端点不可访问"
            })
            return False
    
    def run_full_deployment(self):
        """运行完整部署流程"""
        logger.info("🚀 开始Windows Docker部署和验证...")
        
        print("=" * 80)
        print("OmniLink Windows Docker部署和验证")
        print(f"部署时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        deployment_steps = [
            ("Docker环境检查", self.check_docker_environment),
            ("配置验证", self.validate_configuration),
            ("镜像构建", self.build_images),
            ("服务启动", self.start_services),
            ("服务验证", self.verify_services),
            ("通信测试", self.test_communication)
        ]
        
        try:
            for step_name, step_func in deployment_steps:
                logger.info(f"\n--- 执行: {step_name} ---")
                
                try:
                    success = step_func()
                    if not success:
                        logger.warning(f"{step_name} 失败，停止后续步骤")
                        break
                except Exception as e:
                    logger.error(f"{step_name} 执行异常: {e}")
                    self.deployment_results.append({
                        "step": step_name,
                        "status": "ERROR",
                        "message": f"执行异常: {e}"
                    })
                    break
                
                time.sleep(2)
            
        except KeyboardInterrupt:
            logger.info("用户中断部署流程")
        
        # 生成部署报告
        self.generate_deployment_report()
    
    def generate_deployment_report(self):
        """生成部署报告"""
        print("\n" + "=" * 80)
        print("Windows Docker部署结果报告")
        print("=" * 80)
        
        total_steps = len(self.deployment_results)
        success_steps = sum(1 for r in self.deployment_results if r["status"] == "SUCCESS")
        failed_steps = sum(1 for r in self.deployment_results if r["status"] == "FAILED")
        error_steps = sum(1 for r in self.deployment_results if r["status"] == "ERROR")
        
        print(f"总步骤数: {total_steps}")
        print(f"成功: {success_steps}")
        print(f"失败: {failed_steps}")
        print(f"错误: {error_steps}")
        
        if total_steps > 0:
            success_rate = (success_steps / total_steps) * 100
            print(f"成功率: {success_rate:.1f}%")
        else:
            success_rate = 0
        
        print("\n详细结果:")
        for result in self.deployment_results:
            status_icon = {
                "SUCCESS": "✅",
                "FAILED": "❌",
                "ERROR": "💥"
            }.get(result["status"], "❓")
            
            print(f"{status_icon} {result['step']}: {result['message']}")
        
        print("\n" + "=" * 80)
        
        if success_rate >= 80:
            print("🎉 部署成功！OmniLink主从服务器在Windows Docker环境下运行正常。")
            print("\n📋 访问信息:")
            print("- 主服务器API文档: http://localhost:8000/docs")
            print("- 主服务器Web界面: http://localhost:8000")
            print("- 从服务器健康检查: http://localhost:8001/health")
            print("- 管理员账户: firefly / bro2fhz12")
        elif success_rate >= 50:
            print("⚠️  部分部署成功，但有问题需要修复。")
        else:
            print("❌ 部署失败，需要检查错误并重新部署。")
        
        print("=" * 80)
        
        # 保存部署报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_steps": total_steps,
                "success_steps": success_steps,
                "failed_steps": failed_steps,
                "error_steps": error_steps,
                "success_rate": success_rate
            },
            "deployment_results": self.deployment_results
        }
        
        with open("windows_docker_deployment_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info("部署报告已保存到 windows_docker_deployment_report.json")
        
        # 显示容器状态
        logger.info("当前容器状态:")
        ps_result = self.run_command("docker-compose ps", timeout=30)
        if ps_result["success"]:
            print(f"\n{ps_result['stdout']}")

def main():
    """主函数"""
    print("OmniLink Windows Docker部署器")
    print("确保Docker Desktop已启动并运行正常")
    
    input("按Enter键开始部署...")
    
    deployer = WindowsDockerDeployer()
    deployer.run_full_deployment()

if __name__ == "__main__":
    main() 