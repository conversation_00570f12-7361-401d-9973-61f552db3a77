import time
import threading
import logging

logger = logging.getLogger('api')

class RateLimiter:
    """速率限制器，用于防止DoS攻击和滥用API"""
    
    def __init__(self, window_size=60, max_requests=60):
        """
        初始化速率限制器
        
        Args:
            window_size: 时间窗口大小，单位秒，默认60秒
            max_requests: 窗口内最大请求数，默认60次
        """
        self.window_size = window_size
        self.max_requests = max_requests
        self.counters = {}  # {key: [(timestamp, count), ...]}
        self.lock = threading.Lock()
    
    def check_rate(self, key, increment=1):
        """
        检查并更新速率
        
        Args:
            key: 限制器键（通常是IP或用户标识）
            increment: 增量，默认1
            
        Returns:
            bool: 是否未超过速率限制
        """
        current_time = time.time()
        
        with self.lock:
            # 初始化或清理过期计数
            if key not in self.counters:
                self.counters[key] = []
            else:
                # 移除窗口外的旧计数
                self.counters[key] = [
                    (ts, count) for ts, count in self.counters[key]
                    if current_time - ts < self.window_size
                ]
            
            # 计算当前窗口内的总请求数
            total_requests = sum(count for _, count in self.counters[key])
            
            # 检查是否超出限制
            if total_requests + increment > self.max_requests:
                logger.warning(f"速率限制超出: key={key}, 请求数={total_requests + increment}, 限制={self.max_requests}")
                return False
            
            # 更新计数
            self.counters[key].append((current_time, increment))
            return True
    
    def get_remaining(self, key):
        """
        获取剩余请求数
        
        Args:
            key: 限制器键
            
        Returns:
            int: 剩余请求数
        """
        current_time = time.time()
        
        with self.lock:
            if key not in self.counters:
                return self.max_requests
            
            # 移除窗口外的旧计数
            self.counters[key] = [
                (ts, count) for ts, count in self.counters[key]
                if current_time - ts < self.window_size
            ]
            
            # 计算已用请求数
            used_requests = sum(count for _, count in self.counters[key])
            
            return max(0, self.max_requests - used_requests)
    
    def reset(self, key=None):
        """
        重置计数器
        
        Args:
            key: 特定键，如果为None则重置所有
        """
        with self.lock:
            if key is None:
                self.counters.clear()
            elif key in self.counters:
                del self.counters[key]
    
    def cleanup(self):
        """清理所有过期计数"""
        current_time = time.time()
        
        with self.lock:
            for key in list(self.counters.keys()):
                self.counters[key] = [
                    (ts, count) for ts, count in self.counters[key]
                    if current_time - ts < self.window_size
                ]
                
                # 如果没有活跃计数，移除该键
                if not self.counters[key]:
                    del self.counters[key] 
