from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from typing import Optional, List
from fastapi import HTTPException, status

from common.models import User, Role
from common.schemas.user_schema import UserCreate, UserUpdate
from src.omnilink_main.core.security import get_password_hash

class UserService:
    async def get_user_by_id(self, db: AsyncSession, *, id: int) -> Optional[User]:
        """Gets a user by ID, eagerly loading their roles."""
        statement = select(User).options(selectinload(User.roles)).where(User.id == id)
        result = await db.execute(statement)
        return result.scalars().first()

    async def get_user_by_username(self, db: AsyncSession, *, username: str) -> Optional[User]:
        statement = select(User).options(selectinload(User.roles)).where(User.username == username)
        result = await db.execute(statement)
        return result.scalars().first()

    async def get_all_users(self, db: AsyncSession, *, skip: int = 0, limit: int = 100) -> List[User]:
        statement = select(User).offset(skip).limit(limit)
        result = await db.execute(statement)
        return result.scalars().all()

    async def create_new_user(self, db: AsyncSession, *, user_in: UserCreate) -> User:
        """
        Creates a new user, checking for existence first.
        """
        existing_user = await self.get_user_by_username(db, username=user_in.username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The user with this username already exists in the system.",
            )
        
        hashed_password = get_password_hash(user_in.password)
        db_user = User(
            username=user_in.username,
            email=user_in.email,
            hashed_password=hashed_password,
            is_active=user_in.is_active,
            is_superuser=user_in.is_superuser
        )
        db.add(db_user)
        await db.commit()
        await db.refresh(db_user)
        return db_user

    async def update_existing_user(self, db: AsyncSession, *, user_id: int, user_in: UserUpdate) -> User:
        """
        Updates an existing user.
        """
        db_user = await self.get_user_by_id(db, id=user_id)
        if not db_user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
            
        update_data = user_in.model_dump(exclude_unset=True)
        
        if "password" in update_data and update_data["password"]:
            hashed_password = get_password_hash(update_data["password"])
            db_user.hashed_password = hashed_password
            del update_data["password"]
        
        for field, value in update_data.items():
            setattr(db_user, field, value)
        
        await db.commit()
        await db.refresh(db_user)
        return db_user

    async def delete_existing_user(self, db: AsyncSession, *, id: int) -> User:
        """
        Deletes a user.
        """
        user = await self.get_user_by_id(db, id=id)
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
        
        await db.delete(user)
        await db.commit()
        # The user object is now expired, but we can return it as it was.
        return user

# Create a single, reusable instance of the service
user_service_instance = UserService()
