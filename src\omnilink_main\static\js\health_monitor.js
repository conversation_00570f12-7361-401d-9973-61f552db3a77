/**
 * 从服务器健康监控系统前端JS
 * 实现数据获取、页面更新和图表展示功能
 */

// 全局变量
let serversData = {};
let selectedServerId = null;
let charts = {};
let autoRefreshInterval = null;
let groupsData = [];

// 页面加载后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化数据
    loadServersData();
    loadGroupsData();

    // 注册事件处理器
    document.getElementById('refreshBtn').addEventListener('click', function() {
        loadServersData();
        if (selectedServerId) {
            loadServerDetail(selectedServerId);
        }
    });

    document.getElementById('serverSearch').addEventListener('input', function() {
        filterServerList(this.value);
    });

    // 自动刷新设置 (每60秒)
    autoRefreshInterval = setInterval(function() {
        loadServersData();
        if (selectedServerId) {
            loadServerDetail(selectedServerId);
        }
    }, 60000);
});

// 加载服务器列表数据
function loadServersData() {
    fetch('/api/slave/health/servers')
        .then(response => response.json())
        .then(data => {
            serversData = data.servers || {};
            updateServerList();
            updateStatusCounts();
        })
        .catch(error => {
            console.error('加载服务器数据出错:', error);
            showError('加载服务器数据出错，请检查网络连接或稍后重试。');
        });
}

// 加载服务器组数据
function loadGroupsData() {
    fetch('/api/slave/health/groups')
        .then(response => response.json())
        .then(data => {
            groupsData = data.groups || [];
            updateGroupList();
        })
        .catch(error => {
            console.error('加载服务器组数据出错:', error);
        });
}

// 更新服务器列表显示
function updateServerList() {
    const serverList = document.getElementById('serverList');
    
    // 清空现有列表，保留可能的加载提示
    serverList.innerHTML = '';
    
    if (Object.keys(serversData).length === 0) {
        serverList.innerHTML = '<div class="text-center py-3 text-muted">没有找到服务器</div>';
        return;
    }

    // 按状态对服务器进行排序（严重 > 警告 > 健康 > 未知）
    const sortedServers = Object.entries(serversData).sort((a, b) => {
        const statusOrder = { 'critical': 0, 'warning': 1, 'healthy': 2, 'unknown': 3 };
        return statusOrder[a[1].status] - statusOrder[b[1].status];
    });

    // 创建服务器列表项
    sortedServers.forEach(([serverId, serverData]) => {
        const listItem = document.createElement('a');
        listItem.href = '#';
        listItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';
        listItem.dataset.serverId = serverId;
        
        // 设置状态标记样式
        let statusBadgeClass = 'bg-secondary'; // 默认未知
        if (serverData.status === 'healthy') {
            statusBadgeClass = 'bg-success';
        } else if (serverData.status === 'warning') {
            statusBadgeClass = 'bg-warning';
        } else if (serverData.status === 'critical') {
            statusBadgeClass = 'bg-danger';
        }
        
        // 计算上次更新时间
        const lastUpdate = new Date(serverData.timestamp);
        const timeAgo = getTimeAgo(lastUpdate);
        
        // 设置列表项内容
        listItem.innerHTML = `
            <div>
                <div class="fw-bold">${serverId}</div>
                <small class="text-muted">上次更新: ${timeAgo}</small>
            </div>
            <span class="badge ${statusBadgeClass}">${getStatusText(serverData.status)}</span>
        `;
        
        // 添加点击事件处理
        listItem.addEventListener('click', function(event) {
            event.preventDefault();
            selectServer(serverId);
        });
        
        serverList.appendChild(listItem);
    });
}

// 更新状态计数
function updateStatusCounts() {
    let healthyCount = 0;
    let warningCount = 0;
    let criticalCount = 0;
    let unknownCount = 0;
    
    Object.values(serversData).forEach(server => {
        if (server.status === 'healthy') {
            healthyCount++;
        } else if (server.status === 'warning') {
            warningCount++;
        } else if (server.status === 'critical') {
            criticalCount++;
        } else {
            unknownCount++;
        }
    });
    
    document.getElementById('healthyCount').textContent = healthyCount;
    document.getElementById('warningCount').textContent = warningCount;
    document.getElementById('criticalCount').textContent = criticalCount;
    document.getElementById('unknownCount').textContent = unknownCount;
}

// 更新分组列表
function updateGroupList() {
    const groupList = document.getElementById('groupList');
    
    // 保留"所有服务器"选项
    groupList.innerHTML = '<li><a class="dropdown-item active" href="#" data-group="all">所有服务器</a></li>';
    
    groupsData.forEach(group => {
        const listItem = document.createElement('li');
        listItem.innerHTML = `<a class="dropdown-item" href="#" data-group="${group.name}">${group.name}</a>`;
        
        // 添加点击事件
        listItem.querySelector('a').addEventListener('click', function(event) {
            event.preventDefault();
            selectGroup(group.name);
        });
        
        groupList.appendChild(listItem);
    });
}

// 选择服务器组
function selectGroup(groupName) {
    // 更新UI
    document.querySelectorAll('#groupList .dropdown-item').forEach(item => {
        item.classList.remove('active');
        if (item.dataset.group === groupName) {
            item.classList.add('active');
        }
    });
    
    document.getElementById('groupDropdown').textContent = groupName === 'all' ? '分组显示' : groupName;
    
    // 过滤服务器列表
    if (groupName === 'all') {
        // 显示所有服务器
        document.querySelectorAll('#serverList a').forEach(item => {
            item.style.display = '';
        });
    } else {
        // 获取组内服务器
        const group = groupsData.find(g => g.name === groupName);
        if (group) {
            const serverIds = group.servers || [];
            
            // 仅显示组内服务器
            document.querySelectorAll('#serverList a').forEach(item => {
                if (serverIds.includes(item.dataset.serverId)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        }
    }
}

// 选择服务器显示详情
function selectServer(serverId) {
    selectedServerId = serverId;
    
    // 更新UI选中状态
    document.querySelectorAll('#serverList a').forEach(item => {
        item.classList.remove('active');
        if (item.dataset.serverId === serverId) {
            item.classList.add('active');
        }
    });
    
    // 加载详细信息
    loadServerDetail(serverId);
}

// 加载服务器详细信息
function loadServerDetail(serverId) {
    document.getElementById('detailTitle').textContent = `服务器详情: ${serverId}`;
    document.getElementById('serverDetail').innerHTML = `
        <div class="d-flex justify-content-center py-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    `;
    
    // 获取详细数据
    fetch(`/api/slave/health/servers/${serverId}`)
        .then(response => response.json())
        .then(data => {
            updateServerDetail(data);
            // 加载趋势数据
            loadServerTrends(serverId);
        })
        .catch(error => {
            console.error('加载服务器详情出错:', error);
            document.getElementById('serverDetail').innerHTML = `
                <div class="alert alert-danger">
                    加载服务器详情出错，请检查网络连接或稍后重试。
                </div>
            `;
        });
}

// 更新服务器详情显示
function updateServerDetail(data) {
    const serverDetail = document.getElementById('serverDetail');
    
    // 格式化时间戳
    const lastUpdate = new Date(data.timestamp);
    const formattedTime = lastUpdate.toLocaleString();
    
    // 获取状态样式
    let statusClass = 'secondary';
    if (data.status === 'healthy') {
        statusClass = 'success';
    } else if (data.status === 'warning') {
        statusClass = 'warning';
    } else if (data.status === 'critical') {
        statusClass = 'danger';
    }
    
    // 构建详情内容
    let detailHTML = `
        <div class="mb-4">
            <div class="d-flex justify-content-between">
                <h5>基本信息</h5>
                <span class="badge bg-${statusClass}">${getStatusText(data.status)}</span>
            </div>
            <div class="table-responsive">
                <table class="table table-sm">
                    <tbody>
                        <tr>
                            <th width="25%">服务器ID</th>
                            <td>${data.server_id}</td>
                        </tr>
                        <tr>
                            <th>上次更新</th>
                            <td>${formattedTime}</td>
                        </tr>
                        <tr>
                            <th>运行时间</th>
                            <td>${formatUptime(data.uptime)}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    // 添加维度指标信息
    if (data.metrics) {
        detailHTML += `<h5 class="mb-3">健康指标</h5>`;
        
        // 创建进度条显示各维度状态
        for (const [dim, metric] of Object.entries(data.metrics)) {
            let barClass = 'bg-success';
            if (metric.status === 'warning') {
                barClass = 'bg-warning';
            } else if (metric.status === 'critical') {
                barClass = 'bg-danger';
            }
            
            const percentage = Math.round(metric.value * 100);
            
            detailHTML += `
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>${getDimensionName(dim)}</span>
                        <span>${percentage}%</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar ${barClass}" role="progressbar" style="width: ${percentage}%" 
                            aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            `;
        }
    }
    
    // 显示告警信息
    if (data.alerts && data.alerts.length > 0) {
        detailHTML += `
            <h5 class="mb-3 mt-4">告警信息</h5>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>维度</th>
                            <th>状态</th>
                            <th>上次告警时间</th>
                            <th>次数</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        data.alerts.forEach(alert => {
            let statusClass = 'secondary';
            if (alert.status === 'healthy') {
                statusClass = 'success';
            } else if (alert.status === 'warning') {
                statusClass = 'warning';
            } else if (alert.status === 'critical') {
                statusClass = 'danger';
            }
            
            detailHTML += `
                <tr>
                    <td>${getDimensionName(alert.dimension)}</td>
                    <td><span class="badge bg-${statusClass}">${getStatusText(alert.status)}</span></td>
                    <td>${new Date(alert.last_alert).toLocaleString()}</td>
                    <td>${alert.count}</td>
                </tr>
            `;
        });
        
        detailHTML += `
                    </tbody>
                </table>
            </div>
        `;
    }
    
    serverDetail.innerHTML = detailHTML;
}

// 加载服务器趋势数据
function loadServerTrends(serverId) {
    // 默认加载24小时数据，包括CPU、内存、磁盘和网络维度
    const dimensions = 'cpu,memory,disk,network';
    const hours = 24;
    
    fetch(`/api/slave/health/servers/${serverId}/trends?dimensions=${dimensions}&hours=${hours}`)
        .then(response => response.json())
        .then(data => {
            updateTrendCharts(data);
        })
        .catch(error => {
            console.error('加载服务器趋势数据出错:', error);
        });
}

// 更新趋势图表
function updateTrendCharts(data) {
    const trends = data.trends || {};
    
    // 更新或创建CPU图表
    updateChart('cpuChart', '处理器使用', trends.cpu, '#ff6384');
    
    // 更新或创建内存图表
    updateChart('memoryChart', '内存使用', trends.memory, '#36a2eb');
    
    // 更新或创建磁盘图表
    updateChart('diskChart', '磁盘使用', trends.disk, '#ffcd56');
    
    // 更新或创建网络图表
    updateChart('networkChart', '网络状态', trends.network, '#4bc0c0');
}

// 更新或创建单个图表
function updateChart(chartId, title, trendData, color) {
    const canvas = document.getElementById(chartId);
    
    // 销毁现有图表
    if (charts[chartId]) {
        charts[chartId].destroy();
    }
    
    if (!trendData || !trendData.values || trendData.values.length === 0) {
        // 无数据情况下显示空图表
        charts[chartId] = new Chart(canvas, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: title,
                    data: [],
                    backgroundColor: color,
                    borderColor: color,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: title + ' (无数据)'
                    }
                }
            }
        });
        return;
    }
    
    // 格式化时间戳为可读时间
    const labels = trendData.timestamps.map(ts => {
        const date = new Date(ts);
        return date.toLocaleTimeString();
    });
    
    // 转换数值为百分比
    const values = trendData.values.map(v => v * 100);
    
    // 创建新图表
    charts[chartId] = new Chart(canvas, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: title + ' (%)',
                data: values,
                backgroundColor: color,
                borderColor: color,
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: title
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.raw.toFixed(1) + '%';
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: '使用率 (%)'
                    }
                }
            }
        }
    });
}

// 搜索过滤服务器列表
function filterServerList(searchText) {
    const searchLower = searchText.toLowerCase();
    document.querySelectorAll('#serverList a').forEach(item => {
        const serverId = item.dataset.serverId.toLowerCase();
        if (serverId.includes(searchLower)) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
    });
}

// 显示错误消息
function showError(message) {
    const serverList = document.getElementById('serverList');
    serverList.innerHTML = `
        <div class="alert alert-danger m-3">
            ${message}
        </div>
    `;
}

// 辅助函数: 获取状态文本
function getStatusText(status) {
    switch (status) {
        case 'healthy': return '健康';
        case 'warning': return '警告';
        case 'critical': return '严重';
        default: return '未知';
    }
}

// 辅助函数: 获取维度名称
function getDimensionName(dimension) {
    const dimensionNames = {
        'cpu': '处理器',
        'memory': '内存',
        'disk': '磁盘',
        'network': '网络',
        'service': '服务',
        'usb': 'USB设备',
        'load': '系统负载',
        'connection': '连接数',
        'response_time': '响应时间',
        'availability': '可用性',
        'temperature': '温度'
    };
    
    return dimensionNames[dimension] || dimension;
}

// 辅助函数: 格式化运行时间
function formatUptime(seconds) {
    const days = Math.floor(seconds / (24 * 3600));
    seconds %= (24 * 3600);
    const hours = Math.floor(seconds / 3600);
    seconds %= 3600;
    const minutes = Math.floor(seconds / 60);
    seconds %= 60;
    
    let result = '';
    if (days > 0) {
        result += `${days}天 `;
    }
    if (hours > 0 || days > 0) {
        result += `${hours}小时 `;
    }
    if (minutes > 0 || hours > 0 || days > 0) {
        result += `${minutes}分钟 `;
    }
    result += `${seconds}秒`;
    
    return result;
}

// 辅助函数: 获取相对时间描述
function getTimeAgo(date) {
    const seconds = Math.floor((new Date() - date) / 1000);
    
    if (seconds < 60) {
        return "刚刚";
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        return `${minutes}分钟前`;
    } else if (seconds < 86400) {
        const hours = Math.floor(seconds / 3600);
        return `${hours}小时前`;
    } else {
        const days = Math.floor(seconds / 86400);
        return `${days}天前`;
    }
} 