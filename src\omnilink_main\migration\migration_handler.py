"""
迁移处理器

负责具体的迁移任务执行和进度跟踪，包括数据导出、传输和导入
"""

import os
import time
import json
import logging
import threading
import shutil
import tempfile
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Callable, Union

# 导入迁移服务依赖
from .migration_service import MigrationService, MigrationError

# 配置日志
logger = logging.getLogger("migration_handler")

class MigrationHandler:
    """迁移处理器，负责执行具体的迁移任务"""
    
    # 迁移任务类型
    TASK_TYPES = {
        'full': '完整迁移',
        'config_only': '仅配置迁移',
        'data_only': '仅数据迁移',
        'service_switch': '服务切换'
    }
    
    # 数据类型
    DATA_TYPES = {
        'system_config': '系统配置',
        'user_data': '用户数据',
        'service_data': '服务数据',
        'logs': '日志数据'
    }
    
    def __init__(self, migration_service: MigrationService):
        """
        初始化迁移处理器
        
        参数:
            migration_service: 迁移服务实例
        """
        self.migration_service = migration_service
        self.tasks: Dict[str, Dict[str, Any]] = {}  # 任务ID -> 任务信息
        self.progress_trackers: Dict[str, threading.Thread] = {}  # 任务ID -> 进度跟踪线程
        self.stop_flags: Dict[str, threading.Event] = {}  # 任务ID -> 停止标志
        
        # 注册迁移状态回调
        self._register_migration_callbacks()
        
        # 任务锁
        self._task_lock = threading.Lock()
        
        logger.info("迁移处理器已初始化")
    
    def _register_migration_callbacks(self):
        """注册迁移状态回调"""
        # 注册所有状态的回调
        for state in MigrationService.MIGRATION_STATES:
            self.migration_service.register_state_callback(state, self._handle_migration_state_change)
    
    def _handle_migration_state_change(self, state_data: Dict[str, Any]):
        """
        处理迁移状态变更
        
        参数:
            state_data: 状态数据
        """
        state = state_data.get('state')
        migration_id = None
        
        # 查找对应的迁移ID
        migration_status = self.migration_service.get_migration_status()
        if migration_status:
            migration_id = migration_status.get('id')
        
        if not migration_id:
            logger.warning(f"处理状态变更时无法获取迁移ID: {state}")
            return
        
        # 根据状态执行相应操作
        if state == 'preparing':
            self._start_progress_tracking(migration_id)
        elif state in ['completed', 'failed']:
            self._stop_progress_tracking(migration_id)
    
    def create_migration_task(self, source_info: Dict[str, Any], target_info: Dict[str, Any], 
                             task_type: str = 'full', options: Optional[Dict[str, Any]] = None) -> str:
        """
        创建迁移任务
        
        参数:
            source_info: 源服务器信息
            target_info: 目标服务器信息
            task_type: 任务类型
            options: 迁移选项
            
        返回:
            任务ID
        """
        # 验证任务类型
        if task_type not in self.TASK_TYPES:
            raise ValueError(f"不支持的任务类型: {task_type}")
        
        # 合并选项
        task_options = options or {}
        task_options['task_type'] = task_type
        
        # 添加默认迁移数据类型
        if 'data_types' not in task_options:
            if task_type == 'full':
                task_options['data_types'] = list(self.DATA_TYPES.keys())
            elif task_type == 'data_only':
                task_options['data_types'] = ['user_data', 'service_data']
            elif task_type == 'config_only':
                task_options['data_types'] = ['system_config']
            else:
                task_options['data_types'] = []
        
        # 创建迁移
        migration_id = self.migration_service.start_migration(source_info, target_info, task_options)
        
        # 创建任务信息
        with self._task_lock:
            task_info = {
                'id': migration_id,
                'migration_id': migration_id,
                'source': source_info,
                'target': target_info,
                'type': task_type,
                'options': task_options,
                'created_at': datetime.now().isoformat(),
                'status': 'created',
                'progress': 0
            }
            self.tasks[migration_id] = task_info
            
            # 创建停止标志
            self.stop_flags[migration_id] = threading.Event()
        
        logger.info(f"已创建迁移任务: {migration_id}, 类型: {task_type}")
        return migration_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        参数:
            task_id: 任务ID
            
        返回:
            任务状态信息
        """
        # 检查任务是否存在
        if task_id not in self.tasks:
            return None
        
        # 获取任务信息
        task_info = self.tasks[task_id]
        
        # 获取迁移状态
        migration_status = self.migration_service.get_migration_status(task_info['migration_id'])
        if migration_status:
            # 更新任务状态
            task_info['status'] = migration_status['state']
            task_info['progress'] = migration_status['progress']
            if migration_status['error']:
                task_info['error'] = migration_status['error']
        
        return task_info
    
    def stop_task(self, task_id: str, force: bool = False) -> bool:
        """
        停止任务
        
        参数:
            task_id: 任务ID
            force: 是否强制停止
            
        返回:
            是否成功停止
        """
        # 检查任务是否存在
        if task_id not in self.tasks:
            logger.warning(f"任务不存在: {task_id}")
            return False
        
        # 获取任务信息
        task_info = self.tasks[task_id]
        
        # 设置停止标志
        if task_id in self.stop_flags:
            self.stop_flags[task_id].set()
        
        # 停止迁移
        result = self.migration_service.stop_migration(task_info['migration_id'], force)
        if result:
            logger.info(f"已停止任务: {task_id}")
            
            # 停止进度跟踪
            self._stop_progress_tracking(task_id)
        
        return result
    
    def list_tasks(self, filter_status: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        列出任务
        
        参数:
            filter_status: 过滤状态
            
        返回:
            任务列表
        """
        result = []
        for task_id, task_info in self.tasks.items():
            # 更新任务状态
            updated_task = self.get_task_status(task_id)
            if updated_task:
                task_info = updated_task
            
            # 应用过滤
            if filter_status is None or task_info['status'] == filter_status:
                result.append(task_info)
        
        return result
    
    def get_task_logs(self, task_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取任务日志
        
        参数:
            task_id: 任务ID
            limit: 日志条目限制
            
        返回:
            日志列表
        """
        # 检查任务是否存在
        if task_id not in self.tasks:
            return []
        
        # 获取任务信息
        task_info = self.tasks[task_id]
        
        # 获取迁移状态
        migration_status = self.migration_service.get_migration_status(task_info['migration_id'])
        if not migration_status:
            return []
        
        # 获取日志
        logs = migration_status.get('logs', [])
        
        # 限制日志条目数量
        if limit > 0 and len(logs) > limit:
            logs = logs[-limit:]
        
        return logs
    
    def _start_progress_tracking(self, task_id: str):
        """
        开始进度跟踪
        
        参数:
            task_id: 任务ID
        """
        # 检查任务是否存在
        if task_id not in self.tasks:
            logger.warning(f"任务不存在: {task_id}")
            return
        
        # 检查是否已有跟踪线程
        if task_id in self.progress_trackers and self.progress_trackers[task_id].is_alive():
            logger.info(f"任务 {task_id} 的进度跟踪已在运行")
            return
        
        # 创建停止标志
        if task_id not in self.stop_flags:
            self.stop_flags[task_id] = threading.Event()
        
        # 创建跟踪线程
        self.progress_trackers[task_id] = threading.Thread(
            target=self._progress_tracking_thread,
            args=(task_id,),
            daemon=True
        )
        
        # 启动线程
        self.progress_trackers[task_id].start()
        logger.debug(f"已启动任务 {task_id} 的进度跟踪")
    
    def _stop_progress_tracking(self, task_id: str):
        """
        停止进度跟踪
        
        参数:
            task_id: 任务ID
        """
        # 设置停止标志
        if task_id in self.stop_flags:
            self.stop_flags[task_id].set()
        
        # 等待线程结束
        if task_id in self.progress_trackers and self.progress_trackers[task_id].is_alive():
            self.progress_trackers[task_id].join(timeout=2.0)
            if self.progress_trackers[task_id].is_alive():
                logger.warning(f"任务 {task_id} 的进度跟踪线程未能在超时时间内停止")
            else:
                logger.debug(f"已停止任务 {task_id} 的进度跟踪")
    
    def _progress_tracking_thread(self, task_id: str):
        """
        进度跟踪线程
        
        参数:
            task_id: 任务ID
        """
        stop_flag = self.stop_flags.get(task_id)
        if not stop_flag:
            logger.error(f"任务 {task_id} 的停止标志不存在")
            return
        
        try:
            while not stop_flag.is_set():
                # 获取最新状态
                migration_status = self.migration_service.get_migration_status(task_id)
                if not migration_status:
                    logger.warning(f"无法获取任务 {task_id} 的迁移状态")
                    break
                
                # 更新任务信息
                with self._task_lock:
                    if task_id in self.tasks:
                        self.tasks[task_id]['status'] = migration_status['state']
                        self.tasks[task_id]['progress'] = migration_status['progress']
                        if migration_status.get('error'):
                            self.tasks[task_id]['error'] = migration_status['error']
                
                # 检查完成状态
                if migration_status['state'] in ['completed', 'failed']:
                    logger.info(f"任务 {task_id} 已{migration_status['state']}")
                    break
                
                # 等待一段时间
                for _ in range(5):  # 5秒，每秒检查一次停止标志
                    if stop_flag.is_set():
                        break
                    time.sleep(1)
        
        except Exception as e:
            logger.error(f"进度跟踪线程异常: {str(e)}")
        finally:
            # 清理资源
            if task_id in self.progress_trackers:
                del self.progress_trackers[task_id]
            logger.debug(f"任务 {task_id} 的进度跟踪线程已退出")
    
    def get_migration_speed(self, task_id: str) -> Dict[str, Any]:
        """
        获取迁移速度
        
        参数:
            task_id: 任务ID
            
        返回:
            速度信息
        """
        # 检查任务是否存在
        if task_id not in self.tasks:
            return {"error": "任务不存在"}
        
        # 获取迁移状态
        migration_status = self.migration_service.get_migration_status(task_id)
        if not migration_status:
            return {"error": "无法获取迁移状态"}
        
        # 获取检查点数据
        checkpoints = migration_status.get('checkpoints', {})
        
        # 计算速度
        result = {
            "current_speed": 0,
            "average_speed": 0,
            "estimated_time_remaining": None,
            "data_processed": 0,
            "data_total": 0
        }
        
        # 数据传输检查点
        transfer_start = checkpoints.get('transfer_start', {}).get('data', {})
        transfer_current = checkpoints.get('transfer_current', {}).get('data', {})
        
        if transfer_start and transfer_current:
            start_time = datetime.fromisoformat(transfer_start.get('timestamp', ''))
            current_time = datetime.fromisoformat(transfer_current.get('timestamp', ''))
            start_bytes = transfer_start.get('bytes_transferred', 0)
            current_bytes = transfer_current.get('bytes_transferred', 0)
            total_bytes = transfer_current.get('total_bytes', 0)
            
            # 计算已处理和总数据
            result["data_processed"] = current_bytes
            result["data_total"] = total_bytes
            
            # 计算速度
            time_diff = (current_time - start_time).total_seconds()
            if time_diff > 0:
                bytes_diff = current_bytes - start_bytes
                result["current_speed"] = int(bytes_diff / time_diff)
                result["average_speed"] = int(current_bytes / time_diff)
                
                # 估计剩余时间
                remaining_bytes = total_bytes - current_bytes
                if result["average_speed"] > 0:
                    result["estimated_time_remaining"] = int(remaining_bytes / result["average_speed"])
        
        return result
    
    def get_detailed_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取详细状态
        
        参数:
            task_id: 任务ID
            
        返回:
            详细状态信息
        """
        # 获取基本状态
        basic_status = self.get_task_status(task_id)
        if not basic_status:
            return {"error": "任务不存在"}
        
        # 获取迁移速度
        speed_info = self.get_migration_speed(task_id)
        
        # 获取最近日志
        recent_logs = self.get_task_logs(task_id, 10)
        
        # 组合详细状态
        detailed_status = {
            **basic_status,
            "speed": speed_info,
            "recent_logs": recent_logs
        }
        
        return detailed_status 
