<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理 - Web管理服务</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .filter-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .search-container {
            margin-bottom: 1.5rem;
            display: flex;
            gap: 0.5rem;
        }
        
        .search-input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid var(--gray-color);
            border-radius: var(--border-radius);
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-warning {
            background-color: var(--tertiary-color);
            color: var(--dark-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .device-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1rem;
            position: relative;
            border-left: 4px solid var(--primary-color);
        }
        
        .device-card.online {
            border-left-color: var(--secondary-color);
        }
        
        .device-card.offline {
            border-left-color: var(--danger-color);
        }
        
        .device-card h3 {
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .device-status {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
        }
        
        .device-status.online {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .device-status.offline {
            background-color: var(--danger-color);
            color: white;
        }
        
        .device-detail {
            display: flex;
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
        }
        
        .device-detail-label {
            width: 100px;
            font-weight: bold;
            color: var(--dark-color);
        }
        
        .device-detail-value {
            flex: 1;
            color: var(--gray-color);
        }
        
        .device-actions {
            margin-top: 1rem;
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 1.5rem;
        }
        
        .pagination a {
            padding: 0.5rem 1rem;
            border: 1px solid var(--gray-color);
            margin: 0 0.25rem;
            border-radius: var(--border-radius);
        }
        
        .pagination a.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--light-color);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--gray-color);
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--gray-color);
            border-radius: var(--border-radius);
        }
        
        .modal-footer {
            margin-top: 1.5rem;
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            margin-left: 0.5rem;
        }
        
        .badge-shared {
            background-color: var(--tertiary-color);
            color: var(--dark-color);
        }
    </style>
</head>
<body>
    <header>
        <h1>设备管理</h1>
        <nav>
            <ul>
                <li><a href="{{ url_for('websevs.ind.index') }}">首页</a></li>
                <li><a href="{{ url_for('websevs.ind.dashboard') }}">仪表盘</a></li>
                <li><a href="{{ url_for('websevs.ind.devices') }}">设备管理</a></li>
                <li><a href="{{ url_for('websevs.ind.slaves') }}">从服务器</a></li>
                <li><a href="{{ url_for('websevs.ind.settings') }}">系统设置</a></li>
                <li><a href="{{ url_for('websevs.ind.monitoring') }}">系统监控</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h2>设备列表</h2>
                <button class="btn btn-primary" id="addDeviceBtn">添加设备</button>
            </div>

            <div class="search-container">
                <input type="text" class="search-input" placeholder="搜索设备...">
                <button class="btn btn-primary">搜索</button>
            </div>

            <div class="filters">
                <div class="filter-item">
                    <label>状态:</label>
                    <select class="form-control">
                        <option value="all">全部</option>
                        <option value="online">在线</option>
                        <option value="offline">离线</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>类型:</label>
                    <select class="form-control">
                        <option value="all">全部</option>
                        <option value="usb">USB设备</option>
                        <option value="printer">打印机</option>
                        <option value="scanner">扫描仪</option>
                        <option value="camera">摄像头</option>
                        <option value="audio">音频设备</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>从服务器:</label>
                    <select class="form-control">
                        <option value="all">全部</option>
                        <option value="1">工作站A-1</option>
                        <option value="2">工作站B-2</option>
                        <option value="3">工作站C-3</option>
                        <option value="4">工作站D-4</option>
                        <option value="5">工作站E-5</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>共享状态:</label>
                    <select class="form-control">
                        <option value="all">全部</option>
                        <option value="shared">已共享</option>
                        <option value="not_shared">未共享</option>
                    </select>
                </div>
            </div>

            <div class="device-grid">
                <!-- 设备卡片 -->
                <div class="device-card online">
                    <h3>
                        HP Color LaserJet Pro
                        <span class="device-status online">在线</span>
                    </h3>
                    <div class="device-detail">
                        <div class="device-detail-label">设备ID:</div>
                        <div class="device-detail-value">DEV-12345</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">类型:</div>
                        <div class="device-detail-value">打印机 <span class="badge badge-shared">已共享</span></div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">从服务器:</div>
                        <div class="device-detail-value">工作站A-1</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">连接时间:</div>
                        <div class="device-detail-value">2023-07-15 10:30</div>
                    </div>
                    <div class="device-actions">
                        <button class="btn btn-warning edit-device-btn">编辑</button>
                        <button class="btn btn-danger">断开连接</button>
                    </div>
                </div>

                <div class="device-card online">
                    <h3>
                        罗技 C920 HD Pro
                        <span class="device-status online">在线</span>
                    </h3>
                    <div class="device-detail">
                        <div class="device-detail-label">设备ID:</div>
                        <div class="device-detail-value">DEV-23456</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">类型:</div>
                        <div class="device-detail-value">摄像头 <span class="badge badge-shared">已共享</span></div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">从服务器:</div>
                        <div class="device-detail-value">工作站B-2</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">连接时间:</div>
                        <div class="device-detail-value">2023-07-15 09:15</div>
                    </div>
                    <div class="device-actions">
                        <button class="btn btn-warning edit-device-btn">编辑</button>
                        <button class="btn btn-danger">断开连接</button>
                    </div>
                </div>

                <div class="device-card offline">
                    <h3>
                        EPSON ScanSmart ES-400
                        <span class="device-status offline">离线</span>
                    </h3>
                    <div class="device-detail">
                        <div class="device-detail-label">设备ID:</div>
                        <div class="device-detail-value">DEV-34567</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">类型:</div>
                        <div class="device-detail-value">扫描仪</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">从服务器:</div>
                        <div class="device-detail-value">工作站C-3</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">最后在线:</div>
                        <div class="device-detail-value">2023-07-14 16:45</div>
                    </div>
                    <div class="device-actions">
                        <button class="btn btn-warning edit-device-btn">编辑</button>
                        <button class="btn btn-primary">重新连接</button>
                    </div>
                </div>

                <div class="device-card online">
                    <h3>
                        Focusrite Scarlett 2i2
                        <span class="device-status online">在线</span>
                    </h3>
                    <div class="device-detail">
                        <div class="device-detail-label">设备ID:</div>
                        <div class="device-detail-value">DEV-45678</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">类型:</div>
                        <div class="device-detail-value">音频设备 <span class="badge badge-shared">已共享</span></div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">从服务器:</div>
                        <div class="device-detail-value">工作站D-4</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">连接时间:</div>
                        <div class="device-detail-value">2023-07-15 08:30</div>
                    </div>
                    <div class="device-actions">
                        <button class="btn btn-warning edit-device-btn">编辑</button>
                        <button class="btn btn-danger">断开连接</button>
                    </div>
                </div>

                <div class="device-card online">
                    <h3>
                        西部数据 4TB 移动硬盘
                        <span class="device-status online">在线</span>
                    </h3>
                    <div class="device-detail">
                        <div class="device-detail-label">设备ID:</div>
                        <div class="device-detail-value">DEV-56789</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">类型:</div>
                        <div class="device-detail-value">存储设备</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">从服务器:</div>
                        <div class="device-detail-value">工作站E-5</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">连接时间:</div>
                        <div class="device-detail-value">2023-07-15 11:20</div>
                    </div>
                    <div class="device-actions">
                        <button class="btn btn-warning edit-device-btn">编辑</button>
                        <button class="btn btn-danger">断开连接</button>
                    </div>
                </div>

                <div class="device-card offline">
                    <h3>
                        Xbox 360 控制器
                        <span class="device-status offline">离线</span>
                    </h3>
                    <div class="device-detail">
                        <div class="device-detail-label">设备ID:</div>
                        <div class="device-detail-value">DEV-67890</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">类型:</div>
                        <div class="device-detail-value">游戏控制器</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">从服务器:</div>
                        <div class="device-detail-value">工作站A-1</div>
                    </div>
                    <div class="device-detail">
                        <div class="device-detail-label">最后在线:</div>
                        <div class="device-detail-value">2023-07-14 18:15</div>
                    </div>
                    <div class="device-actions">
                        <button class="btn btn-warning edit-device-btn">编辑</button>
                        <button class="btn btn-primary">重新连接</button>
                    </div>
                </div>
            </div>

            <div class="pagination">
                <a href="#">&laquo;</a>
                <a href="#" class="active">1</a>
                <a href="#">2</a>
                <a href="#">3</a>
                <a href="#">4</a>
                <a href="#">&raquo;</a>
            </div>
        </section>
    </main>

    <!-- 添加/编辑设备模态框 -->
    <div class="modal" id="deviceModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加设备</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form id="deviceForm">
                <div class="form-group">
                    <label for="deviceName">设备名称</label>
                    <input type="text" id="deviceName" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="deviceType">设备类型</label>
                    <select id="deviceType" class="form-control" required>
                        <option value="">选择类型</option>
                        <option value="usb">USB设备</option>
                        <option value="printer">打印机</option>
                        <option value="scanner">扫描仪</option>
                        <option value="camera">摄像头</option>
                        <option value="audio">音频设备</option>
                        <option value="storage">存储设备</option>
                        <option value="controller">游戏控制器</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="slaveServer">从服务器</label>
                    <select id="slaveServer" class="form-control" required>
                        <option value="">选择从服务器</option>
                        <option value="1">工作站A-1</option>
                        <option value="2">工作站B-2</option>
                        <option value="3">工作站C-3</option>
                        <option value="4">工作站D-4</option>
                        <option value="5">工作站E-5</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="deviceDescription">设备描述</label>
                    <textarea id="deviceDescription" class="form-control" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="isShared"> 共享设备
                    </label>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger modal-close-btn">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <footer>
        <p>&copy; 2023 Web管理服务系统 - 版本 1.0.0</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 模态框相关
            const deviceModal = document.getElementById('deviceModal');
            const addDeviceBtn = document.getElementById('addDeviceBtn');
            const editDeviceBtns = document.querySelectorAll('.edit-device-btn');
            const modalCloseBtn = document.querySelector('.modal-close');
            const modalCloseBtns = document.querySelectorAll('.modal-close-btn');
            const deviceForm = document.getElementById('deviceForm');
            const modalTitle = document.getElementById('modalTitle');

            // 打开添加设备模态框
            addDeviceBtn.addEventListener('click', function() {
                modalTitle.textContent = '添加设备';
                deviceForm.reset();
                deviceModal.style.display = 'flex';
            });

            // 打开编辑设备模态框
            editDeviceBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const card = this.closest('.device-card');
                    const deviceName = card.querySelector('h3').textContent.trim().split('\n')[0];
                    const deviceType = card.querySelector('.device-detail:nth-child(3) .device-detail-value').textContent.split(' ')[0];
                    const slaveServer = card.querySelector('.device-detail:nth-child(4) .device-detail-value').textContent;
                    const isShared = card.querySelector('.badge-shared') !== null;

                    modalTitle.textContent = '编辑设备';
                    document.getElementById('deviceName').value = deviceName;
                    
                    // 这里简化处理，实际应用中应该映射到正确的值
                    let typeValue = 'other';
                    if (deviceType.includes('打印机')) typeValue = 'printer';
                    else if (deviceType.includes('扫描仪')) typeValue = 'scanner';
                    else if (deviceType.includes('摄像头')) typeValue = 'camera';
                    else if (deviceType.includes('音频')) typeValue = 'audio';
                    else if (deviceType.includes('存储')) typeValue = 'storage';
                    else if (deviceType.includes('控制器')) typeValue = 'controller';
                    
                    document.getElementById('deviceType').value = typeValue;
                    
                    // 从服务器选择，这里简化处理
                    let serverValue = '1';
                    if (slaveServer.includes('A-1')) serverValue = '1';
                    else if (slaveServer.includes('B-2')) serverValue = '2';
                    else if (slaveServer.includes('C-3')) serverValue = '3';
                    else if (slaveServer.includes('D-4')) serverValue = '4';
                    else if (slaveServer.includes('E-5')) serverValue = '5';
                    
                    document.getElementById('slaveServer').value = serverValue;
                    document.getElementById('isShared').checked = isShared;
                    document.getElementById('deviceDescription').value = '设备描述...'; // 实际中应获取真实描述
                    
                    deviceModal.style.display = 'flex';
                });
            });

            // 关闭模态框
            modalCloseBtn.addEventListener('click', function() {
                deviceModal.style.display = 'none';
            });
            
            modalCloseBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    deviceModal.style.display = 'none';
                });
            });

            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === deviceModal) {
                    deviceModal.style.display = 'none';
                }
            });

            // 表单提交处理
            deviceForm.addEventListener('submit', function(e) {
                e.preventDefault();
                // 收集表单数据
                const deviceData = {
                    name: document.getElementById('deviceName').value,
                    type: document.getElementById('deviceType').value,
                    slaveServer: document.getElementById('slaveServer').value,
                    description: document.getElementById('deviceDescription').value,
                    isShared: document.getElementById('isShared').checked
                };
                
                console.log('设备数据:', deviceData);
                // 这里应该添加AJAX请求保存数据
                
                alert('设备保存成功!');
                deviceModal.style.display = 'none';
            });
        });
    </script>
</body>
</html> 