"""
审计日志模块

提供统一的审计日志记录和查询功能，支持系统操作的全面记录和追踪。
"""

from .audit_events import (
    AuditEventType,
    AuditSeverity,
    AuditCategory,
    get_event_category,
    get_event_severity,
    is_security_event,
    is_admin_operation
)

from .audit_record import (
    AuditRecord,
    create_audit_record
)

from .audit_service import (
    AuditService,
    audit_service,
    log_event,
    query_logs,
    get_log_by_id
)

from .audit_storage import (
    StorageBackend,
    AuditStorageManager
)

from .audit_api import audit_router

# 导出模块公共接口
__all__ = [
    # 事件类型和相关函数
    'AuditEventType',
    'AuditSeverity',
    'AuditCategory',
    'get_event_category',
    'get_event_severity',
    'is_security_event',
    'is_admin_operation',
    
    # 审计记录
    'AuditRecord',
    'create_audit_record',
    
    # 审计服务和便捷函数
    'AuditService',
    'audit_service',
    'log_event',
    'query_logs',
    'get_log_by_id',
    
    # 存储相关
    'StorageBackend',
    'AuditStorageManager',
    
    # API路由
    'audit_router'
] 
