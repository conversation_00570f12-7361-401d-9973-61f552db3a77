"""
从服务器数据模型定义
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class DeviceStatus(BaseModel):
    """Represents the status of a single physical USB device."""
    vendor_id: Optional[str]
    product_id: Optional[str]
    manufacturer: Optional[str]
    model: Optional[str]
    device_path: Optional[str] = Field(None, description="The unique device path provided by the OS, if available.")

class VHInstanceStatus(BaseModel):
    """Represents the status of a single VirtualHere instance."""
    device_id: str = Field(..., description="The internal ID used to track the device being shared.")
    port: int
    pid: int

class SlaveStatusReport(BaseModel):
    """The complete status report sent by the slave server."""
    slave_id: str # This would be a unique identifier for the slave server itself
    devices: List[DeviceStatus]
    vh_instances: List[VHInstanceStatus]

class SlaveServerRegister(BaseModel):
    """从服务器注册模型"""
    server_id: str = Field(..., description="从服务器唯一ID")
    name: str = Field(..., description="从服务器名称")
    description: Optional[str] = Field(None, description="从服务器描述")
    ip_address: str = Field(..., description="从服务器IP地址")
    port: int = Field(..., description="从服务器端口")
    status: str = Field(default="online", description="从服务器状态")
    version: str = Field(..., description="从服务器版本")

class DeviceInfo(BaseModel):
    """设备信息模型"""
    device_path: str = Field(..., description="设备路径（VirtualHere地址）")
    model: str = Field(..., description="设备型号")
    vendor_id: Optional[str] = Field(None, description="厂商ID")
    product_id: Optional[str] = Field(None, description="产品ID")
    manufacturer: Optional[str] = Field(None, description="制造商")
    description: str = Field(..., description="设备描述")
    status: str = Field(default="available", description="设备状态")
    added_at: Optional[str] = Field(None, description="设备添加时间")

class HeartbeatData(BaseModel):
    """心跳数据模型"""
    slave_id: str = Field(..., description="从服务器ID")
    timestamp: str = Field(..., description="时间戳")
    status: str = Field(..., description="服务器状态")
    device_count: int = Field(..., description="设备数量")
    load_average: float = Field(..., description="系统负载")
    memory_usage: float = Field(..., description="内存使用率")

class CommandRequest(BaseModel):
    """命令请求模型"""
    action: str = Field(..., description="命令动作")
    device_path: Optional[str] = Field(None, description="设备路径")
    parameters: Optional[Dict[str, Any]] = Field(None, description="命令参数")
    command_id: Optional[str] = Field(None, description="命令ID")

class CommandResponse(BaseModel):
    """命令响应模型"""
    command_id: str = Field(..., description="命令ID")
    success: bool = Field(..., description="执行是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    timestamp: str = Field(..., description="响应时间戳")

class DeviceShareRequest(BaseModel):
    """设备共享请求模型"""
    device_path: str = Field(..., description="设备路径")
    user_id: Optional[int] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")

class HealthStatus(BaseModel):
    """健康状态模型"""
    status: str = Field(..., description="健康状态")
    slave_id: str = Field(..., description="从服务器ID")
    timestamp: str = Field(..., description="检查时间")
    services: Dict[str, bool] = Field(..., description="服务状态")
    uptime: Optional[float] = Field(None, description="运行时间（秒）")

class SystemInfo(BaseModel):
    """系统信息模型"""
    hostname: str = Field(..., description="主机名")
    platform: str = Field(..., description="平台信息")
    python_version: str = Field(..., description="Python版本")
    cpu_count: int = Field(..., description="CPU核心数")
    memory_total: float = Field(..., description="总内存（GB）")
    disk_usage: Dict[str, float] = Field(..., description="磁盘使用情况")

class VirtualHereStatus(BaseModel):
    """VirtualHere状态模型"""
    is_running: bool = Field(..., description="是否运行中")
    version: Optional[str] = Field(None, description="版本信息")
    port: int = Field(..., description="监听端口")
    shared_devices: List[str] = Field(default_factory=list, description="已共享设备列表")
    connection_count: int = Field(default=0, description="连接数量")
