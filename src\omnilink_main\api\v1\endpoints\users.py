from fastapi import APIRouter, Depends, status
from typing import Any, List
from sqlalchemy.ext.asyncio import AsyncSession

from src.omnilink_main.dependencies.db import get_db
from common.schemas.api_schema import APIResponse
from common.schemas.user_schema import User as UserSchema, UserCreate, UserUpdate
from common.schemas.role_schema import RoleAssignmentRequest, RoleRead
from src.omnilink_main.services.user_service import user_service_instance
from src.omnilink_main.services.role_service import role_service_instance
from src.omnilink_main.dependencies.auth import PermissionChecker

router = APIRouter()

@router.post("/", status_code=status.HTTP_201_CREATED, response_model=APIResponse[UserSchema], dependencies=[Depends(PermissionChecker('users.create'))])
async def create_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_in: UserCreate,
) -> APIResponse[UserSchema]:
    """
    Create a new user.
    (Permission: 'users.create')
    """
    db_user = await user_service_instance.create_new_user(db, user_in=user_in)
    return APIResponse(data=db_user)

@router.get("/", response_model=APIResponse[List[UserSchema]], dependencies=[Depends(PermissionChecker('users.view'))])
async def read_users(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
) -> APIResponse[List[UserSchema]]:
    """
    Retrieve users.
    (Permission: 'users.view')
    """
    users = await user_service_instance.get_all_users(db, skip=skip, limit=limit)
    return APIResponse(data=users)

@router.get("/{user_id}", response_model=APIResponse[UserSchema], dependencies=[Depends(PermissionChecker('users.view'))])
async def read_user_by_id(
    user_id: int,
    db: AsyncSession = Depends(get_db),
) -> APIResponse[UserSchema]:
    """
    Get a specific user by id.
    (Permission: 'users.view')
    """
    user = await user_service_instance.get_user_by_id(db, id=user_id)
    return APIResponse(data=user)

@router.put("/{user_id}", response_model=APIResponse[UserSchema], dependencies=[Depends(PermissionChecker('users.edit'))])
async def update_user(
    *,
    user_id: int,
    user_in: UserUpdate,
    db: AsyncSession = Depends(get_db),
) -> APIResponse[UserSchema]:
    """
    Update a user.
    (Permission: 'users.edit')
    """
    updated_user = await user_service_instance.update_existing_user(db, user_id=user_id, user_in=user_in)
    return APIResponse(data=updated_user)

@router.delete("/{user_id}", response_model=APIResponse[UserSchema], dependencies=[Depends(PermissionChecker('users.delete'))])
async def delete_user(
    *,
    user_id: int,
    db: AsyncSession = Depends(get_db),
) -> APIResponse[UserSchema]:
    """
    Delete a user.
    (Permission: 'users.delete')
    """
    deleted_user = await user_service_instance.delete_existing_user(db, id=user_id)
    return APIResponse(data=deleted_user)

# --- New endpoints for managing user roles ---

@router.post("/{user_id}/roles", response_model=APIResponse[UserSchema], dependencies=[Depends(PermissionChecker('users.edit.roles'))])
async def assign_role_to_user(
    user_id: int,
    role_request: RoleAssignmentRequest,
    db: AsyncSession = Depends(get_db),
) -> APIResponse[UserSchema]:
    """
    Assign a role to a specific user.
    (Permission: 'users.edit.roles')
    """
    updated_user = await role_service_instance.assign_role_to_user(db, user_id=user_id, role_id=role_request.role_id)
    return APIResponse(data=updated_user)

@router.delete("/{user_id}/roles/{role_id}", response_model=APIResponse[UserSchema], dependencies=[Depends(PermissionChecker('users.edit.roles'))])
async def revoke_role_from_user(
    user_id: int,
    role_id: int,
    db: AsyncSession = Depends(get_db),
) -> APIResponse[UserSchema]:
    """
    Revoke a role from a specific user.
    (Permission: 'users.edit.roles')
    """
    updated_user = await role_service_instance.revoke_role_from_user(db, user_id=user_id, role_id=role_id)
    return APIResponse(data=updated_user)