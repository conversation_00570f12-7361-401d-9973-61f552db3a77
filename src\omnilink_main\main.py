from fastapi import FastAP<PERSON>, Depends
from starlette.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from contextlib import asynccontextmanager
import logging
import uvicorn

from src.omnilink_main.api.v1.api import api_router
from src.omnilink_main.core.database import db_engine, Base, AsyncSessionLocal, init_db
from src.omnilink_main.core.services import device_filter_service
from common.models.device import Device
from common.models.slave_server import SlaveServer
from src.omnilink_main.core.config import settings
from src.omnilink_main.core.redis import RedisClient
from src.omnilink_main.communication.ws_manager import WebSocketManager
from src.omnilink_main.core import services
from src.omnilink_main.initial_data import init_initial_data
from src.omnilink_main.ui import main_ui

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局服务实例 (MOVED to core/services.py)
# device_filter_service = DeviceFilterService()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Handles startup and shutdown events.
    """
    logger.info("Application startup...")
    
    # Initialize services that need to start with the app
    app.state.ws_manager = WebSocketManager()
    logger.info("WebSocket Manager initialized.")
    
    # Initialize the database and create tables
    await init_db()
    logger.info("Database initialized.")

    # Populate initial data
    logger.info("Creating initial data...")
    await init_initial_data()
    logger.info("Initial data creation finished.")

    yield
    
    # --- Shutdown ---
    logger.info("Application shutdown...")
    await RedisClient.close_pool()
    logger.info("Redis connection pool closed.")


app = FastAPI(
    title="OmniLink Main Server",
    description="Main server for OmniLink device sharing platform.",
    version="1.0.0",
    lifespan=lifespan
)

# Set all CORS enabled origins
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

app.include_router(api_router, prefix=settings.API_V1_STR)

# Mount the NiceGUI UI to the FastAPI app
main_ui.init(app)
logger.info("NiceGUI UI has been mounted.")

# Optional: Add any other application-wide configurations or middleware here

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000) 