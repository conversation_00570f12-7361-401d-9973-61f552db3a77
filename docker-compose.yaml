version: '3.8'

# OmniLink全联通系统Docker Compose配置文件
# 此文件定义了系统的所有服务容器及其配置

services:
  # 数据库服务
  postgres-db:
    image: postgres:15-alpine
    container_name: omnilink_postgres_db
    env_file:
      - app.env
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./config/db_init_scripts:/docker-entrypoint-initdb.d
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-omnilink} -d ${POSTGRES_DB:-omnilink_db}"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - omnilink_network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: omnilink_redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - omnilink_network

  # 主服务器
  main-server:
    build:
      context: .
      dockerfile: deployment/dockerfiles/Dockerfile.main
    container_name: omnilink_main_server
    command: >
      sh -c "
        until python -c 'import socket; s=socket.socket(); s.settimeout(1); s.connect((\"postgres-db\", 5432)); s.close()' 2>/dev/null; do
          echo 'Waiting for PostgreSQL...'
          sleep 2
        done
        echo 'PostgreSQL is ready!'
        
        until python -c 'import socket; s=socket.socket(); s.settimeout(1); s.connect((\"redis\", 6379)); s.close()' 2>/dev/null; do
          echo 'Waiting for Redis...'
          sleep 2
        done
        echo 'Redis is ready!'
        
        echo 'Running database migrations...'
        alembic upgrade head
        
        echo 'Starting main server...'
        python -m src.omnilink_main.main
      "
    env_file:
      - app.env
    volumes:
      - ./uploads:/app/uploads
      - ./config:/app/config
    ports:
      - "8000:8000"
    depends_on:
      postgres-db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - omnilink_network

  # 从服务器
  slave-server:
    build:
      context: .
      dockerfile: deployment/dockerfiles/Dockerfile.slave
    container_name: omnilink_slave_server
    volumes:
      - /dev:/dev  # 挂载设备目录
      - /sys:/sys  # 挂载系统目录
    ports:
      - "7575:7575" # VirtualHere
      - "8001:8001" # Slave Server API
    privileged: true
    restart: unless-stopped
    environment:
      - SLAVE_ID=slave-001
      - MAIN_SERVER_URL=http://main-server:8000
      - MAIN_SERVER_API_KEY=dev-api-key-12345
      - VIRTUALHERE_HOST=127.0.0.1
      - VIRTUALHERE_PORT=7575
      - SLAVE_SERVER_PORT=8001
      - LOG_LEVEL=INFO
    depends_on:
      - main-server
    networks:
      - omnilink_network

  # Web UI 服务
  # web-ui:
  #   build:
  #     context: .
  #     dockerfile: deployment/dockerfiles/Dockerfile.web
  #   container_name: omnilink_web_ui
  #   ports:
  #     - "8080:80"
  #   depends_on:
  #     - main-server
  #   restart: unless-stopped
  #   networks:
  #     - omnilink_network

networks:
  omnilink_network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  # redis_data:
  # config_data:
  logs_data: # 日志文件 