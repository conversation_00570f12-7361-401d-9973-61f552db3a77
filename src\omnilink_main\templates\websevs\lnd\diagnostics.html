<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统诊断 - 从服务器</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .diagnostic-container {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .diagnostic-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1.5rem;
        }
        
        .diagnostic-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--light-color);
        }
        
        .diagnostic-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin: 0;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .diagnostic-title i {
            font-size: 1.5rem;
        }
        
        .diagnostic-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-tertiary {
            background-color: var(--tertiary-color);
            color: var(--dark-color);
        }
        
        .status-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .status-item {
            padding: 1rem;
            background-color: var(--light-color);
            border-radius: var(--border-radius);
            display: flex;
            flex-direction: column;
        }
        
        .status-label {
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: var(--dark-color);
        }
        
        .status-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .status-info {
            font-size: 0.85rem;
            color: var(--gray-color);
            margin-top: 0.25rem;
        }
        
        .good {
            color: var(--secondary-color);
        }
        
        .warning {
            color: var(--tertiary-color);
        }
        
        .error {
            color: var(--danger-color);
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
            font-size: 0.85rem;
            font-weight: bold;
        }
        
        .badge-good {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .badge-warning {
            background-color: var(--tertiary-color);
            color: var(--dark-color);
        }
        
        .badge-error {
            background-color: var(--danger-color);
            color: white;
        }
        
        .resource-chart {
            width: 100%;
            height: 300px;
            margin: 1.5rem 0;
            position: relative;
        }
        
        .chart-placeholder {
            background-color: var(--light-color);
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius);
            color: var(--gray-color);
        }
        
        .log-container {
            margin-top: 1rem;
            background-color: var(--light-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            font-family: monospace;
            color: var(--dark-color);
            height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
            line-height: 1.4;
            white-space: pre-wrap;
        }
        
        .network-tests {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .test-item {
            background-color: var(--light-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .test-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .test-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .test-desc {
            font-size: 0.85rem;
            color: var(--gray-color);
            margin-bottom: 1rem;
        }
        
        .device-diagnostics {
            margin-top: 1rem;
        }
        
        .diagnostic-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .diagnostic-table th, .diagnostic-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--light-color);
        }
        
        .diagnostic-table th {
            font-weight: bold;
            background-color: var(--light-color);
        }
        
        .diagnostic-table tr:hover {
            background-color: rgba(0,0,0,0.02);
        }
        
        .progress-container {
            width: 100%;
            height: 8px;
            background-color: var(--light-color);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.5rem;
        }
        
        .progress-bar {
            height: 100%;
            border-radius: 4px;
        }
        
        .test-loader {
            border: 3px solid var(--light-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
            display: none;
        }
        
        .running .test-loader {
            display: block;
        }
        
        .test-result {
            font-weight: bold;
            margin-top: 0.5rem;
        }
        
        .running .test-result {
            display: none;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <header>
        <h1>系统诊断</h1>
        <nav>
            <ul>
                <li><a href="{{ url_for('websevs.lnd.index') }}">首页</a></li>
                <li><a href="{{ url_for('websevs.lnd.dashboard') }}">仪表盘</a></li>
                <li><a href="{{ url_for('websevs.lnd.devices') }}">设备管理</a></li>
                <li><a href="{{ url_for('websevs.lnd.settings') }}">系统设置</a></li>
                <li><a href="{{ url_for('websevs.lnd.diagnostics') }}">系统诊断</a></li>
                <li><a href="{{ url_for('websevs.lnd.virtualhere') }}">VirtualHere</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <div class="diagnostic-container">
            <!-- 系统状态卡片 -->
            <div class="diagnostic-card">
                <div class="diagnostic-header">
                    <h2 class="diagnostic-title">🖥️ 系统状态</h2>
                    <div class="diagnostic-actions">
                        <button class="btn btn-primary" id="refreshSystemBtn">刷新状态</button>
                    </div>
                </div>
                
                <div class="status-list">
                    <div class="status-item">
                        <div class="status-label">系统运行时间</div>
                        <div class="status-value">7天5小时</div>
                        <div class="status-info">上次重启: 2023-07-08 10:15</div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">CPU使用率</div>
                        <div class="status-value good">35%</div>
                        <div class="status-info">4核心 Intel i5-8400</div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">内存使用率</div>
                        <div class="status-value warning">65%</div>
                        <div class="status-info">使用: 5.2GB / 总计: 8GB</div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">磁盘使用率</div>
                        <div class="status-value good">40%</div>
                        <div class="status-info">使用: 80GB / 总计: 200GB</div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">服务状态</div>
                        <div class="status-value good">正常运行</div>
                        <div class="status-info">所有服务组件正常</div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">连接设备数</div>
                        <div class="status-value">8个</div>
                        <div class="status-info">在线: 7 / 共享: 6</div>
                    </div>
                </div>
                
                <div class="resource-chart">
                    <div class="chart-placeholder">
                        [资源使用率历史图表]
                    </div>
                </div>
            </div>
            
            <!-- 网络诊断卡片 -->
            <div class="diagnostic-card">
                <div class="diagnostic-header">
                    <h2 class="diagnostic-title">🌐 网络诊断</h2>
                    <div class="diagnostic-actions">
                        <button class="btn btn-primary" id="runAllTestsBtn">运行所有测试</button>
                    </div>
                </div>
                
                <div class="status-list">
                    <div class="status-item">
                        <div class="status-label">网络连接</div>
                        <div class="status-value good">已连接</div>
                        <div class="status-info">有线连接 (1Gbps)</div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">IP地址</div>
                        <div class="status-value">***********01</div>
                        <div class="status-info">MAC: 00:1A:2B:3C:4D:5E</div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">网关连接</div>
                        <div class="status-value good">正常</div>
                        <div class="status-info">*********** (1ms)</div>
                    </div>
                    
                    <div class="status-item">
                        <div class="status-label">主服务器连接</div>
                        <div class="status-value good">正常</div>
                        <div class="status-info">***********00 (2ms)</div>
                    </div>
                </div>
                
                <div class="network-tests">
                    <div class="test-item" id="pingTest">
                        <div class="test-icon">📡</div>
                        <div class="test-title">网络延迟测试</div>
                        <div class="test-desc">测试与主服务器和互联网的网络延迟</div>
                        <button class="btn btn-tertiary run-test-btn">运行测试</button>
                        <div class="test-loader"></div>
                        <div class="test-result good">正常 (平均延迟: 15ms)</div>
                    </div>
                    
                    <div class="test-item" id="bandwidthTest">
                        <div class="test-icon">⚡</div>
                        <div class="test-title">网络带宽测试</div>
                        <div class="test-desc">测试网络上传和下载速度</div>
                        <button class="btn btn-tertiary run-test-btn">运行测试</button>
                        <div class="test-loader"></div>
                        <div class="test-result good">下载: 95Mbps / 上传: 45Mbps</div>
                    </div>
                    
                    <div class="test-item" id="dnsTest">
                        <div class="test-icon">🔍</div>
                        <div class="test-title">DNS解析测试</div>
                        <div class="test-desc">测试DNS服务器响应时间和可靠性</div>
                        <button class="btn btn-tertiary run-test-btn">运行测试</button>
                        <div class="test-loader"></div>
                        <div class="test-result good">正常 (响应时间: 23ms)</div>
                    </div>
                    
                    <div class="test-item" id="connectivityTest">
                        <div class="test-icon">🔄</div>
                        <div class="test-title">服务连通性测试</div>
                        <div class="test-desc">测试与主服务器和VirtualHere的连通性</div>
                        <button class="btn btn-tertiary run-test-btn">运行测试</button>
                        <div class="test-loader"></div>
                        <div class="test-result good">主服务器和VirtualHere连通性正常</div>
                    </div>
                </div>
            </div>
            
            <!-- 设备诊断卡片 -->
            <div class="diagnostic-card">
                <div class="diagnostic-header">
                    <h2 class="diagnostic-title">🔌 设备诊断</h2>
                    <div class="diagnostic-actions">
                        <button class="btn btn-primary" id="refreshDevicesBtn">刷新设备</button>
                        <button class="btn btn-secondary" id="diagAllDevicesBtn">诊断所有设备</button>
                    </div>
                </div>
                
                <div class="device-diagnostics">
                    <table class="diagnostic-table">
                        <thead>
                            <tr>
                                <th>设备名称</th>
                                <th>状态</th>
                                <th>连接类型</th>
                                <th>通信延迟</th>
                                <th>共享状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>HP LaserJet Pro MFP M428fdw</td>
                                <td><span class="status-badge badge-good">正常</span></td>
                                <td>USB 2.0</td>
                                <td>5ms</td>
                                <td>已共享 (2个客户端)</td>
                                <td><button class="btn btn-tertiary">诊断</button></td>
                            </tr>
                            <tr>
                                <td>罗技 C920 HD Pro</td>
                                <td><span class="status-badge badge-good">正常</span></td>
                                <td>USB 3.0</td>
                                <td>3ms</td>
                                <td>已共享 (1个客户端)</td>
                                <td><button class="btn btn-tertiary">诊断</button></td>
                            </tr>
                            <tr>
                                <td>Focusrite Scarlett 2i2</td>
                                <td><span class="status-badge badge-warning">不稳定</span></td>
                                <td>USB 2.0</td>
                                <td>12ms</td>
                                <td>已共享 (0个客户端)</td>
                                <td><button class="btn btn-tertiary">诊断</button></td>
                            </tr>
                            <tr>
                                <td>西部数据 4TB 移动硬盘</td>
                                <td><span class="status-badge badge-good">正常</span></td>
                                <td>USB 3.0</td>
                                <td>2ms</td>
                                <td>已共享 (1个客户端)</td>
                                <td><button class="btn btn-tertiary">诊断</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 系统日志卡片 -->
            <div class="diagnostic-card">
                <div class="diagnostic-header">
                    <h2 class="diagnostic-title">📋 系统日志</h2>
                    <div class="diagnostic-actions">
                        <select class="form-control" id="logLevel">
                            <option value="all">所有日志</option>
                            <option value="error">错误</option>
                            <option value="warning">警告</option>
                            <option value="info" selected>信息</option>
                            <option value="debug">调试</option>
                        </select>
                        <button class="btn btn-primary" id="refreshLogsBtn">刷新日志</button>
                        <button class="btn btn-secondary" id="downloadLogsBtn">下载日志</button>
                    </div>
                </div>
                
                <div class="log-container" id="logOutput">
                    <div class="log-entry">[2023-07-15 15:30:45] [INFO] 系统启动完成，所有服务就绪</div>
                    <div class="log-entry">[2023-07-15 15:31:12] [INFO] 设备连接: HP LaserJet Pro MFP M428fdw (端口: USB001)</div>
                    <div class="log-entry">[2023-07-15 15:31:20] [INFO] 设备共享启动: HP LaserJet Pro MFP M428fdw</div>
                    <div class="log-entry">[2023-07-15 15:32:05] [INFO] 设备连接: 罗技 C920 HD Pro (端口: USB002)</div>
                    <div class="log-entry">[2023-07-15 15:32:10] [INFO] 设备共享启动: 罗技 C920 HD Pro</div>
                    <div class="log-entry">[2023-07-15 15:35:22] [WARNING] Focusrite Scarlett 2i2 设备通信不稳定，延迟波动较大</div>
                    <div class="log-entry">[2023-07-15 15:35:40] [INFO] 设备连接: Focusrite Scarlett 2i2 (端口: USB003)</div>
                    <div class="log-entry">[2023-07-15 15:35:45] [INFO] 设备共享启动: Focusrite Scarlett 2i2</div>
                    <div class="log-entry">[2023-07-15 15:40:10] [INFO] 设备连接: 西部数据 4TB 移动硬盘 (端口: USB004)</div>
                    <div class="log-entry">[2023-07-15 15:40:15] [INFO] 设备共享启动: 西部数据 4TB 移动硬盘</div>
                    <div class="log-entry">[2023-07-15 15:45:30] [INFO] 客户端连接到设备: HP LaserJet Pro MFP M428fdw (客户端IP: ************)</div>
                    <div class="log-entry">[2023-07-15 15:50:22] [INFO] 客户端连接到设备: 罗技 C920 HD Pro (客户端IP: ************)</div>
                    <div class="log-entry">[2023-07-15 16:05:17] [INFO] 客户端连接到设备: 西部数据 4TB 移动硬盘 (客户端IP: ************)</div>
                    <div class="log-entry">[2023-07-15 16:10:45] [WARNING] Focusrite Scarlett 2i2 设备通信超时，自动重试连接</div>
                    <div class="log-entry">[2023-07-15 16:10:50] [INFO] Focusrite Scarlett 2i2 设备连接恢复</div>
                    <div class="log-entry">[2023-07-15 16:15:30] [INFO] 客户端连接到设备: HP LaserJet Pro MFP M428fdw (客户端IP: ************)</div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <p>&copy; 2023 Web管理服务系统 - 版本 1.0.0</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 刷新系统状态按钮
            const refreshSystemBtn = document.getElementById('refreshSystemBtn');
            refreshSystemBtn.addEventListener('click', function() {
                alert('正在刷新系统状态...');
                // 这里实际应用中应该用AJAX请求获取最新系统状态
            });
            
            // 刷新设备状态按钮
            const refreshDevicesBtn = document.getElementById('refreshDevicesBtn');
            refreshDevicesBtn.addEventListener('click', function() {
                alert('正在刷新设备状态...');
                // 这里实际应用中应该用AJAX请求获取最新设备状态
            });
            
            // 诊断所有设备按钮
            const diagAllDevicesBtn = document.getElementById('diagAllDevicesBtn');
            diagAllDevicesBtn.addEventListener('click', function() {
                alert('正在诊断所有设备...');
                // 这里实际应用中应该用AJAX请求启动设备诊断
            });
            
            // 刷新日志按钮
            const refreshLogsBtn = document.getElementById('refreshLogsBtn');
            refreshLogsBtn.addEventListener('click', function() {
                alert('正在刷新系统日志...');
                // 这里实际应用中应该用AJAX请求获取最新日志
            });
            
            // 下载日志按钮
            const downloadLogsBtn = document.getElementById('downloadLogsBtn');
            downloadLogsBtn.addEventListener('click', function() {
                alert('正在准备日志下载...');
                // 这里实际应用中应该触发日志文件下载
            });
            
            // 运行单个网络测试
            const runTestBtns = document.querySelectorAll('.run-test-btn');
            runTestBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const testItem = this.closest('.test-item');
                    testItem.classList.add('running');
                    this.disabled = true;
                    
                    // 模拟测试过程
                    setTimeout(() => {
                        testItem.classList.remove('running');
                        this.disabled = false;
                    }, 2000);
                });
            });
            
            // 运行所有网络测试
            const runAllTestsBtn = document.getElementById('runAllTestsBtn');
            runAllTestsBtn.addEventListener('click', function() {
                const testItems = document.querySelectorAll('.test-item');
                
                testItems.forEach(item => {
                    item.classList.add('running');
                    item.querySelector('.run-test-btn').disabled = true;
                });
                
                // 模拟测试过程，逐个完成测试
                setTimeout(() => {
                    testItems[0].classList.remove('running');
                    testItems[0].querySelector('.run-test-btn').disabled = false;
                    
                    setTimeout(() => {
                        testItems[1].classList.remove('running');
                        testItems[1].querySelector('.run-test-btn').disabled = false;
                        
                        setTimeout(() => {
                            testItems[2].classList.remove('running');
                            testItems[2].querySelector('.run-test-btn').disabled = false;
                            
                            setTimeout(() => {
                                testItems[3].classList.remove('running');
                                testItems[3].querySelector('.run-test-btn').disabled = false;
                                
                                alert('所有网络测试完成!');
                            }, 500);
                        }, 500);
                    }, 500);
                }, 1000);
            });
            
            // 日志级别过滤
            const logLevel = document.getElementById('logLevel');
            logLevel.addEventListener('change', function() {
                alert('已切换日志级别至: ' + this.value);
                // 这里实际应用中应该更新显示的日志条目
            });
        });
    </script>
</body>
</html> 