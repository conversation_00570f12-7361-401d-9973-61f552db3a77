import requests
import json
import os

# --- Configuration ---
MAIN_SERVER_URL = "http://localhost:8000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "password"
SLAVE_ID_TO_CREATE = "test-slave-01"

def generate_key():
    """
    Logs into the main server, creates a new slave server record,
    and retrieves the generated API key.
    """
    session = requests.Session()

    # 1. <PERSON>gin to get JWT token
    login_url = f"{MAIN_SERVER_URL}/api/v1/auth/login"
    login_payload = {
        "username": ADMIN_USERNAME,
        "password": ADMIN_PASSWORD
    }
    login_headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    try:
        print(f"Attempting to log in as '{ADMIN_USERNAME}'...")
        login_response = session.post(login_url, data=login_payload, headers=login_headers, timeout=10)
        login_response.raise_for_status()
        token_data = login_response.json()
        access_token = token_data.get("access_token")
        if not access_token:
            print("Error: 'access_token' not found in login response.")
            return None
        print("Login successful.")
    except requests.exceptions.RequestException as e:
        print(f"Error logging in: {e}")
        print(f"Status Code: {e.response.status_code if e.response else 'N/A'}")
        print(f"Response Body: {e.response.text if e.response else 'N/A'}")
        return None

    # 2. Create a new slave server
    create_slave_url = f"{MAIN_SERVER_URL}/api/v1/slaves"
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    create_payload = {
        "server_id": SLAVE_ID_TO_CREATE,
        "name": f"Test Slave {SLAVE_ID_TO_CREATE}",
        "status": "offline"
    }
    try:
        print(f"Attempting to create slave server with ID '{SLAVE_ID_TO_CREATE}'...")
        create_response = session.post(create_slave_url, json=create_payload, headers=headers, timeout=10)
        
        # Handle cases where the slave already exists (400 or 422 depending on implementation)
        if create_response.status_code == 400 and "already exists" in create_response.text.lower():
             print(f"Slave with ID '{SLAVE_ID_TO_CREATE}' already exists. Attempting to fetch its info...")
             # If it already exists, we can't get the key from this endpoint.
             # This script is primarily for the *initial* creation.
             # A more advanced script would fetch the list and find it.
             # For now, we'll stop and ask the user to handle it.
             print("\n---\nPlease manually check the database or use a future admin UI to retrieve the key for the existing slave.")
             print("Alternatively, delete the existing record and re-run this script.")
             print("---\n")
             return None

        create_response.raise_for_status()
        slave_data = create_response.json()
        api_key = slave_data.get("api_key")

        if not api_key:
            print("Error: 'api_key' not found in create slave response.")
            print("Full response:", json.dumps(slave_data, indent=2))
            return None
        
        print("Slave server created successfully.")
        return api_key

    except requests.exceptions.RequestException as e:
        print(f"Error creating slave server: {e}")
        print(f"Status Code: {e.response.status_code if e.response else 'N/A'}")
        print(f"Response Body: {e.response.text if e.response else 'N/A'}")
        return None


if __name__ == "__main__":
    api_key = generate_key()
    if api_key:
        print("\n" + "="*50)
        print("🎉 Successfully generated API Key!")
        print(f"   Slave ID: {SLAVE_ID_TO_CREATE}")
        print(f"   API Key: {api_key}")
        print("="*50)
        print("\nUse this key in the MAIN_SERVER_API_KEY environment variable when starting the slave server.")
        print(f'Example: set MAIN_SERVER_API_KEY={api_key}')
    else:
        print("\n" + "!"*50)
        print("API Key generation failed. Please check the errors above.")
        print("!"*50) 