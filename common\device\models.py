"""
Device Models - Enhanced device metadata models and related utilities.

This module provides extended device models with rich metadata for USB devices,
supporting advanced device classification, identification, and management.
"""

from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Dict, List, Optional, Union, Tuple, Any
import datetime
import hashlib
import json
import uuid


class DeviceConnectionState(Enum):
    """Enum representing the possible connection states of a device."""
    DISCONNECTED = 0
    CONNECTED = 1
    CONNECTING = 2
    DISCONNECTING = 3
    ERROR = 4
    SUSPENDED = 5
    LOCKED = 6


class DeviceShareState(Enum):
    """Enum representing the possible sharing states of a device."""
    NOT_SHARED = 0
    SHARED = 1
    SHARED_AND_USED = 2
    PENDING = 3
    ERROR = 4


class DeviceType(Enum):
    """Enum representing device types."""
    UNKNOWN = 0
    HUB = auto()
    STORAGE = auto()
    CAMERA = auto()
    AUDIO = auto()
    HID = auto()
    PRINTER = auto()
    NETWORK = auto()
    BLUETOOTH = auto()
    SMART_CARD = auto()
    BIOMETRIC = auto()
    SERIAL = auto()
    CUSTOM = auto()
    # Add more specific device types as needed


class UsbSpeed(Enum):
    """Enum representing USB connection speeds."""
    UNKNOWN = 0
    LOW_SPEED = 1.5  # 1.5 Mbps (USB 1.0)
    FULL_SPEED = 12  # 12 Mbps (USB 1.1)
    HIGH_SPEED = 480  # 480 Mbps (USB 2.0)
    SUPER_SPEED = 5000  # 5 Gbps (USB 3.0)
    SUPER_SPEED_PLUS = 10000  # 10 Gbps (USB 3.1)
    SUPER_SPEED_PLUS_20 = 20000  # 20 Gbps (USB 3.2)


@dataclass
class UsbConfiguration:
    """USB configuration descriptor data."""
    config_id: int
    config_value: int
    max_power: int  # In milliamps
    self_powered: bool = False
    remote_wakeup: bool = False
    description: str = ""


@dataclass
class UsbInterface:
    """USB interface descriptor data."""
    interface_id: int
    interface_num: int
    alt_setting: int = 0
    interface_class: int = 0
    interface_subclass: int = 0
    interface_protocol: int = 0
    description: str = ""


@dataclass
class UsbEndpoint:
    """USB endpoint descriptor data."""
    endpoint_address: int
    attributes: int
    max_packet_size: int
    interval: int


@dataclass
class DeviceDescriptor:
    """Full USB device descriptor information."""
    usb_version: str = ""
    device_class: int = 0
    device_subclass: int = 0
    device_protocol: int = 0
    max_packet_size0: int = 0
    num_configurations: int = 0
    configurations: List[UsbConfiguration] = field(default_factory=list)
    interfaces: List[UsbInterface] = field(default_factory=list)
    endpoints: List[UsbEndpoint] = field(default_factory=list)


@dataclass
class DeviceMetadata:
    """Enhanced device metadata for better identification and management."""
    # Basic identification
    device_uuid: str  # Unique identifier for this device
    vendor_id: int  # USB vendor ID
    product_id: int  # USB product ID
    vendor_name: str  # Vendor name
    product_name: str  # Product name
    serial_number: Optional[str] = None  # Device serial number
    
    # Physical properties
    physical_address: str = ""  # Address on the bus (e.g., 1-2.3)
    connection_path: str = ""  # Full connection path (including hubs)
    hub_depth: int = 0  # How many hubs deep
    port_number: int = 0  # Port number on parent hub
    
    # Enhanced identification
    device_type: DeviceType = DeviceType.UNKNOWN
    device_subtype: str = ""  # More specific categorization
    device_class_name: str = ""  # Human-readable class name
    firmware_version: str = ""  # Device firmware version if available
    speed: UsbSpeed = UsbSpeed.UNKNOWN  # USB connection speed
    
    # Advanced properties
    descriptor: DeviceDescriptor = field(default_factory=DeviceDescriptor)  # Full USB descriptor
    capabilities: List[str] = field(default_factory=list)  # List of device capabilities
    compatible_drivers: List[str] = field(default_factory=list)  # List of compatible drivers
    icon_name: str = ""  # Icon to use for this device type
    
    # Hardware details (new)
    manufacturer_id: str = ""  # Manufacturer identifier if available
    hardware_revision: str = ""  # Hardware revision or version
    chip_model: str = ""  # Main chip model if available
    power_requirements: Dict[str, Any] = field(default_factory=dict)  # Power requirements (current, voltage)
    is_power_hungry: bool = False  # Flag for devices requiring more than standard power
    is_self_powered: bool = False  # Flag for self-powered devices
    physical_ports: Dict[str, Any] = field(default_factory=dict)  # Physical ports on the device
    
    # Compatibility information (new)
    compatibility_issues: List[str] = field(default_factory=list)  # Known compatibility issues
    compatible_os: List[str] = field(default_factory=list)  # Compatible operating systems
    driver_requirements: List[str] = field(default_factory=list)  # Required drivers
    conflict_devices: List[str] = field(default_factory=list)  # Devices known to conflict
    performance_impact: str = ""  # Impact on system performance
    bandwidth_usage: str = ""  # Typical bandwidth usage
    
    # User-defined properties
    friendly_name: str = ""  # User-defined name for the device
    description: str = ""  # User-defined description
    tags: List[str] = field(default_factory=list)  # User-defined tags
    custom_properties: Dict[str, str] = field(default_factory=dict)  # Custom properties
    
    # State tracking
    first_seen: datetime.datetime = field(default_factory=datetime.datetime.now)
    last_seen: datetime.datetime = field(default_factory=datetime.datetime.now)
    connection_count: int = 0  # Number of times connected
    total_connected_time: int = 0  # Total time connected in seconds
    
    # Usage patterns and history (new)
    last_connection_duration: int = 0  # Duration of last connection in seconds
    average_connection_duration: int = 0  # Average connection duration
    connection_history: List[Dict[str, Any]] = field(default_factory=list)  # Connection history (last 10)
    typical_usage_times: List[Dict[str, Any]] = field(default_factory=list)  # Typical usage time patterns
    usage_frequency: str = ""  # Usage frequency category (frequent, occasional, rare)
    last_users: List[str] = field(default_factory=list)  # Recent users (limited list)
    
    # Availability history (new)
    availability_history: List[Dict[str, Any]] = field(default_factory=list)  # Availability history (limited)
    last_error: str = ""  # Last error message
    last_error_time: Optional[datetime.datetime] = None  # Time of last error
    error_count: int = 0  # Number of errors encountered
    
    # Security and identification
    fingerprint: str = ""  # Device fingerprint for identification
    fingerprint_components: Dict[str, str] = field(default_factory=dict)  # Components used in fingerprint
    fingerprint_confidence: float = 0.0  # Confidence level in fingerprint (0.0-1.0)
    fingerprint_last_verified: Optional[datetime.datetime] = None  # When fingerprint was last verified
    is_verified: bool = False  # Whether device has been manually verified
    trust_level: int = 0  # Trust level (0-100)
    
    def __post_init__(self):
        # Generate device fingerprint if not provided
        if not self.fingerprint:
            self.generate_fingerprint()
    
    def generate_fingerprint(self) -> str:
        """Generate a unique fingerprint for the device based on its properties."""
        # Create a dictionary of identifying properties (primary factors)
        primary_factors = {
            "vendor_id": self.vendor_id,
            "product_id": self.product_id,
            "serial_number": self.serial_number or "",
        }
        
        # Secondary factors (add if available)
        secondary_factors = {}
        if self.physical_address:
            secondary_factors["physical_address"] = self.physical_address
        if self.firmware_version:
            secondary_factors["firmware_version"] = self.firmware_version
        if self.hardware_revision:
            secondary_factors["hardware_revision"] = self.hardware_revision
        if self.chip_model:
            secondary_factors["chip_model"] = self.chip_model
            
        # Create combined identifying data
        identifying_data = {**primary_factors, **secondary_factors}
        
        # Store fingerprint components for future verification
        self.fingerprint_components = {
            "primary": json.dumps(primary_factors, sort_keys=True),
            "secondary": json.dumps(secondary_factors, sort_keys=True)
        }
        
        # Set confidence based on available data
        if self.serial_number:
            self.fingerprint_confidence = 0.9  # High confidence with serial number
        elif len(secondary_factors) >= 2:
            self.fingerprint_confidence = 0.7  # Moderate confidence
        else:
            self.fingerprint_confidence = 0.5  # Low confidence
            
        # Convert to a stable JSON string and hash it
        data_str = json.dumps(identifying_data, sort_keys=True)
        self.fingerprint = hashlib.sha256(data_str.encode()).hexdigest()
        self.fingerprint_last_verified = datetime.datetime.now()
        
        return self.fingerprint
    
    def verify_fingerprint(self, device_data: Dict[str, Any]) -> Tuple[bool, float]:
        """
        Verify if the given device data matches this device fingerprint.
        
        Args:
            device_data: Dictionary with device data to verify
            
        Returns:
            Tuple[bool, float]: (is_match, confidence)
        """
        # Extract required fields
        primary_match = True
        secondary_match = True
        primary_factors = json.loads(self.fingerprint_components.get("primary", "{}"))
        secondary_factors = json.loads(self.fingerprint_components.get("secondary", "{}"))
        
        # Check primary factors (must all match)
        for key, value in primary_factors.items():
            if key not in device_data or str(device_data[key]) != str(value):
                primary_match = False
                break
                
        # Check secondary factors (calculate match percentage)
        secondary_match_count = 0
        secondary_total = len(secondary_factors)
        if secondary_total > 0:
            for key, value in secondary_factors.items():
                if key in device_data and str(device_data[key]) == str(value):
                    secondary_match_count += 1
            
            secondary_match = (secondary_match_count / secondary_total) > 0.5
            
        # Calculate confidence
        confidence = 0.0
        if primary_match:
            if secondary_total > 0:
                confidence = 0.6 + (0.4 * secondary_match_count / secondary_total)
            else:
                confidence = 0.6
        
        is_match = primary_match and (secondary_match or secondary_total == 0)
        
        return (is_match, confidence)
    
    def update_trust_level(self, verification_result: bool = None) -> int:
        """
        Update the device trust level based on verification and history.
        
        Args:
            verification_result: Optional explicit verification result
            
        Returns:
            int: Updated trust level (0-100)
        """
        # Start with base trust from fingerprint confidence
        base_trust = int(self.fingerprint_confidence * 60)
        
        # Add trust for connection history
        history_factor = min(20, self.connection_count)
        
        # Add trust for verified status
        verification_factor = 20 if self.is_verified else 0
        
        # Explicit verification can override
        if verification_result is not None:
            if verification_result:
                verification_factor = 20
                self.is_verified = True
            else:
                verification_factor = 0
                self.is_verified = False
        
        # Calculate trust level
        self.trust_level = min(100, base_trust + history_factor + verification_factor)
        
        return self.trust_level
    
    def add_connection_record(self, duration: int, user: Optional[str] = None) -> None:
        """
        Add a connection record to the device history.
        
        Args:
            duration: Connection duration in seconds
            user: User that used the device
        """
        # Create connection record
        record = {
            "start_time": (datetime.datetime.now() - datetime.timedelta(seconds=duration)).isoformat(),
            "end_time": datetime.datetime.now().isoformat(),
            "duration": duration,
            "user": user
        }
        
        # Update connection history (keep last 10)
        self.connection_history.append(record)
        if len(self.connection_history) > 10:
            self.connection_history = self.connection_history[-10:]
        
        # Update last connection duration
        self.last_connection_duration = duration
        
        # Update average duration
        total_duration = self.last_connection_duration
        for r in self.connection_history[:-1]:  # Exclude the one we just added
            total_duration += r.get("duration", 0)
        
        history_size = len(self.connection_history)
        if history_size > 0:
            self.average_connection_duration = total_duration // history_size
            
        # Update user history (if provided)
        if user and user not in self.last_users:
            self.last_users.append(user)
            if len(self.last_users) > 5:
                self.last_users = self.last_users[-5:]
                
        # Update usage frequency
        self._update_usage_frequency()
        
    def add_availability_record(self, is_available: bool, reason: str = "") -> None:
        """
        Add an availability record to the history.
        
        Args:
            is_available: Whether the device is available
            reason: Reason for availability change
        """
        record = {
            "timestamp": datetime.datetime.now().isoformat(),
            "is_available": is_available,
            "reason": reason
        }
        
        # Add to history (keep last 20)
        self.availability_history.append(record)
        if len(self.availability_history) > 20:
            self.availability_history = self.availability_history[-20:]
            
        # Update error tracking
        if not is_available and reason:
            self.last_error = reason
            self.last_error_time = datetime.datetime.now()
            self.error_count += 1
    
    def _update_usage_frequency(self) -> None:
        """Update the usage frequency category based on connection history."""
        now = datetime.datetime.now()
        one_week_ago = now - datetime.timedelta(days=7)
        
        # Count connections in the last week
        recent_connections = 0
        for record in self.connection_history:
            try:
                end_time = datetime.datetime.fromisoformat(record["end_time"])
                if end_time >= one_week_ago:
                    recent_connections += 1
            except (ValueError, KeyError):
                continue
                
        # Categorize frequency
        if recent_connections >= 5:
            self.usage_frequency = "frequent"
        elif recent_connections >= 2:
            self.usage_frequency = "regular"
        elif recent_connections >= 1:
            self.usage_frequency = "occasional"
        else:
            self.usage_frequency = "rare"
    
    def to_dict(self) -> Dict:
        """Convert the metadata to a dictionary."""
        result = {
            "device_uuid": self.device_uuid,
            "vendor_id": self.vendor_id,
            "product_id": self.product_id,
            "vendor_name": self.vendor_name,
            "product_name": self.product_name,
            "serial_number": self.serial_number,
            "physical_address": self.physical_address,
            "connection_path": self.connection_path,
            "hub_depth": self.hub_depth,
            "port_number": self.port_number,
            "device_type": self.device_type.name,
            "device_subtype": self.device_subtype,
            "device_class_name": self.device_class_name,
            "firmware_version": self.firmware_version,
            "speed": self.speed.name,
            "friendly_name": self.friendly_name,
            "description": self.description,
            "tags": self.tags,
            "custom_properties": self.custom_properties,
            "first_seen": self.first_seen.isoformat(),
            "last_seen": self.last_seen.isoformat(),
            "connection_count": self.connection_count,
            "total_connected_time": self.total_connected_time,
            "fingerprint": self.fingerprint,
            
            # New fields
            "manufacturer_id": self.manufacturer_id,
            "hardware_revision": self.hardware_revision,
            "chip_model": self.chip_model,
            "power_requirements": self.power_requirements,
            "is_power_hungry": self.is_power_hungry,
            "is_self_powered": self.is_self_powered,
            "compatibility_issues": self.compatibility_issues,
            "compatible_os": self.compatible_os,
            "last_connection_duration": self.last_connection_duration,
            "average_connection_duration": self.average_connection_duration,
            "usage_frequency": self.usage_frequency,
            "trust_level": self.trust_level,
            "is_verified": self.is_verified,
            "error_count": self.error_count,
            "last_error": self.last_error,
            
            # Complex objects
            "connection_history": self.connection_history,
            "availability_history": self.availability_history,
            "fingerprint_components": self.fingerprint_components,
            "fingerprint_confidence": self.fingerprint_confidence,
        }
        
        # Add datetime objects properly
        if self.fingerprint_last_verified:
            result["fingerprint_last_verified"] = self.fingerprint_last_verified.isoformat()
            
        if self.last_error_time:
            result["last_error_time"] = self.last_error_time.isoformat()
            
        return result
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'DeviceMetadata':
        """Create a DeviceMetadata instance from a dictionary."""
        # Create a copy of the data to avoid modifying the original
        data_copy = data.copy()
        
        # Convert device_type from string to enum
        if "device_type" in data_copy and isinstance(data_copy["device_type"], str):
            try:
                data_copy["device_type"] = DeviceType[data_copy["device_type"]]
            except KeyError:
                data_copy["device_type"] = DeviceType.UNKNOWN
        
        # Convert speed from string to enum
        if "speed" in data_copy and isinstance(data_copy["speed"], str):
            try:
                data_copy["speed"] = UsbSpeed[data_copy["speed"]]
            except KeyError:
                data_copy["speed"] = UsbSpeed.UNKNOWN
        
        # Convert datetime strings to datetime objects
        for dt_field in ["first_seen", "last_seen", "fingerprint_last_verified", "last_error_time"]:
            if dt_field in data_copy and isinstance(data_copy[dt_field], str):
                try:
                    data_copy[dt_field] = datetime.datetime.fromisoformat(data_copy[dt_field])
                except ValueError:
                    if dt_field in ["first_seen", "last_seen"]:
                        data_copy[dt_field] = datetime.datetime.now()
                    else:
                        data_copy[dt_field] = None
        
        # Create and return the instance
        return cls(**data_copy)
    
    def update_last_seen(self):
        """Update the last_seen timestamp to current time."""
        self.last_seen = datetime.datetime.now()
    
    def increment_connection_count(self):
        """Increment the connection count."""
        self.connection_count += 1
    
    def update_connected_time(self, seconds: int):
        """Add to the total connected time."""
        self.total_connected_time += seconds


@dataclass
class DeviceState:
    """Current state of a device including connection and sharing status."""
    device_uuid: str
    connection_state: DeviceConnectionState = DeviceConnectionState.DISCONNECTED
    share_state: DeviceShareState = DeviceShareState.NOT_SHARED
    current_user: Optional[str] = None  # User currently using the device
    locked_by: Optional[str] = None  # User or process that has locked the device
    connection_time: Optional[datetime.datetime] = None  # When the current connection was established
    last_status_change: datetime.datetime = field(default_factory=datetime.datetime.now)
    health_score: int = 100  # 0-100 score indicating device health
    is_online: bool = False
    error_message: str = ""
    warning_message: str = ""
    
    def to_dict(self) -> Dict:
        """Convert the state to a dictionary."""
        return {
            "device_uuid": self.device_uuid,
            "connection_state": self.connection_state.name,
            "share_state": self.share_state.name,
            "current_user": self.current_user,
            "locked_by": self.locked_by,
            "connection_time": self.connection_time.isoformat() if self.connection_time else None,
            "last_status_change": self.last_status_change.isoformat(),
            "health_score": self.health_score,
            "is_online": self.is_online,
            "error_message": self.error_message,
            "warning_message": self.warning_message,
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'DeviceState':
        """Create a DeviceState instance from a dictionary."""
        # Convert connection_state from string to enum
        if "connection_state" in data and isinstance(data["connection_state"], str):
            try:
                data["connection_state"] = DeviceConnectionState[data["connection_state"]]
            except KeyError:
                data["connection_state"] = DeviceConnectionState.DISCONNECTED
        
        # Convert share_state from string to enum
        if "share_state" in data and isinstance(data["share_state"], str):
            try:
                data["share_state"] = DeviceShareState[data["share_state"]]
            except KeyError:
                data["share_state"] = DeviceShareState.NOT_SHARED
        
        # Convert datetime strings to datetime objects
        if "connection_time" in data and data["connection_time"] and isinstance(data["connection_time"], str):
            data["connection_time"] = datetime.datetime.fromisoformat(data["connection_time"])
        
        if "last_status_change" in data and isinstance(data["last_status_change"], str):
            data["last_status_change"] = datetime.datetime.fromisoformat(data["last_status_change"])
        
        # Create and return the instance
        return cls(**data)
    
    def update_connection_state(self, new_state: DeviceConnectionState, error_message: str = ""):
        """Update the connection state and last status change time."""
        self.connection_state = new_state
        self.last_status_change = datetime.datetime.now()
        if new_state == DeviceConnectionState.ERROR:
            self.error_message = error_message
        elif new_state == DeviceConnectionState.CONNECTED:
            self.connection_time = datetime.datetime.now()
            self.is_online = True
            self.error_message = ""
        elif new_state == DeviceConnectionState.DISCONNECTED:
            self.connection_time = None
            self.is_online = False
    
    def update_share_state(self, new_state: DeviceShareState, user: Optional[str] = None):
        """Update the share state and current user."""
        self.share_state = new_state
        self.last_status_change = datetime.datetime.now()
        
        if new_state == DeviceShareState.SHARED_AND_USED:
            self.current_user = user
        elif new_state == DeviceShareState.NOT_SHARED:
            self.current_user = None


@dataclass
class Device:
    """Complete device representation combining metadata and state."""
    metadata: DeviceMetadata
    state: DeviceState
    
    @property
    def device_uuid(self) -> str:
        """Get the device UUID."""
        return self.metadata.device_uuid
    
    def to_dict(self) -> Dict:
        """Convert the device to a dictionary."""
        return {
            "metadata": self.metadata.to_dict(),
            "state": self.state.to_dict(),
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Device':
        """Create a Device instance from a dictionary."""
        metadata = DeviceMetadata.from_dict(data["metadata"])
        state = DeviceState.from_dict(data["state"])
        return cls(metadata=metadata, state=state)
    
    @classmethod
    def create_new(cls, 
                  vendor_id: int, 
                  product_id: int, 
                  vendor_name: str, 
                  product_name: str,
                  serial_number: Optional[str] = None,
                  physical_address: str = "") -> 'Device':
        """Create a new device with basic information."""
        device_uuid = str(uuid.uuid4())
        metadata = DeviceMetadata(
            device_uuid=device_uuid,
            vendor_id=vendor_id,
            product_id=product_id,
            vendor_name=vendor_name,
            product_name=product_name,
            serial_number=serial_number,
            physical_address=physical_address
        )
        state = DeviceState(device_uuid=device_uuid)
        return cls(metadata=metadata, state=state) 
