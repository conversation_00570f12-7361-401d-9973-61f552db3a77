<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>告警设置 - 统一管理控制台</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin_console.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边导航栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="px-3 py-4 text-white">
                        <h5>主从服务器管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.index') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.group_management') }}">
                                <i class="fas fa-server me-2"></i>分组管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.migration_wizard') }}">
                                <i class="fas fa-exchange-alt me-2"></i>迁移向导
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('admin_console.alerts') }}">
                                <i class="fas fa-bell me-2"></i>告警设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.health_monitor') }}">
                                <i class="fas fa-heartbeat me-2"></i>健康监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" id="helpBtn">
                                <i class="fas fa-question-circle me-2"></i>帮助
                            </a>
                        </li>
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>系统</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.system_settings') }}">
                                <i class="fas fa-cog me-2"></i>系统设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>退出
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">告警设置</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshBtn">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="createAlertBtn">
                                <i class="fas fa-plus"></i> 创建告警规则
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 告警概览 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-white bg-danger mb-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-0">严重告警</h6>
                                        <h2 class="my-2" id="criticalAlertCount">0</h2>
                                        <p class="card-text mb-0">需要立即处理</p>
                                    </div>
                                    <i class="fas fa-exclamation-circle fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-warning mb-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-0">警告</h6>
                                        <h2 class="my-2" id="warningAlertCount">0</h2>
                                        <p class="card-text mb-0">需要关注</p>
                                    </div>
                                    <i class="fas fa-exclamation-triangle fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-info mb-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-0">通知</h6>
                                        <h2 class="my-2" id="infoAlertCount">0</h2>
                                        <p class="card-text mb-0">一般提示</p>
                                    </div>
                                    <i class="fas fa-info-circle fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light mb-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-0">总告警规则</h6>
                                        <h2 class="my-2" id="totalAlertRulesCount">0</h2>
                                        <p class="card-text mb-0">已配置规则</p>
                                    </div>
                                    <i class="fas fa-cogs fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 告警规则列表 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-list me-2"></i>告警规则列表
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th>规则名称</th>
                                        <th>类型</th>
                                        <th>触发条件</th>
                                        <th>严重级别</th>
                                        <th>通知方式</th>
                                        <th>状态</th>
                                        <th>最后触发</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="alertRulesList">
                                    <tr>
                                        <td colspan="8" class="text-center">暂无告警规则</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 历史告警 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-history me-2"></i>最近告警
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>告警内容</th>
                                        <th>级别</th>
                                        <th>来源</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="recentAlertsList">
                                    <tr>
                                        <td colspan="6" class="text-center">暂无告警记录</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-end mt-2">
                            <a href="{{ url_for('admin_console.alert_history') }}" class="btn btn-outline-secondary btn-sm">查看更多</a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 创建告警规则模态框 -->
    <div class="modal fade" id="createAlertModal" tabindex="-1" aria-labelledby="createAlertModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createAlertModalLabel">创建告警规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createAlertForm">
                        <div class="mb-3">
                            <label for="alertName" class="form-label">规则名称</label>
                            <input type="text" class="form-control" id="alertName" required placeholder="请输入告警规则名称">
                        </div>
                        
                        <div class="mb-3">
                            <label for="alertType" class="form-label">监控类型</label>
                            <select class="form-select" id="alertType" required>
                                <option value="" selected disabled>请选择监控类型</option>
                                <option value="server_health">服务器健康状态</option>
                                <option value="resource_usage">资源使用情况</option>
                                <option value="connection_status">连接状态</option>
                                <option value="device_availability">设备可用性</option>
                                <option value="performance">系统性能</option>
                                <option value="custom">自定义指标</option>
                            </select>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="metricName" class="form-label">监控指标</label>
                                <select class="form-select" id="metricName" required>
                                    <option value="" selected disabled>请先选择监控类型</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="conditionOperator" class="form-label">条件运算符</label>
                                <select class="form-select" id="conditionOperator" required>
                                    <option value="gt">大于 (>)</option>
                                    <option value="gte">大于等于 (>=)</option>
                                    <option value="lt">小于 (&lt;)</option>
                                    <option value="lte">小于等于 (&lt;=)</option>
                                    <option value="eq">等于 (=)</option>
                                    <option value="neq">不等于 (!=)</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="thresholdValue" class="form-label">阈值</label>
                                <input type="number" class="form-control" id="thresholdValue" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="alertSeverity" class="form-label">严重级别</label>
                            <select class="form-select" id="alertSeverity" required>
                                <option value="critical">严重</option>
                                <option value="warning" selected>警告</option>
                                <option value="info">通知</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">应用范围</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="alertScope" id="alertScopeAll" value="all" checked>
                                <label class="form-check-label" for="alertScopeAll">
                                    所有服务器
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="alertScope" id="alertScopeGroup" value="group">
                                <label class="form-check-label" for="alertScopeGroup">
                                    特定分组
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="alertScope" id="alertScopeServer" value="server">
                                <label class="form-check-label" for="alertScopeServer">
                                    特定服务器
                                </label>
                            </div>
                            <div id="alertScopeSelector" class="mt-2" style="display: none;">
                                <select class="form-select" id="scopeSelection" multiple>
                                    <option disabled>加载中...</option>
                                </select>
                                <div class="form-text">按住Ctrl键可多选</div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">通知方式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="notifyDashboard" checked>
                                <label class="form-check-label" for="notifyDashboard">
                                    系统仪表盘
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="notifyEmail">
                                <label class="form-check-label" for="notifyEmail">
                                    电子邮件
                                </label>
                            </div>
                            <div id="emailRecipients" class="mt-2" style="display: none;">
                                <input type="text" class="form-control" id="emailRecipientsInput" placeholder="多个邮箱使用逗号分隔">
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="notifySMS">
                                <label class="form-check-label" for="notifySMS">
                                    短信
                                </label>
                            </div>
                            <div id="phoneNumbers" class="mt-2" style="display: none;">
                                <input type="text" class="form-control" id="phoneNumbersInput" placeholder="多个手机号使用逗号分隔">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="alertDescription" class="form-label">规则描述</label>
                            <textarea class="form-control" id="alertDescription" rows="3" placeholder="请输入告警规则的详细描述"></textarea>
                        </div>
                        
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="alertEnabled" checked>
                            <label class="form-check-label" for="alertEnabled">启用该规则</label>
                        </div>
                        
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="autoResolve">
                            <label class="form-check-label" for="autoResolve">当条件恢复正常时自动解除告警</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveAlertBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/admin_console.js') }}"></script>
</body>
</html> 