import logging
from typing import List, Optional
from pydantic import AnyHttpUrl, model_validator, PostgresDsn
from pydantic_settings import BaseSettings, SettingsConfigDict

class AppSettings(BaseSettings):
    """
    Application settings, loaded from environment variables or .env file.
    """
    model_config = SettingsConfigDict(
        env_file="app.env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore"
    )

    # Core settings
    PROJECT_NAME: str = "OmniLink"
    SECRET_KEY: str = "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7"
    API_V1_STR: str = "/api/v1"
    SERVER_URL: str = "http://127.0.0.1:8000"

    # Database settings - Assembled from components
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None
    
    POSTGRES_SERVER: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DB: str
    POSTGRES_PORT: int = 5432

    DB_ECHO_LOG: bool = False

    @model_validator(mode="before")
    @classmethod
    def assemble_db_connection(cls, v):
        if isinstance(v, dict) and "SQLALCHEMY_DATABASE_URI" not in v:
            port = v.get("POSTGRES_PORT")
            # Ensure port is an integer
            try:
                port = int(port) if port is not None else 5432
            except (ValueError, TypeError):
                port = 5432

            v["SQLALCHEMY_DATABASE_URI"] = PostgresDsn.build(
                scheme="postgresql+asyncpg",
                username=v.get("POSTGRES_USER"),
                password=v.get("POSTGRES_PASSWORD"),
                host=v.get("POSTGRES_SERVER"),
                port=port,
                path=f"{v.get('POSTGRES_DB') or ''}",
            )
        return v

    # Redis settings
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_URL: Optional[str] = None

    @model_validator(mode="before")
    @classmethod
    def assemble_redis_url(cls, v):
        if isinstance(v, dict) and "REDIS_URL" not in v:
            v["REDIS_URL"] = f"redis://{v.get('REDIS_HOST', 'localhost')}:{v.get('REDIS_PORT', 6379)}"
        return v

    # Token settings
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    REFRESH_TOKEN_EXPIRE_DAYS: int = 30  # Refresh tokens are valid for 30 days
    JWT_ALGORITHM: str = "HS256"
    
    # First superuser
    FIRST_SUPERUSER: str = "admin"
    FIRST_SUPERUSER_EMAIL: str = "<EMAIL>"
    FIRST_SUPERUSER_PASSWORD: str = "password"

    # CORS settings
    BACKEND_CORS_ORIGINS: List[str] = ["*"]

    @model_validator(mode="before")
    @classmethod
    def assemble_cors_origins(cls, values):
        if "BACKEND_CORS_ORIGINS" in values:
            cors_origins = values.get("BACKEND_CORS_ORIGINS")
            if isinstance(cors_origins, str):
                values["BACKEND_CORS_ORIGINS"] = [
                    item.strip() for item in cors_origins.split(",") if item.strip()
                ]
        return values

# --- Logging Configuration ---
logging.basicConfig(level=logging.INFO)
logging.getLogger("uvicorn.access").setLevel(logging.INFO)
logging.getLogger("uvicorn.error").setLevel(logging.INFO)
logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)


# --- Application Settings Instance ---
settings = AppSettings()


