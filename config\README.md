# OmniLink全联通系统配置说明

## 1. 配置文件结构

OmniLink全联通系统采用分层配置结构，配置文件按照以下优先级加载：

1. 环境变量 (最高优先级)
2. 命令行参数
3. 用户配置文件 (`config/user_config.yaml`)
4. 系统配置文件 (`config/system_config.yaml`)
5. 默认配置 (代码内置，最低优先级)

## 2. 配置目录说明

本目录(`ky/config/`)包含所有OmniLink系统配置文件：

```
ky/config/
├── system_config.yaml     # 系统基础配置，不建议直接修改
├── user_config.yaml       # 用户自定义配置，可覆盖系统配置
├── logging_config.yaml    # 日志配置
├── ssl/                   # SSL证书和密钥
│   ├── server.crt         # 服务器证书
│   └── server.key         # 服务器私钥
├── keys/                  # 敏感凭据和密钥
│   ├── sqlpsd.json        # 数据库连接凭据
│   ├── mansever.json      # 主服务器认证信息
│   └── tokens/            # 各类令牌和密钥文件
└── templates/             # 配置模板
    ├── main_server.yaml   # 主服务器配置模板
    ├── slave_server.yaml  # 从服务器配置模板
    └── web_server.yaml    # Web服务器配置模板
```

## 3. 重要配置项

### 3.1 数据库配置 (`sqlpsd.json`)

```json
{
  "db_host": "localhost",
  "db_port": 5432,
  "db_name": "omnilink",
  "db_user": "omnilink_user",
  "db_password": "secure_password",
  "max_connections": 20,
  "connection_timeout": 30
}
```

### 3.2 主服务器配置 (`mansever.json`)

```json
{
  "server_id": "main-server-01",
  "server_host": "0.0.0.0",
  "server_port": 8080,
  "admin_username": "admin",
  "admin_password": "secure_admin_password",
  "jwt_secret": "your_jwt_secret_key",
  "jwt_expiry_hours": 24
}
```

### 3.3 系统配置 (`system_config.yaml`)

```yaml
# 服务器配置
server:
  host: 0.0.0.0
  port: 8080
  workers: 4
  timeout: 60
  ssl_enabled: false
  
# 安全配置
security:
  session_timeout: 3600  # 会话超时时间(秒)
  password_policy:
    min_length: 8
    require_uppercase: true
    require_lowercase: true
    require_digit: true
    require_special: true
  failed_login_attempts: 5
  lockout_duration: 300  # 锁定时间(秒)
  
# 缓存配置
cache:
  type: redis
  host: localhost
  port: 6379
  db: 0
  expire: 3600
  
# 日志配置
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: logs/omnilink.log
  max_size_mb: 100
  backup_count: 10
```

## 4. 环境变量

以下环境变量可覆盖配置文件设置：

| 环境变量 | 说明 | 默认值 |
|---------|------|-------|
| `OMNILINK_DB_HOST` | 数据库主机 | localhost |
| `OMNILINK_DB_PORT` | 数据库端口 | 5432 |
| `OMNILINK_DB_NAME` | 数据库名称 | omnilink |
| `OMNILINK_DB_USER` | 数据库用户 | omnilink_user |
| `OMNILINK_DB_PASSWORD` | 数据库密码 | - |
| `OMNILINK_SERVER_HOST` | 服务器监听地址 | 0.0.0.0 |
| `OMNILINK_SERVER_PORT` | 服务器监听端口 | 8080 |
| `OMNILINK_LOG_LEVEL` | 日志级别 | INFO |
| `OMNILINK_SSL_ENABLED` | 启用SSL | false |
| `OMNILINK_REDIS_HOST` | Redis主机 | localhost |
| `OMNILINK_REDIS_PORT` | Redis端口 | 6379 |

## 5. 配置修改指南

1. **推荐的配置修改方式**：
   - 创建或修改 `user_config.yaml` 文件，只包含需要自定义的配置项
   - 设置环境变量进行临时覆盖

2. **敏感信息配置**：
   - 所有密码、密钥和令牌应存储在 `keys/` 目录下
   - 生产环境中考虑使用环境变量或专用密钥管理服务

3. **SSL配置**：
   - 将证书和私钥放入 `ssl/` 目录
   - 在配置中启用SSL: `ssl_enabled: true`

## 6. Docker环境配置

在Docker环境中，建议使用环境变量或挂载配置文件：

```bash
# 使用环境变量
docker run -d \
  -e OMNILINK_DB_HOST=postgres \
  -e OMNILINK_DB_PASSWORD=secure_password \
  -e OMNILINK_LOG_LEVEL=INFO \
  -p 8080:8080 \
  omnilink/main-server:latest

# 或挂载配置文件
docker run -d \
  -v /path/to/config:/app/config \
  -p 8080:8080 \
  omnilink/main-server:latest
```

## 7. 故障排除

1. **配置加载问题**：
   - 检查配置文件格式(YAML语法)
   - 确认配置文件权限
   - 查看日志中的配置加载错误信息

2. **数据库连接问题**：
   - 验证数据库凭据
   - 检查网络连接和防火墙设置
   - 确认数据库服务正在运行

3. **日志记录问题**：
   - 确保日志目录具有写权限
   - 检查磁盘空间是否充足
   - 验证logging_config.yaml配置 