"""
空闲会话管理器
负责管理用户与USB设备连接的会话超时和自动断开
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Callable, Awaitable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from common.models.device import Device
from common.models.user import User
from src.omnilink_main.core.database import get_async_session
from src.omnilink_main.communication.ws_manager import ws_manager

logger = logging.getLogger(__name__)

class IdleSessionManager:
    """空闲会话管理器"""
    
    def __init__(self, default_timeout_hours: int = 6):
        self.default_timeout_hours = default_timeout_hours
        self.active_sessions: Dict[int, datetime] = {}  # device_id -> last_activity_time
        self.cleanup_task: Optional[asyncio.Task] = None
        self.cleanup_interval = 300  # 5分钟检查一次
        self.session_timeout_callback: Optional[Callable[[int, int], Awaitable[None]]] = None
        
    def register_timeout_callback(self, callback: Callable[[int, int], Awaitable[None]]):
        """注册会话超时回调函数"""
        self.session_timeout_callback = callback
        
    def start_session(self, device_id: int, user_id: int):
        """开始一个新的设备会话"""
        self.active_sessions[device_id] = datetime.utcnow()
        logger.info(f"Started session for device {device_id}, user {user_id}")
        
    def update_session_activity(self, device_id: int):
        """更新会话活动时间"""
        if device_id in self.active_sessions:
            self.active_sessions[device_id] = datetime.utcnow()
            logger.debug(f"Updated activity for device {device_id}")
            
    def end_session(self, device_id: int):
        """结束设备会话"""
        if device_id in self.active_sessions:
            del self.active_sessions[device_id]
            logger.info(f"Ended session for device {device_id}")
            
    async def start_monitoring(self):
        """开始监控空闲会话"""
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("Started idle session monitoring")
            
    async def stop_monitoring(self):
        """停止监控空闲会话"""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            self.cleanup_task = None
            logger.info("Stopped idle session monitoring")
            
    async def _cleanup_loop(self):
        """清理循环，检查和处理超时会话"""
        while True:
            try:
                await self._check_timeout_sessions()
                await asyncio.sleep(self.cleanup_interval)
            except asyncio.CancelledError:
                logger.info("Cleanup loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}", exc_info=True)
                await asyncio.sleep(self.cleanup_interval)
                
    async def _check_timeout_sessions(self):
        """检查超时的会话"""
        current_time = datetime.utcnow()
        timeout_threshold = timedelta(hours=self.default_timeout_hours)
        
        timeout_devices = []
        
        for device_id, last_activity in list(self.active_sessions.items()):
            if current_time - last_activity > timeout_threshold:
                timeout_devices.append(device_id)
                
        if timeout_devices:
            logger.info(f"Found {len(timeout_devices)} timeout sessions")
            
            # 获取数据库会话
            async with get_async_session() as db:
                for device_id in timeout_devices:
                    await self._handle_timeout_session(db, device_id)
                    
    async def _handle_timeout_session(self, db: AsyncSession, device_id: int):
        """处理超时的会话"""
        try:
            # 获取设备信息
            device = await db.get(Device, device_id)
            if not device or not device.current_user_id:
                # 设备不存在或没有当前用户，直接清理
                self.end_session(device_id)
                return
                
            user_id = device.current_user_id
            logger.warning(f"Session timeout for device {device_id}, user {user_id}")
            
            # 调用回调函数处理超时
            if self.session_timeout_callback:
                await self.session_timeout_callback(device_id, user_id)
                
            # 更新设备状态
            device.status = "available"
            device.last_user_id = device.current_user_id
            device.last_user_contact = device.current_user_contact
            device.last_used_at = datetime.utcnow()
            device.current_user_id = None
            device.current_user_contact = None
            device.connected_at = None
            
            await db.commit()
            
            # 通知从服务器停止共享
            if device.slave_server:
                try:
                    command = {
                        "action": "unshare_device",
                        "device_path": device.device_id,
                        "reason": "session_timeout"
                    }
                    await ws_manager.send_command(str(device.slave_server.id), command)
                    logger.info(f"Sent unshare command to slave server for device {device_id}")
                except Exception as e:
                    logger.error(f"Failed to send unshare command for device {device_id}: {e}")
            
            # 结束会话
            self.end_session(device_id)
            
            logger.info(f"Successfully handled timeout session for device {device_id}")
            
        except Exception as e:
            logger.error(f"Error handling timeout session for device {device_id}: {e}", exc_info=True)
            
    def get_session_info(self, device_id: int) -> Optional[Dict]:
        """获取会话信息"""
        if device_id not in self.active_sessions:
            return None
            
        last_activity = self.active_sessions[device_id]
        current_time = datetime.utcnow()
        active_duration = current_time - last_activity
        timeout_threshold = timedelta(hours=self.default_timeout_hours)
        remaining_time = timeout_threshold - active_duration
        
        return {
            "device_id": device_id,
            "last_activity": last_activity.isoformat(),
            "active_duration_seconds": int(active_duration.total_seconds()),
            "remaining_time_seconds": max(0, int(remaining_time.total_seconds())),
            "is_expired": remaining_time.total_seconds() <= 0
        }
        
    def get_all_sessions(self) -> Dict[int, Dict]:
        """获取所有活动会话信息"""
        return {
            device_id: self.get_session_info(device_id)
            for device_id in self.active_sessions
        }

# 全局实例
idle_session_manager = IdleSessionManager() 