<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主从服务器系统 - 设备管理</title>
    <!-- Material Design Web Components -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Material+Icons&display=block" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet" />
    <!-- Material Design 3 Styles -->
    <link href="{{ url_for('static', filename='css/material-components-web.min.css') }}" rel="stylesheet">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin_material.css') }}">
    <!-- Leaflet 地图库 (用于地图视图) -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" integrity="sha256-kLaT2GOSpHechhsozzB+flnD+zUyjE2LlfWPgU04xyI=" crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js" integrity="sha256-WBkoXOwTeyKclOHuWtc+i2uENFpDZ9YPdf5Hf+D7ewM=" crossorigin=""></script>
</head>
<body class="mdc-typography">
    <div class="app-container">
        <!-- 顶部应用栏 -->
        <header class="mdc-top-app-bar mdc-top-app-bar--fixed">
            <div class="mdc-top-app-bar__row">
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-start">
                    <button class="material-icons mdc-top-app-bar__navigation-icon mdc-icon-button" id="menu-button">menu</button>
                    <span class="mdc-top-app-bar__title">主从服务器管理系统 - 设备管理</span>
                </section>
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-end" role="toolbar">
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="refresh-button" aria-label="刷新">refresh</button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="theme-toggle" aria-label="切换主题">dark_mode</button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="user-menu-button" aria-label="用户菜单">account_circle</button>
                    
                    <!-- 用户菜单 -->
                    <div class="mdc-menu mdc-menu-surface" id="user-menu">
                        <ul class="mdc-list" role="menu" aria-hidden="true" aria-orientation="vertical" tabindex="-1">
                            <li class="mdc-list-item" role="menuitem">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="mdc-list-item__text">个人设置</span>
                            </li>
                            <li class="mdc-list-divider" role="separator"></li>
                            <li class="mdc-list-item" role="menuitem" id="logout-button" data-logout-url="/auth/logout">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="mdc-list-item__text">退出登录</span>
                            </li>
                        </ul>
                    </div>
                </section>
            </div>
        </header>

        <!-- 主内容区域 -->
        <div class="app-content mdc-top-app-bar--fixed-adjust">
            <div class="mdc-drawer-app-content">
                <!-- 侧边导航抽屉 -->
                <aside class="mdc-drawer mdc-drawer--dismissible">
                    <div class="mdc-drawer__content">
                        <nav class="mdc-list">
                            <a class="mdc-list-item" href="{{ url_for('admin_dashboard') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">dashboard</span>
                                <span class="mdc-list-item__text">仪表盘</span>
                            </a>
                            <a class="mdc-list-item mdc-list-item--activated" href="{{ url_for('admin_devices') }}" aria-current="page">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">usb</span>
                                <span class="mdc-list-item__text">设备管理</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_group_management') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">dns</span>
                                <span class="mdc-list-item__text">分组管理</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_migration_wizard') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">swap_horiz</span>
                                <span class="mdc-list-item__text">迁移向导</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_alerts') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">notifications</span>
                                <span class="mdc-list-item__text">告警设置</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_health_monitor') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">favorite</span>
                                <span class="mdc-list-item__text">健康监控</span>
                            </a>
                            
                            <hr class="mdc-list-divider">
                            <h6 class="mdc-list-group__subheader">系统</h6>
                            
                            <a class="mdc-list-item" href="{{ url_for('admin_settings') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">settings</span>
                                <span class="mdc-list-item__text">系统设置</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_logs') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">description</span>
                                <span class="mdc-list-item__text">日志查看</span>
                            </a>
                        </nav>
                    </div>
                </aside>

                <!-- 主内容 -->
                <main class="main-content">
                    <div class="page-content">
                        <h1 class="mdc-typography--headline4">设备管理</h1>
                        
                        <!-- 设备管理工具栏 -->
                        <div class="device-toolbar mdc-card">
                            <div class="device-toolbar-content">
                                <!-- 视图切换按钮组 -->
                                <div class="view-toggle-group">
                                    <button class="mdc-button view-toggle-button" id="view-toggle-list" data-view="list">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">view_list</span>
                                        <span class="mdc-button__label">列表视图</span>
                                    </button>
                                    <button class="mdc-button view-toggle-button" id="view-toggle-grid" data-view="grid">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">grid_view</span>
                                        <span class="mdc-button__label">网格视图</span>
                                    </button>
                                    <button class="mdc-button view-toggle-button" id="view-toggle-map" data-view="map">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">map</span>
                                        <span class="mdc-button__label">地图视图</span>
                                    </button>
                                </div>
                                
                                <!-- 搜索框 -->
                                <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon search-field">
                                    <i class="material-icons mdc-text-field__icon">search</i>
                                    <input type="text" id="device-search" class="mdc-text-field__input" placeholder="搜索设备...">
                                    <div class="mdc-notched-outline">
                                        <div class="mdc-notched-outline__leading"></div>
                                        <div class="mdc-notched-outline__notch">
                                            <label for="device-search" class="mdc-floating-label">搜索设备</label>
                                        </div>
                                        <div class="mdc-notched-outline__trailing"></div>
                                    </div>
                                </div>
                                
                                <!-- 过滤和排序 -->
                                <div class="filter-sort-container">
                                    <button class="mdc-button mdc-button--outlined" id="filter-button">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">filter_list</span>
                                        <span class="mdc-button__label">过滤</span>
                                    </button>
                                    <button class="mdc-button mdc-button--outlined" id="sort-button">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">sort</span>
                                        <span class="mdc-button__label">排序</span>
                                    </button>
                                </div>
                                
                                <!-- 添加设备按钮 -->
                                <button class="mdc-button mdc-button--raised" id="add-device-button">
                                    <span class="mdc-button__ripple"></span>
                                    <span class="material-icons mdc-button__icon">add</span>
                                    <span class="mdc-button__label">添加设备</span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 设备批量操作工具栏 (初始隐藏) -->
                        <div class="batch-operations-toolbar mdc-card" style="display: none;">
                            <div class="batch-operations-content">
                                <span class="selected-count">已选择 <span id="selected-device-count">0</span> 个设备</span>
                                
                                <div class="batch-operations-buttons">
                                    <button class="mdc-button" id="batch-enable-button">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">power_settings_new</span>
                                        <span class="mdc-button__label">启用</span>
                                    </button>
                                    <button class="mdc-button" id="batch-disable-button">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">power_off</span>
                                        <span class="mdc-button__label">禁用</span>
                                    </button>
                                    <button class="mdc-button" id="batch-group-button">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">folder</span>
                                        <span class="mdc-button__label">分组</span>
                                    </button>
                                    <button class="mdc-button" id="batch-delete-button">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">delete</span>
                                        <span class="mdc-button__label">删除</span>
                                    </button>
                                </div>
                                
                                <button class="mdc-button" id="cancel-batch-button">
                                    <span class="mdc-button__ripple"></span>
                                    <span class="material-icons mdc-button__icon">close</span>
                                    <span class="mdc-button__label">取消</span>
                                </button>
                            </div>
                        </div>

                        <!-- 设备内容区域 - 基于视图类型加载不同视图 -->
                        <div id="device-content" class="device-content">
                            <!-- 视图内容将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Material Design Web Components JavaScript -->
    <script src="{{ url_for('static', filename='js/material-components-web.min.js') }}"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/admin_material.js') }}"></script>
    <script src="{{ url_for('static', filename='js/device_management.js') }}"></script>
    
    <!-- 设备管理视图控制器 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取当前选择的视图类型（从URL参数或本地存储）
            const urlParams = new URLSearchParams(window.location.search);
            let currentView = urlParams.get('view') || localStorage.getItem('preferredDeviceView') || 'list';
            
            // 初始化视图切换按钮
            const viewButtons = document.querySelectorAll('.view-toggle-button');
            
            // 设置活跃按钮样式
            function setActiveView(viewType) {
                viewButtons.forEach(button => {
                    if (button.dataset.view === viewType) {
                        button.classList.add('mdc-button--raised');
                        button.classList.remove('mdc-button--outlined');
                    } else {
                        button.classList.remove('mdc-button--raised');
                        button.classList.add('mdc-button--outlined');
                    }
                });
                
                // 保存用户偏好到本地存储
                localStorage.setItem('preferredDeviceView', viewType);
                
                // 加载相应的视图内容
                loadDeviceView(viewType);
            }
            
            // 为视图按钮添加点击事件监听器
            viewButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const viewType = button.dataset.view;
                    setActiveView(viewType);
                    // Update URL without full page reload (optional)
                    history.pushState({view: viewType}, '', `?view=${viewType}`);
                });
            });
            
            // 视图加载函数 (示例实现)
            function loadDeviceView(viewType) {
                const contentArea = document.getElementById('device-content');
                console.log(`Loading ${viewType} view...`);
                contentArea.innerHTML = '<div class="mdc-linear-progress mdc-linear-progress--indeterminate"><div class="mdc-linear-progress__buffer"></div><div class="mdc-linear-progress__bar mdc-linear-progress__primary-bar"><span class="mdc-linear-progress__bar-inner"></span></div><div class="mdc-linear-progress__bar mdc-linear-progress__secondary-bar"><span class="mdc-linear-progress__bar-inner"></span></div></div>'; // Show loading indicator

                 // Placeholder content based on view type
                 setTimeout(() => { // Simulate network delay
                    if (viewType === 'list') {
                        contentArea.innerHTML = '<p class="mdc-typography--body1">设备列表视图 (内容待实现)</p>';
                    } else if (viewType === 'grid') {
                        contentArea.innerHTML = '<p class="mdc-typography--body1">设备网格视图 (内容待实现)</p>';
                    } else if (viewType === 'map') {
                        contentArea.innerHTML = '<div id="map-container" style="height: 500px;"></div><p class="mdc-typography--body1">设备地图视图 (内容待实现)</p>';
                        initializeMapView(); // Placeholder for map initialization
                    } else {
                         contentArea.innerHTML = '<p class="mdc-typography--body1 error-message">未知的视图类型。</p>';
                    }
                    initializeViewComponents(viewType); // Call placeholder for component init
                 }, 500); // 0.5 second delay
            }

            // Placeholder for map initialization logic
            function initializeMapView() {
                console.log("Initializing map view (placeholder)...");
                const mapContainer = document.getElementById('map-container');
                 if (typeof L !== 'undefined' && mapContainer) { // Check Leaflet exists
                     try {
                         const map = L.map('map-container').setView([39.9042, 116.4074], 5); // Default view (Beijing)
                         L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                             attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                         }).addTo(map);
                         console.log("Map initialized (placeholder).");
                     } catch (e) {
                         console.error("Error initializing map:", e);
                         mapContainer.innerHTML = '<p class="mdc-typography--body1 error-message">地图加载失败。</p>';
                     }
                 } else if (mapContainer) {
                     console.error("Leaflet library not loaded.");
                     mapContainer.innerHTML = '<p class="mdc-typography--body1 error-message">地图库未加载，无法显示地图视图。</p>';
                 }
            }

            // Placeholder for initializing components within loaded views
            function initializeViewComponents(viewType) {
                console.log(`Initializing components for ${viewType} view (placeholder).`);
                // Add specific initialization logic based on viewType
                // e.g., initialize MDC Data Table for list view:
                // if (viewType === 'list') {
                //    const dataTable = new mdc.dataTable.MDCDataTable(document.querySelector('.mdc-data-table'));
                // }
            }

            // 初始化加载当前视图
            setActiveView(currentView);
        });
        
        // 渲染列表视图
        function renderListView(container, data) {
            // 创建列表视图容器
            const listContainer = document.createElement('div');
            listContainer.className = 'device-list-container animate-fade-in';
            
            // 创建数据表格
            const table = document.createElement('table');
            table.className = 'mdc-data-table__table';
            
            // 创建表头
            const thead = document.createElement('thead');
            thead.innerHTML = `
                <tr class="mdc-data-table__header-row">
                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                        <div class="mdc-checkbox">
                            <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-devices"/>
                            <div class="mdc-checkbox__background"></div>
                        </div>
                    </th>
                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">设备名称</th>
                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">类型</th>
                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">位置</th>
                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">状态</th>
                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">从服务器</th>
                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">上次活动</th>
                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">操作</th>
                </tr>
            `;
            
            // 创建表体
            const tbody = document.createElement('tbody');
            tbody.className = 'mdc-data-table__content';
            
            // 如果没有数据，显示空状态
            if (!data || !data.devices || data.devices.length === 0) {
                tbody.innerHTML = `
                    <tr class="mdc-data-table__row">
                        <td class="mdc-data-table__cell" colspan="8">
                            <div class="empty-state">
                                <span class="material-icons">devices_other</span>
                                <p>暂无设备数据</p>
                            </div>
                        </td>
                    </tr>
                `;
            } else {
                // 填充设备数据
                data.devices.forEach(device => {
                    const tr = document.createElement('tr');
                    tr.className = 'mdc-data-table__row';
                    tr.dataset.deviceId = device.id;
                    
                    // 设置行内容
                    tr.innerHTML = `
                        <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                            <div class="mdc-checkbox">
                                <input type="checkbox" class="mdc-checkbox__native-control device-checkbox" id="device-${device.id}"/>
                                <div class="mdc-checkbox__background"></div>
                            </div>
                        </td>
                        <td class="mdc-data-table__cell">${device.name}</td>
                        <td class="mdc-data-table__cell">${device.type}</td>
                        <td class="mdc-data-table__cell">${device.location || '-'}</td>
                        <td class="mdc-data-table__cell">
                            <span class="status-badge status-badge-${device.status === 'online' ? 'success' : device.status === 'offline' ? 'error' : 'warning'}">
                                <span class="material-icons">${device.status === 'online' ? 'check_circle' : device.status === 'offline' ? 'cancel' : 'warning'}</span>
                                ${device.status === 'online' ? '在线' : device.status === 'offline' ? '离线' : '未知'}
                            </span>
                        </td>
                        <td class="mdc-data-table__cell">${device.slaveServer || '-'}</td>
                        <td class="mdc-data-table__cell">${device.lastActivity || '-'}</td>
                        <td class="mdc-data-table__cell">
                            <button class="mdc-icon-button material-icons" title="查看详情" data-device-id="${device.id}" onclick="showDeviceDetails('${device.id}')">info</button>
                            <button class="mdc-icon-button material-icons" title="编辑设备" data-device-id="${device.id}" onclick="editDevice('${device.id}')">edit</button>
                            <button class="mdc-icon-button material-icons" title="删除设备" data-device-id="${device.id}" onclick="deleteDevice('${device.id}')">delete</button>
                        </td>
                    `;
                    
                    tbody.appendChild(tr);
                });
            }
            
            table.appendChild(thead);
            table.appendChild(tbody);
            listContainer.appendChild(table);
            container.appendChild(listContainer);
            
            // 初始化选择框
            initCheckboxes();
        }
        
        // 渲染网格视图
        function renderGridView(container, data) {
            // 创建网格视图容器
            const gridContainer = document.createElement('div');
            gridContainer.className = 'device-grid-container animate-fade-in';
            
            // 如果没有数据，显示空状态
            if (!data || !data.devices || data.devices.length === 0) {
                gridContainer.innerHTML = `
                    <div class="empty-state">
                        <span class="material-icons">devices_other</span>
                        <p>暂无设备数据</p>
                    </div>
                `;
            } else {
                // 为每个设备创建卡片
                data.devices.forEach(device => {
                    const card = document.createElement('div');
                    card.className = 'device-card mdc-card';
                    card.dataset.deviceId = device.id;
                    
                    // 设置卡片内容
                    card.innerHTML = `
                        <div class="mdc-card__primary-action" tabindex="0">
                            <div class="device-card-header">
                                <div class="device-checkbox-container">
                                    <div class="mdc-checkbox">
                                        <input type="checkbox" class="mdc-checkbox__native-control device-checkbox" id="device-grid-${device.id}"/>
                                        <div class="mdc-checkbox__background"></div>
                                    </div>
                                </div>
                                <span class="status-indicator ${device.status === 'online' ? 'status-success' : device.status === 'offline' ? 'status-error' : 'status-warning'}"></span>
                            </div>
                            <div class="device-card-content">
                                <div class="device-icon-container">
                                    <span class="material-icons device-icon">${getDeviceIcon(device.type)}</span>
                                </div>
                                <h2 class="device-name mdc-typography--headline6">${device.name}</h2>
                                <p class="device-type mdc-typography--body2">${device.type}</p>
                                <p class="device-location mdc-typography--caption">${device.location || '无位置信息'}</p>
                            </div>
                        </div>
                        <div class="mdc-card__actions">
                            <div class="mdc-card__action-buttons">
                                <button class="mdc-button mdc-card__action mdc-card__action--button" onclick="showDeviceDetails('${device.id}')">
                                    <span class="mdc-button__ripple"></span>
                                    <span class="mdc-button__label">详情</span>
                                </button>
                            </div>
                            <div class="mdc-card__action-icons">
                                <button class="mdc-icon-button material-icons mdc-card__action mdc-card__action--icon" title="编辑设备" onclick="editDevice('${device.id}')">edit</button>
                                <button class="mdc-icon-button material-icons mdc-card__action mdc-card__action--icon" title="删除设备" onclick="deleteDevice('${device.id}')">delete</button>
                            </div>
                        </div>
                    `;
                    
                    gridContainer.appendChild(card);
                });
            }
            
            container.appendChild(gridContainer);
            
            // 初始化选择框
            initCheckboxes();
        }
        
        // 渲染地图视图
        function renderMapView(container, data) {
            // 创建地图视图容器
            const mapContainer = document.createElement('div');
            mapContainer.className = 'device-map-container animate-fade-in';
            mapContainer.id = 'device-map';
            
            // 添加额外的设备列表（地图侧边栏）
            const sidebarContainer = document.createElement('div');
            sidebarContainer.className = 'map-sidebar';
            sidebarContainer.innerHTML = `
                <h3 class="mdc-typography--subtitle1">设备列表</h3>
                <div class="map-device-list"></div>
            `;
            
            // 创建地图布局容器
            const mapLayout = document.createElement('div');
            mapLayout.className = 'map-layout';
            
            // 添加地图容器和侧边栏
            mapLayout.appendChild(mapContainer);
            mapLayout.appendChild(sidebarContainer);
            
            container.appendChild(mapLayout);
            
            // 初始化地图
            setTimeout(() => {
                initDeviceMap(data);
                populateMapSidebar(data, sidebarContainer.querySelector('.map-device-list'));
            }, 100);
        }
        
        // 初始化地图
        function initDeviceMap(data) {
            // 创建地图
            const map = L.map('device-map').setView([39.9042, 116.4074], 5); // 默认中国中心位置
            
            // 添加OpenStreetMap图层
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);
            
            // 如果没有数据，显示提示
            if (!data || !data.devices || data.devices.length === 0) {
                // 在地图中心显示提示
                const noDataMarker = L.marker([39.9042, 116.4074]).addTo(map);
                noDataMarker.bindPopup('<b>暂无设备地理位置数据</b>').openPopup();
                return;
            }
            
            // 创建设备标记
            const markers = [];
            data.devices.forEach(device => {
                // 如果设备有位置信息
                if (device.latitude && device.longitude) {
                    const marker = L.marker([device.latitude, device.longitude]).addTo(map);
                    
                    // 设置弹出信息
                    marker.bindPopup(`
                        <div class="map-popup">
                            <h3>${device.name}</h3>
                            <p><strong>类型:</strong> ${device.type}</p>
                            <p><strong>状态:</strong> 
                                <span class="${device.status === 'online' ? 'status-success' : device.status === 'offline' ? 'status-error' : 'status-warning'}">
                                    ${device.status === 'online' ? '在线' : device.status === 'offline' ? '离线' : '未知'}
                                </span>
                            </p>
                            <p><strong>位置:</strong> ${device.location || '未知'}</p>
                            <button class="mdc-button" onclick="showDeviceDetails('${device.id}')">
                                <span class="mdc-button__ripple"></span>
                                <span class="mdc-button__label">查看详情</span>
                            </button>
                        </div>
                    `);
                    
                    markers.push(marker);
                }
            });
            
            // 如果有标记，则自动调整地图以显示所有标记
            if (markers.length > 0) {
                const group = new L.featureGroup(markers);
                map.fitBounds(group.getBounds().pad(0.1));
            }
        }
        
        // 填充地图侧边栏设备列表
        function populateMapSidebar(data, container) {
            // 如果没有数据，显示空状态
            if (!data || !data.devices || data.devices.length === 0) {
                container.innerHTML = '<p class="empty-list">暂无设备数据</p>';
                return;
            }
            
            // 创建设备列表
            const deviceList = document.createElement('ul');
            deviceList.className = 'mdc-list map-device-list';
            
            // 添加设备列表项
            data.devices.forEach(device => {
                const listItem = document.createElement('li');
                listItem.className = 'mdc-list-item';
                listItem.dataset.deviceId = device.id;
                
                // 设置列表项内容
                listItem.innerHTML = `
                    <span class="mdc-list-item__ripple"></span>
                    <span class="mdc-list-item__graphic material-icons">${getDeviceIcon(device.type)}</span>
                    <span class="mdc-list-item__text">
                        <span class="mdc-list-item__primary-text">${device.name}</span>
                        <span class="mdc-list-item__secondary-text">${device.location || '无位置信息'}</span>
                    </span>
                    <span class="status-indicator ${device.status === 'online' ? 'status-success' : device.status === 'offline' ? 'status-error' : 'status-warning'}"></span>
                `;
                
                // 添加点击事件，使地图聚焦到该设备
                listItem.addEventListener('click', () => {
                    if (device.latitude && device.longitude) {
                        // 获取地图实例并聚焦到设备位置
                        const map = L.map('device-map');
                        map.setView([device.latitude, device.longitude], 15);
                        
                        // 查找对应的标记并打开弹窗
                        map.eachLayer(layer => {
                            if (layer instanceof L.Marker && 
                                layer._latlng.lat === device.latitude && 
                                layer._latlng.lng === device.longitude) {
                                layer.openPopup();
                            }
                        });
                    }
                });
                
                deviceList.appendChild(listItem);
            });
            
            container.appendChild(deviceList);
        }
        
        // 根据设备类型获取图标
        function getDeviceIcon(deviceType) {
            switch(deviceType.toLowerCase()) {
                case 'usb':
                    return 'usb';
                case 'printer':
                    return 'print';
                case 'camera':
                    return 'photo_camera';
                case 'scanner':
                    return 'scanner';
                case 'keyboard':
                    return 'keyboard';
                case 'mouse':
                    return 'mouse';
                case 'storage':
                    return 'storage';
                default:
                    return 'devices_other';
            }
        }
        
        // 初始化复选框
        function initCheckboxes() {
            // 获取所有设备复选框
            const deviceCheckboxes = document.querySelectorAll('.device-checkbox');
            
            // 获取全选复选框
            const selectAllCheckbox = document.getElementById('select-all-devices');
            
            if (selectAllCheckbox) {
                // 为全选复选框添加事件监听器
                selectAllCheckbox.addEventListener('change', function() {
                    const isChecked = this.checked;
                    
                    // 更新所有设备复选框状态
                    deviceCheckboxes.forEach(checkbox => {
                        checkbox.checked = isChecked;
                    });
                    
                    // 更新批量操作工具栏可见性
                    updateBatchOperationsToolbar();
                });
            }
            
            // 为每个设备复选框添加事件监听器
            deviceCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    // 更新批量操作工具栏可见性
                    updateBatchOperationsToolbar();
                    
                    // 更新全选复选框状态
                    if (selectAllCheckbox) {
                        const allChecked = Array.from(deviceCheckboxes).every(cb => cb.checked);
                        const someChecked = Array.from(deviceCheckboxes).some(cb => cb.checked);
                        
                        selectAllCheckbox.checked = allChecked;
                        selectAllCheckbox.indeterminate = someChecked && !allChecked;
                    }
                });
            });
        }
        
        // 更新批量操作工具栏可见性
        function updateBatchOperationsToolbar() {
            const deviceCheckboxes = document.querySelectorAll('.device-checkbox');
            const checkedDevices = Array.from(deviceCheckboxes).filter(cb => cb.checked);
            const batchToolbar = document.querySelector('.batch-operations-toolbar');
            const selectedCountElement = document.getElementById('selected-device-count');
            
            if (batchToolbar) {
                if (checkedDevices.length > 0) {
                    batchToolbar.style.display = 'block';
                    
                    if (selectedCountElement) {
                        selectedCountElement.textContent = checkedDevices.length;
                    }
                } else {
                    batchToolbar.style.display = 'none';
                }
            }
        }
    </script>

    <!-- 视图样式 -->
    <style>
        /* 设备列表视图样式 */
        .device-list-container {
            overflow-x: auto;
            margin-top: 16px;
        }
        
        /* 设备网格视图样式 */
        .device-grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        
        .device-card {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .device-card-header {
            display: flex;
            justify-content: space-between;
            padding: 8px 16px;
            align-items: center;
        }
        
        .device-card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 16px;
            text-align: center;
        }
        
        .device-icon-container {
            width: 64px;
            height: 64px;
            border-radius: 32px;
            background-color: var(--md-sys-color-primary-container);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
        }
        
        .device-icon {
            font-size: 32px;
            color: var(--md-sys-color-on-primary-container);
        }
        
        .device-name {
            margin: 8px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
        }
        
        .device-type,
        .device-location {
            margin: 4px 0;
            color: var(--md-sys-color-on-surface-variant);
        }
        
        /* 设备地图视图样式 */
        .map-layout {
            display: flex;
            height: 600px;
            margin-top: 16px;
        }
        
        .device-map-container {
            flex: 1;
            height: 100%;
            border-radius: var(--md-sys-border-radius-medium);
            overflow: hidden;
        }
        
        .map-sidebar {
            width: 300px;
            margin-left: 16px;
            background-color: var(--md-sys-color-surface);
            border-radius: var(--md-sys-border-radius-medium);
            box-shadow: var(--md-sys-elevation-1);
            padding: 16px;
            overflow-y: auto;
        }
        
        .map-device-list {
            padding: 0;
        }
        
        .map-popup {
            padding: 8px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 200px;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: var(--md-sys-color-primary);
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .error-container {
            text-align: center;
            padding: 32px;
        }
        
        .error-icon {
            font-size: 48px;
            color: var(--md-sys-color-error);
            margin-bottom: 16px;
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 32px;
            color: var(--md-sys-color-on-surface-variant);
        }
        
        .empty-state .material-icons {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .map-layout {
                flex-direction: column;
                height: auto;
            }
            
            .device-map-container {
                height: 400px;
            }
            
            .map-sidebar {
                width: 100%;
                margin-left: 0;
                margin-top: 16px;
                max-height: 300px;
            }
        }
    </style>
</body>
</html> 