import os
import logging
import shutil
import json
import subprocess
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
import tempfile
import tarfile
import gzip
import uuid

logger = logging.getLogger(__name__)

class BackupService:
    def __init__(self):
        # 备份存储路径
        self.backup_dir = os.environ.get("BACKUP_DIRECTORY", "backups")
        self.file_backup_dir = os.path.join(self.backup_dir, "files")
        self.db_backup_dir = os.path.join(self.backup_dir, "database")
        
        # 确保目录存在
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(self.file_backup_dir, exist_ok=True)
        os.makedirs(self.db_backup_dir, exist_ok=True)
        
        # 数据库配置
        self.db_host = os.environ.get("DB_HOST", "localhost")
        self.db_port = os.environ.get("DB_PORT", "5432")
        self.db_name = os.environ.get("DB_NAME", "omnilink")
        self.db_user = os.environ.get("DB_USER", "postgres")
        self.db_password = os.environ.get("DB_PASSWORD", "")
        
        # 要备份的目录
        self.dirs_to_backup = [
            "configs",
            "certificates",
            "devices",
            "logs",
            "uploads"
        ]
    
    async def create_file_backup(self, backup_name: str = None) -> Dict[str, Any]:
        """
        创建文件备份
        
        参数:
            backup_name: 备份名称（可选，如不提供则自动生成）
            
        返回:
            Dict[str, Any]: 备份结果
        """
        logger.info("开始创建文件备份...")
        
        try:
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if backup_name:
                safe_backup_name = backup_name.replace(" ", "_").replace("/", "_").replace("\\", "_")
                filename = f"{safe_backup_name}_{timestamp}.tar.gz"
            else:
                filename = f"file_backup_{timestamp}.tar.gz"
                
            # 备份文件完整路径
            backup_path = os.path.join(self.file_backup_dir, filename)
            
            # 确保备份目录存在
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            
            # 创建临时目录
            with tempfile.TemporaryDirectory() as temp_dir:
                # 复制要备份的目录到临时目录
                for dir_name in self.dirs_to_backup:
                    if os.path.exists(dir_name):
                        target_dir = os.path.join(temp_dir, dir_name)
                        os.makedirs(os.path.dirname(target_dir), exist_ok=True)
                        
                        # 如果源是文件，直接复制
                        if os.path.isfile(dir_name):
                            shutil.copy2(dir_name, target_dir)
                        # 如果源是目录，递归复制
                        else:
                            shutil.copytree(dir_name, target_dir)
                
                # 创建元数据文件
                metadata = {
                    "backup_type": "file",
                    "backup_time": datetime.now().isoformat(),
                    "backup_name": backup_name,
                    "backed_up_dirs": self.dirs_to_backup
                }
                
                with open(os.path.join(temp_dir, "backup_metadata.json"), "w") as f:
                    json.dump(metadata, f, indent=2)
                
                # 创建压缩包
                with tarfile.open(backup_path, "w:gz") as tar:
                    tar.add(temp_dir, arcname="")
            
            # 计算备份文件大小
            backup_size = os.path.getsize(backup_path)
            
            logger.info(f"文件备份已创建: {backup_path}")
            return {
                "success": True,
                "backup_path": backup_path,
                "filename": filename,
                "backup_time": metadata["backup_time"],
                "backup_type": "file",
                "size_bytes": backup_size,
                "backed_up_dirs": self.dirs_to_backup
            }
            
        except Exception as e:
            logger.error(f"创建文件备份时发生错误: {str(e)}")
            logger.exception(e)
            return {
                "success": False,
                "error": f"创建文件备份失败: {str(e)}"
            }
    
    async def create_db_backup(self, backup_name: str = None) -> Dict[str, Any]:
        """
        创建数据库备份
        
        参数:
            backup_name: 备份名称（可选，如不提供则自动生成）
            
        返回:
            Dict[str, Any]: 备份结果
        """
        logger.info("开始创建数据库备份...")
        
        try:
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if backup_name:
                safe_backup_name = backup_name.replace(" ", "_").replace("/", "_").replace("\\", "_")
                filename = f"{safe_backup_name}_{timestamp}.sql.gz"
            else:
                filename = f"db_backup_{timestamp}.sql.gz"
                
            # 备份文件完整路径
            backup_path = os.path.join(self.db_backup_dir, filename)
            
            # 确保备份目录存在
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            
            # 使用 pg_dump 创建数据库备份
            env = os.environ.copy()
            
            # 设置 PostgreSQL 密码环境变量
            if self.db_password:
                env["PGPASSWORD"] = self.db_password
                
            # 构建 pg_dump 命令
            cmd = [
                "pg_dump",
                f"-h{self.db_host}",
                f"-p{self.db_port}",
                f"-U{self.db_user}",
                "-Fc",  # 自定义格式
                self.db_name
            ]
            
            # 执行 pg_dump 并将输出保存到文件
            with gzip.open(backup_path, 'wb') as f:
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=f,
                    stderr=asyncio.subprocess.PIPE,
                    env=env
                )
                _, stderr = await process.communicate()
                
            # 检查命令执行结果
            if process.returncode != 0:
                stderr_msg = stderr.decode() if stderr else "未知错误"
                logger.error(f"pg_dump 执行失败: {stderr_msg}")
                return {
                    "success": False,
                    "error": f"数据库备份失败: {stderr_msg}"
                }
            
            # 计算备份文件大小
            backup_size = os.path.getsize(backup_path)
            
            # 创建元数据文件
            metadata = {
                "backup_type": "database",
                "backup_time": datetime.now().isoformat(),
                "backup_name": backup_name,
                "database_name": self.db_name
            }
            
            # 保存元数据文件
            metadata_path = os.path.join(self.db_backup_dir, f"{os.path.splitext(filename)[0]}.meta.json")
            with open(metadata_path, "w") as f:
                json.dump(metadata, f, indent=2)
            
            logger.info(f"数据库备份已创建: {backup_path}")
            return {
                "success": True,
                "backup_path": backup_path,
                "filename": filename,
                "backup_time": metadata["backup_time"],
                "backup_type": "database",
                "size_bytes": backup_size,
                "database_name": self.db_name
            }
            
        except Exception as e:
            logger.error(f"创建数据库备份时发生错误: {str(e)}")
            logger.exception(e)
            return {
                "success": False,
                "error": f"创建数据库备份失败: {str(e)}"
            }
    
    async def create_full_backup(self, backup_name: str = None) -> Dict[str, Any]:
        """
        创建完整备份（文件和数据库）
        
        参数:
            backup_name: 备份名称（可选，如不提供则自动生成）
            
        返回:
            Dict[str, Any]: 备份结果
        """
        logger.info("开始创建完整备份...")
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if not backup_name:
                backup_name = f"full_backup_{timestamp}"
                
            # 创建文件备份
            file_backup_result = await self.create_file_backup(f"{backup_name}_files")
            
            # 创建数据库备份
            db_backup_result = await self.create_db_backup(f"{backup_name}_db")
            
            # 检查备份结果
            success = file_backup_result["success"] and db_backup_result["success"]
            
            # 生成备份记录
            result = {
                "success": success,
                "backup_name": backup_name,
                "backup_time": datetime.now().isoformat(),
                "file_backup": file_backup_result,
                "db_backup": db_backup_result
            }
            
            if not success:
                errors = []
                if not file_backup_result["success"]:
                    errors.append(file_backup_result.get("error", "文件备份失败"))
                if not db_backup_result["success"]:
                    errors.append(db_backup_result.get("error", "数据库备份失败"))
                result["errors"] = errors
                
            # 保存备份记录
            record_path = os.path.join(self.backup_dir, f"{backup_name}_record.json")
            with open(record_path, "w") as f:
                json.dump(result, f, indent=2)
                
            logger.info(f"完整备份已创建: {backup_name}")
            return result
            
        except Exception as e:
            logger.error(f"创建完整备份时发生错误: {str(e)}")
            logger.exception(e)
            return {
                "success": False,
                "error": f"创建完整备份失败: {str(e)}"
            }
    
    async def restore_file_backup(self, backup_path: str, target_dir: str = None) -> Dict[str, Any]:
        """
        从文件备份恢复
        
        参数:
            backup_path: 备份文件路径
            target_dir: 恢复目标目录（可选，默认为原始位置）
            
        返回:
            Dict[str, Any]: 恢复结果
        """
        logger.info(f"从文件备份恢复: {backup_path}")
        
        try:
            # 检查备份文件是否存在
            if not os.path.exists(backup_path):
                return {
                    "success": False,
                    "error": f"备份文件不存在: {backup_path}"
                }
                
            # 创建临时目录用于解压
            with tempfile.TemporaryDirectory() as temp_dir:
                # 解压备份文件
                with tarfile.open(backup_path, "r:gz") as tar:
                    tar.extractall(path=temp_dir)
                    
                # 检查元数据
                metadata_path = os.path.join(temp_dir, "backup_metadata.json")
                if os.path.exists(metadata_path):
                    with open(metadata_path, "r") as f:
                        metadata = json.load(f)
                else:
                    metadata = {"backup_type": "file", "backed_up_dirs": self.dirs_to_backup}
                    
                # 恢复文件
                backed_up_dirs = metadata.get("backed_up_dirs", self.dirs_to_backup)
                restored_dirs = []
                
                for dir_name in backed_up_dirs:
                    source_path = os.path.join(temp_dir, dir_name)
                    
                    # 如果指定了目标目录，则恢复到该目录
                    if target_dir:
                        dest_path = os.path.join(target_dir, dir_name)
                    else:
                        dest_path = dir_name
                        
                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                    
                    # 如果源是文件
                    if os.path.isfile(source_path):
                        shutil.copy2(source_path, dest_path)
                    # 如果源是目录
                    elif os.path.isdir(source_path):
                        # 如果目标目录已存在，先删除
                        if os.path.exists(dest_path):
                            shutil.rmtree(dest_path)
                        # 复制目录
                        shutil.copytree(source_path, dest_path)
                        
                    restored_dirs.append(dir_name)
                    
            logger.info(f"文件备份已恢复: {backup_path}")
            return {
                "success": True,
                "backup_path": backup_path,
                "restored_dirs": restored_dirs,
                "target_dir": target_dir or "原始位置"
            }
            
        except Exception as e:
            logger.error(f"恢复文件备份时发生错误: {str(e)}")
            logger.exception(e)
            return {
                "success": False,
                "error": f"恢复文件备份失败: {str(e)}"
            }
    
    async def restore_db_backup(self, backup_path: str) -> Dict[str, Any]:
        """
        从数据库备份恢复
        
        参数:
            backup_path: 备份文件路径
            
        返回:
            Dict[str, Any]: 恢复结果
        """
        logger.info(f"从数据库备份恢复: {backup_path}")
        
        try:
            # 检查备份文件是否存在
            if not os.path.exists(backup_path):
                return {
                    "success": False,
                    "error": f"备份文件不存在: {backup_path}"
                }
                
            # 使用 pg_restore 恢复数据库
            env = os.environ.copy()
            
            # 设置 PostgreSQL 密码环境变量
            if self.db_password:
                env["PGPASSWORD"] = self.db_password
                
            # 解压备份文件到临时文件
            with tempfile.NamedTemporaryFile(suffix='.sql') as temp_file:
                with gzip.open(backup_path, 'rb') as f_in:
                    temp_file.write(f_in.read())
                    temp_file.flush()
                
                # 构建 pg_restore 命令
                cmd = [
                    "pg_restore",
                    f"-h{self.db_host}",
                    f"-p{self.db_port}",
                    f"-U{self.db_user}",
                    "-d", self.db_name,
                    "-c",  # 清除（删除）恢复前的数据库对象
                    temp_file.name
                ]
                
                # 执行 pg_restore
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    env=env
                )
                stdout, stderr = await process.communicate()
                
                # 检查命令执行结果
                if process.returncode != 0:
                    stderr_msg = stderr.decode() if stderr else "未知错误"
                    logger.error(f"pg_restore 执行失败: {stderr_msg}")
                    return {
                        "success": False,
                        "error": f"数据库恢复失败: {stderr_msg}"
                    }
            
            logger.info(f"数据库备份已恢复: {backup_path}")
            return {
                "success": True,
                "backup_path": backup_path,
                "database_name": self.db_name
            }
            
        except Exception as e:
            logger.error(f"恢复数据库备份时发生错误: {str(e)}")
            logger.exception(e)
            return {
                "success": False,
                "error": f"恢复数据库备份失败: {str(e)}"
            }
    
    async def restore_full_backup(self, file_backup_path: str, db_backup_path: str, target_dir: str = None) -> Dict[str, Any]:
        """
        从完整备份恢复
        
        参数:
            file_backup_path: 文件备份路径
            db_backup_path: 数据库备份路径
            target_dir: 恢复文件的目标目录（可选，默认为原始位置）
            
        返回:
            Dict[str, Any]: 恢复结果
        """
        logger.info(f"从完整备份恢复: 文件={file_backup_path}, 数据库={db_backup_path}")
        
        try:
            # 恢复文件备份
            file_restore_result = await self.restore_file_backup(file_backup_path, target_dir)
            
            # 恢复数据库备份
            db_restore_result = await self.restore_db_backup(db_backup_path)
            
            # 检查恢复结果
            success = file_restore_result["success"] and db_restore_result["success"]
            
            # 生成恢复记录
            result = {
                "success": success,
                "restore_time": datetime.now().isoformat(),
                "file_restore": file_restore_result,
                "db_restore": db_restore_result
            }
            
            if not success:
                errors = []
                if not file_restore_result["success"]:
                    errors.append(file_restore_result.get("error", "文件恢复失败"))
                if not db_restore_result["success"]:
                    errors.append(db_restore_result.get("error", "数据库恢复失败"))
                result["errors"] = errors
                
            logger.info("完整备份已恢复")
            return result
            
        except Exception as e:
            logger.error(f"恢复完整备份时发生错误: {str(e)}")
            logger.exception(e)
            return {
                "success": False,
                "error": f"恢复完整备份失败: {str(e)}"
            }
    
    async def list_backups(self, backup_type: str = None) -> List[Dict[str, Any]]:
        """
        列出可用的备份
        
        参数:
            backup_type: 备份类型（"file"、"database" 或 None 表示所有类型）
            
        返回:
            List[Dict[str, Any]]: 备份信息列表
        """
        logger.info(f"列出备份，类型: {backup_type or '所有'}")
        
        try:
            backups = []
            
            # 列出文件备份
            if backup_type is None or backup_type == "file":
                if os.path.exists(self.file_backup_dir):
                    for filename in os.listdir(self.file_backup_dir):
                        if filename.endswith('.tar.gz'):
                            file_path = os.path.join(self.file_backup_dir, filename)
                            file_stat = os.stat(file_path)
                            
                            backup_info = {
                                "type": "file",
                                "filename": filename,
                                "path": file_path,
                                "size": file_stat.st_size,
                                "created_at": datetime.fromtimestamp(file_stat.st_ctime).isoformat()
                            }
                            
                            backups.append(backup_info)
            
            # 列出数据库备份
            if backup_type is None or backup_type == "database":
                if os.path.exists(self.db_backup_dir):
                    for filename in os.listdir(self.db_backup_dir):
                        if filename.endswith('.sql.gz'):
                            file_path = os.path.join(self.db_backup_dir, filename)
                            file_stat = os.stat(file_path)
                            
                            backup_info = {
                                "type": "database",
                                "filename": filename,
                                "path": file_path,
                                "size": file_stat.st_size,
                                "created_at": datetime.fromtimestamp(file_stat.st_ctime).isoformat()
                            }
                            
                            # 尝试加载元数据
                            metadata_path = os.path.join(self.db_backup_dir, f"{os.path.splitext(filename)[0]}.meta.json")
                            if os.path.exists(metadata_path):
                                try:
                                    with open(metadata_path, "r") as f:
                                        metadata = json.load(f)
                                        backup_info["metadata"] = metadata
                                except Exception as e:
                                    logger.warning(f"读取备份元数据失败: {str(e)}")
                                    
                            backups.append(backup_info)
            
            # 按创建时间排序，最新的在前
            backups.sort(key=lambda x: x["created_at"], reverse=True)
            return backups
            
        except Exception as e:
            logger.error(f"列出备份时发生错误: {str(e)}")
            logger.exception(e)
            return []
    
    async def delete_backup(self, backup_path: str) -> Dict[str, Any]:
        """
        删除备份
        
        参数:
            backup_path: 备份文件路径
            
        返回:
            Dict[str, Any]: 删除结果
        """
        logger.info(f"删除备份: {backup_path}")
        
        try:
            # 检查备份文件是否存在
            if not os.path.exists(backup_path):
                return {
                    "success": False,
                    "error": f"备份文件不存在: {backup_path}"
                }
                
            # 安全检查：确保文件路径在备份目录内
            backup_path = os.path.abspath(backup_path)
            backup_dir = os.path.abspath(self.backup_dir)
            if not backup_path.startswith(backup_dir):
                return {
                    "success": False,
                    "error": f"安全错误: 尝试删除备份目录外的文件: {backup_path}"
                }
                
            # 删除备份文件
            os.remove(backup_path)
            
            # 如果是数据库备份，还需要删除元数据文件
            if backup_path.endswith('.sql.gz'):
                metadata_path = os.path.join(os.path.dirname(backup_path), f"{os.path.splitext(os.path.basename(backup_path))[0]}.meta.json")
                if os.path.exists(metadata_path):
                    os.remove(metadata_path)
                    
            logger.info(f"已删除备份: {backup_path}")
            return {
                "success": True,
                "deleted_path": backup_path
            }
            
        except Exception as e:
            logger.error(f"删除备份时发生错误: {str(e)}")
            logger.exception(e)
            return {
                "success": False,
                "error": f"删除备份失败: {str(e)}"
            }
            
    async def schedule_automatic_backup(self, schedule_type: str = "daily", max_backups: int = 7) -> Dict[str, Any]:
        """
        设置自动备份计划
        
        参数:
            schedule_type: 计划类型（"daily"、"weekly"、"monthly"）
            max_backups: 保留的最大备份数量
            
        返回:
            Dict[str, Any]: 设置结果
        """
        logger.info(f"设置自动备份计划: 类型={schedule_type}, 最大备份数={max_backups}")
        
        try:
            # 创建配置文件
            config = {
                "schedule_type": schedule_type,
                "max_backups": max_backups,
                "enabled": True,
                "last_backup": None,
                "next_backup": None
            }
            
            # 根据计划类型计算下次备份时间
            now = datetime.now()
            next_backup = None
            
            # 实际的下次备份时间计算可能需要使用更复杂的日程计算库（如 schedule 或 croniter）
            # 这里使用简单的实现
            
            # 保存配置
            config_path = os.path.join(self.backup_dir, "backup_schedule.json")
            with open(config_path, "w") as f:
                json.dump(config, f, indent=2)
                
            logger.info(f"已设置自动备份计划: {schedule_type}")
            return {
                "success": True,
                "schedule_type": schedule_type,
                "max_backups": max_backups,
                "config_path": config_path
            }
            
        except Exception as e:
            logger.error(f"设置自动备份计划时发生错误: {str(e)}")
            logger.exception(e)
            return {
                "success": False,
                "error": f"设置自动备份计划失败: {str(e)}"
            }
    
    async def run_automatic_backup(self) -> Dict[str, Any]:
        """
        执行自动备份（由定时任务调用）
        
        返回:
            Dict[str, Any]: 备份结果
        """
        logger.info("执行自动备份...")
        
        try:
            # 加载配置
            config_path = os.path.join(self.backup_dir, "backup_schedule.json")
            if not os.path.exists(config_path):
                return {
                    "success": False,
                    "error": "找不到自动备份配置文件"
                }
                
            with open(config_path, "r") as f:
                config = json.load(f)
                
            if not config.get("enabled", False):
                return {
                    "success": False,
                    "error": "自动备份已禁用"
                }
                
            # 执行备份
            backup_result = await self.create_full_backup("auto_backup")
            
            # 更新配置
            config["last_backup"] = datetime.now().isoformat()
            
            # 根据计划类型计算下次备份时间
            # 实际的下次备份时间计算可能需要使用更复杂的日程计算库
            
            # 保存配置
            with open(config_path, "w") as f:
                json.dump(config, f, indent=2)
                
            # 清理旧备份
            max_backups = config.get("max_backups", 7)
            
            # 列出所有文件备份
            file_backups = []
            if os.path.exists(self.file_backup_dir):
                for filename in os.listdir(self.file_backup_dir):
                    if filename.startswith("auto_backup") and filename.endswith('.tar.gz'):
                        file_path = os.path.join(self.file_backup_dir, filename)
                        file_stat = os.stat(file_path)
                        file_backups.append({
                            "path": file_path,
                            "created_at": datetime.fromtimestamp(file_stat.st_ctime)
                        })
                        
            # 按创建时间排序，最旧的在前
            file_backups.sort(key=lambda x: x["created_at"])
            
            # 删除多余的旧备份
            if len(file_backups) > max_backups:
                for backup in file_backups[:(len(file_backups) - max_backups)]:
                    await self.delete_backup(backup["path"])
                    logger.info(f"已删除旧的自动文件备份: {backup['path']}")
                    
            # 列出所有数据库备份
            db_backups = []
            if os.path.exists(self.db_backup_dir):
                for filename in os.listdir(self.db_backup_dir):
                    if filename.startswith("auto_backup") and filename.endswith('.sql.gz'):
                        file_path = os.path.join(self.db_backup_dir, filename)
                        file_stat = os.stat(file_path)
                        db_backups.append({
                            "path": file_path,
                            "created_at": datetime.fromtimestamp(file_stat.st_ctime)
                        })
                        
            # 按创建时间排序，最旧的在前
            db_backups.sort(key=lambda x: x["created_at"])
            
            # 删除多余的旧备份
            if len(db_backups) > max_backups:
                for backup in db_backups[:(len(db_backups) - max_backups)]:
                    await self.delete_backup(backup["path"])
                    logger.info(f"已删除旧的自动数据库备份: {backup['path']}")
            
            return backup_result
            
        except Exception as e:
            logger.error(f"执行自动备份时发生错误: {str(e)}")
            logger.exception(e)
            return {
                "success": False,
                "error": f"自动备份失败: {str(e)}"
            } 
