"""
认证服务配置模块

提供认证服务的配置管理功能
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict, field
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

@dataclass
class AuthServerConfig:
    """认证服务器配置"""
    host: str = "0.0.0.0"           # 监听地址
    port: int = 65520               # 监听端口
    workers: int = 4                # 工作进程数
    backlog: int = 100             # 连接队列大小
    keepalive: int = 60            # 保持连接时间(秒)
    timeout: int = 30              # 请求超时时间(秒)

@dataclass
class TLSConfig:
    """TLS配置"""
    enabled: bool = True           # 是否启用TLS
    cert_file: str = None         # 证书文件路径
    key_file: str = None          # 私钥文件路径
    allow_http: bool = True       # 允许HTTP降级
    min_version: str = "TLS1.2"   # 最低TLS版本
    ciphers: str = (              # 加密套件
        "ECDHE+AESGCM:"
        "ECDHE+CHACHA20:"
        "DHE+AESGCM:"
        "DHE+CHACHA20"
    )

@dataclass
class JWTConfig:
    """JWT配置"""
    secret: str = None            # 密钥
    algorithm: str = "HS256"      # 算法
    expire: int = 3600           # 过期时间(秒)
    refresh_expire: int = 86400  # 刷新令牌过期时间(秒)

@dataclass
class SessionConfig:
    """会话配置"""
    timeout: int = 3600          # 会话超时时间(秒)
    max_sessions: int = 10       # 每个用户最大会话数
    cleanup_interval: int = 300  # 清理间隔(秒)

@dataclass
class SecurityConfig:
    """安全配置"""
    max_retries: int = 3         # 最大重试次数
    retry_interval: int = 5      # 重试间隔(秒)
    lockout_time: int = 300      # 锁定时间(秒)
    min_password_length: int = 8  # 最小密码长度
    password_complexity: Dict = None  # 密码复杂度要求

class AuthConfig(BaseModel):
    """认证服务配置"""
    server: AuthServerConfig = Field(default_factory=AuthServerConfig)
    tls: TLSConfig = Field(default_factory=TLSConfig)
    jwt: JWTConfig = Field(default_factory=JWTConfig)
    session: SessionConfig = Field(default_factory=SessionConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    
    @classmethod
    def load(cls, config_file: str) -> 'AuthConfig':
        """从配置文件加载配置
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            认证配置对象
        """
        try:
            if not os.path.exists(config_file):
                logger.warning(f"配置文件不存在: {config_file}")
                return cls()
                
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
                
            config = cls()
            
            # 服务器配置
            if 'server' in config_data:
                server_data = config_data['server']
                config.server = AuthServerConfig(
                    host=server_data.get('host', config.server.host),
                    port=server_data.get('port', config.server.port),
                    workers=server_data.get('workers', config.server.workers),
                    backlog=server_data.get('backlog', config.server.backlog),
                    keepalive=server_data.get('keepalive', config.server.keepalive),
                    timeout=server_data.get('timeout', config.server.timeout)
                )
                
            # TLS配置
            if 'tls' in config_data:
                tls_data = config_data['tls']
                config.tls = TLSConfig(
                    enabled=tls_data.get('enabled', config.tls.enabled),
                    cert_file=tls_data.get('cert_file', config.tls.cert_file),
                    key_file=tls_data.get('key_file', config.tls.key_file),
                    allow_http=tls_data.get('allow_http', config.tls.allow_http),
                    min_version=tls_data.get('min_version', config.tls.min_version),
                    ciphers=tls_data.get('ciphers', config.tls.ciphers)
                )
                
            # JWT配置
            if 'jwt' in config_data:
                jwt_data = config_data['jwt']
                config.jwt = JWTConfig(
                    secret=jwt_data.get('secret', config.jwt.secret),
                    algorithm=jwt_data.get('algorithm', config.jwt.algorithm),
                    expire=jwt_data.get('expire', config.jwt.expire),
                    refresh_expire=jwt_data.get(
                        'refresh_expire',
                        config.jwt.refresh_expire
                    )
                )
                
            # 会话配置
            if 'session' in config_data:
                session_data = config_data['session']
                config.session = SessionConfig(
                    timeout=session_data.get('timeout', config.session.timeout),
                    max_sessions=session_data.get(
                        'max_sessions',
                        config.session.max_sessions
                    ),
                    cleanup_interval=session_data.get(
                        'cleanup_interval',
                        config.session.cleanup_interval
                    )
                )
                
            # 安全配置
            if 'security' in config_data:
                security_data = config_data['security']
                config.security = SecurityConfig(
                    max_retries=security_data.get(
                        'max_retries',
                        config.security.max_retries
                    ),
                    retry_interval=security_data.get(
                        'retry_interval',
                        config.security.retry_interval
                    ),
                    lockout_time=security_data.get(
                        'lockout_time',
                        config.security.lockout_time
                    ),
                    min_password_length=security_data.get(
                        'min_password_length',
                        config.security.min_password_length
                    ),
                    password_complexity=security_data.get(
                        'password_complexity',
                        config.security.password_complexity
                    )
                )
                
            return config
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return cls()
            
    def save(self, config_file: str) -> bool:
        """保存配置到文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            是否成功
        """
        try:
            # 转换为字典
            config_data = {
                'server': asdict(self.server),
                'tls': asdict(self.tls),
                'jwt': asdict(self.jwt),
                'session': asdict(self.session),
                'security': asdict(self.security)
            }
            
            # 保存到文件
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.safe_dump(config_data, f, default_flow_style=False)
                
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
            
    def validate(self) -> Tuple[bool, Optional[str]]:
        """验证配置
        
        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 验证服务器配置
            if not 0 <= self.server.port <= 65535:
                return False, "无效的端口号"
                
            if self.server.workers < 1:
                return False, "工作进程数必须大于0"
                
            # 验证TLS配置
            if self.tls.enabled:
                if not self.tls.cert_file or not self.tls.key_file:
                    return False, "TLS启用时必须指定证书和私钥文件"
                    
                if not os.path.exists(self.tls.cert_file):
                    return False, f"证书文件不存在: {self.tls.cert_file}"
                    
                if not os.path.exists(self.tls.key_file):
                    return False, f"私钥文件不存在: {self.tls.key_file}"
                    
            # 验证JWT配置
            if not self.jwt.secret:
                return False, "必须指定JWT密钥"
                
            if self.jwt.expire < 60:
                return False, "JWT过期时间必须大于60秒"
                
            # 验证会话配置
            if self.session.timeout < 60:
                return False, "会话超时时间必须大于60秒"
                
            if self.session.max_sessions < 1:
                return False, "每个用户最大会话数必须大于0"
                
            # 验证安全配置
            if self.security.max_retries < 1:
                return False, "最大重试次数必须大于0"
                
            if self.security.retry_interval < 1:
                return False, "重试间隔必须大于0秒"
                
            if self.security.min_password_length < 8:
                return False, "最小密码长度必须大于等于8"
                
            return True, None
            
        except Exception as e:
            return False, str(e)
            
    def get_default_config_file(self) -> str:
        """获取默认配置文件路径"""
        return os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            'auth.yml'
        ) 

@dataclass
class AuthConfigManager:
    config_path: str
    _config: Optional[AuthConfig] = field(init=False, default=None)

    def __post_init__(self):
        self.reload_config() 
