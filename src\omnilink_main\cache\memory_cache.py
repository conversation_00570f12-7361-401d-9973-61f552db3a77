# E:\key\ky\src\cache\memory_cache.py
"""
内存缓存 (L1 Cache) 实现模块

基于 cachetools 库，提供统一的内存缓存管理。
"""

import time
import threading
from cachetools import LRUCache, TTLCache, cached, keys
import logging

# 获取 logger 实例
logger = logging.getLogger(__name__)

# 默认配置 (后续应从配置文件加载)
DEFAULT_LRU_MAXSIZE = 1024
DEFAULT_TTL_MAXSIZE = 2048
DEFAULT_TTL_SECONDS = 300  # 默认5分钟

class CacheManager:
    """
    内存缓存管理器

    负责创建和管理不同的命名缓存实例。
    确保缓存实例的单例性。
    """
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        # 实现单例模式
        if not cls._instance:
            with cls._lock:
                # Double-check locking
                if not cls._instance:
                    cls._instance = super(CacheManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """
        初始化缓存管理器，创建预定义的缓存实例。
        """
        if self._initialized:
            return
        with self._lock:
            if self._initialized:
                return
            self._caches = {}
            # 创建一些默认的、常用的缓存实例
            self.create_lru_cache('default_lru', maxsize=DEFAULT_LRU_MAXSIZE)
            self.create_ttl_cache('default_ttl', maxsize=DEFAULT_TTL_MAXSIZE, ttl=DEFAULT_TTL_SECONDS)
            self._initialized = True

    def create_lru_cache(self, name: str, maxsize: int):
        """
        创建或获取一个 LRU 缓存实例。

        Args:
            name (str): 缓存实例的名称。
            maxsize (int): 缓存的最大条目数。

        Returns:
            LRUCache: LRU 缓存实例。
        """
        with self._lock:
            if name not in self._caches:
                self._caches[name] = LRUCache(maxsize=maxsize)
            elif not isinstance(self._caches[name], LRUCache) or self._caches[name].maxsize != maxsize:
                # 如果存在同名但类型或大小不同的缓存，则覆盖（或抛出异常，根据策略决定）
                logger.warning(f"覆盖已存在的缓存 '{name}' 以创建新的 LRU 缓存 (maxsize={maxsize})")
                self._caches[name] = LRUCache(maxsize=maxsize)
            return self._caches[name]

    def create_ttl_cache(self, name: str, maxsize: int, ttl: int):
        """
        创建或获取一个 TTL 缓存实例。

        Args:
            name (str): 缓存实例的名称。
            maxsize (int): 缓存的最大条目数。
            ttl (int): 缓存条目的存活时间（秒）。

        Returns:
            TTLCache: TTL 缓存实例。
        """
        with self._lock:
            if name not in self._caches:
                self._caches[name] = TTLCache(maxsize=maxsize, ttl=ttl)
            elif not isinstance(self._caches[name], TTLCache) or \
                 self._caches[name].maxsize != maxsize or self._caches[name].ttl != ttl:
                logger.warning(f"覆盖已存在的缓存 '{name}' 以创建新的 TTL 缓存 (maxsize={maxsize}, ttl={ttl})")
                self._caches[name] = TTLCache(maxsize=maxsize, ttl=ttl)
            return self._caches[name]

    def get_cache(self, name: str):
        """
        根据名称获取已创建的缓存实例。

        Args:
            name (str): 缓存实例的名称。

        Returns:
            Cache: 对应的缓存实例，如果不存在则返回 None。
        """
        return self._caches.get(name)

    def get_default_lru_cache(self):
        """
        获取默认的 LRU 缓存实例。
        """
        return self.get_cache('default_lru')

    def get_default_ttl_cache(self):
        """
        获取默认的 TTL 缓存实例。
        """
        return self.get_cache('default_ttl')

    def clear_cache(self, name: str):
        """
        清空指定名称的缓存。

        Args:
            name (str): 缓存实例的名称。
        """
        cache = self.get_cache(name)
        if cache:
            cache.clear()
            logger.info(f"缓存 '{name}' 已清空。")
        else:
            logger.warning(f"尝试清空不存在的缓存 '{name}'。")

    def clear_all_caches(self):
        """
        清空所有已管理的缓存实例。
        谨慎使用！
        """
        with self._lock:
            for name, cache in self._caches.items():
                cache.clear()
            logger.info("所有内存缓存已清空。")

# 全局缓存管理器实例
_cache_manager_instance = None
_cache_manager_lock = threading.Lock()

def get_cache_manager():
    """
    获取 CacheManager 的单例实例（懒加载）。
    """
    global _cache_manager_instance
    if _cache_manager_instance is None:
        with _cache_manager_lock:
            if _cache_manager_instance is None:
                _cache_manager_instance = CacheManager()
    return _cache_manager_instance

# --- 装饰器辅助函数 ---

def lru_cache_decorator(maxsize=DEFAULT_LRU_MAXSIZE, typed=False, cache_name='default_lru_decorator'):
    """
    提供 LRU 缓存装饰器。

    Args:
        maxsize (int): 缓存最大条目数。
        typed (bool): 是否区分不同类型的参数 (e.g., 3 和 3.0)。
        cache_name (str): 用于存储此装饰器使用的缓存实例的名称。

    Returns:
        function: 装饰器函数。
    """
    # 确保为该装饰器创建一个独立的缓存实例
    cache_instance = get_cache_manager().create_lru_cache(cache_name, maxsize)
    return cached(cache=cache_instance, key=keys.hashkey, typed=typed)

def ttl_cache_decorator(maxsize=DEFAULT_TTL_MAXSIZE, ttl=DEFAULT_TTL_SECONDS, timer=time.monotonic, typed=False, cache_name='default_ttl_decorator'):
    """
    提供 TTL 缓存装饰器。

    Args:
        maxsize (int): 缓存最大条目数。
        ttl (int): 存活时间（秒）。
        timer (function): 时间源。
        typed (bool): 是否区分不同类型的参数。
        cache_name (str): 用于存储此装饰器使用的缓存实例的名称。

    Returns:
        function: 装饰器函数。
    """
    # 确保为该装饰器创建一个独立的缓存实例
    cache_instance = get_cache_manager().create_ttl_cache(cache_name, maxsize, ttl)
    # 注意：TTLCache本身没有typed参数，hashkey的typed在这里生效
    return cached(cache=cache_instance, key=keys.hashkey, typed=typed)


# --- 使用示例 ---

if __name__ == '__main__':
    # 配置基本的日志记录器以查看输出
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    cache_manager = get_cache_manager() # 获取实例

    logger.info("开始缓存管理演示...") # 使用logger

    # 获取默认缓存
    lru_c = cache_manager.get_default_lru_cache()
    ttl_c = cache_manager.get_default_ttl_cache()

    # 直接使用缓存 API
    lru_c['key1'] = 'value1_lru'
    ttl_c['key1'] = 'value1_ttl'
    logger.info(f"LRU Get key1: {lru_c.get('key1')}") # 使用logger
    logger.info(f"TTL Get key1: {ttl_c.get('key1')}") # 使用logger
    logger.info(f"LRU Size: {lru_c.currsize}") # 使用logger
    logger.info(f"TTL Size: {ttl_c.currsize}") # 使用logger

    # 使用装饰器示例
    @lru_cache_decorator(maxsize=2)
    def expensive_calculation_lru(x, y):
        logger.info(f"LRU Decorator: Calculating {x} + {y}...") # 使用logger
        time.sleep(0.1) # 模拟耗时操作
        return x + y

    @ttl_cache_decorator(maxsize=5, ttl=1) # ttl设置为1秒
    def expensive_calculation_ttl(a, b):
        logger.info(f"TTL Decorator: Calculating {a} * {b}...") # 使用logger
        time.sleep(0.1) # 模拟耗时操作
        return a * b

    logger.info("\n--- LRU Decorator Test ---")
    logger.info(f"Result 1: {expensive_calculation_lru(1, 2)}")
    logger.info(f"Result 2: {expensive_calculation_lru(1, 2)}") # Should hit cache
    logger.info(f"Result 3: {expensive_calculation_lru(2, 3)}")
    logger.info(f"Result 4: {expensive_calculation_lru(3, 4)}") # (1,2) should be evicted
    logger.info(f"Result 5: {expensive_calculation_lru(1, 2)}") # Should calculate again
    logger.info(f"LRU Decorator Cache Info: {cache_manager.get_cache('default_lru_decorator')}")


    logger.info("\n--- TTL Decorator Test ---")
    logger.info(f"Result 1: {expensive_calculation_ttl(5, 6)}")
    logger.info(f"Result 2: {expensive_calculation_ttl(5, 6)}") # Should hit cache
    time.sleep(1.1) # Wait for TTL to expire
    logger.info(f"Result 3: {expensive_calculation_ttl(5, 6)}") # Should calculate again due to TTL expiry
    logger.info(f"TTL Decorator Cache Info: {cache_manager.get_cache('default_ttl_decorator')}")

    # 清空缓存
    # cache_manager.clear_cache('default_lru')
    # cache_manager.clear_all_caches()
    logger.info("缓存管理演示结束。") # 使用logger
