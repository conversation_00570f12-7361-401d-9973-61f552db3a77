from typing import Optional
from pydantic import BaseModel, Field
import datetime

# Shared properties
class SlaveServerBase(BaseModel):
    server_id: str = Field(..., description="Unique hardware-based ID for the slave server (e.g., MAC address)")
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    ip_address: Optional[str] = Field(None, max_length=50)
    port: Optional[int] = Field(None)
    status: Optional[str] = Field(None, max_length=20)
    version: Optional[str] = Field(None, max_length=20)

# Properties to receive on item creation
class SlaveServerCreate(SlaveServerBase):
    pass

# Properties to receive on item update
class SlaveServerUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    ip_address: Optional[str] = Field(None, max_length=50)
    port: Optional[int] = Field(None)
    status: Optional[str] = Field(None, max_length=20)
    version: Optional[str] = Field(None, max_length=20)

# Properties shared by models stored in DB
class SlaveServerInDBBase(SlaveServerBase):
    id: int
    server_id: str
    name: str
    status: str
    last_seen: Optional[datetime.datetime] = None
    organization_id: Optional[int] = None
    created_at: datetime.datetime
    updated_at: datetime.datetime

    class Config:
        from_attributes = True

# Properties to return to client
class SlaveServer(SlaveServerInDBBase):
    pass

# Properties to return to client on creation
class SlaveServerCreateResponse(SlaveServer):
    api_key: str

# Properties stored in DB
class SlaveServerInDB(SlaveServerInDBBase):
    pass

class SlaveServerRegister(SlaveServerBase):
    pass 