#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Docker部署工具脚本

提供OmniLink系统在Docker环境中的部署管理功能，支持构建、启动、停止、重启等操作。
"""

import os
import sys
import argparse
import subprocess
import json
import shutil
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union


class DockerManager:
    """Docker部署管理器"""
    
    def __init__(self, base_dir: str, compose_file: str = "docker-compose.yaml"):
        """
        初始化Docker管理器
        
        Args:
            base_dir: 项目根目录路径
            compose_file: Docker Compose 文件名
        """
        self.base_dir = Path(base_dir)
        self.deploy_dir = self.base_dir / "ky" / "deployment"
        self.compose_file = self.deploy_dir / compose_file
        self.config_dir = self.base_dir / "config" / "keys"
        self.config_files = ["sqlpsd.json", "redis_config.json", "mansever.json"]
        self.version = "1.0.0"  # 默认版本号
        
        # 读取版本号（如果存在）
        version_file = self.base_dir / "VERSION"
        if version_file.exists():
            try:
                with open(version_file, "r") as f:
                    self.version = f.read().strip()
            except Exception as e:
                print(f"读取版本号文件失败: {e}")
    
    def check_docker(self) -> bool:
        """
        检查Docker和Docker Compose是否已安装
        
        Returns:
            bool: 检查结果，True表示已安装
        """
        try:
            # 检查Docker是否安装
            docker_result = subprocess.run(
                ["docker", "--version"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            if docker_result.returncode != 0:
                print("Docker未安装或无法访问")
                return False
            
            # 检查Docker Compose是否安装
            compose_result = subprocess.run(
                ["docker-compose", "--version"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            if compose_result.returncode != 0:
                print("Docker Compose未安装或无法访问")
                return False
            
            print(f"Docker: {docker_result.stdout.strip()}")
            print(f"Docker Compose: {compose_result.stdout.strip()}")
            return True
        
        except Exception as e:
            print(f"检查Docker环境时发生错误: {e}")
            return False
    
    def build_images(self, no_cache: bool = False) -> bool:
        """
        构建Docker镜像
        
        Args:
            no_cache: 是否禁用缓存
        
        Returns:
            bool: 构建结果，True表示成功
        """
        try:
            cmd = ["docker-compose", "-f", str(self.compose_file), "build"]
            if no_cache:
                cmd.append("--no-cache")
            
            print("构建Docker镜像中...")
            result = subprocess.run(cmd, cwd=self.deploy_dir)
            
            if result.returncode == 0:
                print("Docker镜像构建成功")
                return True
            else:
                print("Docker镜像构建失败")
                return False
        
        except Exception as e:
            print(f"构建Docker镜像时发生错误: {e}")
            return False
    
    def check_configs(self) -> bool:
        """
        检查必要的配置文件是否存在
        
        Returns:
            bool: 检查结果，True表示所有配置文件都存在
        """
        missing_files = []
        
        # 确保配置目录存在
        if not self.config_dir.exists():
            try:
                os.makedirs(self.config_dir, exist_ok=True)
                print(f"创建配置目录: {self.config_dir}")
            except Exception as e:
                print(f"无法创建配置目录 {self.config_dir}: {e}")
                return False
        
        # 检查所有必要的配置文件
        for config_file in self.config_files:
            file_path = self.config_dir / config_file
            if not file_path.exists():
                missing_files.append(config_file)
        
        if missing_files:
            print("缺少以下配置文件:")
            for file in missing_files:
                print(f"  - {file}")
            
            self.create_sample_configs(missing_files)
            return False
        
        return True
    
    def create_sample_configs(self, missing_files: List[str]):
        """
        创建示例配置文件
        
        Args:
            missing_files: 缺少的配置文件列表
        """
        for file in missing_files:
            file_path = self.config_dir / file
            
            try:
                # 根据文件类型创建不同的示例配置
                if file == "sqlpsd.json":
                    sample_config = {
                        "host": "localhost",
                        "port": 5432,
                        "database": "omnilink",
                        "user": "postgres",
                        "password": "请修改为安全密码"
                    }
                elif file == "redis_config.json":
                    sample_config = {
                        "host": "localhost",
                        "port": 6379,
                        "db": 0,
                        "password": "",
                        "ssl": False,
                        "timeout": 5
                    }
                elif file == "mansever.json":
                    sample_config = {
                        "host": "localhost",
                        "port": 8000,
                        "admin_username": "admin",
                        "admin_password": "请修改为安全密码",
                        "secret_key": "请修改为随机字符串",
                        "jwt_secret": "请修改为随机字符串",
                        "debug": False
                    }
                else:
                    sample_config = {"message": "请根据需要配置此文件"}
                
                # 写入示例配置
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(sample_config, f, indent=4, ensure_ascii=False)
                
                print(f"已创建示例配置文件: {file_path}")
                print("请修改此文件中的配置值后再重新运行部署")
            
            except Exception as e:
                print(f"创建示例配置文件 {file} 时出错: {e}")
    
    def start(self, detached: bool = True) -> bool:
        """
        启动Docker容器
        
        Args:
            detached: 是否在后台运行容器
        
        Returns:
            bool: 启动结果，True表示成功
        """
        # 检查配置文件
        if not self.check_configs():
            print("配置文件检查未通过，请先配置必要的文件")
            return False
        
        try:
            cmd = ["docker-compose", "-f", str(self.compose_file), "up"]
            if detached:
                cmd.append("-d")
            
            print("启动OmniLink容器中...")
            result = subprocess.run(cmd, cwd=self.deploy_dir)
            
            if result.returncode == 0:
                print("OmniLink容器已成功启动")
                return True
            else:
                print("OmniLink容器启动失败")
                return False
        
        except Exception as e:
            print(f"启动OmniLink容器时发生错误: {e}")
            return False
    
    def stop(self) -> bool:
        """
        停止Docker容器
        
        Returns:
            bool: 停止结果，True表示成功
        """
        try:
            cmd = ["docker-compose", "-f", str(self.compose_file), "down"]
            
            print("停止OmniLink容器中...")
            result = subprocess.run(cmd, cwd=self.deploy_dir)
            
            if result.returncode == 0:
                print("OmniLink容器已停止")
                return True
            else:
                print("停止OmniLink容器失败")
                return False
        
        except Exception as e:
            print(f"停止OmniLink容器时发生错误: {e}")
            return False
    
    def restart(self) -> bool:
        """
        重启Docker容器
        
        Returns:
            bool: 重启结果，True表示成功
        """
        return self.stop() and self.start()
    
    def show_logs(self, service: Optional[str] = None, follow: bool = False, tail: int = 100) -> None:
        """
        查看容器日志
        
        Args:
            service: 服务名称，None表示所有服务
            follow: 是否持续显示新的日志
            tail: 显示最近的日志行数
        """
        try:
            cmd = ["docker-compose", "-f", str(self.compose_file), "logs"]
            
            if follow:
                cmd.append("-f")
            
            cmd.extend(["--tail", str(tail)])
            
            if service:
                cmd.append(service)
            
            print(f"查看{'所有服务' if not service else service}的日志...")
            subprocess.run(cmd, cwd=self.deploy_dir)
        
        except Exception as e:
            print(f"查看日志时发生错误: {e}")
    
    def show_status(self) -> None:
        """显示容器状态"""
        try:
            cmd = ["docker-compose", "-f", str(self.compose_file), "ps"]
            
            print("OmniLink容器状态:")
            subprocess.run(cmd, cwd=self.deploy_dir)
        
        except Exception as e:
            print(f"查看容器状态时发生错误: {e}")
    
    def run_migrations(self) -> bool:
        """
        运行数据库迁移
        
        Returns:
            bool: 迁移结果，True表示成功
        """
        try:
            cmd = [
                "docker-compose", 
                "-f", 
                str(self.compose_file), 
                "exec", 
                "main_server",
                "python", 
                "-m", 
                "ky.tools.db_migrator",
                "upgrade"
            ]
            
            print("运行数据库迁移...")
            result = subprocess.run(cmd, cwd=self.deploy_dir)
            
            if result.returncode == 0:
                print("数据库迁移成功")
                return True
            else:
                print("数据库迁移失败")
                return False
        
        except Exception as e:
            print(f"运行数据库迁移时发生错误: {e}")
            return False
    
    def backup_database(self, output_dir: Optional[str] = None) -> bool:
        """
        备份数据库
        
        Args:
            output_dir: 输出目录，默认为当前目录
        
        Returns:
            bool: 备份结果，True表示成功
        """
        if output_dir is None:
            output_dir = os.getcwd()
        else:
            Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        backup_file = f"omnilink_backup_{timestamp}.sql"
        backup_path = Path(output_dir) / backup_file
        
        try:
            # 获取数据库配置
            db_config = self.get_db_config()
            if not db_config:
                return False
            
            cmd = [
                "docker-compose", 
                "-f", 
                str(self.compose_file), 
                "exec", 
                "-T",  # 非TTY模式，避免pg_dump输出问题
                "db",
                "pg_dump", 
                "-U", db_config.get("user", "postgres"),
                "-d", db_config.get("database", "omnilink"),
                "-F", "p",  # 纯文本格式
                "-f", "/tmp/backup.sql"
            ]
            
            print("备份数据库中...")
            result = subprocess.run(cmd, cwd=self.deploy_dir)
            
            if result.returncode == 0:
                # 从容器内复制备份文件到本地
                copy_cmd = [
                    "docker-compose", 
                    "-f", 
                    str(self.compose_file), 
                    "cp", 
                    "db:/tmp/backup.sql", 
                    str(backup_path)
                ]
                
                copy_result = subprocess.run(copy_cmd, cwd=self.deploy_dir)
                
                if copy_result.returncode == 0:
                    print(f"数据库备份成功: {backup_path}")
                    return True
                else:
                    print("复制备份文件失败")
                    return False
            else:
                print("数据库备份失败")
                return False
        
        except Exception as e:
            print(f"备份数据库时发生错误: {e}")
            return False
    
    def get_db_config(self) -> Dict:
        """
        获取数据库配置
        
        Returns:
            Dict: 数据库配置信息
        """
        config_file = self.config_dir / "sqlpsd.json"
        
        try:
            if not config_file.exists():
                print(f"数据库配置文件不存在: {config_file}")
                return {}
            
            with open(config_file, "r", encoding="utf-8") as f:
                return json.load(f)
        
        except Exception as e:
            print(f"读取数据库配置失败: {e}")
            return {}


def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="OmniLink系统Docker部署工具")
    
    parser.add_argument(
        "command", 
        choices=[
            "build", 
            "start", 
            "stop", 
            "restart", 
            "logs", 
            "status", 
            "migrate",
            "backup",
            "check"
        ],
        help="执行的操作"
    )
    
    parser.add_argument(
        "--base-dir", 
        type=str, 
        default=".",
        help="项目根目录路径，默认为当前目录"
    )
    
    parser.add_argument(
        "--compose-file", 
        type=str, 
        default="docker-compose.yaml",
        help="Docker Compose文件名"
    )
    
    parser.add_argument(
        "--no-cache", 
        action="store_true",
        help="构建时不使用缓存"
    )
    
    parser.add_argument(
        "--detached", 
        action="store_true", 
        default=True,
        help="在后台运行容器"
    )
    
    parser.add_argument(
        "--service", 
        type=str, 
        help="指定服务名称（用于logs命令）"
    )
    
    parser.add_argument(
        "--follow", 
        action="store_true",
        help="持续显示新的日志（用于logs命令）"
    )
    
    parser.add_argument(
        "--tail", 
        type=int, 
        default=100,
        help="显示最近的日志行数（用于logs命令）"
    )
    
    parser.add_argument(
        "--output-dir", 
        type=str,
        help="输出目录（用于backup命令）"
    )
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()
    
    try:
        manager = DockerManager(args.base_dir, args.compose_file)
        
        if args.command == "build":
            if not manager.check_docker():
                return 1
            success = manager.build_images(args.no_cache)
        
        elif args.command == "start":
            if not manager.check_docker():
                return 1
            success = manager.start(args.detached)
        
        elif args.command == "stop":
            if not manager.check_docker():
                return 1
            success = manager.stop()
        
        elif args.command == "restart":
            if not manager.check_docker():
                return 1
            success = manager.restart()
        
        elif args.command == "logs":
            if not manager.check_docker():
                return 1
            manager.show_logs(args.service, args.follow, args.tail)
            success = True
        
        elif args.command == "status":
            if not manager.check_docker():
                return 1
            manager.show_status()
            success = True
        
        elif args.command == "migrate":
            if not manager.check_docker():
                return 1
            success = manager.run_migrations()
        
        elif args.command == "backup":
            if not manager.check_docker():
                return 1
            success = manager.backup_database(args.output_dir)
        
        elif args.command == "check":
            docker_ok = manager.check_docker()
            configs_ok = manager.check_configs()
            success = docker_ok and configs_ok
            
            if success:
                print("\n✅ 系统检查通过，可以进行部署")
            else:
                print("\n❌ 系统检查未通过，请解决上述问题后重试")
        
        return 0 if success else 1
    
    except Exception as e:
        print(f"执行命令时发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 