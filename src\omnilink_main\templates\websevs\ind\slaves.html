<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>从服务器管理 - Web管理服务</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .server-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .server-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1.5rem;
            position: relative;
            border-left: 4px solid var(--primary-color);
        }
        
        .server-card.online {
            border-left-color: var(--secondary-color);
        }
        
        .server-card.offline {
            border-left-color: var(--danger-color);
        }
        
        .server-card.warning {
            border-left-color: var(--tertiary-color);
        }
        
        .server-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--light-color);
        }
        
        .server-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .server-status {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
        }
        
        .server-status.online {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .server-status.offline {
            background-color: var(--danger-color);
            color: white;
        }
        
        .server-status.warning {
            background-color: var(--tertiary-color);
            color: var(--dark-color);
        }
        
        .server-detail {
            display: flex;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .server-detail-label {
            width: 100px;
            font-weight: bold;
            color: var(--dark-color);
        }
        
        .server-detail-value {
            flex: 1;
            color: var(--gray-color);
        }
        
        .server-actions {
            margin-top: 1.5rem;
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-warning {
            background-color: var(--tertiary-color);
            color: var(--dark-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .resource-bars {
            margin: 1rem 0;
        }
        
        .resource-bar {
            margin-bottom: 0.75rem;
        }
        
        .resource-bar-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
        }
        
        .progress-container {
            height: 0.5rem;
            background-color: var(--light-color);
            border-radius: 1rem;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            border-radius: 1rem;
        }
        
        .progress-bar.cpu {
            background-color: var(--primary-color);
        }
        
        .progress-bar.memory {
            background-color: var(--tertiary-color);
        }
        
        .progress-bar.disk {
            background-color: var(--secondary-color);
        }
        
        .devices-summary {
            margin-top: 1rem;
            background-color: var(--light-color);
            padding: 0.75rem;
            border-radius: var(--border-radius);
        }
        
        .device-count {
            display: flex;
            justify-content: space-around;
            margin-top: 0.5rem;
        }
        
        .count-item {
            text-align: center;
        }
        
        .count-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .count-label {
            font-size: 0.8rem;
            color: var(--gray-color);
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--light-color);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--gray-color);
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--gray-color);
            border-radius: var(--border-radius);
        }
        
        .modal-footer {
            margin-top: 1.5rem;
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }

        .search-container {
            margin-bottom: 1.5rem;
            display: flex;
            gap: 0.5rem;
        }
        
        .search-input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid var(--gray-color);
            border-radius: var(--border-radius);
        }
    </style>
</head>
<body>
    <header>
        <h1>从服务器管理</h1>
        <nav>
            <ul>
                <li><a href="{{ url_for('websevs.ind.index') }}">首页</a></li>
                <li><a href="{{ url_for('websevs.ind.dashboard') }}">仪表盘</a></li>
                <li><a href="{{ url_for('websevs.ind.devices') }}">设备管理</a></li>
                <li><a href="{{ url_for('websevs.ind.slaves') }}">从服务器</a></li>
                <li><a href="{{ url_for('websevs.ind.settings') }}">系统设置</a></li>
                <li><a href="{{ url_for('websevs.ind.monitoring') }}">系统监控</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h2>从服务器列表</h2>
                <button class="btn btn-primary" id="addServerBtn">添加从服务器</button>
            </div>

            <div class="search-container">
                <input type="text" class="search-input" placeholder="搜索从服务器...">
                <button class="btn btn-primary">搜索</button>
            </div>

            <div class="server-grid">
                <!-- 服务器卡片 -->
                <div class="server-card online">
                    <div class="server-header">
                        <span class="server-name">工作站A-1</span>
                        <span class="server-status online">在线</span>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">IP地址:</div>
                        <div class="server-detail-value">*************</div>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">状态:</div>
                        <div class="server-detail-value">正常运行中</div>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">运行时间:</div>
                        <div class="server-detail-value">7天5小时</div>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">上次同步:</div>
                        <div class="server-detail-value">2023-07-15 15:30</div>
                    </div>
                    
                    <div class="resource-bars">
                        <div class="resource-bar">
                            <div class="resource-bar-label">
                                <span>CPU</span>
                                <span>45%</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar cpu" style="width: 45%"></div>
                            </div>
                        </div>
                        <div class="resource-bar">
                            <div class="resource-bar-label">
                                <span>内存</span>
                                <span>60%</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar memory" style="width: 60%"></div>
                            </div>
                        </div>
                        <div class="resource-bar">
                            <div class="resource-bar-label">
                                <span>磁盘</span>
                                <span>30%</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar disk" style="width: 30%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="devices-summary">
                        <div style="font-weight: bold; margin-bottom: 0.5rem;">设备统计</div>
                        <div class="device-count">
                            <div class="count-item">
                                <div class="count-value">12</div>
                                <div class="count-label">总计</div>
                            </div>
                            <div class="count-item">
                                <div class="count-value">10</div>
                                <div class="count-label">在线</div>
                            </div>
                            <div class="count-item">
                                <div class="count-value">8</div>
                                <div class="count-label">已共享</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="server-actions">
                        <button class="btn btn-warning edit-server-btn">编辑</button>
                        <button class="btn btn-primary">重启服务</button>
                        <button class="btn btn-danger">断开连接</button>
                    </div>
                </div>
                
                <div class="server-card warning">
                    <div class="server-header">
                        <span class="server-name">工作站B-2</span>
                        <span class="server-status warning">资源紧张</span>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">IP地址:</div>
                        <div class="server-detail-value">*************</div>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">状态:</div>
                        <div class="server-detail-value">CPU/内存使用率高</div>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">运行时间:</div>
                        <div class="server-detail-value">14天3小时</div>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">上次同步:</div>
                        <div class="server-detail-value">2023-07-15 15:15</div>
                    </div>
                    
                    <div class="resource-bars">
                        <div class="resource-bar">
                            <div class="resource-bar-label">
                                <span>CPU</span>
                                <span>85%</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar cpu" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="resource-bar">
                            <div class="resource-bar-label">
                                <span>内存</span>
                                <span>90%</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar memory" style="width: 90%"></div>
                            </div>
                        </div>
                        <div class="resource-bar">
                            <div class="resource-bar-label">
                                <span>磁盘</span>
                                <span>45%</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar disk" style="width: 45%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="devices-summary">
                        <div style="font-weight: bold; margin-bottom: 0.5rem;">设备统计</div>
                        <div class="device-count">
                            <div class="count-item">
                                <div class="count-value">8</div>
                                <div class="count-label">总计</div>
                            </div>
                            <div class="count-item">
                                <div class="count-value">8</div>
                                <div class="count-label">在线</div>
                            </div>
                            <div class="count-item">
                                <div class="count-value">6</div>
                                <div class="count-label">已共享</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="server-actions">
                        <button class="btn btn-warning edit-server-btn">编辑</button>
                        <button class="btn btn-primary">重启服务</button>
                        <button class="btn btn-danger">断开连接</button>
                    </div>
                </div>
                
                <div class="server-card offline">
                    <div class="server-header">
                        <span class="server-name">工作站C-3</span>
                        <span class="server-status offline">离线</span>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">IP地址:</div>
                        <div class="server-detail-value">*************</div>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">状态:</div>
                        <div class="server-detail-value">无法连接</div>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">最后在线:</div>
                        <div class="server-detail-value">2023-07-14 18:45</div>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">上次同步:</div>
                        <div class="server-detail-value">2023-07-14 18:30</div>
                    </div>
                    
                    <div class="devices-summary">
                        <div style="font-weight: bold; margin-bottom: 0.5rem;">设备统计 (离线前)</div>
                        <div class="device-count">
                            <div class="count-item">
                                <div class="count-value">5</div>
                                <div class="count-label">总计</div>
                            </div>
                            <div class="count-item">
                                <div class="count-value">0</div>
                                <div class="count-label">在线</div>
                            </div>
                            <div class="count-item">
                                <div class="count-value">0</div>
                                <div class="count-label">已共享</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="server-actions">
                        <button class="btn btn-warning edit-server-btn">编辑</button>
                        <button class="btn btn-primary">重新连接</button>
                        <button class="btn btn-danger">删除</button>
                    </div>
                </div>
                
                <div class="server-card online">
                    <div class="server-header">
                        <span class="server-name">工作站D-4</span>
                        <span class="server-status online">在线</span>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">IP地址:</div>
                        <div class="server-detail-value">*************</div>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">状态:</div>
                        <div class="server-detail-value">正常运行中</div>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">运行时间:</div>
                        <div class="server-detail-value">2天11小时</div>
                    </div>
                    <div class="server-detail">
                        <div class="server-detail-label">上次同步:</div>
                        <div class="server-detail-value">2023-07-15 15:28</div>
                    </div>
                    
                    <div class="resource-bars">
                        <div class="resource-bar">
                            <div class="resource-bar-label">
                                <span>CPU</span>
                                <span>30%</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar cpu" style="width: 30%"></div>
                            </div>
                        </div>
                        <div class="resource-bar">
                            <div class="resource-bar-label">
                                <span>内存</span>
                                <span>50%</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar memory" style="width: 50%"></div>
                            </div>
                        </div>
                        <div class="resource-bar">
                            <div class="resource-bar-label">
                                <span>磁盘</span>
                                <span>25%</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar disk" style="width: 25%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="devices-summary">
                        <div style="font-weight: bold; margin-bottom: 0.5rem;">设备统计</div>
                        <div class="device-count">
                            <div class="count-item">
                                <div class="count-value">10</div>
                                <div class="count-label">总计</div>
                            </div>
                            <div class="count-item">
                                <div class="count-value">10</div>
                                <div class="count-label">在线</div>
                            </div>
                            <div class="count-item">
                                <div class="count-value">7</div>
                                <div class="count-label">已共享</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="server-actions">
                        <button class="btn btn-warning edit-server-btn">编辑</button>
                        <button class="btn btn-primary">重启服务</button>
                        <button class="btn btn-danger">断开连接</button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 添加/编辑服务器模态框 -->
    <div class="modal" id="serverModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加从服务器</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form id="serverForm">
                <div class="form-group">
                    <label for="serverName">服务器名称</label>
                    <input type="text" id="serverName" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="serverIP">IP地址</label>
                    <input type="text" id="serverIP" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="serverPort">端口</label>
                    <input type="number" id="serverPort" class="form-control" required value="22">
                </div>
                <div class="form-group">
                    <label for="serverUser">登录用户名</label>
                    <input type="text" id="serverUser" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="serverPassword">登录密码</label>
                    <input type="password" id="serverPassword" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="serverDescription">服务器描述</label>
                    <textarea id="serverDescription" class="form-control" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="autoConnect"> 自动连接
                    </label>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger modal-close-btn">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <footer>
        <p>&copy; 2023 Web管理服务系统 - 版本 1.0.0</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 模态框相关
            const serverModal = document.getElementById('serverModal');
            const addServerBtn = document.getElementById('addServerBtn');
            const editServerBtns = document.querySelectorAll('.edit-server-btn');
            const modalCloseBtn = document.querySelector('.modal-close');
            const modalCloseBtns = document.querySelectorAll('.modal-close-btn');
            const serverForm = document.getElementById('serverForm');
            const modalTitle = document.getElementById('modalTitle');

            // 打开添加服务器模态框
            addServerBtn.addEventListener('click', function() {
                modalTitle.textContent = '添加从服务器';
                serverForm.reset();
                serverModal.style.display = 'flex';
            });

            // 打开编辑服务器模态框
            editServerBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const card = this.closest('.server-card');
                    const serverName = card.querySelector('.server-name').textContent;
                    const serverIP = card.querySelector('.server-detail:nth-child(2) .server-detail-value').textContent;
                    const autoConnect = true; // 假设已连接的服务器默认是自动连接的

                    modalTitle.textContent = '编辑从服务器';
                    document.getElementById('serverName').value = serverName;
                    document.getElementById('serverIP').value = serverIP;
                    document.getElementById('serverPort').value = '22'; // 假设默认端口
                    document.getElementById('serverUser').value = 'admin'; // 假设默认用户名
                    document.getElementById('serverPassword').value = ''; // 密码不显示
                    document.getElementById('serverDescription').value = '从服务器描述...'; // 假设描述
                    document.getElementById('autoConnect').checked = autoConnect;
                    
                    serverModal.style.display = 'flex';
                });
            });

            // 关闭模态框
            modalCloseBtn.addEventListener('click', function() {
                serverModal.style.display = 'none';
            });
            
            modalCloseBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    serverModal.style.display = 'none';
                });
            });

            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === serverModal) {
                    serverModal.style.display = 'none';
                }
            });

            // 表单提交处理
            serverForm.addEventListener('submit', function(e) {
                e.preventDefault();
                // 收集表单数据
                const serverData = {
                    name: document.getElementById('serverName').value,
                    ip: document.getElementById('serverIP').value,
                    port: document.getElementById('serverPort').value,
                    user: document.getElementById('serverUser').value,
                    password: document.getElementById('serverPassword').value,
                    description: document.getElementById('serverDescription').value,
                    autoConnect: document.getElementById('autoConnect').checked
                };
                
                console.log('服务器数据:', serverData);
                // 这里应该添加AJAX请求保存数据
                
                alert('服务器保存成功!');
                serverModal.style.display = 'none';
            });
        });
    </script>
</body>
</html> 