"""
数据导入器类

用于导入系统数据，包括数据库、配置、设备信息等。
"""

import os
import json
import time
import logging
import shutil
import zipfile
from typing import Dict, List, Any, Optional, Set

logger = logging.getLogger(__name__)

class ImportResult:
    """导入结果类"""
    
    def __init__(self, success: bool, message: str, details: Optional[Dict[str, Any]] = None):
        """
        初始化导入结果
        
        Args:
            success: 是否成功
            message: 消息
            details: 详细信息
        """
        self.success = success
        self.message = message
        self.details = details or {}
        
    def __str__(self) -> str:
        """返回结果的字符串表示"""
        return f"{'成功' if self.success else '失败'}: {self.message}"
        
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        Returns:
            字典表示
        """
        return {
            "success": self.success,
            "message": self.message,
            "details": self.details
        }
        
class DataImporter:
    """数据导入器类，用于导入系统数据"""
    
    def __init__(self, import_file: str, temp_dir: Optional[str] = None):
        """
        初始化数据导入器
        
        Args:
            import_file: 导入文件路径
            temp_dir: 临时目录，如果不指定则使用系统临时目录
        """
        self.import_file = import_file
        self.temp_dir = temp_dir or os.path.join(os.path.dirname(import_file), "temp_import")
        
        # 导入的元数据
        self.metadata: Dict[str, Any] = {}
        
        # 组件目录
        self.component_dirs: Dict[str, str] = {}
        
        # 已导入组件
        self.imported_components: Set[str] = set()
        
    def prepare_import(self) -> ImportResult:
        """
        准备导入环境
        
        Returns:
            导入结果
        """
        try:
            # 检查导入文件是否存在
            if not os.path.exists(self.import_file):
                return ImportResult(False, f"导入文件不存在: {self.import_file}")
                
            # 清理临时目录（如果存在）
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                
            # 创建临时目录
            os.makedirs(self.temp_dir, exist_ok=True)
            
            # 解压导入文件
            with zipfile.ZipFile(self.import_file, 'r') as zipf:
                zipf.extractall(self.temp_dir)
                
            # 加载元数据
            metadata_file = os.path.join(self.temp_dir, "metadata.json")
            if not os.path.exists(metadata_file):
                return ImportResult(False, "导入包中缺少元数据文件")
                
            with open(metadata_file, 'r', encoding='utf-8') as f:
                self.metadata = json.load(f)
                
            # 检查元数据
            if "components" not in self.metadata:
                return ImportResult(False, "元数据缺少组件信息")
                
            # 初始化组件目录
            for component in self.metadata["components"]:
                component_dir = os.path.join(self.temp_dir, component)
                if os.path.exists(component_dir):
                    self.component_dirs[component] = component_dir
                    
            # 检查必要的组件是否存在
            missing_components = []
            for component in self.metadata["components"]:
                if component not in self.component_dirs:
                    missing_components.append(component)
                    
            if missing_components:
                return ImportResult(
                    False, 
                    f"导入包中缺少必要的组件目录: {', '.join(missing_components)}",
                    {"missing_components": missing_components}
                )
                
            logger.info(f"导入环境准备完成: {self.import_file}")
            return ImportResult(True, f"导入环境准备完成，发现 {len(self.component_dirs)} 个组件")
            
        except zipfile.BadZipFile:
            return ImportResult(False, f"无效的ZIP文件: {self.import_file}")
        except json.JSONDecodeError:
            return ImportResult(False, "元数据文件格式无效")
        except Exception as e:
            logger.exception(f"准备导入环境出错: {e}")
            return ImportResult(False, f"准备导入环境出错: {str(e)}")
            
    def import_database(self, db_config: Dict[str, Any]) -> ImportResult:
        """
        导入数据库
        
        Args:
            db_config: 数据库配置，包含目标数据库信息
            
        Returns:
            导入结果
        """
        try:
            component = "database"
            logger.info(f"开始导入组件: {component}")
            
            # 检查组件是否在元数据中
            if component not in self.metadata["components"]:
                return ImportResult(False, f"导入包中不包含 {component} 组件")
                
            # 检查组件目录是否存在
            if component not in self.component_dirs:
                return ImportResult(False, f"找不到 {component} 的目录")
                
            component_dir = self.component_dirs[component]
            component_meta = self.metadata["components"][component]
            
            # 获取导入文件
            db_file = component_meta.get("file")
            if not db_file:
                return ImportResult(False, f"{component} 组件元数据中缺少文件信息")
                
            db_file_path = os.path.join(component_dir, db_file)
            if not os.path.exists(db_file_path):
                return ImportResult(False, f"找不到 {component} 的文件: {db_file}")
                
            # 在实际项目中，这里会根据数据库类型执行不同的导入操作
            db_type = component_meta.get("type", "sqlite")
            db_name = component_meta.get("name", "default")
            
            # 模拟导入过程
            time.sleep(1.5)
            
            # 记录导入的组件
            self.imported_components.add(component)
            
            logger.info(f"{component} 导入完成")
            return ImportResult(
                True, 
                f"{component} 导入成功",
                {"db_type": db_type, "db_name": db_name}
            )
            
        except Exception as e:
            logger.exception(f"导入 {component} 出错: {e}")
            return ImportResult(False, f"导入 {component} 出错: {str(e)}")
            
    def import_configurations(self, config_target_dir: str) -> ImportResult:
        """
        导入配置文件
        
        Args:
            config_target_dir: 配置文件的目标目录
            
        Returns:
            导入结果
        """
        try:
            component = "configurations"
            logger.info(f"开始导入组件: {component}")
            
            # 检查组件是否在元数据中
            if component not in self.metadata["components"]:
                return ImportResult(False, f"导入包中不包含 {component} 组件")
                
            # 检查组件目录是否存在
            if component not in self.component_dirs:
                return ImportResult(False, f"找不到 {component} 的目录")
                
            component_dir = self.component_dirs[component]
            component_meta = self.metadata["components"][component]
            
            # 创建目标目录
            os.makedirs(config_target_dir, exist_ok=True)
            
            # 在实际项目中，这里会将配置文件复制到目标目录
            # 此处为示例实现
            config_count = component_meta.get("count", 0)
            
            # 模拟复制配置文件
            for i in range(config_count):
                source_dir = os.path.join(component_dir, f"config_{i}")
                if os.path.exists(source_dir):
                    config_file = os.path.join(source_dir, "config.json")
                    if os.path.exists(config_file):
                        # 在实际项目中，这里会复制或合并配置文件
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config_data = json.load(f)
                            
                        target_file = os.path.join(config_target_dir, f"imported_config_{i}.json")
                        with open(target_file, 'w', encoding='utf-8') as f:
                            json.dump(config_data, f, indent=2, ensure_ascii=False)
                            
            # 记录导入的组件
            self.imported_components.add(component)
            
            logger.info(f"{component} 导入完成")
            return ImportResult(
                True, 
                f"{component} 导入成功, 共 {config_count} 个配置",
                {"count": config_count, "target_dir": config_target_dir}
            )
            
        except Exception as e:
            logger.exception(f"导入 {component} 出错: {e}")
            return ImportResult(False, f"导入 {component} 出错: {str(e)}")
            
    def import_devices(self) -> ImportResult:
        """
        导入设备信息
        
        Returns:
            导入结果
        """
        try:
            component = "devices"
            logger.info(f"开始导入组件: {component}")
            
            # 检查组件是否在元数据中
            if component not in self.metadata["components"]:
                return ImportResult(False, f"导入包中不包含 {component} 组件")
                
            # 检查组件目录是否存在
            if component not in self.component_dirs:
                return ImportResult(False, f"找不到 {component} 的目录")
                
            component_dir = self.component_dirs[component]
            component_meta = self.metadata["components"][component]
            
            # 获取导入文件
            devices_file = component_meta.get("file")
            if not devices_file:
                return ImportResult(False, f"{component} 组件元数据中缺少文件信息")
                
            devices_file_path = os.path.join(component_dir, devices_file)
            if not os.path.exists(devices_file_path):
                return ImportResult(False, f"找不到 {component} 的文件: {devices_file}")
                
            # 加载设备数据
            with open(devices_file_path, 'r', encoding='utf-8') as f:
                devices_data = json.load(f)
                
            # 在实际项目中，这里会将设备数据导入到系统中
            # 此处为示例实现
            device_count = component_meta.get("count", 0)
            
            # 模拟导入过程
            time.sleep(1)
            
            # 记录导入的组件
            self.imported_components.add(component)
            
            logger.info(f"{component} 导入完成")
            return ImportResult(
                True, 
                f"{component} 导入成功, 共 {device_count} 个设备",
                {"count": device_count, "data": devices_data}
            )
            
        except Exception as e:
            logger.exception(f"导入 {component} 出错: {e}")
            return ImportResult(False, f"导入 {component} 出错: {str(e)}")
            
    def import_users(self) -> ImportResult:
        """
        导入用户信息
        
        Returns:
            导入结果
        """
        try:
            component = "users"
            logger.info(f"开始导入组件: {component}")
            
            # 检查组件是否在元数据中
            if component not in self.metadata["components"]:
                return ImportResult(False, f"导入包中不包含 {component} 组件")
                
            # 检查组件目录是否存在
            if component not in self.component_dirs:
                return ImportResult(False, f"找不到 {component} 的目录")
                
            component_dir = self.component_dirs[component]
            component_meta = self.metadata["components"][component]
            
            # 获取导入文件
            users_file = component_meta.get("file")
            if not users_file:
                return ImportResult(False, f"{component} 组件元数据中缺少文件信息")
                
            users_file_path = os.path.join(component_dir, users_file)
            if not os.path.exists(users_file_path):
                return ImportResult(False, f"找不到 {component} 的文件: {users_file}")
                
            # 加载用户数据
            with open(users_file_path, 'r', encoding='utf-8') as f:
                users_data = json.load(f)
                
            # 在实际项目中，这里会将用户数据导入到系统中
            # 此处为示例实现
            user_count = component_meta.get("count", 0)
            
            # 模拟导入过程
            time.sleep(0.8)
            
            # 记录导入的组件
            self.imported_components.add(component)
            
            logger.info(f"{component} 导入完成")
            return ImportResult(
                True, 
                f"{component} 导入成功, 共 {user_count} 个用户",
                {"count": user_count, "data": users_data}
            )
            
        except Exception as e:
            logger.exception(f"导入 {component} 出错: {e}")
            return ImportResult(False, f"导入 {component} 出错: {str(e)}")
            
    def get_metadata(self) -> Dict[str, Any]:
        """
        获取导入包元数据
        
        Returns:
            元数据字典
        """
        return self.metadata
        
    def cleanup(self):
        """清理临时文件"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"临时目录已清理: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"清理临时文件出错: {e}")
            
    def get_import_info(self) -> Dict[str, Any]:
        """
        获取导入信息
        
        Returns:
            导入信息字典
        """
        return {
            "import_file": self.import_file,
            "temp_dir": self.temp_dir,
            "imported_components": list(self.imported_components),
            "metadata": self.metadata
        } 
