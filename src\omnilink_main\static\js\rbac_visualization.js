/**
 * RBAC角色继承关系可视化
 * 使用D3.js实现角色继承关系的可视化展示
 */

class RBACVisualization {
  /**
   * 创建RBAC可视化组件
   * @param {string} containerId - 容器元素ID
   * @param {Object} options - 配置选项
   */
  constructor(containerId, options = {}) {
    this.containerId = containerId;
    this.container = document.getElementById(containerId);
    if (!this.container) {
      console.error(`容器元素不存在: ${containerId}`);
      return;
    }
    
    // 默认配置
    this.config = {
      width: options.width || this.container.clientWidth || 800,
      height: options.height || 600,
      nodeRadius: options.nodeRadius || 25,
      nodePadding: options.nodePadding || 15,
      colors: options.colors || {
        node: '#1976d2',
        selectedNode: '#e91e63',
        inheritanceLink: '#78909c',
        text: '#ffffff',
        selectedText: '#ffffff',
        background: 'transparent'
      },
      animation: {
        duration: options.animationDuration || 800,
        ease: d3.easeCubicInOut
      }
    };
    
    // 数据状态
    this.data = {
      nodes: [],
      links: []
    };
    
    this.selectedNode = null;
    
    // 初始化SVG
    this.initializeSVG();
    
    // 创建力导向图
    this.initializeForceSimulation();
    
    // 设置事件监听器
    window.addEventListener('resize', this.handleResize.bind(this));
  }
  
  /**
   * 初始化SVG元素
   * @private
   */
  initializeSVG() {
    // 清空容器
    this.container.innerHTML = '';
    
    // 创建SVG
    this.svg = d3.select(`#${this.containerId}`)
      .append('svg')
      .attr('width', this.config.width)
      .attr('height', this.config.height)
      .attr('class', 'rbac-visualization-svg')
      .style('background-color', this.config.colors.background);
    
    // 创建链接和节点的容器组
    this.linksGroup = this.svg.append('g').attr('class', 'links');
    this.nodesGroup = this.svg.append('g').attr('class', 'nodes');
    
    // 添加缩放和平移功能
    this.zoom = d3.zoom()
      .scaleExtent([0.1, 3])
      .on('zoom', (event) => {
        this.linksGroup.attr('transform', event.transform);
        this.nodesGroup.attr('transform', event.transform);
      });
    
    this.svg.call(this.zoom);
    
    // 添加图例
    this.addLegend();
  }
  
  /**
   * 添加图例
   * @private
   */
  addLegend() {
    const legend = this.svg.append('g')
      .attr('class', 'legend')
      .attr('transform', `translate(20, ${this.config.height - 60})`);
    
    // 角色节点图例
    legend.append('circle')
      .attr('cx', 10)
      .attr('cy', 10)
      .attr('r', 10)
      .attr('fill', this.config.colors.node);
    
    legend.append('text')
      .attr('x', 25)
      .attr('y', 15)
      .text('角色')
      .attr('font-size', '12px')
      .attr('fill', '#333');
    
    // 继承关系图例
    legend.append('line')
      .attr('x1', 0)
      .attr('y1', 40)
      .attr('x2', 20)
      .attr('y2', 40)
      .attr('stroke', this.config.colors.inheritanceLink)
      .attr('stroke-width', 2);
    
    legend.append('text')
      .attr('x', 25)
      .attr('y', 45)
      .text('继承关系')
      .attr('font-size', '12px')
      .attr('fill', '#333');
  }
  
  /**
   * 初始化力导向模拟
   * @private
   */
  initializeForceSimulation() {
    // 创建力导向模拟
    this.simulation = d3.forceSimulation()
      .force('link', d3.forceLink().id(d => d.id).distance(150))
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(this.config.width / 2, this.config.height / 2))
      .force('collision', d3.forceCollide().radius(this.config.nodeRadius * 2));
    
    // 设置模拟事件处理
    this.simulation.on('tick', () => this.updateVisualization());
  }
  
  /**
   * 更新可视化
   * @private
   */
  updateVisualization() {
    // 更新链接
    this.links
      .attr('x1', d => d.source.x)
      .attr('y1', d => d.source.y)
      .attr('x2', d => d.target.x)
      .attr('y2', d => d.target.y);
    
    // 更新节点组
    this.nodeGroups
      .attr('transform', d => `translate(${d.x}, ${d.y})`);
  }
  
  /**
   * 设置角色数据
   * @param {Array} roles - 角色数据数组
   * @param {Array} inheritances - 角色继承关系数组
   */
  setData(roles, inheritances) {
    // 转换数据格式
    const nodes = roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description,
      type: 'role'
    }));
    
    // 创建继承关系链接
    const links = inheritances.map(link => ({
      source: link.child,
      target: link.parent,
      type: 'inheritance'
    }));
    
    // 更新数据
    this.data.nodes = nodes;
    this.data.links = links;
    
    // 重绘可视化
    this.render();
  }
  
  /**
   * 渲染可视化
   */
  render() {
    // 绑定数据到链接
    this.links = this.linksGroup.selectAll('line')
      .data(this.data.links)
      .join(
        enter => enter.append('line')
          .attr('stroke', this.config.colors.inheritanceLink)
          .attr('stroke-width', 2)
          .attr('marker-end', 'url(#arrow)')
          .attr('class', 'inheritance-link'),
        update => update
          .attr('stroke', this.config.colors.inheritanceLink),
        exit => exit.remove()
      );
    
    // 绑定数据到节点组
    this.nodeGroups = this.nodesGroup.selectAll('.node-group')
      .data(this.data.nodes)
      .join(
        enter => {
          const group = enter.append('g')
            .attr('class', 'node-group')
            .call(d3.drag()
              .on('start', this.dragstarted.bind(this))
              .on('drag', this.dragged.bind(this))
              .on('end', this.dragended.bind(this))
            )
            .on('click', (event, d) => this.handleNodeClick(event, d));
          
          // 添加节点圆形
          group.append('circle')
            .attr('r', this.config.nodeRadius)
            .attr('fill', this.config.colors.node)
            .attr('class', 'node-circle');
          
          // 添加节点文本
          group.append('text')
            .attr('dy', '.35em')
            .attr('text-anchor', 'middle')
            .attr('fill', this.config.colors.text)
            .text(d => d.name)
            .attr('class', 'node-text');
          
          return group;
        },
        update => update
          .select('.node-circle')
          .attr('fill', d => (this.selectedNode && d.id === this.selectedNode.id) 
            ? this.config.colors.selectedNode 
            : this.config.colors.node),
        exit => exit.remove()
      );
    
    // 更新模拟
    this.simulation.nodes(this.data.nodes);
    this.simulation.force('link').links(this.data.links);
    this.simulation.alpha(1).restart();
  }
  
  /**
   * 处理节点点击事件
   * @param {Event} event - 事件对象
   * @param {Object} node - 节点数据
   */
  handleNodeClick(event, node) {
    // 更新选中状态
    this.selectedNode = (this.selectedNode && this.selectedNode.id === node.id) ? null : node;
    
    // 更新节点样式
    this.nodesGroup.selectAll('.node-circle')
      .attr('fill', d => (this.selectedNode && d.id === this.selectedNode.id) 
        ? this.config.colors.selectedNode 
        : this.config.colors.node);
    
    // 高亮显示相关链接
    if (this.selectedNode) {
      this.highlightConnections(this.selectedNode.id);
    } else {
      this.resetHighlighting();
    }
    
    // 触发选择事件
    if (typeof this.onNodeSelect === 'function') {
      this.onNodeSelect(this.selectedNode);
    }
  }
  
  /**
   * 高亮显示与指定节点相关的连接
   * @param {string} nodeId - 节点ID
   */
  highlightConnections(nodeId) {
    // 高亮链接
    this.links
      .attr('stroke', d => (d.source.id === nodeId || d.target.id === nodeId) 
        ? '#ff9800' 
        : this.config.colors.inheritanceLink)
      .attr('stroke-width', d => (d.source.id === nodeId || d.target.id === nodeId) ? 4 : 2)
      .attr('stroke-opacity', d => (d.source.id === nodeId || d.target.id === nodeId) ? 1 : 0.3);
    
    // 高亮相关节点
    this.nodeGroups
      .attr('opacity', d => {
        // 查找与选中节点相关的连接
        const isConnected = this.data.links.some(link => 
          (link.source.id === nodeId && link.target.id === d.id) || 
          (link.target.id === nodeId && link.source.id === d.id));
        
        return (d.id === nodeId || isConnected) ? 1 : 0.3;
      });
  }
  
  /**
   * 重置高亮显示
   */
  resetHighlighting() {
    this.links
      .attr('stroke', this.config.colors.inheritanceLink)
      .attr('stroke-width', 2)
      .attr('stroke-opacity', 1);
    
    this.nodeGroups
      .attr('opacity', 1);
  }
  
  /**
   * 处理拖动开始事件
   * @param {Event} event - 事件对象
   * @param {Object} d - 节点数据
   */
  dragstarted(event, d) {
    if (!event.active) this.simulation.alphaTarget(0.3).restart();
    d.fx = d.x;
    d.fy = d.y;
  }
  
  /**
   * 处理拖动事件
   * @param {Event} event - 事件对象
   * @param {Object} d - 节点数据
   */
  dragged(event, d) {
    d.fx = event.x;
    d.fy = event.y;
  }
  
  /**
   * 处理拖动结束事件
   * @param {Event} event - 事件对象
   * @param {Object} d - 节点数据
   */
  dragended(event, d) {
    if (!event.active) this.simulation.alphaTarget(0);
    d.fx = null;
    d.fy = null;
  }
  
  /**
   * 处理窗口大小调整事件
   */
  handleResize() {
    // 更新尺寸
    this.config.width = this.container.clientWidth;
    
    // 重设SVG尺寸
    this.svg
      .attr('width', this.config.width)
      .attr('height', this.config.height);
    
    // 更新中心力
    this.simulation.force('center', d3.forceCenter(this.config.width / 2, this.config.height / 2));
    
    // 重启模拟
    this.simulation.alpha(0.3).restart();
  }
  
  /**
   * 注册节点选择回调
   * @param {Function} callback - 回调函数
   */
  onSelect(callback) {
    this.onNodeSelect = callback;
  }
  
  /**
   * 选择指定ID的节点
   * @param {string} nodeId - 节点ID
   */
  selectNode(nodeId) {
    const node = this.data.nodes.find(n => n.id === nodeId);
    if (node) {
      // 创建一个模拟事件对象
      const event = { type: 'click' };
      this.handleNodeClick(event, node);
    }
  }
  
  /**
   * 导出可视化为SVG图像
   * @returns {string} SVG字符串
   */
  exportSVG() {
    // 获取SVG元素
    const svgEl = this.container.querySelector('svg');
    if (!svgEl) return null;
    
    // 克隆SVG元素
    const clonedSvg = svgEl.cloneNode(true);
    
    // 添加样式
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      .node-circle { fill: ${this.config.colors.node}; }
      .node-circle.selected { fill: ${this.config.colors.selectedNode}; }
      .inheritance-link { stroke: ${this.config.colors.inheritanceLink}; stroke-width: 2px; }
      .node-text { fill: ${this.config.colors.text}; font-size: 12px; }
    `;
    clonedSvg.appendChild(styleEl);
    
    // 转换为字符串
    const serializer = new XMLSerializer();
    return serializer.serializeToString(clonedSvg);
  }
  
  /**
   * 清除可视化
   */
  clear() {
    this.data.nodes = [];
    this.data.links = [];
    this.selectedNode = null;
    this.render();
  }
  
  /**
   * 销毁可视化组件
   */
  destroy() {
    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize.bind(this));
    
    // 停止模拟
    if (this.simulation) {
      this.simulation.stop();
      this.simulation = null;
    }
    
    // 清空容器
    if (this.container) {
      this.container.innerHTML = '';
    }
  }
}

// 当文档加载完成后初始化可视化
document.addEventListener('DOMContentLoaded', () => {
  // 检查是否存在可视化容器
  const container = document.getElementById('role-hierarchy-visualization');
  if (!container) return;
  
  // 创建可视化实例
  window.rbacVisualizer = new RBACVisualization('role-hierarchy-visualization');
  
  // 测试数据
  const testRoles = [
    { id: 'admin', name: '管理员', description: '系统管理员' },
    { id: 'manager', name: '经理', description: '部门经理' },
    { id: 'user', name: '普通用户', description: '基本用户' },
    { id: 'guest', name: '访客', description: '临时访问用户' },
    { id: 'operator', name: '操作员', description: '系统操作员' }
  ];
  
  const testInheritances = [
    { parent: 'admin', child: 'manager' },
    { parent: 'manager', child: 'user' },
    { parent: 'user', child: 'guest' },
    { parent: 'manager', child: 'operator' }
  ];
  
  // 设置测试数据
  window.rbacVisualizer.setData(testRoles, testInheritances);
  
  // 注册选择回调
  window.rbacVisualizer.onSelect(node => {
    console.log('选择节点:', node);
    if (node) {
      document.getElementById('role-details-content').innerHTML = `
        <div class="role-details">
          <h3>${node.name}</h3>
          <p>${node.description || '无描述'}</p>
        </div>
      `;
    }
  });
}); 