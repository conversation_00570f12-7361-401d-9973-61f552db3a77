"""
锁管理器，用于防止资源竞争
"""
import time
import threading
from typing import Dict, Any, Optional

# 简单的内存锁管理
class LockManager:
    """锁管理器"""
    
    _locks = {}  # 锁记录
    _lock = threading.Lock()  # 用于保护_locks的互斥锁
    
    @classmethod
    def acquire_lock(cls, resource_type: str, resource_id: Any, user_id: int, timeout: int = 300) -> bool:
        """
        获取资源锁
        
        Args:
            resource_type: 资源类型
            resource_id: 资源ID
            user_id: 用户ID
            timeout: 锁超时时间（秒）
            
        Returns:
            bool: 是否成功获取锁
        """
        lock_key = f"{resource_type}:{resource_id}"
        
        with cls._lock:
            # 检查锁是否存在
            if lock_key in cls._locks:
                lock_info = cls._locks[lock_key]
                
                # 检查锁是否过期
                if time.time() < lock_info['expire_time']:
                    # 如果是同一用户，则刷新锁
                    if lock_info['user_id'] == user_id:
                        lock_info['expire_time'] = time.time() + timeout
                        return True
                    
                    # 锁被其他用户持有且未过期
                    return False
                
                # 锁已过期，可以获取
                cls._locks[lock_key] = {
                    'user_id': user_id,
                    'acquire_time': time.time(),
                    'expire_time': time.time() + timeout
                }
                return True
            
            # 锁不存在，创建新锁
            cls._locks[lock_key] = {
                'user_id': user_id,
                'acquire_time': time.time(),
                'expire_time': time.time() + timeout
            }
            return True
    
    @classmethod
    def release_lock(cls, resource_type: str, resource_id: Any, user_id: int) -> bool:
        """
        释放资源锁
        
        Args:
            resource_type: 资源类型
            resource_id: 资源ID
            user_id: 用户ID
            
        Returns:
            bool: 是否成功释放锁
        """
        lock_key = f"{resource_type}:{resource_id}"
        
        with cls._lock:
            # 检查锁是否存在
            if lock_key in cls._locks:
                lock_info = cls._locks[lock_key]
                
                # 只能释放自己的锁
                if lock_info['user_id'] == user_id:
                    del cls._locks[lock_key]
                    return True
                
                return False
            
            # 锁不存在
            return True
    
    @classmethod
    def get_lock_info(cls, resource_type: str, resource_id: Any) -> Optional[Dict[str, Any]]:
        """
        获取锁信息
        
        Args:
            resource_type: 资源类型
            resource_id: 资源ID
            
        Returns:
            Optional[Dict[str, Any]]: 锁信息，如果不存在则返回None
        """
        lock_key = f"{resource_type}:{resource_id}"
        
        with cls._lock:
            # 检查锁是否存在
            if lock_key in cls._locks:
                lock_info = cls._locks[lock_key].copy()
                
                # 检查锁是否过期
                if time.time() < lock_info['expire_time']:
                    return lock_info
                
                # 锁已过期，删除
                del cls._locks[lock_key]
            
            return None
    
    @classmethod
    def cleanup_expired_locks(cls) -> int:
        """
        清理过期的锁
        
        Returns:
            int: 清理的锁数量
        """
        count = 0
        current_time = time.time()
        
        with cls._lock:
            for lock_key in list(cls._locks.keys()):
                lock_info = cls._locks[lock_key]
                
                if current_time >= lock_info['expire_time']:
                    del cls._locks[lock_key]
                    count += 1
        
        return count 
