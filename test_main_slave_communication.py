#!/usr/bin/env python3
"""
主从服务器通信测试脚本
用于验证主服务器和从服务器之间的通信是否正常
"""

import asyncio
import aiohttp
import json
import logging
import time
from typing import Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MainSlaveCommTest:
    def __init__(self):
        self.main_server_url = "http://localhost:8000"
        self.slave_server_url = "http://localhost:8001"
        self.api_key = "dev-api-key-12345"
        self.admin_token = None

    async def login_admin(self) -> bool:
        """管理员登录获取token"""
        try:
            async with aiohttp.ClientSession() as session:
                login_data = {
                    "username": "firefly",
                    "password": "bro2fhz12"
                }
                
                async with session.post(
                    f"{self.main_server_url}/api/v1/auth/login",
                    json=login_data
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.admin_token = data.get("access_token")
                        logger.info("✅ 管理员登录成功")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 管理员登录失败: {response.status} - {error_text}")
                        return False
        except Exception as e:
            logger.error(f"❌ 登录异常: {e}")
            return False

    async def test_main_server_health(self) -> bool:
        """测试主服务器健康状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.main_server_url}/health") as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"✅ 主服务器健康状态: {data}")
                        return True
                    else:
                        logger.error(f"❌ 主服务器健康检查失败: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"❌ 主服务器连接异常: {e}")
            return False

    async def test_slave_server_health(self) -> bool:
        """测试从服务器健康状态"""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {self.api_key}"}
                async with session.get(f"{self.slave_server_url}/health", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"✅ 从服务器健康状态: {data}")
                        return True
                    else:
                        logger.error(f"❌ 从服务器健康检查失败: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"❌ 从服务器连接异常: {e}")
            return False

    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始主从服务器通信测试...")
        
        tests = [
            ("主服务器健康检查", self.test_main_server_health),
            ("从服务器健康检查", self.test_slave_server_health),
            ("管理员登录", self.login_admin),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🔍 执行测试: {test_name}")
            try:
                if await test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    logger.error(f"❌ {test_name} - 失败")
            except Exception as e:
                logger.error(f"❌ {test_name} - 异常: {e}")
            
            # 测试间隔
            await asyncio.sleep(1)
        
        logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            logger.info("🎉 所有测试通过！主从服务器通信正常。")
        else:
            logger.warning(f"⚠️  有 {total - passed} 个测试失败，请检查配置和服务状态。")
        
        return passed == total

async def main():
    """主函数"""
    print("=" * 60)
    print("OmniLink 主从服务器通信测试")
    print("=" * 60)
    
    tester = MainSlaveCommTest()
    success = await tester.run_all_tests()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 测试完成：所有功能正常")
    else:
        print("❌ 测试完成：发现问题，需要修复")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main()) 