from typing import Any, Dict, List, Optional, Set, Union, Tuple
import logging
import time
from datetime import datetime
import threading
import traceback

from .filter_engine import DeviceFilterEngine
from .models import (
    FilterOperator, FilterLogic, FilterCondition, FilterGroup
)

logger = logging.getLogger(__name__)

class DeviceFilterService:
    """设备过滤服务类，提供设备过滤相关功能"""
    
    def __init__(self, 
                 cache_ttl: int = 10,
                 max_cache_size: int = 1000):
        """
        初始化设备过滤服务
        
        参数:
            cache_ttl: 缓存过期时间（秒）
            max_cache_size: 最大缓存条目数
        """
        self._filter_engine = DeviceFilterEngine(
            cache_size=max_cache_size,
            cache_ttl=cache_ttl
        )
        self._lock = threading.RLock()
    
    def add_device(self, device_id: str, device_data: Dict[str, Any]) -> bool:
        """
        添加设备到过滤引擎
        
        参数:
            device_id: 设备ID
            device_data: 设备数据
            
        返回:
            bool: 是否添加成功
        """
        try:
            with self._lock:
                return self._filter_engine.add_device(device_id, device_data)
        except Exception as e:
            logger.error(f"添加设备失败: {e}")
            logger.debug(traceback.format_exc())
            return False
    
    def update_device(self, device_id: str, device_data: Dict[str, Any]) -> bool:
        """
        更新设备数据
        
        参数:
            device_id: 设备ID
            device_data: 设备数据
            
        返回:
            bool: 是否更新成功
        """
        try:
            with self._lock:
                return self._filter_engine.update_device(device_id, device_data)
        except Exception as e:
            logger.error(f"更新设备失败: {e}")
            logger.debug(traceback.format_exc())
            return False
    
    def remove_device(self, device_id: str) -> bool:
        """
        移除设备
        
        参数:
            device_id: 设备ID
            
        返回:
            bool: 是否移除成功
        """
        try:
            with self._lock:
                return self._filter_engine.remove_device(device_id)
        except Exception as e:
            logger.error(f"移除设备失败: {e}")
            logger.debug(traceback.format_exc())
            return False
    
    def get_device(self, device_id: str) -> Optional[Dict[str, Any]]:
        """
        获取设备数据
        
        参数:
            device_id: 设备ID
            
        返回:
            Optional[Dict[str, Any]]: 设备数据，不存在则返回None
        """
        try:
            return self._filter_engine.get_device(device_id)
        except Exception as e:
            logger.error(f"获取设备失败: {e}")
            logger.debug(traceback.format_exc())
            return None
    
    def get_all_devices(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有设备
        
        返回:
            Dict[str, Dict[str, Any]]: 设备ID到设备数据的映射
        """
        try:
            return self._filter_engine.get_all_devices()
        except Exception as e:
            logger.error(f"获取所有设备失败: {e}")
            logger.debug(traceback.format_exc())
            return {}
    
    def filter_devices_with_condition(self, condition: FilterCondition) -> Dict[str, Dict[str, Any]]:
        """
        使用单个条件过滤设备
        
        参数:
            condition: 过滤条件
            
        返回:
            Dict[str, Dict[str, Any]]: 匹配的设备
        """
        try:
            return self._filter_devices(condition=condition)
        except Exception as e:
            logger.error(f"条件过滤设备失败: {e}")
            logger.debug(traceback.format_exc())
            return {}
    
    def filter_devices_with_group(self, filter_group: FilterGroup) -> Dict[str, Dict[str, Any]]:
        """
        使用条件组过滤设备
        
        参数:
            filter_group: 过滤条件组
            
        返回:
            Dict[str, Dict[str, Any]]: 匹配的设备
        """
        try:
            return self._filter_devices(filter_group=filter_group)
        except Exception as e:
            logger.error(f"条件组过滤设备失败: {e}")
            logger.debug(traceback.format_exc())
            return {}
    
    def filter_devices_with_dict(self, filter_dict: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        使用字典表示的过滤条件过滤设备
        
        参数:
            filter_dict: 过滤条件组的字典表示
            
        返回:
            Dict[str, Dict[str, Any]]: 匹配的设备
        """
        try:
            filter_group = FilterGroup.from_dict(filter_dict)
            return self._filter_devices(filter_group=filter_group)
        except Exception as e:
            logger.error(f"字典过滤设备失败: {e}")
            logger.debug(traceback.format_exc())
            return {}
    
    def filter_devices_with_simple_condition(
        self, field: str, operator: Union[FilterOperator, str], value: Any = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        使用简单条件过滤设备
        
        参数:
            field: 字段名
            operator: 操作符
            value: 比较值
            
        返回:
            Dict[str, Dict[str, Any]]: 匹配的设备
        """
        try:
            # 处理操作符
            if isinstance(operator, str):
                operator = FilterOperator(operator)
            
            # 创建条件
            condition = FilterCondition(field, operator, value)
            
            return self._filter_devices(condition=condition)
        except Exception as e:
            logger.error(f"简单条件过滤设备失败: {e}")
            logger.debug(traceback.format_exc())
            return {}
    
    def _filter_devices(
        self, 
        condition: Optional[FilterCondition] = None,
        filter_group: Optional[FilterGroup] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        内部过滤设备方法
        
        参数:
            condition: 过滤条件
            filter_group: 过滤条件组
            
        返回:
            Dict[str, Dict[str, Any]]: 匹配的设备
        """
        # 如果同时提供了condition和filter_group，优先使用filter_group
        if filter_group:
            # 使用条件组过滤
            return self._filter_engine.filter_devices_with_group(filter_group)
        elif condition:
            # 使用单个条件过滤
            return self._filter_engine.filter_devices_with_condition(condition)
        else:
            # 没有提供条件，返回所有设备
            return self._filter_engine.get_all_devices()
    
    def count_devices_with_condition(self, condition: FilterCondition) -> int:
        """
        使用单个条件统计设备数量
        
        参数:
            condition: 过滤条件
            
        返回:
            int: 匹配的设备数量
        """
        try:
            return self._filter_engine.count_devices_with_condition(condition)
        except Exception as e:
            logger.error(f"条件统计设备数量失败: {e}")
            logger.debug(traceback.format_exc())
            return 0
    
    def count_devices_with_group(self, filter_group: FilterGroup) -> int:
        """
        使用条件组统计设备数量
        
        参数:
            filter_group: 过滤条件组
            
        返回:
            int: 匹配的设备数量
        """
        try:
            return self._filter_engine.count_devices_with_group(filter_group)
        except Exception as e:
            logger.error(f"条件组统计设备数量失败: {e}")
            logger.debug(traceback.format_exc())
            return 0
    
    def count_devices_with_dict(self, filter_dict: Dict[str, Any]) -> int:
        """
        使用字典表示的过滤条件统计设备数量
        
        参数:
            filter_dict: 过滤条件组的字典表示
            
        返回:
            int: 匹配的设备数量
        """
        try:
            filter_group = FilterGroup.from_dict(filter_dict)
            return self._filter_engine.count_devices_with_group(filter_group)
        except Exception as e:
            logger.error(f"字典统计设备数量失败: {e}")
            logger.debug(traceback.format_exc())
            return 0
    
    def add_index_field(self, field: str) -> bool:
        """
        添加索引字段
        
        参数:
            field: 字段名
            
        返回:
            bool: 是否添加成功
        """
        try:
            with self._lock:
                return self._filter_engine.add_index_field(field)
        except Exception as e:
            logger.error(f"添加索引字段失败: {e}")
            logger.debug(traceback.format_exc())
            return False
    
    def remove_index_field(self, field: str) -> bool:
        """
        移除索引字段
        
        参数:
            field: 字段名
            
        返回:
            bool: 是否移除成功
        """
        try:
            with self._lock:
                return self._filter_engine.remove_index_field(field)
        except Exception as e:
            logger.error(f"移除索引字段失败: {e}")
            logger.debug(traceback.format_exc())
            return False
    
    def get_index_fields(self) -> List[str]:
        """
        获取所有索引字段
        
        返回:
            List[str]: 索引字段列表
        """
        try:
            return self._filter_engine.get_index_fields()
        except Exception as e:
            logger.error(f"获取索引字段失败: {e}")
            logger.debug(traceback.format_exc())
            return []
    
    def clear_cache(self) -> None:
        """
        清除缓存
        """
        try:
            self._filter_engine.clear_cache()
        except Exception as e:
            logger.error(f"清除缓存失败: {e}")
            logger.debug(traceback.format_exc())
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        返回:
            Dict[str, Any]: 缓存统计信息
        """
        try:
            return self._filter_engine.get_cache_stats()
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            logger.debug(traceback.format_exc())
            return {}
    
    def get_field_unique_values(self, field: str) -> Set[Any]:
        """
        获取字段的唯一值集合
        
        参数:
            field: 字段名
            
        返回:
            Set[Any]: 唯一值集合
        """
        try:
            return self._filter_engine.get_field_unique_values(field)
        except Exception as e:
            logger.error(f"获取字段唯一值失败: {e}")
            logger.debug(traceback.format_exc())
            return set()
    
    def get_field_value_distribution(self, field: str) -> Dict[Any, int]:
        """
        获取字段值的分布情况
        
        参数:
            field: 字段名
            
        返回:
            Dict[Any, int]: 值到数量的映射
        """
        try:
            return self._filter_engine.get_field_value_distribution(field)
        except Exception as e:
            logger.error(f"获取字段值分布失败: {e}")
            logger.debug(traceback.format_exc())
            return {}
    
    def get_snapshot(self) -> Dict[str, Any]:
        """
        获取过滤引擎快照
        
        返回:
            Dict[str, Any]: 引擎快照
        """
        try:
            return self._filter_engine.get_snapshot()
        except Exception as e:
            logger.error(f"获取引擎快照失败: {e}")
            logger.debug(traceback.format_exc())
            return {
                "device_count": 0,
                "index_fields": [],
                "cache_stats": {},
                "timestamp": datetime.now().isoformat()
            }
    
    def build_filter_from_text(self, text_query: str) -> Optional[FilterGroup]:
        """
        (Experimental) Build a filter group from a natural language text query.
        This is a placeholder for a more advanced NLP-based query parser.
        """
        # A very basic implementation
        # For a real implementation, consider using libraries like NLTK, spaCy, or a dedicated parser
        if "available" in text_query.lower():
            return FilterGroup(
                logic=FilterLogic.AND,
                conditions=[FilterCondition(field="status", operator=FilterOperator.EQUAL, value="available")]
            )
        return None

device_filter_service = DeviceFilterService() 