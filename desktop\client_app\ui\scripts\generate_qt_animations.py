#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Material Design QT动画生成器

根据动效定义生成QT动画类，实现Material Design风格的动效
"""

import os
import json
from pathlib import Path
import sys

# 设置路径
SCRIPT_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
PARENT_DIR = SCRIPT_DIR.parent

# 导入motion模块
sys.path.append(str(SCRIPT_DIR))
from motion import MotionSystem, AnimationType, EasingType

# 定义路径
THEMES_DIR = PARENT_DIR / "themes"
MOTION_JSON_PATH = THEMES_DIR / "motion.json"
QT_OUTPUT_PATH = PARENT_DIR / "qt" / "material_animations.py"

def ensure_dir_exists(file_path):
    """确保目录存在"""
    directory = os.path.dirname(file_path)
    if not os.path.exists(directory):
        os.makedirs(directory)

def generate_qt_animation_module():
    """
    生成QT动画模块代码
    
    Returns:
        生成的Python代码字符串
    """
    # 创建MotionSystem实例
    motion_system = MotionSystem()
    
    # 获取动效数据
    motion_data = motion_system.load_motion_json()
    
    # 生成Python模块头部
    code = '#!/usr/bin/env python\n'
    code += '# -*- coding: utf-8 -*-\n\n'
    code += '"""\n'
    code += 'Material Design 动画系统 - QT实现\n'
    code += '\n'
    code += '自动生成，请勿手动修改\n'
    code += '"""\n\n'
    
    # 导入必要的库
    code += 'from PyQt5.QtCore import QPropertyAnimation, QEasingCurve, QPoint, QSize, QRect\n'
    code += 'from PyQt5.QtCore import Qt, QParallelAnimationGroup, QSequentialAnimationGroup\n'
    code += 'from PyQt5.QtWidgets import QWidget, QGraphicsOpacityEffect\n'
    code += 'from enum import Enum, auto\n\n'
    
    # 动画类型枚举
    code += 'class AnimationType(Enum):\n'
    code += '    """动画类型枚举"""\n'
    for anim_type in AnimationType:
        code += f'    {anim_type.name} = auto()\n'
    code += '\n'
    
    # 缓动类型枚举
    code += 'class EasingType(Enum):\n'
    code += '    """缓动类型枚举"""\n'
    for easing_type in EasingType:
        code += f'    {easing_type.name} = auto()\n'
    code += '\n'
    
    # 添加常量定义
    code += '# 动画持续时间常量\n'
    durations = motion_data.get('duration', {})
    for name, value in durations.items():
        code += f'DURATION_{name.upper()} = {value}\n'
    code += '\n'
    
    # QT缓动曲线映射
    code += '# 缓动曲线映射\n'
    code += 'EASING_CURVE_MAP = {\n'
    code += '    EasingType.STANDARD: QEasingCurve.OutCubic,\n'
    code += '    EasingType.ACCELERATED: QEasingCurve.InCubic,\n'
    code += '    EasingType.DECELERATED: QEasingCurve.OutCubic,\n'
    code += '    EasingType.LINEAR: QEasingCurve.Linear,\n'
    code += '    EasingType.EMPHASIZED: QEasingCurve.OutQuint,\n'
    code += '    EasingType.EMPHASIZED_ACCELERATE: QEasingCurve.InOutQuint,\n'
    code += '    EasingType.EMPHASIZED_DECELERATE: QEasingCurve.OutQuint,\n'
    code += '}\n\n'
    
    # 主动画类
    code += motion_system.generate_qt_animation_class()
    
    # 添加实用功能
    code += '\n# 实用功能\n'
    code += 'def apply_fade_in(widget, duration=None, easing_type=EasingType.STANDARD):\n'
    code += '    """应用淡入动画到控件\n'
    code += '    \n'
    code += '    Args:\n'
    code += '        widget: 目标控件\n'
    code += '        duration: 持续时间，默认为标准持续时间\n'
    code += '        easing_type: 缓动类型\n'
    code += '        \n'
    code += '    Returns:\n'
    code += '        创建的动画对象\n'
    code += '    """\n'
    code += '    if duration is None:\n'
    code += '        duration = DURATION_STANDARD\n'
    code += '        \n'
    code += '    # 创建MaterialAnimations实例\n'
    code += '    animations = MaterialAnimations(widget)\n'
    code += '    # 应用淡入动画\n'
    code += '    return animations.fade_in(duration, easing_type)\n'
    code += '\n'
    
    code += 'def apply_fade_out(widget, duration=None, easing_type=EasingType.ACCELERATED):\n'
    code += '    """应用淡出动画到控件\n'
    code += '    \n'
    code += '    Args:\n'
    code += '        widget: 目标控件\n'
    code += '        duration: 持续时间，默认为标准持续时间\n'
    code += '        easing_type: 缓动类型\n'
    code += '        \n'
    code += '    Returns:\n'
    code += '        创建的动画对象\n'
    code += '    """\n'
    code += '    if duration is None:\n'
    code += '        duration = DURATION_STANDARD\n'
    code += '        \n'
    code += '    # 创建MaterialAnimations实例\n'
    code += '    animations = MaterialAnimations(widget)\n'
    code += '    # 应用淡出动画\n'
    code += '    return animations.fade_out(duration, easing_type)\n'
    code += '\n'
    
    code += 'def apply_slide_in(widget, direction=Qt.RightToLeft, duration=None, easing_type=EasingType.STANDARD):\n'
    code += '    """应用滑入动画到控件\n'
    code += '    \n'
    code += '    Args:\n'
    code += '        widget: 目标控件\n'
    code += '        direction: 方向，使用Qt方向常量\n'
    code += '        duration: 持续时间，默认为标准持续时间\n'
    code += '        easing_type: 缓动类型\n'
    code += '        \n'
    code += '    Returns:\n'
    code += '        创建的动画对象\n'
    code += '    """\n'
    code += '    if duration is None:\n'
    code += '        duration = DURATION_STANDARD\n'
    code += '        \n'
    code += '    # 创建MaterialAnimations实例\n'
    code += '    animations = MaterialAnimations(widget)\n'
    code += '    # 应用滑入动画\n'
    code += '    return animations.slide_in(direction, duration, easing_type)\n'
    code += '\n'
    
    # 添加涟漪效果组件
    code += '# 涟漪效果组件\n'
    code += 'class RippleEffect(QWidget):\n'
    code += '    """Material Design涟漪效果实现"""\n'
    code += '    \n'
    code += '    def __init__(self, parent=None):\n'
    code += '        """初始化涟漪效果控件\n'
    code += '        \n'
    code += '        Args:\n'
    code += '            parent: 父控件\n'
    code += '        """\n'
    code += '        super().__init__(parent)\n'
    code += '        self.setAttribute(Qt.WA_TransparentForMouseEvents)\n'
    code += '        self.setAttribute(Qt.WA_NoSystemBackground)\n'
    code += '        self.setStyleSheet("background-color: rgba(255, 255, 255, 0.12);")\n'
    code += '        self.hide()\n'
    code += '        \n'
    code += '        # 设置圆形\n'
    code += '        self.setMask(self.shape())\n'
    code += '        \n'
    code += '        # 创建动画组\n'
    code += '        self.animation_group = QSequentialAnimationGroup(self)\n'
    code += '        \n'
    code += '        # 透明度效果\n'
    code += '        self.opacity_effect = QGraphicsOpacityEffect(self)\n'
    code += '        self.setGraphicsEffect(self.opacity_effect)\n'
    code += '        self.opacity_effect.setOpacity(0.0)\n'
    code += '    \n'
    code += '    def shape(self):\n'
    code += '        """获取控件形状"""\n'
    code += '        # 创建圆形遮罩\n'
    code += '        from PyQt5.QtGui import QPainterPath, QRegion\n'
    code += '        path = QPainterPath()\n'
    code += '        path.addEllipse(self.rect())\n'
    code += '        return QRegion(path.toFillPolygon().toPolygon())\n'
    code += '    \n'
    code += '    def start(self, point, parent_size):\n'
    code += '        """开始涟漪动画\n'
    code += '        \n'
    code += '        Args:\n'
    code += '            point: 触发点\n'
    code += '            parent_size: 父控件尺寸\n'
    code += '        """\n'
    code += '        # 计算涟漪最终尺寸\n'
    code += '        radius = max(parent_size.width(), parent_size.height()) * 1.2\n'
    code += '        \n'
    code += '        # 设置初始位置和尺寸\n'
    code += '        self.setGeometry(point.x() - 5, point.y() - 5, 10, 10)\n'
    code += '        \n'
    code += '        # 清除之前的动画\n'
    code += '        self.animation_group.clear()\n'
    code += '        \n'
    code += '        # 创建放大动画\n'
    code += '        grow_anim = QPropertyAnimation(self, b"geometry")\n'
    code += '        grow_anim.setDuration(DURATION_STANDARD)\n'
    code += '        grow_anim.setStartValue(self.geometry())\n'
    code += '        final_x = point.x() - radius / 2\n'
    code += '        final_y = point.y() - radius / 2\n'
    code += '        grow_anim.setEndValue(QRect(int(final_x), int(final_y), int(radius), int(radius)))\n'
    code += '        grow_anim.setEasingCurve(EASING_CURVE_MAP[EasingType.STANDARD])\n'
    code += '        \n'
    code += '        # 创建淡入动画\n'
    code += '        fade_in = QPropertyAnimation(self.opacity_effect, b"opacity")\n'
    code += '        fade_in.setDuration(int(DURATION_STANDARD * 0.5))\n'
    code += '        fade_in.setStartValue(0.0)\n'
    code += '        fade_in.setEndValue(1.0)\n'
    code += '        fade_in.setEasingCurve(QEasingCurve.OutCubic)\n'
    code += '        \n'
    code += '        # 创建淡出动画\n'
    code += '        fade_out = QPropertyAnimation(self.opacity_effect, b"opacity")\n'
    code += '        fade_out.setDuration(int(DURATION_STANDARD * 0.5))\n'
    code += '        fade_out.setStartValue(1.0)\n'
    code += '        fade_out.setEndValue(0.0)\n'
    code += '        fade_out.setEasingCurve(QEasingCurve.OutCubic)\n'
    code += '        \n'
    code += '        # 添加动画到动画组\n'
    code += '        enter_group = QParallelAnimationGroup()\n'
    code += '        enter_group.addAnimation(grow_anim)\n'
    code += '        enter_group.addAnimation(fade_in)\n'
    code += '        \n'
    code += '        self.animation_group.addAnimation(enter_group)\n'
    code += '        self.animation_group.addAnimation(fade_out)\n'
    code += '        \n'
    code += '        # 显示控件并启动动画\n'
    code += '        self.show()\n'
    code += '        self.animation_group.start()\n'
    code += '\n'
    
    # 添加状态层类
    code += '# 状态层类\n'
    code += 'class StateLayer(QWidget):\n'
    code += '    """Material Design状态层实现"""\n'
    code += '    \n'
    code += '    def __init__(self, parent=None):\n'
    code += '        """初始化状态层控件\n'
    code += '        \n'
    code += '        Args:\n'
    code += '            parent: 父控件\n'
    code += '        """\n'
    code += '        super().__init__(parent)\n'
    code += '        self.setAttribute(Qt.WA_TransparentForMouseEvents)\n'
    code += '        self.setAttribute(Qt.WA_NoSystemBackground)\n'
    code += '        self.setStyleSheet("background-color: rgba(0, 0, 0, 0);")\n'
    code += '        \n'
    code += '        # 透明度效果\n'
    code += '        self.opacity_effect = QGraphicsOpacityEffect(self)\n'
    code += '        self.setGraphicsEffect(self.opacity_effect)\n'
    code += '        self.opacity_effect.setOpacity(0.0)\n'
    code += '        \n'
    code += '        # 动画\n'
    code += '        self.hover_animation = QPropertyAnimation(self.opacity_effect, b"opacity")\n'
    code += '        self.hover_animation.setDuration(DURATION_STANDARD)\n'
    code += '        self.hover_animation.setEasingCurve(EASING_CURVE_MAP[EasingType.STANDARD])\n'
    code += '        \n'
    code += '        # 添加涟漪效果\n'
    code += '        self.ripple = RippleEffect(self)\n'
    code += '    \n'
    code += '    def set_hover(self, is_hover):\n'
    code += '        """设置悬停状态\n'
    code += '        \n'
    code += '        Args:\n'
    code += '            is_hover: 是否悬停\n'
    code += '        """\n'
    code += '        # 悬停透明度为0.08\n'
    code += '        self.hover_animation.setStartValue(self.opacity_effect.opacity())\n'
    code += '        self.hover_animation.setEndValue(0.08 if is_hover else 0.0)\n'
    code += '        self.hover_animation.start()\n'
    code += '    \n'
    code += '    def set_pressed(self, is_pressed, point=None):\n'
    code += '        """设置按下状态\n'
    code += '        \n'
    code += '        Args:\n'
    code += '            is_pressed: 是否按下\n'
    code += '            point: 触发点\n'
    code += '        """\n'
    code += '        # 按下透明度为0.12\n'
    code += '        self.hover_animation.stop()\n'
    code += '        self.hover_animation.setStartValue(self.opacity_effect.opacity())\n'
    code += '        self.hover_animation.setEndValue(0.12 if is_pressed else 0.0)\n'
    code += '        self.hover_animation.start()\n'
    code += '        \n'
    code += '        # 触发涟漪效果\n'
    code += '        if is_pressed and point:\n'
    code += '            self.ripple.start(point, self.size())\n'
    code += '\n'
    
    # Qt应用示例
    code += '# 示例用法\n'
    code += 'if __name__ == "__main__":\n'
    code += '    import sys\n'
    code += '    from PyQt5.QtWidgets import QApplication, QPushButton, QVBoxLayout, QMainWindow\n'
    code += '    \n'
    code += '    app = QApplication(sys.argv)\n'
    code += '    \n'
    code += '    class MaterialButton(QPushButton):\n'
    code += '        """Material Design风格按钮"""\n'
    code += '        \n'
    code += '        def __init__(self, text, parent=None):\n'
    code += '            super().__init__(text, parent)\n'
    code += '            self.state_layer = StateLayer(self)\n'
    code += '            self.setMinimumHeight(36)\n'
    code += '            self.setStyleSheet("""\n'
    code += '                QPushButton {\n'
    code += '                    background-color: #6200EE;\n'
    code += '                    color: white;\n'
    code += '                    border: none;\n'
    code += '                    border-radius: 4px;\n'
    code += '                    padding: 8px 16px;\n'
    code += '                    font-weight: 500;\n'
    code += '                }\n'
    code += '            """)\n'
    code += '        \n'
    code += '        def resizeEvent(self, event):\n'
    code += '            """调整状态层大小"""\n'
    code += '            super().resizeEvent(event)\n'
    code += '            self.state_layer.resize(self.size())\n'
    code += '        \n'
    code += '        def enterEvent(self, event):\n'
    code += '            """鼠标进入事件"""\n'
    code += '            super().enterEvent(event)\n'
    code += '            self.state_layer.set_hover(True)\n'
    code += '        \n'
    code += '        def leaveEvent(self, event):\n'
    code += '            """鼠标离开事件"""\n'
    code += '            super().leaveEvent(event)\n'
    code += '            self.state_layer.set_hover(False)\n'
    code += '        \n'
    code += '        def mousePressEvent(self, event):\n'
    code += '            """鼠标按下事件"""\n'
    code += '            super().mousePressEvent(event)\n'
    code += '            self.state_layer.set_pressed(True, event.pos())\n'
    code += '        \n'
    code += '        def mouseReleaseEvent(self, event):\n'
    code += '            """鼠标释放事件"""\n'
    code += '            super().mouseReleaseEvent(event)\n'
    code += '            self.state_layer.set_pressed(False)\n'
    code += '    \n'
    code += '    # 创建窗口\n'
    code += '    window = QMainWindow()\n'
    code += '    window.setWindowTitle("Material Design动画示例")\n'
    code += '    \n'
    code += '    # 创建中央控件\n'
    code += '    central_widget = QWidget()\n'
    code += '    window.setCentralWidget(central_widget)\n'
    code += '    \n'
    code += '    # 创建布局\n'
    code += '    layout = QVBoxLayout(central_widget)\n'
    code += '    \n'
    code += '    # 添加按钮\n'
    code += '    fade_in_button = MaterialButton("淡入动画")\n'
    code += '    fade_out_button = MaterialButton("淡出动画")\n'
    code += '    slide_in_button = MaterialButton("滑入动画")\n'
    code += '    \n'
    code += '    layout.addWidget(fade_in_button)\n'
    code += '    layout.addWidget(fade_out_button)\n'
    code += '    layout.addWidget(slide_in_button)\n'
    code += '    \n'
    code += '    # 创建演示控件\n'
    code += '    demo_widget = QWidget()\n'
    code += '    demo_widget.setMinimumSize(200, 200)\n'
    code += '    demo_widget.setStyleSheet("background-color: #BB86FC; border-radius: 8px;")\n'
    code += '    layout.addWidget(demo_widget)\n'
    code += '    \n'
    code += '    # 创建动画实例\n'
    code += '    animations = MaterialAnimations(demo_widget)\n'
    code += '    \n'
    code += '    # 连接按钮信号\n'
    code += '    fade_in_button.clicked.connect(lambda: animations.fade_in().start())\n'
    code += '    fade_out_button.clicked.connect(lambda: animations.fade_out().start())\n'
    code += '    slide_in_button.clicked.connect(lambda: animations.slide_in().start())\n'
    code += '    \n'
    code += '    # 显示窗口\n'
    code += '    window.resize(400, 500)\n'
    code += '    window.show()\n'
    code += '    \n'
    code += '    sys.exit(app.exec_())\n'
    
    return code

def save_qt_module(code, output_path=None):
    """
    保存Python代码到文件
    
    Args:
        code: Python代码内容
        output_path: 输出路径，默认为预定义的QT_OUTPUT_PATH
    """
    if output_path is None:
        output_path = QT_OUTPUT_PATH
    
    # 确保目录存在
    ensure_dir_exists(output_path)
    
    # 保存Python文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(code)
    
    print(f"QT动画模块已保存到: {output_path}")

def main():
    """主函数"""
    try:
        # 确保主题目录存在
        os.makedirs(THEMES_DIR, exist_ok=True)
        
        # 生成QT动画模块代码
        code = generate_qt_animation_module()
        
        # 保存Python文件
        save_qt_module(code)
        
        print("QT动画模块生成成功!")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main() 