#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
认证相关的API路由
提供用户登录、注册、令牌管理和多因素认证功能
"""

import os
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Annotated, Union
from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr, ValidationError
from jose import JWTError, jwt

logger = logging.getLogger(__name__)

# 尝试导入相关模块
try:
    from main_server.services.user_service import UserService
    from main_server.services.auth_service import AuthService
    from main_server.services.token_service import TokenService
    from main_server.services.mfa_service import MFAService
    from main_server.models.user_model import User
    from main_server.api import api_models
except ImportError:
    logger.warning("认证相关模块不可用，使用模拟实现")
    
    # 创建模拟的服务和模型
    class User:
        def __init__(self, id=1, username="test", email="<EMAIL>", is_active=True):
            self.id = id
            self.username = username
            self.email = email
            self.is_active = is_active
            self.mfa_enabled = False
            self.email_verified = False
    
    class UserService:
        async def authenticate_user(self, username: str, password: str):
            if username == "test" and password == "test":
                return User(), "SUCCESS"
            return None, "INVALID_CREDENTIALS"
        
        async def get_user_by_id(self, user_id: int):
            return User(id=user_id)
        
        async def create_user(self, user_data: dict):
            return User()
    
    class AuthService:
        def __init__(self):
            pass
        
        async def login(self, username: str, password: str):
            if username == "test" and password == "test":
                return {"access_token": "fake-token", "token_type": "bearer"}
            return None
    
    class TokenService:
        def __init__(self):
            self.secret_key = "fake-secret-key"
        
        def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None):
            return "fake-access-token"
        
        def verify_token(self, token: str, credentials_exception):
            if token == "fake-access-token":
                return {"user_id": 1, "sub": "test"}
            raise credentials_exception
    
    class MFAService:
        async def setup_totp(self, user_id: int):
            return {"secret": "fake-secret", "uri": "fake-uri", "qr_code": "fake-qr"}
        
        async def verify_totp(self, user_id: int, code: str):
            return code == "123456"
    
    class api_models:
        class TokenData(BaseModel):
            sub: Optional[str] = None
            user_id: Optional[int] = None

# OAuth2 配置
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/login")

# 路由器配置
router = APIRouter(prefix="/auth", tags=["认证"])
mfa_router = APIRouter(prefix="/mfa", tags=["多因素认证"])

# --- Pydantic 模型定义 ---

class TokenResponse(BaseModel):
    """令牌响应模型"""
    access_token: str
    token_type: str
    refresh_token: Optional[str] = None

class LoginPasswordPhaseResponse(BaseModel):
    """登录密码阶段响应模型"""
    status: str  # LOGIN_SUCCESS_NO_MFA, MFA_REQUIRED_TOTP, MFA_REQUIRED_EMAIL
    username: str
    email: Optional[EmailStr] = None
    message: Optional[str] = None

class LoginMfaVerifyRequest(BaseModel):
    """登录MFA验证请求模型"""
    username: str
    mfa_code: str
    mfa_method: str  # TOTP or EMAIL_CODE

class EmailVerificationConfirmRequest(BaseModel):
    """邮箱验证确认请求模型"""
    code: str

class TOTPSetupResponse(BaseModel):
    """TOTP设置响应模型"""
    secret: str
    uri: str
    qr_code_image: str
    message: str = "使用认证器应用扫描二维码并用生成的代码确认"

class TOTPConfirmRequest(BaseModel):
    """TOTP确认请求模型"""
    totp_secret_from_setup: Optional[str] = None
    totp_code: str

class BackupCodesResponse(BaseModel):
    """备份码响应模型"""
    backup_codes: List[str]
    message: str = "请安全存储这些备份码，它们不会再次显示"

class MFAStatusResponse(BaseModel):
    """MFA状态响应模型"""
    username: str
    mfa_enabled: bool
    mfa_method: Optional[str] = None
    email_verified: bool
    is_totp_setup: bool
    is_email_mfa_setup: bool

class MessageResponse(BaseModel):
    """消息响应模型"""
    message: str
    detail: Optional[str] = None

class MFADisableRequest(BaseModel):
    """MFA禁用请求模型"""
    current_password: str

class EmailMFASetupConfirmRequest(BaseModel):
    """邮箱MFA设置确认请求模型"""
    code: str

class UserCreateRequest(BaseModel):
    """用户创建请求模型"""
    username: str
    email: EmailStr
    password: str

class UserInDB(BaseModel):
    """数据库用户模型"""
    id: Any
    username: str
    email: EmailStr
    password_hash: str
    is_active: bool = True
    mfa_enabled: bool = False
    mfa_method: Optional[str] = None
    email_verified: bool = False
    totp_secret_encrypted: Optional[bytes] = None
    totp_backup_codes_encrypted: Optional[str] = None

# --- 依赖项函数 ---

def get_user_service() -> UserService:
    """获取用户服务实例"""
    return UserService()

def get_auth_service() -> AuthService:
    """获取认证服务实例"""
    return AuthService()

def get_token_service() -> TokenService:
    """获取令牌服务实例"""
    return TokenService()

def get_mfa_service() -> MFAService:
    """获取MFA服务实例"""
    return MFAService()

async def get_current_active_user_token_data(
    token: str = Depends(oauth2_scheme),
    token_service: TokenService = Depends(get_token_service)
) -> api_models.TokenData:
    """
    获取当前活跃用户的令牌数据
    
    参数:
        token: JWT令牌
        token_service: 令牌服务
        
    返回:
        令牌数据
        
    异常:
        HTTPException: 令牌无效或过期
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload_dict = token_service.verify_token(token, credentials_exception)
        
        if not payload_dict:
            raise credentials_exception

        user_id_from_token = payload_dict.get("user_id")
        username_from_token = payload_dict.get("sub", payload_dict.get("username"))

        if user_id_from_token is None:
            logger.error(f"令牌负载中未找到用户ID: {payload_dict}")
            raise credentials_exception
        
        try:
            user_id_int = int(user_id_from_token)
        except ValueError:
            logger.error(f"令牌中的用户ID不是有效的整数: {user_id_from_token}")
            raise credentials_exception

        return api_models.TokenData(sub=username_from_token, user_id=user_id_int)
    
    except JWTError as e:
        logger.error(f"JWT错误: {e}")
        raise credentials_exception
    except ValidationError as e:
        logger.error(f"令牌数据验证错误: {e}")
        raise credentials_exception
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取当前用户令牌数据时发生意外错误: {e}")
        raise credentials_exception

async def get_current_db_user(
    token_data: api_models.TokenData = Depends(get_current_active_user_token_data),
    user_service: UserService = Depends(get_user_service)
) -> User:
    """
    获取当前数据库用户对象
    
    参数:
        token_data: 令牌数据
        user_service: 用户服务
        
    返回:
        用户对象
        
    异常:
        HTTPException: 用户不存在或不活跃
    """
    if token_data.user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, 
            detail="无效令牌: 用户ID缺失"
        )

    user = await user_service.get_user_by_id(token_data.user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, 
            detail="根据令牌未找到用户"
        )
        
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail="非活动用户"
        )
        
    return user

# --- API 端点实现 ---

@router.post("/login/password", response_model=LoginPasswordPhaseResponse)
async def login_password_phase(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    user_service: UserService = Depends(get_user_service)
):
    """
    用户登录的第一阶段：验证用户名和密码
    
    参数:
        form_data: OAuth2密码请求表单
        user_service: 用户服务
        
    返回:
        登录密码阶段响应
        
    异常:
        HTTPException: 认证失败
    """
    try:
        user, auth_status_msg = await user_service.authenticate_user(
            form_data.username, form_data.password
        )
        
        if not user:
            if auth_status_msg == "INVALID_CREDENTIALS":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED, 
                    detail="用户名或密码错误",
                    headers={"WWW-Authenticate": "Bearer"}
                )
            elif auth_status_msg == "ACCOUNT_INACTIVE":
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, 
                    detail="账户未激活"
                )
            elif auth_status_msg == "ACCOUNT_LOCKED":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN, 
                    detail="账户已锁定"
                )
            else:
                logger.error(f"用户 {form_data.username} 的未知认证失败: {auth_status_msg}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
                    detail="登录时发生未知认证错误"
                )
        
        # 检查是否需要MFA
        if hasattr(user, 'mfa_enabled') and user.mfa_enabled:
            if hasattr(user, 'mfa_method'):
                if user.mfa_method == "TOTP":
                    return LoginPasswordPhaseResponse(
                        status="MFA_REQUIRED_TOTP",
                        username=user.username,
                        message="需要TOTP验证码"
                    )
                elif user.mfa_method == "EMAIL_CODE":
                    return LoginPasswordPhaseResponse(
                        status="MFA_REQUIRED_EMAIL",
                        username=user.username,
                        email=user.email,
                        message="需要邮箱验证码"
                    )
        
        # 无需MFA，直接登录成功
        return LoginPasswordPhaseResponse(
            status="LOGIN_SUCCESS_NO_MFA",
            username=user.username,
            message="登录成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录密码阶段发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录过程中发生内部错误"
        )

@router.post("/login/mfa", response_model=TokenResponse)
async def login_mfa_verify(
    request: LoginMfaVerifyRequest,
    user_service: UserService = Depends(get_user_service),
    token_service: TokenService = Depends(get_token_service),
    mfa_service: MFAService = Depends(get_mfa_service)
):
    """
    用户登录的MFA验证阶段
    
    参数:
        request: MFA验证请求
        user_service: 用户服务
        token_service: 令牌服务
        mfa_service: MFA服务
        
    返回:
        令牌响应
        
    异常:
        HTTPException: MFA验证失败
    """
    try:
        # 实现具体的MFA验证逻辑
        # 验证用户存在
        user = await user_service.authenticate_user(request.username, "")
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在"
            )
        
        # 根据MFA方法进行验证
        is_valid = False
        if request.mfa_method == "TOTP":
            # 验证TOTP代码
            is_valid = await mfa_service.verify_totp(user.id, request.mfa_code)
        elif request.mfa_method == "EMAIL_CODE":
            # 验证邮箱代码（这里需要实现邮箱验证逻辑）
            # 暂时使用简单验证
            is_valid = len(request.mfa_code) == 6 and request.mfa_code.isdigit()
        
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="MFA验证码无效"
            )
        
        # 验证成功，生成访问令牌
        access_token_expires = timedelta(minutes=30)
        access_token = token_service.create_access_token(
            data={"sub": request.username, "user_id": 1},
            expires_delta=access_token_expires
        )
        
        return TokenResponse(
            access_token=access_token,
            token_type="bearer"
        )
        
    except Exception as e:
        logger.error(f"MFA验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="MFA验证失败"
        )

@router.post("/login", response_model=TokenResponse)
async def login_direct(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    直接登录（无MFA）
    
    参数:
        form_data: OAuth2密码请求表单
        auth_service: 认证服务
        
    返回:
        令牌响应
        
    异常:
        HTTPException: 登录失败
    """
    try:
        result = await auth_service.login(form_data.username, form_data.password)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        return TokenResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"直接登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录过程中发生内部错误"
        )

@router.post("/register", response_model=MessageResponse)
async def register_user(
    request: UserCreateRequest,
    user_service: UserService = Depends(get_user_service)
):
    """
    用户注册
    
    参数:
        request: 用户创建请求
        user_service: 用户服务
        
    返回:
        消息响应
        
    异常:
        HTTPException: 注册失败
    """
    try:
        user_data = {
            "username": request.username,
            "email": request.email,
            "password": request.password
        }
        
        user = await user_service.create_user(user_data)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户注册失败"
            )
        
        return MessageResponse(
            message="用户注册成功",
            detail="请检查您的邮箱以激活账户"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册过程中发生内部错误"
        )

@router.get("/me", response_model=UserInDB)
async def get_current_user(current_user: User = Depends(get_current_db_user)):
    """
    获取当前用户信息
    
    参数:
        current_user: 当前用户
        
    返回:
        用户信息
    """
    return UserInDB(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        password_hash="[HIDDEN]",
        is_active=current_user.is_active,
        mfa_enabled=getattr(current_user, 'mfa_enabled', False),
        email_verified=getattr(current_user, 'email_verified', False)
    )

# --- MFA 相关端点 ---

@mfa_router.post("/totp/setup", response_model=TOTPSetupResponse)
async def setup_totp_mfa(
    current_user: User = Depends(get_current_db_user),
    mfa_service: MFAService = Depends(get_mfa_service)
):
    """
    设置TOTP MFA
    
    参数:
        current_user: 当前用户
        mfa_service: MFA服务
        
    返回:
        TOTP设置响应
    """
    try:
        result = await mfa_service.setup_totp(current_user.id)
        
        return TOTPSetupResponse(
            secret=result["secret"],
            uri=result["uri"],
            qr_code_image=result["qr_code"]
        )
        
    except Exception as e:
        logger.error(f"设置TOTP MFA失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="设置TOTP失败"
        )

@mfa_router.post("/totp/confirm", response_model=MessageResponse)
async def confirm_totp_mfa(
    request: TOTPConfirmRequest,
    current_user: User = Depends(get_current_db_user),
    mfa_service: MFAService = Depends(get_mfa_service)
):
    """
    确认TOTP MFA设置
    
    参数:
        request: TOTP确认请求
        current_user: 当前用户
        mfa_service: MFA服务
        
    返回:
        消息响应
    """
    try:
        is_valid = await mfa_service.verify_totp(current_user.id, request.totp_code)
        
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="TOTP验证码无效"
            )
        
        return MessageResponse(message="TOTP MFA设置成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认TOTP MFA失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="确认TOTP失败"
        )

@mfa_router.get("/status", response_model=MFAStatusResponse)
async def get_mfa_status(current_user: User = Depends(get_current_db_user)):
    """
    获取MFA状态
    
    参数:
        current_user: 当前用户
        
    返回:
        MFA状态响应
    """
    return MFAStatusResponse(
        username=current_user.username,
        mfa_enabled=getattr(current_user, 'mfa_enabled', False),
        mfa_method=getattr(current_user, 'mfa_method', None),
        email_verified=getattr(current_user, 'email_verified', False),
        is_totp_setup=getattr(current_user, 'mfa_method', None) == 'TOTP',
        is_email_mfa_setup=getattr(current_user, 'mfa_method', None) == 'EMAIL_CODE'
    )

# 将MFA路由包含到主路由中
router.include_router(mfa_router)

# 健康检查端点
@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "auth-api"}

 
