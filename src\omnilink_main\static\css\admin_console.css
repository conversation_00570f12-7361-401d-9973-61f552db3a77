/* 
 * 管理控制台CSS样式
 * 用于主从服务器管理系统的Web界面样式定义
 */

/* 全局样式 */
body {
  font-size: .875rem;
  background-color: #f8f9fa;
}

.feather {
  width: 16px;
  height: 16px;
  vertical-align: text-bottom;
}

/* 侧边栏导航 */
.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100; /* 位于主内容之后 */
  padding: 0;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

@media (max-width: 767.98px) {
  .sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15);
  }
}

.sidebar-sticky {
  position: relative;
  top: 0;
  height: calc(100vh - 48px);
  padding-top: .5rem;
  overflow-x: hidden;
  overflow-y: auto; /* 侧边栏内容过多时可滚动 */
}

.sidebar .nav-link {
  font-weight: 500;
  color: rgba(255, 255, 255, .75);
  padding: .5rem 1rem;
}

.sidebar .nav-link:hover {
  color: #fff;
}

.sidebar .nav-link.active {
  color: #fff;
  background-color: rgba(255, 255, 255, .1);
}

.sidebar .nav-link .feather {
  margin-right: 4px;
  color: #999;
}

.sidebar .nav-link.active .feather {
  color: inherit;
}

.sidebar-heading {
  font-size: .75rem;
  text-transform: uppercase;
}

/* 主内容区 */
main {
  padding-top: 1.5rem;
}

/* 卡片样式优化 */
.card {
  margin-bottom: 1.5rem;
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: box-shadow 0.3s ease-in-out;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  font-weight: 500;
}

/* 状态卡片 */
.status-card {
  border-radius: 0.5rem;
  border: none;
  transition: transform 0.3s ease;
}

.status-card:hover {
  transform: translateY(-5px);
}

/* 表格样式 */
.table {
  font-size: 0.875rem;
}

.table thead th {
  border-top: none;
  border-bottom-width: 1px;
  font-weight: 500;
  background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* 健康状态指示器 */
.health-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.health-indicator.healthy {
  background-color: #28a745;
}

.health-indicator.warning {
  background-color: #ffc107;
}

.health-indicator.critical {
  background-color: #dc3545;
}

.health-indicator.unknown {
  background-color: #6c757d;
}

/* 按钮样式优化 */
.btn {
  font-weight: 500;
}

.btn-sm {
  font-size: .775rem;
}

/* 表单控件样式 */
.form-control:focus {
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
}

/* 分页样式 */
.pagination {
  margin-bottom: 0;
}

/* 进度条样式 */
.progress {
  height: 0.5rem;
  background-color: #e9ecef;
  border-radius: 1rem;
}

/* 树形视图样式 */
.jstree-default .jstree-clicked {
  background-color: rgba(13, 110, 253, 0.15);
}

.jstree-default .jstree-hovered {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 导航标签样式 */
.nav-tabs .nav-link {
  color: #495057;
  font-weight: 500;
}

.nav-tabs .nav-link.active {
  color: #0d6efd;
  font-weight: 500;
}

/* 模态框样式 */
.modal-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #bbb;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 警告标签样式 */
.badge {
  font-weight: 500;
  padding: 0.35em 0.65em;
}

/* 加载过渡效果 */
.loader {
  border: 3px solid #f3f3f3;
  border-radius: 50%;
  border-top: 3px solid #0d6efd;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移动设备适配 */
@media (max-width: 767.98px) {
  .card-body {
    padding: 1rem;
  }
  
  main {
    padding-top: 1rem;
  }
  
  .btn-toolbar {
    justify-content: center;
    margin-top: 0.5rem;
  }
}

/* 打印样式 */
@media print {
  .sidebar {
    display: none;
  }
  
  .navbar, 
  .btn-toolbar, 
  footer {
    display: none;
  }
  
  main {
    margin-left: 0;
    padding-top: 0;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}

/* 主题颜色 */
:root {
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
}

/* 辅助类 */
.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.clickable {
  cursor: pointer;
}

.opacity-50 {
  opacity: 0.5;
} 