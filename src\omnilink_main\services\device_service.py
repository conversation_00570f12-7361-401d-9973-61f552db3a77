from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import Optional, List, Tuple
from datetime import datetime
from fastapi import HTTPException, status

from common.models.device import Device
from common.models.user import User
from common.schemas.device_schema import Device<PERSON><PERSON>, DeviceUpdate
from src.omnilink_main.communication.ws_manager import ws_manager
from src.omnilink_main.utils.port_pool import port_pool
from common.rules_engine.filter_service import device_filter_service

class DeviceService:
    async def get_by_id(self, db: AsyncSession, *, id: int) -> Optional[Device]:
        return await db.get(Device, id)

    async def get_devices_for_user(self, db: AsyncSession, *, user: User) -> List[Device]:
        """
        Gets all devices a user is allowed to see based on their roles and associated policy rules.
        This encapsulates the complex filtering logic.
        """
        if not user.roles:
            return []

        # 1. Get all rules associated with the user's roles
        all_role_filters = []
        for role in user.roles:
            if role.policy_rules:
                for rule in role.policy_rules:
                    if rule.is_active and rule.rule_definition:
                        all_role_filters.append(rule.rule_definition)
        
        if not all_role_filters:
            return []

        # 2. Combine rules into a single filter group (OR logic between roles)
        combined_filter = {
            "logic": "OR",
            "conditions": [],
            "subgroups": all_role_filters
        }
        
        # 3. Use the filter service to get filtered device IDs
        filtered_device_dict = device_filter_service.filter_devices_with_dict(combined_filter)
        filtered_device_ids = [int(id) for id in filtered_device_dict.keys()]

        if not filtered_device_ids:
            return []

        # 4. Fetch the full device objects from the database
        statement = select(Device).where(Device.id.in_(filtered_device_ids))
        result = await db.execute(statement)
        return result.scalars().all()

    async def connect_device(self, db: AsyncSession, *, device_id: int, user: User) -> dict:
        """
        Handles the core logic for connecting a user to a device.
        """
        # First, verify the user has permission to see this device at all
        allowed_devices = await self.get_devices_for_user(db, user=user)
        if device_id not in [d.id for d in allowed_devices]:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Device not found or permission denied")
            
        db_device = await self.get_by_id(db, id=device_id)
        if not db_device: # Should be caught by above check, but good for safety
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Device not found")

        if db_device.status != "available":
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Device is not available. Current status: {db_device.status}")

        if not db_device.slave_server:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Device is not associated with a slave server.")

        port = port_pool.lease_port()
        if not port:
            raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="No available ports for device sharing.")

        try:
            command = {"action": "START_VH_INSTANCE", "device_path": db_device.device_id, "port": port}
            await ws_manager.send_command(str(db_device.slave_server.id), command)
        except Exception as e:
            port_pool.release_port(port)
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to communicate with slave server: {e}")

        db_device.status = "in_use"
        db_device.current_user_id = user.id
        db_device.connected_at = datetime.utcnow()
        await db.commit()
        
        return {"message": f"Device connection initiated on port {port}.", "port": port}

    async def disconnect_device(self, db: AsyncSession, *, device_id: int, user: User) -> dict:
        """
        Handles the core logic for disconnecting a user from a device.
        """
        db_device = await self.get_by_id(db, id=device_id)
        if not db_device:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Device not found")

        # Basic permission: only the user using it can disconnect.
        # A 'force_disconnect' permission could be checked here for admins.
        if db_device.current_user_id != user.id:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="You are not the current user of this device.")
            
        # The logic to send STOP_VH_INSTANCE is missing from slave, but we can assume it's there
        # and would be called here.
        
        db_device.status = "available"
        db_device.current_user_id = None
        db_device.connected_at = None
        await db.commit()
        return {"message": "Device disconnected successfully."}

device_service_instance = DeviceService()
device_service = device_service_instance  # 为了向后兼容 
device_service = device_service_instance
