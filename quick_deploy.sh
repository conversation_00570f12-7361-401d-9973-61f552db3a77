#!/bin/bash

echo "🚀 开始部署OmniLink系统..."

# 清理现有环境
echo "🧹 清理现有环境..."
docker-compose down -v 2>/dev/null || true

# 拉取基础镜像
echo "📦 拉取基础镜像..."
docker pull postgres:16-alpine
docker pull redis:6.2-alpine

# 构建应用镜像
echo "🔨 构建主服务器镜像..."
docker-compose build --no-cache main-server

echo "🔨 构建从服务器镜像..."
docker-compose build --no-cache slave-server

# 启动服务
echo "🚀 启动所有服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

echo ""
echo "🎉 部署完成!"
echo "📍 访问地址:"
echo "   - Web界面: http://localhost:8000"
echo "   - API文档: http://localhost:8000/docs"
echo "   - 管理员账户: firefly / bro2fhz12"
echo ""
echo "📋 常用命令:"
echo "   - 查看日志: docker-compose logs -f"
echo "   - 停止服务: docker-compose down"
echo "   - 重启服务: docker-compose restart" 