import os
import ast
import json
from pathlib import Path
from typing import List, Set, Dict, Tuple

# --- 配置 ---
# 要分析的目录
TARGET_DIRECTORIES = ["src/omnilink_main", "common"]
# 已知的第三方库（可以从requirements.txt生成）
# 为简化，我们在这里手动维护一个基础列表，实际应用中可以动态生成
KNOWN_3RD_PARTY_LIBS = {
    "fastapi", "uvicorn", "sqlalchemy", "pydantic", "alembic",
    "requests", "websockets", "pyjwt", "passlib", "bcrypt",
    "pydantic_settings", "asyncpg", "psycopg2-binary"
}
# 冲突库检测
CONFLICTING_LIBS = {
    "peewee": "sqlalchemy",
    "flask": "fastapi"
}

class ImportAnalyzer(ast.NodeVisitor):
    """AST节点访问器，用于提取导入信息"""
    def __init__(self, project_root: Path, project_modules: Set[str]):
        self.imports = set()
        self.project_root = project_root
        self.project_modules = project_modules

    def visit_Import(self, node):
        for alias in node.names:
            self.add_import(alias.name)
        self.generic_visit(node)

    def visit_ImportFrom(self, node):
        if node.module:
            self.add_import(node.module)
        self.generic_visit(node)

    def add_import(self, import_name: str):
        # 只关心顶级模块
        top_level_module = import_name.split('.')[0]
        self.imports.add(top_level_module)

def find_project_modules(project_root: Path, target_dirs: List[str]) -> Set[str]:
    """查找项目中的所有模块和包"""
    modules = set()
    for directory in target_dirs:
        base_path = project_root / directory
        if not base_path.exists(): continue
        for path in base_path.rglob("*.py"):
            # 将文件路径转换为模块路径
            relative_path = path.relative_to(project_root)
            module_path = str(relative_path.with_suffix("")).replace(os.path.sep, '.')
            modules.add(module_path)
            # 添加父包
            parts = module_path.split('.')
            for i in range(1, len(parts)):
                modules.add(".".join(parts[:i]))
    return modules

def analyze_file(path: Path, project_root: Path, project_modules: Set[str]) -> Set[str]:
    """分析单个文件的导入"""
    try:
        with open(path, "r", encoding="utf-8") as f:
            content = f.read()
            tree = ast.parse(content)
            analyzer = ImportAnalyzer(project_root, project_modules)
            analyzer.visit(tree)
            return analyzer.imports
    except Exception as e:
        print(f"Error analyzing {path}: {e}")
        return set()

def run_analysis(project_root: Path, target_dirs: List[str]):
    """执行完整的依赖分析"""
    print("--- 开始项目依赖分析 ---")

    project_modules = find_project_modules(project_root, target_dirs)
    all_files = []
    for directory in target_dirs:
        dir_path = project_root / directory
        if dir_path.exists():
            all_files.extend(list(dir_path.rglob("*.py")))

    file_import_map: Dict[str, Set[str]] = {}
    all_external_imports: Set[str] = set()
    
    # 1. 解析每个文件的导入
    print(f"\n[1/4] 正在解析 {len(all_files)} 个文件...")
    for file_path in all_files:
        relative_path_str = str(file_path.relative_to(project_root))
        imports = analyze_file(file_path, project_root, project_modules)
        
        # 区分内部和外部导入
        internal_imports = {imp for imp in imports if imp in project_modules or imp.split('.')[0] in project_modules}
        external_imports = imports - internal_imports
        
        file_import_map[relative_path_str] = external_imports
        all_external_imports.update(external_imports)
    print("文件解析完成。")

    # 2. 检测库冲突
    print("\n[2/4] 正在检测库冲突...")
    conflict_report: Dict[str, List[str]] = {lib: [] for lib in CONFLICTING_LIBS}
    has_conflict = False
    for file, imports in file_import_map.items():
        for conflict_lib in CONFLICTING_LIBS:
            if conflict_lib in imports:
                conflict_report[conflict_lib].append(file)
                has_conflict = True
    
    if has_conflict:
        print("!!! 发现库冲突 !!!")
        for lib, files in conflict_report.items():
            if files:
                print(f"  - 发现已废弃的库 '{lib}' (应使用 '{CONFLICTING_LIBS[lib]}') 存在于以下文件中:")
                for f in files:
                    print(f"    - {f}")
    else:
        print("未发现库冲突。")

    # 3. 识别未知或缺失的第三方依赖
    print("\n[3/4] 正在识别未知依赖...")
    unknown_libs = all_external_imports - KNOWN_3RD_PARTY_LIBS
    if unknown_libs:
        print("!!! 发现未知或缺失的第三方依赖 !!!")
        print("请检查这些库是否应被添加到`KNOWN_3RD_PARTY_LIBS`或`requirements.txt`中:")
        for lib in sorted(list(unknown_libs)):
            print(f"  - {lib}")
    else:
        print("所有外部依赖均为已知。")
        
    # 4. 识别孤立模块（暂不实现，较为复杂，需要构建完整的有向图）
    print("\n[4/4] 识别孤立模块（暂未实现）")
    print("需要构建完整的项目导入图来精确识别从未被导入的模块。")


    print("\n--- 分析完成 ---")

if __name__ == "__main__":
    # 假设脚本在项目根目录的	ools文件夹下
    project_root_path = Path(__file__).parent.parent
    run_analysis(project_root_path, TARGET_DIRECTORIES)
