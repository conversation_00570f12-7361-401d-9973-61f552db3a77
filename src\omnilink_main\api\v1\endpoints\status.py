from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, <PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Any, Optional

from src.omnilink_main.dependencies.db import get_db
from common.schemas.status_schema import SlaveStatusReport, StatusResponse
from src.omnilink_main.services import status_service, slave_server_service

router = APIRouter()

@router.get(
    "/",
    response_model=StatusResponse,
    summary="Get API Status",
    description="A simple health check endpoint to confirm the API is running."
)
def get_status() -> StatusResponse:
    """
    Health check endpoint.
    """
    return StatusResponse(status="ok")


async def verify_api_key(
    authorization: Optional[str] = Header(None),
    db: AsyncSession = Depends(get_db)
) -> dict:
    if authorization is None or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    token = authorization.split(" ")[1]
    slave = await slave_server_service.get_by_api_key(db, api_key=token)
    if not slave:
        raise HTTPException(status_code=403, detail="Invalid API Key")
    return {"id": slave.id, "server_id": slave.server_id}


@router.post("/report")
async def handle_status_report(
    report: SlaveStatusReport,
    db: AsyncSession = Depends(get_db),
    slave_info: dict = Depends(verify_api_key)
) -> Any:
    """
    Receives and processes status reports from slave servers.
    """
    # Verify that the slave reporting is the one authenticated
    if report.slave_id != slave_info["server_id"]:
        raise HTTPException(status_code=403, detail="Reported slave_id does not match authenticated slave")
    
    await status_service.process_slave_report(db=db, slave_id=slave_info["id"], report=report)
    
    return {"status": "ok", "message": "Report processed"} 