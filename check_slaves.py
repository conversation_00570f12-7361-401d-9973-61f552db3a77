import requests
import json

API_URL = "http://localhost:8000/api/v1/slaves"

def check_slaves():
    print(f"Querying slave servers from: {API_URL}")
    try:
        response = requests.get(API_URL, timeout=5)
        if response.status_code == 200:
            print("Successfully retrieved data from the main server.")
            data = response.json()
            print("--- Slave Server List ---")
            if data and data.get("data"):
                print(json.dumps(data["data"], indent=2))
            else:
                print("No slave servers found or response format is unexpected.")
            print("-------------------------")
        else:
            print(f"Error: Received status code {response.status_code}")
            try:
                print("Response content:")
                print(response.json())
            except json.JSONDecodeError:
                print(response.text)
    except requests.exceptions.RequestException as e:
        print(f"An error occurred while connecting to the main server: {e}")

if __name__ == "__main__":
    check_slaves() 