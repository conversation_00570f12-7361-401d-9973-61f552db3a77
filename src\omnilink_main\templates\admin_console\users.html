<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主从服务器系统 - 用户权限管理</title>
    <!-- Material Design Web Components -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Material+Icons&display=block" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet" />
    <!-- Material Design 3 Styles -->
    <link href="{{ url_for('static', filename='css/material-components-web.min.css') }}" rel="stylesheet">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin_material.css') }}">
    <!-- D3.js用于权限继承关系可视化 -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
</head>
<body class="mdc-typography">
    <div class="app-container">
        <!-- 顶部应用栏 -->
        <header class="mdc-top-app-bar mdc-top-app-bar--fixed">
            <div class="mdc-top-app-bar__row">
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-start">
                    <button class="material-icons mdc-top-app-bar__navigation-icon mdc-icon-button" id="menu-button">menu</button>
                    <span class="mdc-top-app-bar__title">主从服务器管理系统 - 用户权限管理</span>
                </section>
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-end" role="toolbar">
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="refresh-button" aria-label="刷新">refresh</button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="theme-toggle" aria-label="切换主题">dark_mode</button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="user-menu-button" aria-label="用户菜单">account_circle</button>
                    
                    <!-- 用户菜单 -->
                    <div class="mdc-menu mdc-menu-surface" id="user-menu">
                        <ul class="mdc-list" role="menu" aria-hidden="true" aria-orientation="vertical" tabindex="-1">
                            <li class="mdc-list-item" role="menuitem">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="mdc-list-item__text">个人设置</span>
                            </li>
                            <li class="mdc-list-divider" role="separator"></li>
                            <li class="mdc-list-item" role="menuitem" id="logout-button" data-logout-url="/auth/logout">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="mdc-list-item__text">退出登录</span>
                            </li>
                        </ul>
                    </div>
                </section>
            </div>
        </header>

        <!-- 主内容区域 -->
        <div class="app-content mdc-top-app-bar--fixed-adjust">
            <div class="mdc-drawer-app-content">
                <!-- 侧边导航抽屉 -->
                <aside class="mdc-drawer mdc-drawer--dismissible">
                    <div class="mdc-drawer__content">
                        <nav class="mdc-list">
                            <a class="mdc-list-item" href="{{ url_for('admin_dashboard') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">dashboard</span>
                                <span class="mdc-list-item__text">仪表盘</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_devices') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">usb</span>
                                <span class="mdc-list-item__text">设备管理</span>
                            </a>
                            <a class="mdc-list-item mdc-list-item--activated" href="{{ url_for('admin_users') }}" aria-current="page">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">people</span>
                                <span class="mdc-list-item__text">用户权限</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_group_management') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">dns</span>
                                <span class="mdc-list-item__text">分组管理</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_migration_wizard') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">swap_horiz</span>
                                <span class="mdc-list-item__text">迁移向导</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_alerts') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">notifications</span>
                                <span class="mdc-list-item__text">告警设置</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_health_monitor') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">favorite</span>
                                <span class="mdc-list-item__text">健康监控</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_logs') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">description</span>
                                <span class="mdc-list-item__text">日志查看</span>
                            </a>
                            
                            <hr class="mdc-list-divider">
                            <h6 class="mdc-list-group__subheader">系统</h6>
                            
                            <a class="mdc-list-item" href="{{ url_for('admin_settings') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">settings</span>
                                <span class="mdc-list-item__text">系统设置</span>
                            </a>
                        </nav>
                    </div>
                </aside>

                <!-- 主内容 -->
                <main class="main-content">
                    <div class="page-content">
                        <h1 class="mdc-typography--headline4">用户权限管理</h1>
                        
                        <!-- 用户管理工具栏 -->
                        <div class="user-toolbar mdc-card">
                            <div class="user-toolbar-content">
                                <!-- 视图切换按钮组 -->
                                <div class="view-toggle-group">
                                    <button class="mdc-button view-toggle-button" id="view-toggle-users" data-view="users">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">people</span>
                                        <span class="mdc-button__label">用户管理</span>
                                    </button>
                                    <button class="mdc-button view-toggle-button" id="view-toggle-roles" data-view="roles">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">shield</span>
                                        <span class="mdc-button__label">角色管理</span>
                                    </button>
                                    <button class="mdc-button view-toggle-button" id="view-toggle-permissions" data-view="permissions">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">security</span>
                                        <span class="mdc-button__label">权限设置</span>
                                    </button>
                                </div>
                                
                                <!-- 搜索框 -->
                                <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon search-field">
                                    <i class="material-icons mdc-text-field__icon">search</i>
                                    <input type="text" id="user-search" class="mdc-text-field__input" placeholder="搜索用户...">
                                    <div class="mdc-notched-outline">
                                        <div class="mdc-notched-outline__leading"></div>
                                        <div class="mdc-notched-outline__notch">
                                            <label for="user-search" class="mdc-floating-label">搜索</label>
                                        </div>
                                        <div class="mdc-notched-outline__trailing"></div>
                                    </div>
                                </div>
                                
                                <!-- 过滤和排序 -->
                                <div class="filter-sort-container">
                                    <button class="mdc-button mdc-button--outlined" id="filter-button">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">filter_list</span>
                                        <span class="mdc-button__label">过滤</span>
                                    </button>
                                    <!-- Status Filter Menu -->
                                    <div class="mdc-menu mdc-menu-surface" id="status-filter-menu">
                                        <ul class="mdc-list" role="menu" aria-hidden="true" aria-orientation="vertical" tabindex="-1">
                                            <li class="mdc-list-item" role="menuitem" data-value="all">
                                                <span class="mdc-list-item__ripple"></span>
                                                <span class="mdc-list-item__text">所有状态</span>
                                            </li>
                                            <li class="mdc-list-item" role="menuitem" data-value="true">
                                                <span class="mdc-list-item__ripple"></span>
                                                <span class="mdc-list-item__text">已激活</span>
                                            </li>
                                            <li class="mdc-list-item" role="menuitem" data-value="false">
                                                <span class="mdc-list-item__ripple"></span>
                                                <span class="mdc-list-item__text">未激活</span>
                                            </li>
                                        </ul>
                                    </div>

                                    <button class="mdc-button mdc-button--outlined" id="sort-button" style="display: none;"> <!-- Hide generic sort button for now -->
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">sort</span>
                                        <span class="mdc-button__label">排序</span>
                                    </button>
                                </div>
                                
                                <!-- 添加用户/角色按钮 -->
                                <button class="mdc-button mdc-button--raised" id="add-item-button">
                                    <span class="mdc-button__ripple"></span>
                                    <span class="material-icons mdc-button__icon">add</span>
                                    <span class="mdc-button__label">添加用户</span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 用户批量操作工具栏 (初始隐藏) -->
                        <div class="batch-operations-toolbar mdc-card" style="display: none;">
                            <div class="batch-operations-content">
                                <span class="selected-count">已选择 <span id="selected-item-count">0</span> 个用户</span>
                                
                                <div class="batch-operations-buttons">
                                    <button class="mdc-button" id="batch-enable-button">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">check_circle</span>
                                        <span class="mdc-button__label">启用</span>
                                    </button>
                                    <button class="mdc-button" id="batch-disable-button">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">block</span>
                                        <span class="mdc-button__label">禁用</span>
                                    </button>
                                    <button class="mdc-button" id="batch-role-button">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">shield</span>
                                        <span class="mdc-button__label">分配角色</span>
                                    </button>
                                    <button class="mdc-button" id="batch-delete-button">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="material-icons mdc-button__icon">delete</span>
                                        <span class="mdc-button__label">删除</span>
                                    </button>
                                </div>
                                
                                <button class="mdc-button" id="cancel-batch-button">
                                    <span class="mdc-button__ripple"></span>
                                    <span class="material-icons mdc-button__icon">close</span>
                                    <span class="mdc-button__label">取消</span>
                                </button>
                            </div>
                        </div>

                        <!-- Content Area - Switches based on view -->
                        <div id="user-management-content" class="user-management-content">
                            <!-- User List View (Default) -->
                            <div id="users-view" class="view-container">
                                <h2 class="mdc-typography--headline5">用户列表</h2>
                                <!-- User Table Here -->
                                <div class="mdc-data-table" data-mdc-auto-init="MDCDataTable">
                                    <div class="mdc-data-table__table-container">
                                        <table class="mdc-data-table__table" aria-label="用户列表" id="user-management-table">
                                            <thead>
                                                <tr class="mdc-data-table__header-row">
                                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                                                        <div class="mdc-checkbox mdc-data-table__header-row-checkbox mdc-checkbox--selected">
                                                            <input type="checkbox" class="mdc-checkbox__native-control" aria-label="选择所有用户"/>
                                                            <div class="mdc-checkbox__background">
                                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                                </svg>
                                                                <div class="mdc-checkbox__mixedmark"></div>
                                                            </div>
                                                            <div class="mdc-checkbox__ripple"></div>
                                                        </div>
                                                    </th>
                                                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col" data-sort-by="username">
                                                        用户名 <span class="material-icons sort-icon" style="font-size: 1em; vertical-align: middle;">unfold_more</span>
                                                    </th>
                                                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col" data-sort-by="email">
                                                        邮箱 <span class="material-icons sort-icon" style="font-size: 1em; vertical-align: middle;">unfold_more</span>
                                                    </th>
                                                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">角色</th>
                                                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col" data-sort-by="status">
                                                        状态 <span class="material-icons sort-icon" style="font-size: 1em; vertical-align: middle;">unfold_more</span>
                                                    </th>
                                                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col" data-sort-by="created_at">
                                                        创建日期 <span class="material-icons sort-icon" style="font-size: 1em; vertical-align: middle;">unfold_more</span>
                                                    </th>
                                                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody class="mdc-data-table__content">
                                                
                                            </tbody>
                                        </table>
                                    </div>
                                    <!-- Pagination (Placeholder) -->
                                     <div class="mdc-data-table__pagination">
                                        </div>
                                </div>
                            </div>

                            <!-- Role Management View -->
                            <div id="roles-view" class="view-container" style="display: none;">
                                <h2 class="mdc-typography--headline5">角色管理</h2>
                                <div class="mdc-layout-grid">
                                    <div class="mdc-layout-grid__inner">
                                        <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-4">
                                             <h3 class="mdc-typography--headline6">角色列表</h3>
                                             <!-- Role List Here -->
                                             <ul class="mdc-list" id="role-list">
                                                 
                                             </ul>
                                        </div>
                                        <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-8">
                                            <h3 class="mdc-typography--headline6">角色权限</h3>
                                            <div id="role-permissions-container">
                                                 <!-- Permissions Tree/List for selected role -->
                                                 <p>请选择一个角色以查看或编辑权限。</p>
                                            </div>
                                             <h3 class="mdc-typography--headline6">权限继承关系 (占位符)</h3>
                                             <div id="permission-inheritance-viz" style="min-height: 200px; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center;">
                                                 <p>权限继承关系图谱 (D3.js)</p>
                                             </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Permission Settings View -->
                             <div id="permissions-view" class="view-container" style="display: none;">
                                 <h2 class="mdc-typography--headline5">权限设置 (全局)</h2>
                                 <!-- Global Permission Settings / Definition -->
                                 <p>全局权限定义和配置界面 (内容待实现)</p>
                             </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>
    
    <!-- 添加/编辑用户对话框 -->
    <div class="mdc-dialog" id="user-dialog">
        <div class="mdc-dialog__container">
            <div class="mdc-dialog__surface" role="dialog" aria-modal="true" aria-labelledby="user-dialog-title" aria-describedby="user-dialog-content">
                <h2 class="mdc-dialog__title" id="user-dialog-title">添加用户</h2>
                <div class="mdc-dialog__content" id="user-dialog-content">
                    <form id="user-form">
                        <input type="hidden" id="edit-user-id" name="editUserId">
                        <div class="mdc-layout-grid">
                            <div class="mdc-layout-grid__inner">
                                <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-12">
                                    <div class="mdc-text-field mdc-text-field--outlined" id="username-field" style="width: 100%;">
                                        <input type="text" class="mdc-text-field__input" id="username" name="username" required>
                                        <div class="mdc-notched-outline">
                                            <div class="mdc-notched-outline__leading"></div>
                                            <div class="mdc-notched-outline__notch">
                                                <label for="username" class="mdc-floating-label">用户名</label>
                                            </div>
                                            <div class="mdc-notched-outline__trailing"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-12">
                                    <div class="mdc-text-field mdc-text-field--outlined" id="email-field" style="width: 100%;">
                                        <input type="email" class="mdc-text-field__input" id="email" name="email" required>
                                        <div class="mdc-notched-outline">
                                            <div class="mdc-notched-outline__leading"></div>
                                            <div class="mdc-notched-outline__notch">
                                                <label for="email" class="mdc-floating-label">电子邮箱</label>
                                            </div>
                                            <div class="mdc-notched-outline__trailing"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-12">
                                    <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-trailing-icon" id="password-field" style="width: 100%;">
                                        <input type="password" class="mdc-text-field__input" id="password" name="password">
                                        <i class="material-icons mdc-text-field__icon mdc-text-field__icon--trailing" tabindex="0" role="button" id="password-visibility">visibility_off</i>
                                        <div class="mdc-notched-outline">
                                            <div class="mdc-notched-outline__leading"></div>
                                            <div class="mdc-notched-outline__notch">
                                                <label for="password" class="mdc-floating-label">密码</label>
                                            </div>
                                            <div class="mdc-notched-outline__trailing"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-12">
                                    <div class="mdc-text-field mdc-text-field--outlined" id="full-name-field" style="width: 100%;">
                                        <input type="text" class="mdc-text-field__input" id="full_name" name="full_name">
                                        <div class="mdc-notched-outline">
                                            <div class="mdc-notched-outline__leading"></div>
                                            <div class="mdc-notched-outline__notch">
                                                <label for="full_name" class="mdc-floating-label">全名</label>
                                            </div>
                                            <div class="mdc-notched-outline__trailing"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-12">
                                    <div class="mdc-select mdc-select--outlined" id="role-select" style="width: 100%;">
                                        <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false" aria-labelledby="role-select-label role-select-selected-text">
                                            <span class="mdc-select__selected-text"></span>
                                            <span class="mdc-select__dropdown-icon">
                                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                                </svg>
                                            </span>
                                            <span class="mdc-notched-outline">
                                                <span class="mdc-notched-outline__leading"></span>
                                                <span class="mdc-notched-outline__notch">
                                                    <span class="mdc-floating-label">角色</span>
                                                </span>
                                                <span class="mdc-notched-outline__trailing"></span>
                                            </span>
                                        </div>
                                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                            <ul class="mdc-list" id="role-select-list">
                                                <!-- 角色选项将通过JavaScript动态加载 -->
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-12">
                                    <div class="mdc-form-field">
                                        <div class="mdc-checkbox" id="active-checkbox-container">
                                            <input type="checkbox" class="mdc-checkbox__native-control" id="is_active" name="is_active" checked/>
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                        </div>
                                        <label for="is_active">激活用户</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="mdc-dialog__actions">
                    <button type="button" class="mdc-button mdc-dialog__button" data-mdc-dialog-action="cancel">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">取消</span>
                    </button>
                    <button type="button" class="mdc-button mdc-button--raised mdc-dialog__button" id="save-user-button">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">保存</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="mdc-dialog__scrim"></div>
    </div>
    
    <!-- 添加/编辑角色对话框 -->
    <div class="mdc-dialog" id="role-dialog">
        <div class="mdc-dialog__container">
            <div class="mdc-dialog__surface" role="dialog" aria-modal="true" aria-labelledby="role-dialog-title" aria-describedby="role-dialog-content">
                <h2 class="mdc-dialog__title" id="role-dialog-title">添加角色</h2>
                <div class="mdc-dialog__content" id="role-dialog-content">
                    <form id="role-form">
                        <div class="mdc-layout-grid">
                            <div class="mdc-layout-grid__inner">
                                <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-12">
                                    <div class="mdc-text-field mdc-text-field--outlined" id="role-name-field" style="width: 100%;">
                                        <input type="text" class="mdc-text-field__input" id="role-name" name="role-name" required>
                                        <div class="mdc-notched-outline">
                                            <div class="mdc-notched-outline__leading"></div>
                                            <div class="mdc-notched-outline__notch">
                                                <label for="role-name" class="mdc-floating-label">角色名称</label>
                                            </div>
                                            <div class="mdc-notched-outline__trailing"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-12">
                                    <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--textarea" id="role-description-field" style="width: 100%;">
                                        <textarea class="mdc-text-field__input" id="role-description" name="role-description" rows="3"></textarea>
                                        <div class="mdc-notched-outline">
                                            <div class="mdc-notched-outline__leading"></div>
                                            <div class="mdc-notched-outline__notch">
                                                <label for="role-description" class="mdc-floating-label">角色描述</label>
                                            </div>
                                            <div class="mdc-notched-outline__trailing"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-12">
                                    <h3 class="mdc-typography--subtitle1">继承自：</h3>
                                    <div id="parent-roles-checkboxes">
                                        <!-- 父角色复选框将通过JavaScript动态加载 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="mdc-dialog__actions">
                    <button type="button" class="mdc-button mdc-dialog__button" data-mdc-dialog-action="cancel">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">取消</span>
                    </button>
                    <button type="button" class="mdc-button mdc-button--raised mdc-dialog__button" id="save-role-button">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">保存</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="mdc-dialog__scrim"></div>
    </div>
    
    <!-- Material Design 3 脚本 -->
    <script src="{{ url_for('static', filename='js/material-components-web.min.js') }}"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/admin_material.js') }}"></script>
    <script src="{{ url_for('static', filename='js/websocket_client.js') }}"></script>
    <script src="{{ url_for('static', filename='js/rbac_visualization.js') }}"></script>
    <script src="{{ url_for('static', filename='js/user_management.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const userDialog = new mdc.dialog.MDCDialog(document.getElementById('user-dialog'));
            const roleDialog = new mdc.dialog.MDCDialog(document.getElementById('role-dialog')); // Assuming it exists for init
            const roleSelectMDC = new mdc.select.MDCSelect(document.getElementById('role-select'));

            // Initialize text fields and other components if needed here
            document.querySelectorAll('.mdc-text-field').forEach((tf) => new mdc.textField.MDCTextField(tf));
            document.querySelectorAll('.mdc-checkbox').forEach((cb) => new mdc.checkbox.MDCCheckbox(cb));
            document.querySelectorAll('.mdc-button').forEach(button => new mdc.ripple.MDCRipple(button));

            // Initial population of roles for user dialog
            loadRolesForUserDialogSelect();
            setupRoleItemEventListeners(); // Setup listeners for initially rendered roles by Jinja2
            document.getElementById('role-list').dataset.listenersAttached = 'true';

            function showView(viewId) {
                document.getElementById('users-view').style.display = 'none';
                document.getElementById('roles-view').style.display = 'none';
                document.getElementById('permissions-view').style.display = 'none';
                document.getElementById(viewId).style.display = 'block';

                if (viewId === 'roles-view') {
                    // Roles are server-rendered by Jinja2. Listeners are set up once on DOMContentLoaded.
                    // If roles could be dynamically reloaded, listeners would need re-attachment.
                    document.getElementById('role-permissions-container').innerHTML = '<p>请选择一个角色以查看或编辑权限。</p>';
                } else if (viewId === 'users-view') {
                    // Placeholder: logic to refresh user list if needed, e.g., after adding/editing a user.
                }
            }
            window.showView = showView; // Make it globally accessible if called from HTML attributes

            function displayRolePermissions(permissions, containerId = 'role-permissions-container') {
                const container = document.getElementById(containerId);
                if (!container) return;
                container.innerHTML = ''; 
                if (!permissions || Object.keys(permissions).length === 0) { // Check if permissions object is empty
                    container.innerHTML = '<p>此角色没有特定权限或权限数据不可用。</p>';
                    return;
                }
                const ul = document.createElement('ul');
                ul.className = 'mdc-list';
                for (const permKey in permissions) {
                    if (permissions.hasOwnProperty(permKey)) {
                        const perm = permissions[permKey]; // perm is an object like { name: '...', description: '...', assigned: true/false }
                        const li = document.createElement('li');
                        li.className = 'mdc-list-item';
                        const permId = `perm-${permKey.replace(/[^a-zA-Z0-9]/g, '-')}`;
                        li.innerHTML = `
                            <span class="mdc-list-item__ripple"></span>
                            <span class="mdc-list-item__graphic">
                                <div class="mdc-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" id="${permId}" ${(perm.assigned || perm) ? 'checked' : ''} disabled/>
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24"><path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/></svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                </div>
                            </span>
                            <label class="mdc-list-item__text" for="${permId}">${perm.name || permKey} (${perm.description || '自定义权限'})</label>`;
                        ul.appendChild(li);
                    }
                }
                container.appendChild(ul);
                container.querySelectorAll('.mdc-checkbox').forEach(el => new mdc.checkbox.MDCCheckbox(el));
            }

            function setupRoleItemEventListeners() {
                const roleListItems = document.querySelectorAll('#role-list .mdc-list-item');
                roleListItems.forEach(item => {
                    item.addEventListener('click', function(event) {
                        roleListItems.forEach(i => i.classList.remove('mdc-list-item--activated'));
                        event.currentTarget.classList.add('mdc-list-item--activated');
                        
                        const roleId = event.currentTarget.dataset.roleId;
                        const roleName = event.currentTarget.querySelector('.mdc-list-item__text').textContent;
                        document.getElementById('role-permissions-container').innerHTML = `<p>正在加载角色 (${roleName}) 的权限...</p>`;

                        fetch(`/api/admin/roles/${roleId}/permissions`)
                            .then(response => {
                                if (!response.ok) throw new Error(`HTTP ${response.status} - ${response.statusText}`);
                                return response.json();
                            })
                            .then(data => {
                                displayRolePermissions(data.permissions || data); // Backend might return {permissions: {...}} or just {...}
                            })
                            .catch(error => {
                                console.error('Error fetching role permissions:', error);
                                document.getElementById('role-permissions-container').innerHTML = '<p style="color:red;">无法加载权限：' + error.message + '</p>';
                            });
                        
                        // 调用D3.js绘图函数
                        drawPermissionGraph(roleId); 
                    });
                });
            }
            
            function loadRolesForUserDialogSelect(selectListId = 'role-select-list', mdcSelectId = 'role-select') {
                const listElement = document.getElementById(selectListId);
                const mdcSelectElement = document.getElementById(mdcSelectId);
                if (!listElement || !mdcSelectElement) return;

                fetch('/api/admin/roles')
                    .then(response => response.ok ? response.json() : Promise.reject('Failed to load roles for dialog'))
                    .then(roles => {
                        listElement.innerHTML = ''; // Clear existing roles
                        if (roles && roles.length > 0) {
                            roles.forEach(role => {
                                const li = document.createElement('li');
                                li.className = 'mdc-list-item';
                                li.setAttribute('data-value', role.id);
                                li.innerHTML = `<span class="mdc-list-item__ripple"></span>
                                                <span class="mdc-list-item__text">${role.name}</span>`;
                                listElement.appendChild(li);
                            });
                            // Re-initialize MDC Select if it was already initialized
                            // This is often needed if items are added after initial `attachTo`
                            if (roleSelectMDC) {
                                roleSelectMDC.layoutOptions(); // Recalculate layout for new options
                                // Optionally set a default value or handle no selection
                                // roleSelectMDC.value = roles[0].id; 
                            } else {
                                console.warn('MDCSelect for roles not found during dynamic population.');
                            }
                        } else {
                            listElement.innerHTML = '<li class="mdc-list-item mdc-list-item--disabled"><span class="mdc-list-item__text">无可用角色</span></li>';
                        }
                    })
                    .catch(error => {
                        console.error("Error loading roles for user dialog select:", error);
                        listElement.innerHTML = '<li class="mdc-list-item mdc-list-item--disabled"><span class="mdc-list-item__text">无法加载角色</span></li>';
                    });
            }

            // Event Listeners for dialogs
            const addUserButton = document.getElementById('add-user-button');
            if (addUserButton) {
                addUserButton.addEventListener('click', () => {
                    document.getElementById('user-dialog-title').textContent = '添加用户';
                    document.getElementById('user-form').reset();
                    document.getElementById('edit-user-id').value = ''; // Clear user ID for add mode
                    roleSelectMDC.selectedIndex = -1; // Deselect any role
                    userDialog.open();
                });
            }

            const userTableBody = document.querySelector('#user-management-table .mdc-data-table__content'); // Ensure your table has id="user-management-table"
            if (userTableBody) {
                 userTableBody.addEventListener('click', function(event) {
                    const editButton = event.target.closest('.edit-user-button');
                    if (editButton) {
                        const userId = editButton.dataset.userId;
                        document.getElementById('user-dialog-title').textContent = '编辑用户';
                        document.getElementById('edit-user-id').value = userId; // Store the userId

                        fetch(`/api/admin/users/${userId}`)
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error(`HTTP error! status: ${response.status}`);
                                }
                                return response.json();
                            })
                            .then(userData => {
                                document.getElementById('username').value = userData.username || '';
                                document.getElementById('email').value = userData.email || '';
                                document.getElementById('full_name').value = userData.full_name || '';
                                const isActiveCheckbox = document.getElementById('is_active');
                                if (isActiveCheckbox) isActiveCheckbox.checked = userData.is_active;
                                
                                ['username', 'email', 'full_name'].forEach(id => {
                                    const el = document.getElementById(id);
                                    const mdcField = el && el.parentElement && el.parentElement.classList.contains('mdc-text-field') ? mdc.textField.MDCTextField.attachTo(el.parentElement) : null;
                                    if (mdcField) {
                                        mdcField.value = el.value; // Ensure MDC component knows the new value
                                        mdcField.layout();
                                    }
                                });

                                if (userData.role_id && roleSelectMDC) {
                                    roleSelectMDC.value = userData.role_id;
                                } else if (roleSelectMDC) {
                                    roleSelectMDC.selectedIndex = -1;
                                }
                                userDialog.open();
                            })
                            .catch(error => {
                                console.error('Error fetching user data for edit:', error);
                                if (typeof showNotification === 'function') {
                                    showNotification('错误', '无法加载用户信息: ' + error.message, 'error');
                                }
                            });
                    }

                    const deleteButton = event.target.closest('.delete-user-button');
                    if (deleteButton) {
                        const userId = deleteButton.dataset.userId;
                        const userName = deleteButton.closest('tr')?.querySelector('td:first-child')?.textContent || '未知用户';

                        if (confirm(`确定要删除用户 "${userName}" (ID: ${userId})吗？此操作无法撤销。`)) {
                            fetch(`/api/admin/users/${userId}`, {
                                method: 'DELETE',
                                headers: { // Add CSRF token if needed
                                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '' 
                                }
                            })
                            .then(response => {
                                if (!response.ok) {
                                     return response.json().then(err => Promise.reject({ status: response.status, body: err }));
                                }
                                return response.status === 204 ? {} : response.json(); 
                            })
                            .then(data => {
                                showNotification('成功', `用户 "${userName}" 已删除。`, 'success');
                                if (typeof loadUsersAndRefreshTable === 'function') { 
                                     loadUsersAndRefreshTable();
                                } else {
                                    deleteButton.closest('tr')?.remove();
                                }
                            })
                            .catch(error => {
                                console.error('Error deleting user:', error);
                                const errorMsg = error.body && error.body.detail ? (Array.isArray(error.body.detail) ? error.body.detail[0].msg : error.body.detail) : (error.message || '删除用户失败');
                                showNotification('错误', `删除用户 "${userName}" 失败: ${errorMsg}`, 'error');
                            });
                        }
                    }
                });
            }

            const saveUserButton = document.getElementById('save-user-button');
            if (saveUserButton) {
                saveUserButton.addEventListener('click', () => {
                    const form = document.getElementById('user-form');
                    const usernameInput = document.getElementById('username');
                    const emailInput = document.getElementById('email');
                    const passwordInput = document.getElementById('password'); 
                    const fullNameInput = document.getElementById('full_name');
                    const isActiveCheckbox = document.getElementById('is_active');
                    const selectedRole = roleSelectMDC ? roleSelectMDC.value : null;
                    const editingUserId = document.getElementById('edit-user-id').value;

                    if (!usernameInput.value.trim()) {
                        showNotification('验证错误', '用户名不能为空。', 'warning');
                        mdc.textField.MDCTextField.attachTo(usernameInput.parentElement).focus();
                        return;
                    }
                    if (!emailInput.value.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailInput.value.trim())) {
                        showNotification('验证错误', '请输入有效的邮箱地址。', 'warning');
                        mdc.textField.MDCTextField.attachTo(emailInput.parentElement).focus();
                        return;
                    }
            
                    const userData = {
                        username: usernameInput.value.trim(),
                        email: emailInput.value.trim(),
                        full_name: fullNameInput.value.trim() || null,
                        is_active: isActiveCheckbox ? isActiveCheckbox.checked : true,
                        role_id: selectedRole || null
                    };

                    if (passwordInput.value) { 
                        userData.password = passwordInput.value;
                    }

                    let method = 'POST';
                    let url = '/api/admin/users';

                    if (editingUserId) {
                        method = 'PUT';
                        url = `/api/admin/users/${editingUserId}`;
                    }

                    fetch(url, {
                        method: method,
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '' 
                        },
                        body: JSON.stringify(userData)
                    })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(err => Promise.reject({ status: response.status, body: err }));
                        }
                        return response.json();
                    })
                    .then(data => {
                        showNotification('成功', `用户已${editingUserId ? '更新' : '添加'}。`, 'success');
                        userDialog.close();
                        if (typeof loadUsersAndRefreshTable === 'function') { 
                            loadUsersAndRefreshTable();
                        }
                    })
                    .catch(error => {
                        console.error('Error saving user:', error);
                        const errorMsg = error.body && error.body.detail ? (Array.isArray(error.body.detail) ? error.body.detail[0].msg : error.body.detail) : (error.message || '保存用户失败');
                        showNotification('错误', `保存用户失败: ${errorMsg}`, 'error');
                    });
                });
            }

            // MDC Component Instances
            let statusFilterMenuMDC;

            // Debounce function
            function debounce(func, delay) {
                let timeout;
                return function(...args) {
                    const context = this;
                    clearTimeout(timeout);
                    timeout = setTimeout(() => func.apply(context, args), delay);
                };
            }

            // Initialize MDC components
            const statusFilterMenuElement = document.getElementById('status-filter-menu');
            if (statusFilterMenuElement) {
                statusFilterMenuMDC = new mdc.menu.MDCMenu(statusFilterMenuElement);
            }

            const filterButton = document.getElementById('filter-button');
            if (filterButton && statusFilterMenuMDC) {
                filterButton.addEventListener('click', () => statusFilterMenuMDC.open = true);
            }

            // Enhance loadUsersAndRefreshTable
            function loadUsersAndRefreshTable(page = 1, searchTerm = '', filters = {}, sort = {}) {
                currentPage = page; // Update global currentPage
                currentSearchTerm = searchTerm; // Update global searchTerm
                currentFilters = {...currentFilters, ...filters}; // Merge new filters
                currentSort = {...currentSort, ...sort}; // Merge new sort options
                console.log(`Loading users: Page=${currentPage}, Search='${currentSearchTerm}', Filters=`, currentFilters, `Sort=`, currentSort);
                const userTableBody = document.querySelector('#user-management-table .mdc-data-table__content');
                if (!userTableBody) {
                    console.error("User table body (#user-management-table .mdc-data-table__content) not found for refresh.");
                    return;
                }
                let queryParams = new URLSearchParams({
                    limit: itemsPerPage,
                    offset: (currentPage - 1) * itemsPerPage,
                    sort_by: currentSort.by || 'username', 
                    sort_order: currentSort.order || 'asc'
                });
                if (currentSearchTerm) {
                    queryParams.append('search', currentSearchTerm);
                }
                if (currentFilters.isActive !== null && currentFilters.isActive !== 'all') {
                    queryParams.append('is_active', currentFilters.isActive);
                }
                // Add other filters like roleId if implemented
                // if (currentFilters.roleId) { queryParams.append('role_id', currentFilters.roleId); }
                userTableBody.innerHTML = `<tr><td colspan="7" class="mdc-data-table__cell" style="text-align:center;">正在加载用户...</td></tr>`;
                fetch(`/api/admin/users?${queryParams.toString()}`) 
                    .then(response => {
                        if (!response.ok) throw new Error(`获取用户失败: ${response.status} ${response.statusText}`);
                        return response.json();
                    })
                    .then(pagedData => {
                        userTableBody.innerHTML = ''; 
                        const users = pagedData.data; // Assuming API returns { data: [], total: X, ...}
                        const totalUsers = pagedData.total;
                        if (users && users.length > 0) {
                            users.forEach(user => {
                                const row = userTableBody.insertRow();
                                row.dataset.rowId = user.id;
                                row.className = 'mdc-data-table__row';
                                
                                // Checkbox cell
                                let cell = row.insertCell();
                                cell.className = 'mdc-data-table__cell mdc-data-table__cell--checkbox';
                                cell.innerHTML = `<div class="mdc-checkbox mdc-data-table__row-checkbox"><input type="checkbox" class="mdc-checkbox__native-control" aria-labelledby="u${user.id}-name" data-user-id="${user.id}"/><div class="mdc-checkbox__background"><svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24"><path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/></svg><div class="mdc-checkbox__mixedmark"></div></div><div class="mdc-checkbox__ripple"></div></div>`;
                                new mdc.checkbox.MDCCheckbox(cell.querySelector('.mdc-checkbox'));

                                // Data cells
                                row.insertCell().outerHTML = `<th class="mdc-data-table__cell" scope="row" id="u${user.id}-name">${user.username || 'N/A'}</th>`;
                                row.insertCell().outerHTML = `<td class="mdc-data-table__cell">${user.email || 'N/A'}</td>`;
                                row.insertCell().outerHTML = `<td class="mdc-data-table__cell">${user.role_name || 'N/A'}</td>`;
                                row.insertCell().outerHTML = `<td class="mdc-data-table__cell">${user.is_active ? '活跃' : '禁用'}</td>`;
                                row.insertCell().outerHTML = `<td class="mdc-data-table__cell">${new Date(user.created_at || Date.now()).toLocaleDateString()}</td>`;
                                
                                const actionsCell = row.insertCell();
                                actionsCell.className = 'mdc-data-table__cell';
                                actionsCell.innerHTML = `
                                    <button class="mdc-icon-button material-icons edit-user-button" title="编辑用户" data-user-id="${user.id}">edit</button>
                                    <button class="mdc-icon-button material-icons delete-user-button" title="删除用户" data-user-id="${user.id}">delete</button>
                                `;
                                actionsCell.querySelectorAll('.mdc-icon-button').forEach(button => new mdc.ripple.MDCRipple(button));
                            });
                        } else {
                            const row = userTableBody.insertRow();
                            const cell = row.insertCell();
                            cell.colSpan = 7; 
                            cell.className = 'mdc-data-table__cell';
                            cell.textContent = '没有找到符合条件的用户。';
                        }
                        // Call function to update pagination UI (to be implemented)
                        updatePaginationControls(totalUsers, itemsPerPage, currentPage);
                    })
                    .catch(error => {
                        console.error("Error refreshing user list:", error);
                        userTableBody.innerHTML = `<tr><td colspan="7" class="mdc-data-table__cell" style="text-align:center; color:red;">无法加载用户列表: ${error.message}</td></tr>`;
                        updatePaginationControls(0, itemsPerPage, 1); // Reset pagination on error
                    });
            }

            // Search functionality
            const searchInput = document.getElementById('user-search');
            if (searchInput) {
                searchInput.addEventListener('input', debounce(() => {
                    loadUsersAndRefreshTable(1, searchInput.value.trim(), currentFilters, currentSort);
                }, 500)); // 500ms debounce
            }
            
            // Status Filter Functionality
            if (statusFilterMenuElement) {
                statusFilterMenuElement.addEventListener('MDCMenu:selected', (event) => {
                    const selectedValue = event.detail.item.dataset.value;
                    currentFilters.isActive = selectedValue === 'all' ? null : (selectedValue === 'true');
                    loadUsersAndRefreshTable(1, currentSearchTerm, currentFilters, currentSort);
                });
            }

            // Sorting Functionality
            document.querySelectorAll('#user-management-table th[data-sort-by]').forEach(headerCell => {
                headerCell.addEventListener('click', () => {
                    const sortByField = headerCell.dataset.sortBy;
                    if (currentSort.by === sortByField) {
                        currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentSort.by = sortByField;
                        currentSort.order = 'asc';
                    }
                    updateSortIcons();
                    loadUsersAndRefreshTable(1, currentSearchTerm, currentFilters, currentSort);
                });
            });

            function updateSortIcons() {
                document.querySelectorAll('#user-management-table th[data-sort-by] .sort-icon').forEach(icon => {
                    icon.textContent = 'swap_vert'; // Reset all icons
                });
                const activeSortIcon = document.querySelector(`#user-management-table th[data-sort-by="${currentSort.by}"] .sort-icon`);
                if (activeSortIcon) {
                    activeSortIcon.textContent = currentSort.order === 'asc' ? 'arrow_upward' : 'arrow_downward';
                }
            }
            
            // Initial load
            loadUsersAndRefreshTable(currentPage, currentSearchTerm, currentFilters, currentSort);

            function updatePaginationControls(totalItems, perPage, page) {
                // Placeholder for pagination UI update logic
                console.log(`Pagination: Total=${totalItems}, PerPage=${perPage}, CurrentPage=${page}`);
                const paginationTotal = document.getElementById('pagination-total-items');
                const paginationPageInfo = document.getElementById('pagination-page-info');
                const prevButton = document.getElementById('pagination-prev');
                const nextButton = document.getElementById('pagination-next');

                if (!paginationTotal || !paginationPageInfo || !prevButton || !nextButton) return;
                const totalPages = Math.ceil(totalItems / perPage) || 1;
                paginationTotal.textContent = `共 ${totalItems} 项`;
                paginationPageInfo.textContent = `${page} / ${totalPages}`;

                prevButton.disabled = page <= 1;
                nextButton.disabled = page >= totalPages;

                // Remove old listeners to prevent multiple calls if this is called repeatedly
                const newPrevButton = prevButton.cloneNode(true);
                prevButton.parentNode.replaceChild(newPrevButton, prevButton);
                if (!newPrevButton.disabled) {
                    newPrevButton.addEventListener('click', () => loadUsersAndRefreshTable(currentPage - 1, currentSearchTerm, currentFilters, currentSort));
                }
                
                const newNextButton = nextButton.cloneNode(true);
                nextButton.parentNode.replaceChild(newNextButton, nextButton);
                if (!newNextButton.disabled) {
                    newNextButton.addEventListener('click', () => loadUsersAndRefreshTable(currentPage + 1, currentSearchTerm, currentFilters, currentSort));
                }
            }

            function drawPermissionGraph(roleId, containerId = 'permission-inheritance-viz') {
                const graphContainer = d3.select("#" + containerId);
                graphContainer.html('<p>正在加载权限继承关系图...</p>'); // Clear previous and show loading

                fetch(`/api/roles/${roleId}/inheritance-graph`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status} - ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        graphContainer.html(""); // Clear loading message
                        if (!data || !data.nodes || data.nodes.length === 0) {
                            graphContainer.html('<p>没有可显示的权限继承关系。</p>');
                            return;
                        }

                        const nodes = data.nodes;
                        const links = data.links;

                        const containerRect = graphContainer.node().getBoundingClientRect();
                        const width = containerRect.width || 600;
                        const height = containerRect.height || 300; 

                        const svg = graphContainer.append("svg")
                            .attr("width", width)
                            .attr("height", height)
                            .attr("viewBox", [0, 0, width, height])
                            .attr("style", "max-width: 100%; height: auto;");

                        svg.append("defs").selectAll("marker")
                            .data(["arrow"])
                            .join("marker")
                            .attr("id", String)
                            .attr("viewBox", "0 -5 10 10")
                            .attr("refX", 15)
                            .attr("refY", 0)
                            .attr("markerWidth", 6)
                            .attr("markerHeight", 6)
                            .attr("orient", "auto")
                            .append("path")
                            .attr("fill", "#999")
                            .attr("d", "M0,-5L10,0L0,5");

                        const simulation = d3.forceSimulation(nodes)
                            .force("link", d3.forceLink(links).id(d => d.id).distance(100))
                            .force("charge", d3.forceManyBody().strength(-300))
                            .force("center", d3.forceCenter(width / 2, height / 2))
                            .force("x", d3.forceX())
                            .force("y", d3.forceY());

                        const link = svg.append("g")
                            .attr("stroke", "#999")
                            .attr("stroke-opacity", 0.6)
                            .selectAll("line")
                            .data(links)
                            .join("line")
                            .attr("stroke-width", d => Math.sqrt(d.value || 1))
                            .attr("marker-end", "url(#arrow)");

                        const node = svg.append("g")
                            .attr("stroke", "#fff")
                            .attr("stroke-width", 1.5)
                            .selectAll("g")
                            .data(nodes)
                            .join("g");
                        
                        node.append("circle")
                            .attr("r", 10)
                            .attr("fill", d => d.id === roleId ? "#ff6347" : (d.is_system_role ? "#777" : "#1f77b4"))
                            .call(drag(simulation));

                        node.append("text")
                            .attr("x", 12)
                            .attr("y", "0.31em")
                            .text(d => d.name)
                            .clone(true).lower()
                            .attr("fill", "none")
                            .attr("stroke", "white")
                            .attr("stroke-width", 3);
                        
                        node.append("title")
                             .text(d => `${d.name}${d.description ? '\\n' + d.description : ''}`);

                        simulation.on("tick", () => {
                            link
                                .attr("x1", d => d.source.x)
                                .attr("y1", d => d.source.y)
                                .attr("x2", d => d.target.x)
                                .attr("y2", d => d.target.y);

                            node
                                .attr("transform", d => `translate(${d.x},${d.y})`);
                        });

                        function drag(simulation) {
                            function dragstarted(event, d) {
                                if (!event.active) simulation.alphaTarget(0.3).restart();
                                d.fx = d.x;
                                d.fy = d.y;
                            }
                            function dragged(event, d) {
                                d.fx = event.x;
                                d.fy = event.y;
                            }
                            function dragended(event, d) {
                                if (!event.active) simulation.alphaTarget(0);
                                d.fx = null;
                                d.fy = null;
                            }
                            return d3.drag()
                                .on("start", dragstarted)
                                .on("drag", dragged)
                                .on("end", dragended);
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching or drawing permission graph:', error);
                        graphContainer.html(`<p style=\"color:red;\">无法加载权限继承图：${error.message}</p>`);
                    });
            }

            // Initial load and sort icon update, now after all function definitions and event listeners
            document.addEventListener('DOMContentLoaded', () => {
                // Ensure all MDC components that might have been added dynamically by Jinja2 or other JS are initialized.
                mdc.autoInit(); 
                
                loadUsersAndRefreshTable(currentPage, currentSearchTerm, currentFilters, currentSort);
                updateSortIcons();
            });
        });
    </script>

    <aside class="mdc-snackbar">
      <div class="mdc-snackbar__surface" role="status" aria-live="polite">
        <div class="mdc-snackbar__label" aria-atomic="false"></div>
        <div class="mdc-snackbar__actions" aria-atomic="true">
          <button type="button" class="mdc-button mdc-snackbar__action">
            <div class="mdc-button__ripple"></div>
            <span class="mdc-button__label">关闭</span>
          </button>
        </div>
      </div>
    </aside>
</body>
</html> 