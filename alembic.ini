[alembic]
# path to migration scripts
script_location = alembic
# ... existing code ...
# for remote databases, the tagged version number is used to prevent
# simultaneous upgrades from happening.
# revision_environment = false

# Set to true if you want to log to stdout in addition to the logger.
# log_to_stdout = false


# -dd will cause alembic to profile queries using cProfile and output
# the profile data to the file alembic_profiling.sql.
# output_encoding = utf-8

sqlalchemy.url = %%(DATABASE_URL)s

# ... existing code ...

[post_write_hooks]
# ... post write hooks...

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S 