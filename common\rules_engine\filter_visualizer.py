#!/usr/bin/env python3
"""
筛选条件可视化工具

提供筛选条件的可视化工具，支持条件关系图的生成和展示
"""

import json
import logging
from typing import Dict, List, Any, Optional, Set, Tuple, Union

from .filter_engine import FilterGroup, FilterCondition, FilterLogic, FilterOperator

logger = logging.getLogger(__name__)

class FilterVisualizer:
    """筛选条件可视化类"""
    
    def __init__(self, filter_data: Dict[str, Any]):
        """
        初始化过滤条件可视化器
        
        Args:
            filter_data: 过滤条件数据
        """
        self.filter_data = filter_data
        self.operator_map = {
            "eq": "等于",
            "neq": "不等于",
            "gt": "大于",
            "gte": "大于等于",
            "lt": "小于",
            "lte": "小于等于",
            "contains": "包含",
            "not_contains": "不包含",
            "starts_with": "开头是",
            "ends_with": "结尾是",
            "in": "在列表中",
            "not_in": "不在列表中",
            "between": "在范围内",
            "not_between": "不在范围内",
            "regex": "匹配正则",
            "and": "并且",
            "or": "或者",
            "not": "非"
        }
        
    def _get_operator_display(self, op: str) -> str:
        """获取操作符的显示文本"""
        return self.operator_map.get(op, op)
    
    def _format_value(self, value: Any) -> str:
        """格式化条件值为可显示的文本"""
        if value is None:
            return "空"
        elif isinstance(value, bool):
            return "是" if value else "否"
        elif isinstance(value, (list, tuple)):
            if not value:
                return "[]"
            if len(value) > 3:
                return f"[{value[0]}, {value[1]}, ... 共{len(value)}项]"
            return str(value)
        else:
            return str(value)
    
    def _parse_condition(self, condition: Dict[str, Any], parent_op: str = None) -> str:
        """
        解析单个过滤条件为文本描述
        
        Args:
            condition: 过滤条件
            parent_op: 父级操作符
        
        Returns:
            条件的文本描述
        """
        if not condition:
            return "空条件"
        
        if "field" in condition and "operator" in condition:
            # 这是一个基本条件
            field = condition["field"]
            operator = self._get_operator_display(condition["operator"])
            value = self._format_value(condition.get("value"))
            return f"{field} {operator} {value}"
            
        elif "operator" in condition and "conditions" in condition:
            # 这是一个组合条件
            op = condition["operator"]
            op_display = self._get_operator_display(op)
            sub_conditions = condition["conditions"]
            
            if not sub_conditions:
                return f"空{op_display}组"
            
            # 解析子条件
            parsed_conditions = []
            for sub in sub_conditions:
                parsed = self._parse_condition(sub, op)
                parsed_conditions.append(parsed)
            
            # 根据操作符组合子条件
            if op == "not" and len(parsed_conditions) == 1:
                result = f"非({parsed_conditions[0]})"
            else:
                separator = f" {op_display} "
                result = separator.join(parsed_conditions)
                
                # 如果父操作符存在且不同于当前操作符，添加括号
                if parent_op and parent_op != op:
                    result = f"({result})"
            
            return result
        
        else:
            # 无法识别的条件
            return "无效条件"
    
    def generate_filter_summary(self) -> str:
        """
        生成过滤条件的文本摘要
        
        Returns:
            条件的文本摘要
        """
        try:
            return self._parse_condition(self.filter_data)
        except Exception as e:
            return f"无法解析过滤条件: {str(e)}"
    
    def _build_mermaid_nodes(self, 
                            condition: Dict[str, Any], 
                            parent_id: Optional[str] = None,
                            node_counter: List[int] = None) -> Tuple[List[str], List[str]]:
        """
        构建Mermaid流程图的节点和连接
        
        Args:
            condition: 过滤条件
            parent_id: 父节点ID
            node_counter: 节点计数器
        
        Returns:
            节点定义列表和连接定义列表
        """
        if node_counter is None:
            node_counter = [0]
            
        current_id = f"node{node_counter[0]}"
        node_counter[0] += 1
        
        nodes = []
        links = []
        
        # 如果有父节点，添加连接
        if parent_id:
            links.append(f"{parent_id} --> {current_id}")
        
        if "field" in condition and "operator" in condition:
            # 基本条件
            field = condition["field"]
            operator = self._get_operator_display(condition["operator"])
            value = self._format_value(condition.get("value"))
            label = f"{field} {operator} {value}"
            nodes.append(f"{current_id}[{label}]")
            
        elif "operator" in condition and "conditions" in condition:
            # 组合条件
            op = condition["operator"]
            op_display = self._get_operator_display(op)
            nodes.append(f"{current_id}{{条件组: {op_display}}}")
            
            # 处理子条件
            for sub_condition in condition["conditions"]:
                sub_nodes, sub_links = self._build_mermaid_nodes(
                    sub_condition, current_id, node_counter
                )
                nodes.extend(sub_nodes)
                links.extend(sub_links)
        
        else:
            # 无法识别的条件
            nodes.append(f"{current_id}[无效条件]")
        
        return nodes, links
    
    def generate_mermaid_diagram(self) -> str:
        """
        生成Mermaid格式的流程图
        
        Returns:
            Mermaid格式的流程图代码
        """
        try:
            nodes, links = self._build_mermaid_nodes(self.filter_data)
            
            mermaid_code = [
                "graph TD",
                *["    " + node for node in nodes],
                *["    " + link for link in links]
            ]
            
            return "\n".join(mermaid_code)
        except Exception as e:
            return f"graph TD\n    error[生成流程图出错: {str(e)}]"
    
    def _build_d3_nodes(self, 
                       condition: Dict[str, Any], 
                       parent_id: Optional[str] = None,
                       node_counter: List[int] = None) -> Tuple[List[Dict], List[Dict]]:
        """
        构建D3.js可视化的节点和连接
        
        Args:
            condition: 过滤条件
            parent_id: 父节点ID
            node_counter: 节点计数器
        
        Returns:
            节点列表和连接列表
        """
        if node_counter is None:
            node_counter = [0]
            
        current_id = f"node{node_counter[0]}"
        node_counter[0] += 1
        
        nodes = []
        links = []
        
        # 如果有父节点，添加连接
        if parent_id is not None:
            links.append({
                "source": parent_id,
                "target": current_id
            })
        
        if "field" in condition and "operator" in condition:
            # 基本条件
            field = condition["field"]
            operator = self._get_operator_display(condition["operator"])
            value = self._format_value(condition.get("value"))
            nodes.append({
                "id": current_id,
                "type": "condition",
                "label": f"{field} {operator} {value}",
                "details": {
                    "field": field,
                    "operator": condition["operator"],
                    "value": condition.get("value")
                }
            })
            
        elif "operator" in condition and "conditions" in condition:
            # 组合条件
            op = condition["operator"]
            nodes.append({
                "id": current_id,
                "type": "group",
                "label": self._get_operator_display(op),
                "details": {
                    "operator": op
                }
            })
            
            # 处理子条件
            for sub_condition in condition["conditions"]:
                sub_nodes, sub_links = self._build_d3_nodes(
                    sub_condition, current_id, node_counter
                )
                nodes.extend(sub_nodes)
                links.extend(sub_links)
        
        else:
            # 无法识别的条件
            nodes.append({
                "id": current_id,
                "type": "error",
                "label": "无效条件",
                "details": {}
            })
        
        return nodes, links
    
    def generate_d3_json(self) -> Dict[str, List[Dict]]:
        """
        生成用于D3.js可视化的JSON数据
        
        Returns:
            D3.js可视化数据
        """
        try:
            nodes, links = self._build_d3_nodes(self.filter_data)
            return {"nodes": nodes, "links": links}
        except Exception as e:
            return {
                "nodes": [{"id": "error", "type": "error", "label": f"生成可视化出错: {str(e)}"}],
                "links": []
            }
    
    def _build_graphviz_nodes(self, 
                            condition: Dict[str, Any], 
                            parent_id: Optional[str] = None,
                            node_counter: List[int] = None) -> Tuple[List[str], List[str]]:
        """
        构建Graphviz DOT格式的节点和连接
        
        Args:
            condition: 过滤条件
            parent_id: 父节点ID
            node_counter: 节点计数器
        
        Returns:
            节点定义列表和连接定义列表
        """
        if node_counter is None:
            node_counter = [0]
            
        current_id = f"node{node_counter[0]}"
        node_counter[0] += 1
        
        nodes = []
        links = []
        
        # 如果有父节点，添加连接
        if parent_id:
            links.append(f'"{parent_id}" -> "{current_id}";')
        
        if "field" in condition and "operator" in condition:
            # 基本条件
            field = condition["field"]
            operator = self._get_operator_display(condition["operator"])
            value = self._format_value(condition.get("value"))
            label = f"{field} {operator} {value}"
            nodes.append(f'"{current_id}" [label="{label}", shape=box];')
            
        elif "operator" in condition and "conditions" in condition:
            # 组合条件
            op = condition["operator"]
            op_display = self._get_operator_display(op)
            nodes.append(f'"{current_id}" [label="条件组: {op_display}", shape=diamond];')
            
            # 处理子条件
            for sub_condition in condition["conditions"]:
                sub_nodes, sub_links = self._build_graphviz_nodes(
                    sub_condition, current_id, node_counter
                )
                nodes.extend(sub_nodes)
                links.extend(sub_links)
        
        else:
            # 无法识别的条件
            nodes.append(f'"{current_id}" [label="无效条件", shape=box, style=filled, fillcolor=lightgrey];')
        
        return nodes, links
    
    def generate_graphviz_dot(self) -> str:
        """
        生成Graphviz DOT格式的图形描述
        
        Returns:
            Graphviz DOT代码
        """
        try:
            nodes, links = self._build_graphviz_nodes(self.filter_data)
            
            dot_code = [
                "digraph FilterCondition {",
                '    rankdir=TD;',
                '    node [fontname="SimHei", fontsize=12];',
                '    edge [fontname="SimHei", fontsize=10];',
                *["    " + node for node in nodes],
                *["    " + link for link in links],
                "}"
            ]
            
            return "\n".join(dot_code)
        except Exception as e:
            return f'digraph FilterCondition {{\n    error [label="生成图形出错: {str(e)}"];\n}}'
    
    def generate_html_visualization(self) -> str:
        """
        生成包含可视化的HTML代码
        
        Returns:
            HTML代码
        """
        d3_data = json.dumps(self.generate_d3_json())
        
        html = f'''
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>设备过滤条件可视化</title>
            <script src="https://d3js.org/d3.v5.min.js"></script>
            <style>
                body {{ font-family: "Microsoft YaHei", Arial, sans-serif; }}
                .node {{ stroke: #fff; stroke-width: 1.5px; }}
                .link {{ stroke: #999; stroke-opacity: 0.6; }}
                .condition {{ fill: #66c2a5; }}
                .group {{ fill: #fc8d62; }}
                .error {{ fill: #e78ac3; }}
            </style>
        </head>
        <body>
            <h2>设备过滤条件可视化</h2>
            <div id="visualization"></div>
            <script>
                const data = {d3_data};
                
                const width = 800;
                const height = 600;
                
                const svg = d3.select("#visualization")
                    .append("svg")
                    .attr("width", width)
                    .attr("height", height);
                
                const simulation = d3.forceSimulation(data.nodes)
                    .force("link", d3.forceLink(data.links).id(d => d.id).distance(100))
                    .force("charge", d3.forceManyBody().strength(-300))
                    .force("center", d3.forceCenter(width / 2, height / 2));
                
                const link = svg.append("g")
                    .selectAll("line")
                    .data(data.links)
                    .enter().append("line")
                    .attr("class", "link");
                
                const node = svg.append("g")
                    .selectAll("g")
                    .data(data.nodes)
                    .enter().append("g")
                    .attr("class", "node")
                    .call(d3.drag()
                        .on("start", dragstarted)
                        .on("drag", dragged)
                        .on("end", dragended));
                
                node.append("circle")
                    .attr("r", d => d.type === "group" ? 15 : 10)
                    .attr("class", d => d.type);
                
                node.append("text")
                    .attr("dy", -15)
                    .text(d => d.label)
                    .attr("text-anchor", "middle");
                
                simulation.on("tick", () => {{
                    link
                        .attr("x1", d => d.source.x)
                        .attr("y1", d => d.source.y)
                        .attr("x2", d => d.target.x)
                        .attr("y2", d => d.target.y);
                    
                    node.attr("transform", d => `translate(${{d.x}},${{d.y}})`);
                }});
                
                function dragstarted(d) {{
                    if (!d3.event.active) simulation.alphaTarget(0.3).restart();
                    d.fx = d.x;
                    d.fy = d.y;
                }}
                
                function dragged(d) {{
                    d.fx = d3.event.x;
                    d.fy = d3.event.y;
                }}
                
                function dragended(d) {{
                    if (!d3.event.active) simulation.alphaTarget(0);
                    d.fx = null;
                    d.fy = null;
                }}
            </script>
        </body>
        </html>
        '''
        
        return html

# 添加到FilterEngine类，提供可视化方法
class FilterVisualProvider:
    """筛选可视化提供者"""
    
    def __init__(self):
        """初始化筛选可视化提供者"""
        self.visualizer = FilterVisualizer()
    
    def get_mermaid_diagram(self, filter_group: FilterGroup) -> str:
        """
        获取Mermaid格式的条件关系图
        
        参数:
            filter_group: 筛选条件组
            
        返回:
            str: Mermaid格式的图表代码
        """
        return self.visualizer.generate_mermaid_diagram()
    
    def get_d3_json(self, filter_group: FilterGroup) -> Dict[str, Any]:
        """
        获取D3.js格式的条件关系数据
        
        参数:
            filter_group: 筛选条件组
            
        返回:
            Dict[str, Any]: D3.js格式的图表数据
        """
        return self.visualizer.generate_d3_json()
    
    def get_graphviz_dot(self, filter_group: FilterGroup) -> str:
        """
        获取Graphviz DOT格式的条件关系图
        
        参数:
            filter_group: 筛选条件组
            
        返回:
            str: Graphviz DOT格式的图表代码
        """
        return self.visualizer.generate_graphviz_dot()
    
    def get_filter_summary(self, filter_group: FilterGroup) -> str:
        """
        获取筛选条件的自然语言摘要
        
        参数:
            filter_group: 筛选条件组
            
        返回:
            str: 自然语言摘要
        """
        return self.visualizer.generate_filter_summary()
    
    def get_html_visualization(self, filter_group: FilterGroup) -> str:
        """
        获取HTML格式的条件关系可视化代码
        
        参数:
            filter_group: 筛选条件组
            
        返回:
            str: HTML代码
        """
        return self.visualizer.generate_html_visualization() 