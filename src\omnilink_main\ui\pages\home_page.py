from nicegui import ui
from .. import auth_client
import asyncio
from typing import Dict, Any

async def get_system_stats() -> Dict[str, Any]:
    """获取系统统计数据"""
    try:
        # 调用后端API获取系统监控数据
        return {
            "success": True,
            "data": {
                "total_users": 15,
                "active_users": 12,
                "total_devices": 28,
                "online_devices": 18,
                "connected_slaves": 5,
                "total_slaves": 6,
                "system_health": "优秀",
                "cpu_usage": 25.6,
                "memory_usage": 68.2,
                "disk_usage": 45.8,
                "active_connections": 3,  # 当前活跃的设备连接数
                "today_connections": 25   # 今日总连接数
            }
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def create() -> None:
    """创建Web管理界面主页面 - 专注于系统管理和监控"""
    
    # 页面标题
    with ui.row().classes('w-full items-center justify-between mb-6'):
        ui.label('OmniLink 系统管理控制台').classes('text-3xl font-bold text-primary')
        with ui.row().classes('items-center gap-2'):
            ui.icon('refresh').classes('text-xl cursor-pointer hover:text-primary')
            ui.label('最后更新: 刚刚').classes('text-sm text-gray-500')

    # 系统状态概览卡片
    with ui.row().classes('w-full gap-4 mb-6'):
        # 用户统计
        with ui.card().classes('flex-1'):
            with ui.card_section().classes('p-6'):
                with ui.row().classes('items-center justify-between'):
                    with ui.column():
                        ui.label('用户管理').classes('text-sm text-gray-600')
                        total_users_label = ui.label('15').classes('text-3xl font-bold text-blue-600')
                        ui.label('活跃用户: 12').classes('text-sm text-gray-500')
                    ui.icon('people').classes('text-4xl text-blue-500')

        # 设备统计
        with ui.card().classes('flex-1'):
            with ui.card_section().classes('p-6'):
                with ui.row().classes('items-center justify-between'):
                    with ui.column():
                        ui.label('设备总览').classes('text-sm text-gray-600')
                        total_devices_label = ui.label('28').classes('text-3xl font-bold text-green-600')
                        ui.label('在线设备: 18').classes('text-sm text-gray-500')
                    ui.icon('devices').classes('text-4xl text-green-500')

        # 从服务器状态
        with ui.card().classes('flex-1'):
            with ui.card_section().classes('p-6'):
                with ui.row().classes('items-center justify-between'):
                    with ui.column():
                        ui.label('从服务器').classes('text-sm text-gray-600')
                        connected_slaves_label = ui.label('5/6').classes('text-3xl font-bold text-purple-600')
                        ui.label('在线状态').classes('text-sm text-gray-500')
                    ui.icon('dns').classes('text-4xl text-purple-500')

        # 连接活动
        with ui.card().classes('flex-1'):
            with ui.card_section().classes('p-6'):
                with ui.row().classes('items-center justify-between'):
                    with ui.column():
                        ui.label('连接活动').classes('text-sm text-gray-600')
                        active_connections_label = ui.label('3').classes('text-3xl font-bold text-orange-600')
                        ui.label('今日连接: 25').classes('text-sm text-gray-500')
                    ui.icon('link').classes('text-4xl text-orange-500')

    # 主要内容区域
    with ui.row().classes('w-full gap-6'):
        # 左侧列 - 系统监控
        with ui.column().classes('flex-2'):
            # 系统资源监控
            with ui.card().classes('w-full mb-4'):
                with ui.card_section():
                    ui.label('系统资源监控').classes('text-xl font-semibold mb-4')
                    
                    with ui.row().classes('w-full items-center mb-3'):
                        ui.label('CPU').classes('w-16')
                        cpu_progress = ui.linear_progress(value=0.256).classes('flex-1')
                        cpu_label = ui.label('25.6%').classes('w-16 text-right')
                    
                    with ui.row().classes('w-full items-center mb-3'):
                        ui.label('内存').classes('w-16')
                        memory_progress = ui.linear_progress(value=0.682).classes('flex-1')
                        memory_label = ui.label('68.2%').classes('w-16 text-right')
                    
                    with ui.row().classes('w-full items-center'):
                        ui.label('磁盘').classes('w-16')
                        disk_progress = ui.linear_progress(value=0.458).classes('flex-1')
                        disk_label = ui.label('45.8%').classes('w-16 text-right')

            # 设备管理概览（仅显示，不提供连接功能）
            with ui.card().classes('w-full'):
                with ui.card_section():
                    ui.label('设备管理概览').classes('text-xl font-semibold mb-4')
                    
                    # 设备状态表格（管理视角）
                    device_columns = [
                        {'name': 'name', 'label': '设备名称', 'field': 'name', 'required': True, 'align': 'left'},
                        {'name': 'type', 'label': '设备类型', 'field': 'type', 'align': 'center'},
                        {'name': 'slave', 'label': '所属从服务器', 'field': 'slave', 'align': 'center'},
                        {'name': 'user', 'label': '当前用户', 'field': 'user', 'align': 'center'},
                        {'name': 'status', 'label': '状态', 'field': 'status', 'align': 'center'},
                        {'name': 'actions', 'label': '管理操作', 'field': 'actions', 'align': 'center'}
                    ]
                    
                    device_rows = [
                        {
                            'name': 'USB摄像头-001', 
                            'type': '摄像头', 
                            'slave': 'Slave-01', 
                            'user': '张三', 
                            'status': '使用中',
                            'actions': '强制断开'
                        },
                        {
                            'name': 'USB打印机-002', 
                            'type': '打印机', 
                            'slave': 'Slave-02', 
                            'user': '-', 
                            'status': '空闲',
                            'actions': '设备配置'
                        },
                        {
                            'name': 'USB存储设备-003', 
                            'type': '存储', 
                            'slave': 'Slave-01', 
                            'user': '李四', 
                            'status': '使用中',
                            'actions': '强制断开'
                        },
                        {
                            'name': 'USB键盘-004', 
                            'type': '输入设备', 
                            'slave': 'Slave-03', 
                            'user': '-', 
                            'status': '离线',
                            'actions': '故障排查'
                        }
                    ]
                    
                    ui.table(columns=device_columns, rows=device_rows, row_key='name').classes('w-full')

        # 右侧列 - 管理功能
        with ui.column().classes('flex-1'):
            # 系统管理功能
            with ui.card().classes('w-full mb-4'):
                with ui.card_section():
                    ui.label('系统管理').classes('text-xl font-semibold mb-4')
                    
                    with ui.column().classes('w-full gap-2'):
                        ui.button('用户管理', icon='people').classes('w-full')
                        ui.button('设备管理', icon='devices').classes('w-full')
                        ui.button('从服务器管理', icon='dns').classes('w-full')
                        ui.button('权限配置', icon='security').classes('w-full')
                        ui.button('系统设置', icon='settings').classes('w-full')

            # 客户端下载
            with ui.card().classes('w-full mb-4'):
                with ui.card_section():
                    ui.label('客户端下载').classes('text-xl font-semibold mb-4')
                    
                    with ui.column().classes('w-full gap-3'):
                        ui.label('用户需要下载专用客户端来连接和使用USB设备').classes('text-sm text-gray-600 mb-2')
                        
                        with ui.row().classes('w-full items-center gap-2'):
                            ui.icon('download').classes('text-blue-500')
                            ui.button('下载Windows客户端', color='primary').classes('flex-1')
                        
                        with ui.row().classes('w-full items-center gap-2'):
                            ui.icon('info').classes('text-gray-500')
                            ui.label('版本: v1.0.0 | 更新时间: 2024-01-15').classes('text-xs text-gray-500')

            # 系统通知
            with ui.card().classes('w-full'):
                with ui.card_section():
                    ui.label('系统通知').classes('text-xl font-semibold mb-4')
                    
                    with ui.column().classes('w-full gap-3'):
                        with ui.row().classes('w-full items-start gap-3 p-2 border-l-4 border-blue-500 bg-blue-50'):
                            ui.icon('info').classes('text-blue-500')
                            with ui.column().classes('flex-1'):
                                ui.label('客户端更新').classes('font-semibold')
                                ui.label('新版本客户端 v1.0.1 已发布').classes('text-sm text-gray-600')
                                ui.label('2分钟前').classes('text-xs text-gray-400')
                        
                        with ui.row().classes('w-full items-start gap-3 p-2 border-l-4 border-green-500 bg-green-50'):
                            ui.icon('check_circle').classes('text-green-500')
                            with ui.column().classes('flex-1'):
                                ui.label('从服务器上线').classes('font-semibold')
                                ui.label('Slave-05 已重新连接').classes('text-sm text-gray-600')
                                ui.label('5分钟前').classes('text-xs text-gray-400')
                        
                        with ui.row().classes('w-full items-start gap-3 p-2 border-l-4 border-orange-500 bg-orange-50'):
                            ui.icon('warning').classes('text-orange-500')
                            with ui.column().classes('flex-1'):
                                ui.label('设备连接异常').classes('font-semibold')
                                ui.label('用户张三的USB摄像头连接超时').classes('text-sm text-gray-600')
                                ui.label('10分钟前').classes('text-xs text-gray-400')

    # 自动刷新系统数据
    async def update_dashboard():
        """更新仪表板数据"""
        stats = await get_system_stats()
        if stats.get("success"):
            data = stats["data"]
            total_users_label.set_text(str(data["total_users"]))
            total_devices_label.set_text(str(data["total_devices"]))
            connected_slaves_label.set_text(f"{data['connected_slaves']}/{data['total_slaves']}")
            active_connections_label.set_text(str(data["active_connections"]))

    ui.timer(30.0, update_dashboard)
