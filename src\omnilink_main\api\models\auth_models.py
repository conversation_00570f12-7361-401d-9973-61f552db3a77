from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: str | None = None

class TokenResponse(Token):
    user_id: int
    username: str
    full_name: Optional[str] = None
    role: str # 添加角色信息
    session_salt: str = Field(..., description="Base64 encoded session salt tied to this login session") # 新增会话盐字段
    expires_in: int # Add token expiration time in seconds

class LoginRequest(BaseModel):
    username: str
    password: str

# --- 新增用户和角色相关模型 ---

class RoleBasic(BaseModel):
    """角色的基本信息"""
    id: int
    name: str
    code: str # 角色的唯一编码, e.g., 'admin', 'editor'

    class Config:
        from_attributes = True

class RoleResponse(RoleBasic):
    """角色响应模型，可包含更多详情"""
    description: Optional[str] = None
    permissions: List[str] = Field([], description="此角色拥有的权限代码列表")

class UserBasic(BaseModel):
    """用户的基本信息"""
    id: int
    username: str
    full_name: Optional[str] = None
    email: Optional[str] = None

    class Config:
        from_attributes = True

class UserResponse(UserBasic):
    """用户响应模型"""
    is_active: bool
    roles: List[RoleBasic] = Field([], description="用户拥有的角色列表")
    created_at: datetime
    last_login: Optional[datetime] = None
    # 可根据需要添加 company, department 等信息

class UserRoleAssignmentRequest(BaseModel):
    """用户角色分配请求模型"""
    role_id: int = Field(..., description="要分配给用户的角色ID")

class RoleCreateRequest(BaseModel):
    name: str = Field(..., min_length=2, max_length=50)
    code: str = Field(..., min_length=2, max_length=50, description="角色的唯一编码，例如: 'data_viewer'")
    description: Optional[str] = Field(None, max_length=255)
    permissions: List[str] = Field([], description="分配给此角色的权限代码列表")

class RoleUpdateRequest(BaseModel):
    name: Optional[str] = Field(None, min_length=2, max_length=50)
    description: Optional[str] = Field(None, max_length=255)
    permissions: Optional[List[str]] = Field(None, description="完整的权限代码列表以替换现有权限")

class UserSession(BaseModel):
    # ... other fields
    class Config:
        from_attributes = True

class LoginHistory(BaseModel):
    # ... existing code ...
    user_agent: Optional[str] = None

    class Config:
        from_attributes = True 