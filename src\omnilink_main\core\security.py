from datetime import datetime, timedelta
from typing import Any, Dict, Optional
import uuid

from jose import jwt, J<PERSON><PERSON><PERSON>r
from passlib.context import Crypt<PERSON>ontext
from src.omnilink_main.core.config import settings

# --- Password Hashing Setup ---
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verifies a plain password against a hashed one."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hashes a plain password."""
    return pwd_context.hash(password)


# --- JWT Token Creation ---
def create_access_token(
    subject: Any, expires_delta: Optional[timedelta] = None
) -> str:
    """
    Creates a new JWT access token with a JTI claim.
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode: Dict[str, Any] = {
        "exp": expire, 
        "sub": str(subject),
        "jti": str(uuid.uuid4()), # JWT ID for blacklisting
        "type": "access"
    }
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt

def create_refresh_token(
    subject: Any, expires_delta: Optional[timedelta] = None
) -> str:
    """
    Creates a new JWT refresh token.
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            days=settings.REFRESH_TOKEN_EXPIRE_DAYS
        )
    
    to_encode: Dict[str, Any] = {
        "exp": expire, 
        "sub": str(subject),
        "jti": str(uuid.uuid4()), # Refresh tokens can also be blacklisted
        "type": "refresh"
    }
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt

def decode_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Decodes a JWT token and returns its payload.
    Returns None if the token is invalid or expired.
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
        )
        return payload
    except JWTError:
        return None
