#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证服务模块

提供用户认证、授权、会话管理和权限控制功能。
"""

import os
import ssl
import time
import jwt
import logging
import asyncio
import aiohttp
import json
import smtplib
import secrets
import string
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Optional, Tuple, Any, List, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend
from common.models.user import User
from cryptography.x509 import Name, NameAttribute, CertificateBuilder
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography import x509
import ipaddress
import bcrypt
from enum import Enum
import hashlib
from cryptography.x509.oid import NameOID

logger = logging.getLogger(__name__)

class UserRole(Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"
    OPERATOR = "operator"

class Permission(Enum):
    """权限枚举"""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    ADMIN = "admin"
    DEVICE_MANAGE = "device_manage"
    USER_MANAGE = "user_manage"
    SYSTEM_CONFIG = "system_config"

@dataclass
class AuthConfig:
    """认证配置"""
    port: int = 65520
    jwt_secret: str = ""
    jwt_algorithm: str = "HS256"
    jwt_expire: int = 3600
    tls_cert: str = ""
    tls_key: str = ""
    allow_http: bool = True
    session_timeout: int = 3600
    max_retries: int = 3
    retry_interval: int = 5
    max_login_attempts: int = 5
    login_lockout_duration: int = 900
    email_notifications_enabled: bool = False
    email_smtp_server: str = ""
    email_smtp_port: int = 587
    email_use_tls: bool = True
    email_username: str = ""
    email_password: str = ""
    email_sender: str = ""
    email_admin_receivers: List[str] = field(default_factory=list)
    verification_code_length: int = 6
    verification_code_expire: int = 300  # 5分钟
    # Slave token specific configuration
    slave_jwt_secret: str = ""
    slave_jwt_algorithm: str = "HS256"
    slave_jwt_expire_minutes: int = 10080  # 7 days

@dataclass
class User:
    """用户数据类"""
    id: int
    username: str
    email: str
    role: UserRole
    permissions: List[Permission]
    is_active: bool = True
    created_at: Optional[datetime] = None
    last_login: Optional[datetime] = None

@dataclass
class Session:
    """会话数据类"""
    session_id: str
    user_id: int
    created_at: datetime
    expires_at: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    is_active: bool = True

class AuthService:
    """认证服务类"""
    
    def __init__(self, config: AuthConfig, cache_service: Optional[Any] = None, audit_service: Optional[Any] = None):
        """初始化认证服务
        
        Args:
            config: 认证配置
            cache_service: 缓存服务实例
            audit_service: 审计服务实例
        """
        logger.info("正在初始化认证服务...")
        
        self.config = config
        self.cache_service = cache_service
        self.audit_service = audit_service

        # 生成JWT密钥
        if not config.jwt_secret:
            self.config.jwt_secret = secrets.token_hex(32)
            logger.warning("未提供JWT密钥，已生成随机密钥。请注意，服务重启后令牌将失效。")
            
        # 生成TLS证书和私钥
        if not config.tls_cert or not config.tls_key:
            logger.warning("未提供TLS证书，将生成自签名证书。")
            self._generate_self_signed_cert()
            
        # SSL上下文
        self.ssl_context = self._create_ssl_context()
        
        # 会话存储
        self._sessions: Dict[str, Dict] = {}
        
        # 运行状态
        self._running = False
        self._server = None
        self._cleanup_task = None
        
        # 内存存储（生产环境应使用数据库）
        self._users: Dict[int, User] = {}
        self._user_passwords: Dict[int, str] = {}  # 存储密码哈希
        self._username_to_id: Dict[str, int] = {}
        self._next_user_id = 1
        
        # 创建默认管理员用户
        self._create_default_admin()
        
        logger.info("认证服务初始化完成。")
        
    async def start(self):
        """启动服务"""
        if self._running:
            logger.warning("认证服务已在运行中")
            return
            
        try:
            self._running = True
            
            # 创建服务器
            self._server = await asyncio.start_server(
                self._handle_connection,
                host='0.0.0.0',
                port=self.config.port,
                ssl=self.ssl_context if not self.config.allow_http else None
            )
            
            # 启动清理任务
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            
            logger.info(f"认证服务已启动 - 端口: {self.config.port}")
            
        except Exception as e:
            logger.error(f"启动认证服务失败: {e}")
            self._running = False
            raise
        
    async def stop(self):
        """停止服务"""
        if not self._running:
            logger.warning("认证服务未在运行")
            return
            
        try:
            self._running = False
            
            # 停止服务器
            if self._server:
                self._server.close()
                await self._server.wait_closed()
                self._server = None
                
            # 停止清理任务
            if self._cleanup_task:
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass
                self._cleanup_task = None
                
            logger.info("认证服务已停止")
            
        except Exception as e:
            logger.error(f"停止认证服务失败: {e}")
        
    def _load_email_config(self):
        """
        DEPRECATED: This method is no longer used. 
        Email configuration should be passed directly to the AuthConfig object upon initialization.
        The service itself should not be responsible for loading configuration files.
        """
        logger.warning("AuthService._load_email_config is deprecated and should not be used.")
        # The configuration is now directly available in self.config
        self.email_config = {
            "smtp_host": self.config.email_smtp_server,
            "smtp_port": self.config.email_smtp_port,
            "smtp_user": self.config.email_username,
            "smtp_password": self.config.email_password,
            "sender_email": self.config.email_sender,
            "use_tls": self.config.email_use_tls,
        }
        
        # 验证配置完整性
        if not all([self.email_config["smtp_host"],
                    self.email_config["smtp_port"],
                    self.email_config["sender_email"]]):
            logger.warning("邮件服务配置不完整，邮件发送功能可能不可用。")

    def _generate_verification_code(self) -> str:
        """生成验证码"""
        try:
            # 生成指定长度的数字验证码
            digits = string.digits
            code = ''.join(secrets.choice(digits) for _ in range(self.config.verification_code_length))
            return code
        except Exception as e:
            logger.error(f"生成验证码失败: {e}")
            # 返回默认验证码
            return "123456"

    async def send_verification_code_to_user(self, username: str, method: str = 'email') -> Tuple[bool, Optional[str]]:
        """向用户发送验证码
        
        Args:
            username: 用户名或邮箱地址
            method: 发送方式 ('email', 'sms')
            
        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 消息或错误描述)
        """
        try:
            if not username:
                return False, "用户名或目标地址不能为空"

            # 生成验证码
            code = self._generate_verification_code()
            
            # 存储验证码到缓存
            if self.cache_service:
                cache_key = f"verify_code:{method}:{username}"
                try:
                    await self.cache_service.set(
                        cache_key, 
                        code, 
                        expire=self.config.verification_code_expire
                    )
                    logger.info(f"验证码已存储到缓存: {cache_key}")
                except Exception as e:
                    logger.error(f"存储验证码到缓存失败: {e}")
                    return False, "验证码存储失败"
            else:
                logger.warning("缓存服务不可用，验证码功能受限")
                return False, "验证码服务不可用"

            # 根据方法发送验证码
            if method == 'email':
                success, message = await self._send_verification_code_by_email(username, code)
            elif method == 'sms':
                success, message = await self._send_verification_code_by_sms(username, code)
            else:
                return False, f"不支持的发送方式: {method}"

            if success:
                logger.info(f"验证码已发送给用户 {username} (方式: {method})")
                return True, "验证码发送成功"
            else:
                return False, message or "验证码发送失败"
                
        except Exception as e:
            logger.error(f"发送验证码失败: {e}")
            return False, f"发送验证码时发生错误: {e}"

    async def _send_verification_code_by_email(self, email: str, code: str) -> Tuple[bool, Optional[str]]:
        """通过邮件发送验证码"""
        try:
            if not self.email_config:
                return False, "邮件服务未配置"

            subject = "OmniLink 验证码"
            body = f"""
            您好，
            
            您的验证码是: {code}
            
            此验证码将在 {self.config.verification_code_expire // 60} 分钟后过期。
            
            如果您没有请求此验证码，请忽略此邮件。
            
            OmniLink 系统
            """

            success = self._send_email(subject, body, [email])
            if success:
                return True, "邮件发送成功"
            else:
                return False, "邮件发送失败"
                
        except Exception as e:
            logger.error(f"邮件发送验证码失败: {e}")
            return False, f"邮件发送失败: {e}"

    async def _send_verification_code_by_sms(self, phone: str, code: str) -> Tuple[bool, Optional[str]]:
        """通过短信发送验证码"""
        try:
            # 这里应该集成实际的短信服务提供商
            # 例如阿里云短信、腾讯云短信等
            logger.warning("短信发送功能尚未实现")
            return False, "短信发送功能暂不可用"
            
        except Exception as e:
            logger.error(f"短信发送验证码失败: {e}")
            return False, f"短信发送失败: {e}"

    async def verify_user_code(self, username: str, method: str, code: str) -> bool:
        """验证用户提供的验证码
        
        Args:
            username: 用户名
            method: 验证方式
            code: 验证码
            
        Returns:
            bool: 验证是否成功
        """
        try:
            if not self.cache_service:
                logger.error("缓存服务不可用，无法验证验证码")
                return False

            cache_key = f"verify_code:{method}:{username}"
            stored_code = await self.cache_service.get(cache_key)
            
            if not stored_code:
                logger.warning(f"验证码不存在或已过期: {cache_key}")
                return False
                
            if stored_code == code:
                # 验证成功，删除验证码
                await self.cache_service.delete(cache_key)
                logger.info(f"用户 {username} 验证码验证成功")
                return True
            else:
                logger.warning(f"用户 {username} 验证码验证失败")
                return False
                
        except Exception as e:
            logger.error(f"验证验证码失败: {e}")
            return False

    async def authenticate(self,
                          username: str,
                          password: str,
                          device_info: Optional[Dict] = None,
                          source_ip: Optional[str] = None) -> Tuple[bool, Optional[str], Optional[str]]:
        """用户认证
        
        Args:
            username: 用户名
            password: 密码
            device_info: 设备信息
            source_ip: 来源IP
            
        Returns:
            Tuple[bool, Optional[str], Optional[str]]: (是否成功, 令牌, 错误信息)
        """
        try:
            logger.info(f"用户认证请求: {username} from {source_ip}")
            
            # 检查登录尝试次数
            if self.cache_service:
                lockout_key = f"login_attempts:{username}"
                attempts = await self.cache_service.get(lockout_key) or 0
                
                if attempts >= self.config.max_login_attempts:
                    logger.warning(f"用户 {username} 登录尝试次数过多，账户已锁定")
                    return False, None, "账户已锁定，请稍后再试"

            # 这里应该验证用户凭据
            # 实际实现中应该查询数据库验证用户名和密码
            user_valid = await self._validate_user_credentials(username, password)
            
            if user_valid:
                # 清除登录尝试计数
                if self.cache_service:
                    await self.cache_service.delete(f"login_attempts:{username}")
                
                # 生成令牌
                token = self._generate_token(username, device_info)
                
                # 记录审计日志
                if self.audit_service:
                    await self.audit_service.log_event(
                        "user_login",
                        username,
                        {"source_ip": source_ip, "device_info": device_info}
                    )
                
                logger.info(f"用户 {username} 认证成功")
                return True, token, None
            else:
                # 增加登录尝试计数
                if self.cache_service:
                    lockout_key = f"login_attempts:{username}"
                    attempts = await self.cache_service.get(lockout_key) or 0
                    attempts += 1
                    await self.cache_service.set(
                        lockout_key, 
                        attempts, 
                        expire=self.config.login_lockout_duration
                    )
                
                logger.warning(f"用户 {username} 认证失败")
                return False, None, "用户名或密码错误"
                
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            return False, None, f"认证过程中发生错误: {e}"

    async def _validate_user_credentials(self, username: str, password: str) -> bool:
        """验证用户凭据
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            bool: 验证是否成功
        """
        try:
            # 这里应该实现实际的用户验证逻辑
            # 例如查询数据库，验证密码哈希等
            
            # 临时实现：简单的用户名密码验证
            # 实际项目中应该替换为真实的数据库查询
            if username == "admin" and password == "admin123":
                return True
            
            # 实际实现示例（需要数据库连接）:
            # user = await User.get_by_username(username)
            # if user and bcrypt.checkpw(password.encode('utf-8'), user.password_hash):
            #     return True
            
            return False
            
        except Exception as e:
            logger.error(f"验证用户凭据失败: {e}")
            return False

    async def validate_token(self, token: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        """验证令牌
        
        Args:
            token: JWT令牌
            
        Returns:
            Tuple[bool, Optional[Dict], Optional[str]]: (是否有效, 用户信息, 错误信息)
        """
        try:
            payload = jwt.decode(
                token,
                self.config.jwt_secret,
                algorithms=[self.config.jwt_algorithm]
            )
            
            # 检查令牌是否过期
            exp = payload.get('exp')
            if exp and datetime.utcnow().timestamp() > exp:
                return False, None, "令牌已过期"
            
            return True, payload, None
            
        except (jwt.ExpiredSignatureError, jwt.InvalidTokenError) as e:
            return False, None, f"令牌无效或已过期: {e}"
        except Exception as e:
            logger.error(f"验证令牌失败: {e}")
            return False, None, f"令牌验证失败: {e}"

    def _generate_token(self, username: str, device_info: Optional[Dict] = None) -> str:
        """生成JWT"""
        now = datetime.now()
        payload = {
            'username': username,
            'iat': now.timestamp(),
            'exp': (now + timedelta(seconds=self.config.jwt_expire)).timestamp(),
            'device_info': device_info
        }
        return jwt.encode(payload, self.config.jwt_secret, algorithm=self.config.jwt_algorithm)

    def _generate_self_signed_cert(self):
        """生成自签名证书"""
        try:
            # 生成私钥
            key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
                backend=default_backend()
            )

            # 创建证书主题和颁发者
            subject = issuer = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, u"CN"),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, u"State"),
                x509.NameAttribute(NameOID.LOCALITY_NAME, u"City"),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, u"My Company"),
                x509.NameAttribute(NameOID.COMMON_NAME, u"localhost"),
            ])

            # 构建证书
            cert = x509.CertificateBuilder().subject_name(
                subject
            ).issuer_name(
                issuer
            ).public_key(
                key.public_key()
            ).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                datetime.utcnow()
            ).not_valid_after(
                # 证书有效期为一年
                datetime.utcnow() + timedelta(days=365)
            ).add_extension(
                # 添加主题备用名称，以便浏览器信任
                x509.SubjectAlternativeName([x509.DNSName(u"localhost")]),
                critical=False,
            # 使用SHA256签名
            ).sign(key, hashes.SHA256(), default_backend())

            # 将密钥和证书存储在配置对象中（PEM格式）
            self.config.tls_key = key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ).decode('utf-8')
            
            self.config.tls_cert = cert.public_bytes(
                encoding=serialization.Encoding.PEM
            ).decode('utf-8')
            
            logger.info("已生成自签名TLS证书。")

        except Exception as e:
            logger.error(f"生成自签名证书失败: {e}")

    def _create_ssl_context(self) -> Optional[ssl.SSLContext]:
        """创建SSL上下文"""
        try:
            if not self.config.tls_cert or not self.config.tls_key:
                return None
                
            context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            context.load_cert_chain(self.config.tls_cert, self.config.tls_key)
            return context
            
        except Exception as e:
            logger.error(f"创建SSL上下文失败: {e}")
            return None

    async def _handle_connection(self,
                                reader: asyncio.StreamReader,
                                writer: asyncio.StreamWriter):
        """处理连接"""
        try:
            # 读取请求数据
            data = await reader.read(1024)
            if not data:
                return
                
            # 解析请求
            request = json.loads(data.decode('utf-8'))
            
            # 处理认证请求
            if request.get('action') == 'authenticate':
                username = request.get('username')
                password = request.get('password')
                device_info = request.get('device_info')
                
                success, token, error = await self.authenticate(
                    username, password, device_info
                )
                
                response = {
                    'success': success,
                    'token': token,
                    'error': error
                }
            else:
                response = {
                    'success': False,
                    'error': '不支持的操作'
                }
            
            # 发送响应
            response_data = json.dumps(response, ensure_ascii=False).encode('utf-8')
            writer.write(response_data)
            await writer.drain()
            
        except Exception as e:
            logger.error(f"处理连接失败: {e}")
        finally:
            writer.close()
            await writer.wait_closed()

    async def _cleanup_loop(self):
        """清理循环"""
        try:
            while self._running:
                await asyncio.sleep(60)  # 每分钟清理一次
                
                # 清理过期会话
                current_time = time.time()
                # 使用 self._sessions
                expired_sessions = [
                    session_id for session_id, session in self._sessions.items()
                    if current_time - session.get('created_at', 0) > self.config.session_timeout
                ]
                
                for session_id in expired_sessions:
                    if session_id in self._sessions:
                        del self._sessions[session_id]
                    
                if expired_sessions:
                    logger.info(f"清理了 {len(expired_sessions)} 个过期会话")
                    
        except asyncio.CancelledError:
            # 任务被取消是正常操作
            pass
        except Exception as e:
            logger.error(f"清理循环失败: {e}")

    def _get_jwt_config_for_slave(self) -> Tuple[str, str, int]:
        """获取从服务器JWT配置"""
        if not self.config.slave_jwt_secret:
             logger.error("从服务器JWT密钥未配置！")
             raise ValueError("Slave JWT secret is not configured in AuthConfig.")
        
        return (
            self.config.slave_jwt_secret,
            self.config.slave_jwt_algorithm,
            self.config.slave_jwt_expire_minutes,
        )

    def create_slave_access_token(self, slave_data: Dict[str, Any]) -> Optional[Tuple[str, int]]:
        """为从服务器创建访问令牌"""
        try:
            secret, algorithm, expire_minutes = self._get_jwt_config_for_slave()
            
            issued_at = datetime.utcnow()
            expiration_time = issued_at + timedelta(minutes=expire_minutes)
            
            payload = {
                "iat": issued_at,
                "exp": expiration_time,
                "sub": slave_data.get("server_id"),  # 主题为从服务器的唯一ID
                "type": "slave_token",
                **slave_data
            }
            
            token = jwt.encode(payload, secret, algorithm=algorithm)
            return token, int(expiration_time.timestamp())
        except (ValueError, jwt.PyJWTError) as e:
            logger.error(f"为从服务器创建访问令牌失败: {e}")
            return None

    def refresh_slave_token(self, current_token_str: str) -> Optional[Tuple[str, int]]:
        """刷新从服务器的访问令牌"""
        try:
            secret, algorithm, _ = self._get_jwt_config_for_slave()
            
            try:
                payload = jwt.decode(
                    current_token_str,
                    secret,
                    algorithms=[algorithm],
                    options={"verify_exp": False} # 允许已过期的令牌进行刷新
                )
            except jwt.InvalidTokenError as e:
                logger.error(f"无效的从服务器令牌，无法刷新: {e}")
                return None

            # 检查令牌类型
            if payload.get("type") != "slave_token":
                logger.warning("尝试刷新非从服务器类型的令牌")
                return None
                
            # 检查原始过期时间，防止滥用
            # 例如，可以设置一个最长刷新宽限期
            original_exp = datetime.utcfromtimestamp(payload.get("exp", 0))
            if datetime.utcnow() > original_exp + timedelta(days=1): # 比如，过期超过1天则不允许刷新
                 logger.warning(f"从服务器令牌已过期太久，拒绝刷新: {original_exp}")
                 return None

            # 移除旧的元数据，准备创建新令牌
            slave_data = {k: v for k, v in payload.items() if k not in ['iat', 'exp', 'sub', 'type']}

            return self.create_slave_access_token(slave_data)
        except (ValueError, jwt.PyJWTError) as e:
            logger.error(f"刷新从服务器令牌失败: {e}")
            return None

    def _send_account_lockout_notification(self, username: str, user_id: str, reason: str, 
                                         source_ip: Optional[str] = None, 
                                         device_info: Optional[Dict] = None):
        """发送账户锁定通知"""
        try:
            if not self.email_config or not self.config.email_admin_receivers:
                logger.warning("邮件配置或管理员邮箱列表未设置，无法发送锁定通知")
                return

            subject = f"OmniLink 账户锁定通知 - {username}"
            body = f"""
            管理员您好，
            
            用户账户已被锁定：
            - 用户名: {username}
            - 用户ID: {user_id}
            - 锁定原因: {reason}
            - 来源IP: {source_ip or '未知'}
            - 设备信息: {device_info or '未知'}
            - 锁定时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            
            请及时处理。
            
            OmniLink 安全系统
            """

            self._send_email(subject, body, self.config.email_admin_receivers)
            
        except Exception as e:
            logger.error(f"发送账户锁定通知失败: {e}")

    def _send_email(self, subject: str, body: str, receivers: List[str], is_html: bool = False) -> bool:
        """发送邮件"""
        try:
            if not self.email_config:
                logger.error("邮件配置未设置")
                return False

            msg = MIMEMultipart()
            msg['From'] = self.email_config['sender_email']
            msg['To'] = ', '.join(receivers)
            msg['Subject'] = subject

            msg.attach(MIMEText(body, 'html' if is_html else 'plain', 'utf-8'))

            with smtplib.SMTP(self.email_config['smtp_host'], self.email_config['smtp_port']) as server:
                if self.email_config['use_tls']:
                    server.starttls()
                
                if self.email_config['smtp_user'] and self.email_config['smtp_password']:
                    server.login(self.email_config['smtp_user'], self.email_config['smtp_password'])
                
                server.send_message(msg)

            logger.info(f"邮件发送成功: {subject}")
            return True

        except Exception as e:
            logger.error(f"邮件发送失败: {e}")
            return False

    def _create_default_admin(self):
        """创建默认管理员用户"""
        admin_user = User(
            id=1,
            username="admin",
            email="<EMAIL>",
            role=UserRole.ADMIN,
            permissions=[p for p in Permission],
            created_at=datetime.now()
        )
        
        # 默认密码：admin123
        admin_password = self._hash_password("admin123")
        
        self._users[1] = admin_user
        self._user_passwords[1] = admin_password
        self._username_to_id["admin"] = 1
        self._next_user_id = 2
        
        logger.info("默认管理员用户已创建: admin/admin123")
    
    def _hash_password(self, password: str) -> str:
        """哈希密码"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"


def get_auth_service(config: Optional[AuthConfig] = None, 
                    cache_service: Optional[Any] = None, 
                    audit_service: Optional[Any] = None) -> AuthService:
    """获取认证服务实例
    
    Args:
        config: 认证配置
        cache_service: 缓存服务
        audit_service: 审计服务
        
    Returns:
        AuthService: 认证服务实例
    """
    if config is None:
        config = AuthConfig()
    
    return AuthService(config, cache_service, audit_service) 
