/**
 * WebSocket客户端模块
 * 
 * 用于与服务端进行实时数据交互
 */

class WebSocketClient {
  /**
   * 创建WebSocket客户端
   * 
   * @param {string} connectionType 连接类型（dashboard/monitoring/device/system）
   * @param {Object} options 配置选项
   * @param {string} options.clientId 客户端ID（可选，默认自动生成）
   * @param {number} options.reconnectDelay 重连延迟（毫秒，默认5000）
   * @param {number} options.maxReconnectAttempts 最大重连尝试次数（默认10）
   * @param {boolean} options.autoReconnect 是否自动重连（默认true）
   * @param {function} options.onConnected 连接成功回调
   * @param {function} options.onDisconnected 断开连接回调
   * @param {function} options.onMessage 消息接收回调
   * @param {function} options.onError 错误回调
   */
  constructor(connectionType, options = {}) {
    // 连接类型
    this.connectionType = connectionType;
    
    // 配置选项
    this.clientId = options.clientId || null;
    this.reconnectDelay = options.reconnectDelay || 5000;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    this.autoReconnect = options.autoReconnect !== false;
    
    // 回调函数
    this.onConnectedCallback = options.onConnected || (() => {});
    this.onDisconnectedCallback = options.onDisconnected || (() => {});
    this.onMessageCallback = options.onMessage || (() => {});
    this.onErrorCallback = options.onError || (() => {});
    
    // 消息处理器映射
    this.messageHandlers = {};
    
    // 连接状态
    this.connected = false;
    this.connecting = false;
    this.reconnectAttempts = 0;
    this.reconnectTimer = null;
    
    // WebSocket实例
    this.ws = null;
    
    // 心跳检测
    this.pingInterval = options.pingInterval || 30000;
    this.pingTimer = null;
    this.lastPongTime = 0;
  }
  
  /**
   * 构建WebSocket连接URL
   * 
   * @returns {string} WebSocket连接URL
   * @private
   */
  _buildUrl() {
    // 获取当前URL协议（http/https）
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    
    // 获取当前主机名和端口
    const host = window.location.host;
    
    // 构建URL
    let url = `${protocol}//${host}/ws/${this.connectionType}`;
    
    // 添加客户端ID（如果有）
    if (this.clientId) {
      url += `?client_id=${encodeURIComponent(this.clientId)}`;
    }
    
    return url;
  }
  
  /**
   * 连接WebSocket服务器
   * 
   * @returns {Promise<boolean>} 连接是否成功
   */
  async connect() {
    // 如果已连接或正在连接，则直接返回
    if (this.connected || this.connecting) {
      return this.connected;
    }
    
    this.connecting = true;
    
    try {
      // 创建WebSocket连接
      const url = this._buildUrl();
      this.ws = new WebSocket(url);
      
      // 设置事件处理器
      this.ws.onopen = this._handleOpen.bind(this);
      this.ws.onclose = this._handleClose.bind(this);
      this.ws.onmessage = this._handleMessage.bind(this);
      this.ws.onerror = this._handleError.bind(this);
      
      // 等待连接完成
      return await new Promise((resolve) => {
        // 设置连接超时
        const timeoutId = setTimeout(() => {
          if (!this.connected) {
            this.connecting = false;
            resolve(false);
          }
        }, 10000);
        
        // 设置临时回调
        const originalCallback = this.onConnectedCallback;
        this.onConnectedCallback = () => {
          clearTimeout(timeoutId);
          originalCallback();
          this.onConnectedCallback = originalCallback;
          resolve(true);
        };
      });
    } catch (error) {
      console.error('WebSocket连接异常:', error);
      this.connecting = false;
      this._handleError(error);
      return false;
    }
  }
  
  /**
   * 断开WebSocket连接
   */
  disconnect() {
    // 停止心跳检测
    this._stopPing();
    
    // 停止重连计时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    // 如果已连接，则断开连接
    if (this.ws) {
      // 关闭WebSocket连接
      try {
        this.ws.close();
      } catch (error) {
        console.error('关闭WebSocket连接异常:', error);
      }
      
      this.ws = null;
      this.connected = false;
      this.connecting = false;
    }
  }
  
  /**
   * 发送消息
   * 
   * @param {string} type 消息类型
   * @param {Object} data 消息数据
   * @returns {boolean} 是否发送成功
   */
  sendMessage(type, data = {}) {
    if (!this.connected) {
      console.warn('WebSocket未连接，无法发送消息');
      return false;
    }
    
    try {
      // 构建消息
      const message = {
        type,
        data,
        time: new Date().toISOString()
      };
      
      // 发送消息
      this.ws.send(JSON.stringify(message));
      
      return true;
    } catch (error) {
      console.error('发送消息异常:', error);
      this._handleError(error);
      return false;
    }
  }
  
  /**
   * 发送Ping消息
   * 
   * @private
   */
  _sendPing() {
    this.sendMessage('ping');
  }
  
  /**
   * 启动心跳检测
   * 
   * @private
   */
  _startPing() {
    // 停止现有心跳
    this._stopPing();
    
    // 记录当前时间
    this.lastPongTime = Date.now();
    
    // 启动心跳检测定时器
    this.pingTimer = setInterval(() => {
      // 发送心跳
      this._sendPing();
      
      // 检查上次接收Pong的时间
      const now = Date.now();
      if (now - this.lastPongTime > this.pingInterval * 2) {
        // 如果超过两个心跳周期没有收到Pong，则认为连接已断开
        console.warn('心跳检测超时，重新连接...');
        this._reconnect();
      }
    }, this.pingInterval);
  }
  
  /**
   * 停止心跳检测
   * 
   * @private
   */
  _stopPing() {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }
  
  /**
   * WebSocket连接打开处理器
   * 
   * @param {Event} event 事件对象
   * @private
   */
  _handleOpen(event) {
    console.log(`WebSocket已连接: ${this.connectionType}`);
    
    this.connected = true;
    this.connecting = false;
    this.reconnectAttempts = 0;
    
    // 启动心跳
    this._startPing();
    
    // 触发连接成功回调
    this.onConnectedCallback();
  }
  
  /**
   * WebSocket连接关闭处理器
   * 
   * @param {CloseEvent} event 事件对象
   * @private
   */
  _handleClose(event) {
    const wasConnected = this.connected;
    
    this.connected = false;
    this.connecting = false;
    
    // 停止心跳
    this._stopPing();
    
    // 触发断开连接回调（如果之前已连接）
    if (wasConnected) {
      console.log(`WebSocket已断开: ${this.connectionType}, 代码: ${event.code}, 原因: ${event.reason}`);
      this.onDisconnectedCallback({
        code: event.code,
        reason: event.reason
      });
    }
    
    // 如果配置了自动重连，则尝试重新连接
    if (this.autoReconnect) {
      this._reconnect();
    }
  }
  
  /**
   * WebSocket消息接收处理器
   * 
   * @param {MessageEvent} event 事件对象
   * @private
   */
  _handleMessage(event) {
    try {
      // 解析消息
      const message = JSON.parse(event.data);
      
      // 处理Pong消息
      if (message.type === 'pong') {
        this.lastPongTime = Date.now();
        return;
      }
      
      // 查找消息处理器
      const handler = this.messageHandlers[message.type];
      if (handler) {
        // 调用专门的消息处理器
        handler(message);
      }
      
      // 触发通用消息回调
      this.onMessageCallback(message);
    } catch (error) {
      console.error('处理消息异常:', error, event.data);
    }
  }
  
  /**
   * WebSocket错误处理器
   * 
   * @param {Event|Error} error 错误对象
   * @private
   */
  _handleError(error) {
    console.error(`WebSocket错误: ${this.connectionType}`, error);
    
    // 触发错误回调
    this.onErrorCallback(error);
  }
  
  /**
   * 重新连接WebSocket
   * 
   * @private
   */
  _reconnect() {
    // 如果已达到最大重连尝试次数，则不再重连
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.warn(`已达到最大重连尝试次数: ${this.maxReconnectAttempts}`);
      return;
    }
    
    // 如果正在连接或已连接，则不再重连
    if (this.connecting || this.connected) {
      return;
    }
    
    // 如果已设置重连定时器，则不再重连
    if (this.reconnectTimer) {
      return;
    }
    
    // 增加重连尝试次数
    this.reconnectAttempts++;
    
    // 计算重连延迟（指数退避策略）
    const delay = Math.min(
      this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts - 1),
      60000 // 最大60秒
    );
    
    console.log(`尝试重新连接 ${this.connectionType} (${this.reconnectAttempts}/${this.maxReconnectAttempts})，延迟: ${delay}ms`);
    
    // 设置重连定时器
    this.reconnectTimer = setTimeout(async () => {
      this.reconnectTimer = null;
      
      try {
        // 尝试连接
        await this.connect();
      } catch (error) {
        console.error('重新连接异常:', error);
      }
    }, delay);
  }
  
  /**
   * 注册消息处理器
   * 
   * @param {string} messageType 消息类型
   * @param {function} handler 处理函数
   */
  registerMessageHandler(messageType, handler) {
    this.messageHandlers[messageType] = handler;
  }
  
  /**
   * 取消注册消息处理器
   * 
   * @param {string} messageType 消息类型
   */
  unregisterMessageHandler(messageType) {
    delete this.messageHandlers[messageType];
  }
  
  /**
   * 请求数据
   */
  requestData() {
    this.sendMessage('request_data');
  }
}

/**
 * 创建仪表盘WebSocket连接
 * 
 * @param {Object} options 配置选项
 * @returns {WebSocketClient} WebSocket客户端实例
 */
function createDashboardSocket(options = {}) {
  // 默认处理器
  const defaultHandlers = {
    onMessage: (message) => {
      if (message.type === 'dashboard_update') {
        console.log('接收到仪表盘更新数据:', message.data);
        updateDashboardUI(message.data);
      }
    }
  };
  
  // 合并选项
  const mergedOptions = { ...defaultHandlers, ...options };
  
  // 创建WebSocket客户端
  const client = new WebSocketClient('dashboard', mergedOptions);
  
  // 注册消息处理器
  client.registerMessageHandler('dashboard_update', (message) => {
    updateDashboardUI(message.data);
  });
  
  client.registerMessageHandler('notification', (message) => {
    showNotification(message.title, message.message, message.notification_type);
  });
  
  return client;
}

/**
 * 监控WebSocket客户端
 * 负责与后端WebSocket服务建立连接，接收和发送监控数据
 */

// 创建监控WebSocket连接
function createMonitoringSocket(options = {}) {
  // 默认配置
  const defaultOptions = {
    reconnectDelay: 1000,       // 初始重连延迟（毫秒）
    maxReconnectDelay: 30000,   // 最大重连延迟（毫秒）
    reconnectBackoff: 1.5,      // 重连延迟指数增长因子
    maxReconnectAttempts: 10,   // 最大重连尝试次数
    dataRefreshInterval: 15000, // 数据刷新间隔（毫秒）
    onConnected: () => {},      // 连接成功回调
    onDisconnected: () => {},   // 断开连接回调
    onData: () => {},           // 数据接收回调
    onError: () => {}           // 错误回调
  };
  
  // 合并选项
  const config = { ...defaultOptions, ...options };
  
  // 内部状态
  let socket = null;
  let reconnectAttempts = 0;
  let reconnectTimeout = null;
  let dataRefreshInterval = null;
  let isConnected = false;
  
  // 消息类型
  const MESSAGE_TYPES = {
    CONNECT: 'connect',
    DISCONNECT: 'disconnect',
    REQUEST_DATA: 'request_data',
    SYSTEM_STATUS: 'system_status',
    SERVER_STATUS: 'server_status',
    ERROR: 'error'
  };

  /**
   * 建立WebSocket连接
   * @returns {Promise<boolean>} 连接是否成功
   */
  async function connect() {
    return new Promise((resolve) => {
      try {
        // 清除现有连接
        if (socket) {
          socket.close();
        }
        
        // 构建WebSocket URL
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        const url = `${protocol}//${host}/api/ws/monitoring`;
        
        // 创建新的WebSocket连接
        socket = new WebSocket(url);
        
        // 连接建立事件
        socket.onopen = () => {
          console.log('监控WebSocket连接已建立');
          isConnected = true;
          reconnectAttempts = 0;
          
          // 发送连接消息
          sendMessage({
            type: MESSAGE_TYPES.CONNECT,
            data: {
              client_id: generateClientId(),
              client_type: 'monitoring',
              timestamp: Date.now()
            }
          });
          
          // 设置定时刷新数据
          startDataRefresh();
          
          // 调用连接成功回调
          if (typeof config.onConnected === 'function') {
            config.onConnected();
          }
          
          resolve(true);
        };
        
        // 接收消息事件
        socket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            handleMessage(message);
          } catch (error) {
            console.error('解析WebSocket消息失败:', error);
            if (typeof config.onError === 'function') {
              config.onError(error);
            }
          }
        };
        
        // 连接关闭事件
        socket.onclose = (event) => {
          isConnected = false;
          console.log(`监控WebSocket连接已关闭: ${event.code} ${event.reason}`);
          
          // 清除数据刷新定时器
          clearInterval(dataRefreshInterval);
          
          // 调用断开连接回调
          if (typeof config.onDisconnected === 'function') {
            config.onDisconnected({
              code: event.code,
              reason: event.reason,
              wasClean: event.wasClean
            });
          }
          
          // 尝试重新连接
          scheduleReconnect();
          
          if (!event.wasClean) {
            resolve(false);
          }
        };
        
        // 连接错误事件
        socket.onerror = (error) => {
          console.error('监控WebSocket错误:', error);
          if (typeof config.onError === 'function') {
            config.onError(error);
          }
          resolve(false);
        };
      } catch (error) {
        console.error('创建监控WebSocket连接失败:', error);
        if (typeof config.onError === 'function') {
          config.onError(error);
        }
        resolve(false);
      }
    });
  }
  
  /**
   * 关闭WebSocket连接
   */
  function disconnect() {
    if (socket && socket.readyState === WebSocket.OPEN) {
      // 发送断开连接消息
      sendMessage({
        type: MESSAGE_TYPES.DISCONNECT,
        data: {
          client_id: generateClientId(),
          timestamp: Date.now()
        }
      });
      
      // 关闭连接
      socket.close(1000, '客户端主动断开连接');
    }
    
    // 清除重连定时器
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout);
      reconnectTimeout = null;
    }
    
    // 清除数据刷新定时器
    if (dataRefreshInterval) {
      clearInterval(dataRefreshInterval);
      dataRefreshInterval = null;
    }
  }
  
  /**
   * 发送消息
   * @param {Object} message 消息对象
   * @returns {boolean} 是否发送成功
   */
  function sendMessage(message) {
    if (!socket || socket.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket未连接，无法发送消息');
      return false;
    }
    
    try {
      const messageString = JSON.stringify(message);
      socket.send(messageString);
      return true;
    } catch (error) {
      console.error('发送WebSocket消息失败:', error);
      if (typeof config.onError === 'function') {
        config.onError(error);
      }
      return false;
    }
  }
  
  /**
   * 请求监控数据
   * @returns {boolean} 是否请求成功
   */
  function requestData() {
    return sendMessage({
      type: MESSAGE_TYPES.REQUEST_DATA,
      data: {
        client_id: generateClientId(),
        timestamp: Date.now()
      }
    });
  }
  
  /**
   * 处理接收到的消息
   * @param {Object} message 消息对象
   */
  function handleMessage(message) {
    console.log('收到WebSocket消息:', message);
    
    // 根据消息类型处理
    switch (message.type) {
      case MESSAGE_TYPES.SYSTEM_STATUS:
        // 处理系统状态消息
        if (typeof config.onData === 'function') {
          config.onData(message.data);
        }
        
        // 更新监控UI
        if (typeof window.updateMonitoringUI === 'function') {
          window.updateMonitoringUI(message.data);
        }
        break;
        
      case MESSAGE_TYPES.SERVER_STATUS:
        // 处理服务器状态消息
        if (typeof config.onData === 'function') {
          config.onData(message.data);
        }
        
        // 更新服务器状态
        if (typeof window.updateServersStatus === 'function') {
          window.updateServersStatus(message.data);
        }
        break;
        
      case MESSAGE_TYPES.ERROR:
        // 处理错误消息
        console.error('服务器返回错误:', message.data);
        if (typeof config.onError === 'function') {
          config.onError(message.data);
        }
        break;
        
      default:
        console.warn('未知WebSocket消息类型:', message.type);
        break;
    }
  }
  
  /**
   * 安排重新连接
   */
  function scheduleReconnect() {
    // 如果超过最大重连次数，放弃重连
    if (reconnectAttempts >= config.maxReconnectAttempts) {
      console.warn(`达到最大重连尝试次数(${config.maxReconnectAttempts})，放弃重连`);
      return;
    }
    
    // 清除现有的重连定时器
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout);
    }
    
    // 计算下一次重连延迟（指数退避）
    const delay = Math.min(
      config.reconnectDelay * Math.pow(config.reconnectBackoff, reconnectAttempts),
      config.maxReconnectDelay
    );
    
    console.log(`计划在${delay}毫秒后尝试重新连接(尝试${reconnectAttempts + 1}/${config.maxReconnectAttempts})`);
    
    // 设置重连定时器
    reconnectTimeout = setTimeout(() => {
      reconnectAttempts++;
      console.log(`尝试重新连接(${reconnectAttempts}/${config.maxReconnectAttempts})...`);
      connect();
    }, delay);
  }
  
  /**
   * 开始数据刷新定时器
   */
  function startDataRefresh() {
    // 清除现有的定时器
    if (dataRefreshInterval) {
      clearInterval(dataRefreshInterval);
    }
    
    // 设置新的定时器
    dataRefreshInterval = setInterval(() => {
      if (isConnected) {
        console.log('定时请求监控数据...');
        requestData();
      }
    }, config.dataRefreshInterval);
  }
  
  /**
   * 生成客户端ID
   * @returns {string} 客户端ID
   */
  function generateClientId() {
    // 如果已经存在客户端ID，直接返回
    if (window.clientId) {
      return window.clientId;
    }
    
    // 生成新的客户端ID
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 10);
    window.clientId = `monitor-${timestamp}-${random}`;
    
    return window.clientId;
  }
  
  // 返回公共API
  return {
    connect,
    disconnect,
    sendMessage,
    requestData,
    isConnected: () => isConnected
  };
}

/**
 * 更新仪表盘界面
 * 
 * @param {Object} data 更新数据
 */
function updateDashboardUI(data) {
  // 更新服务器状态卡片
  if (data.servers) {
    if (document.getElementById('slaveCount')) {
      document.getElementById('slaveCount').textContent = 
        (data.servers.online + data.servers.offline) || '0';
    }
    if (document.getElementById('slaveOnline')) {
      document.getElementById('slaveOnline').textContent = data.servers.online || '0';
    }
  }
  
  // 更新设备状态卡片
  if (data.devices) {
    if (document.getElementById('deviceCount')) {
      document.getElementById('deviceCount').textContent = 
        (data.devices.connected + data.devices.disconnected) || '0';
    }
    if (document.getElementById('deviceActive')) {
      document.getElementById('deviceActive').textContent = data.devices.connected || '0';
    }
  }
  
  // 更新资源使用情况
  if (data.resources) {
    // 更新CPU使用率
    const cpuGauge = document.getElementById('cpuGauge');
    if (cpuGauge && window.gaugeCharts && window.gaugeCharts.cpu) {
      window.gaugeCharts.cpu.update(data.resources.cpu_usage);
    }
    
    // 更新内存使用率
    const memoryGauge = document.getElementById('memoryGauge');
    if (memoryGauge && window.gaugeCharts && window.gaugeCharts.memory) {
      window.gaugeCharts.memory.update(data.resources.memory_usage);
    }
    
    // 更新磁盘使用率
    const diskGauge = document.getElementById('diskGauge');
    if (diskGauge && window.gaugeCharts && window.gaugeCharts.disk) {
      window.gaugeCharts.disk.update(data.resources.disk_usage);
    }
  }
}

/**
 * 更新监控界面
 * 
 * @param {Object} data 更新数据
 */
function updateMonitoringUI(data) {
  // 更新服务器状态表格
  if (data.servers && Array.isArray(data.servers)) {
    const serversTable = document.getElementById('serversTable');
    if (serversTable) {
      const tbody = serversTable.querySelector('tbody');
      if (tbody) {
        // 清空表格
        tbody.innerHTML = '';
        
        // 添加服务器数据行
        data.servers.forEach(server => {
          const row = document.createElement('tr');
          row.className = 'mdc-data-table__row';
          
          // 状态类
          let statusClass = '';
          if (server.status === 'online') {
            statusClass = 'status-success';
          } else if (server.status === 'warning') {
            statusClass = 'status-warning';
          } else if (server.status === 'offline') {
            statusClass = 'status-error';
          }
          
          row.innerHTML = `
            <td class="mdc-data-table__cell">${server.id}</td>
            <td class="mdc-data-table__cell ${statusClass}">${server.status}</td>
            <td class="mdc-data-table__cell">${server.resources.cpu_usage}%</td>
            <td class="mdc-data-table__cell">${server.resources.memory_usage}%</td>
            <td class="mdc-data-table__cell">${server.resources.disk_usage}%</td>
          `;
          
          tbody.appendChild(row);
        });
      }
    }
  }
}

/**
 * 显示通知
 * 
 * @param {string} title 通知标题
 * @param {string} message 通知内容
 * @param {string} type 通知类型（info/warning/error/success）
 */
function showNotification(title, message, type = 'info') {
  // 检查通知容器是否存在，不存在则创建
  let container = document.getElementById('notification-container');
  if (!container) {
    container = document.createElement('div');
    container.id = 'notification-container';
    container.style.position = 'fixed';
    container.style.top = '72px';
    container.style.right = '16px';
    container.style.zIndex = '1000';
    document.body.appendChild(container);
  }
  
  // 创建通知元素
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.style.marginBottom = '8px';
  notification.style.padding = '12px 16px';
  notification.style.borderRadius = '4px';
  notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
  notification.style.backgroundColor = '#fff';
  notification.style.maxWidth = '350px';
  notification.style.animation = 'notification-fade-in 0.3s ease-out forwards';
  
  // 根据类型设置边框颜色
  switch (type) {
    case 'success':
      notification.style.borderLeft = '4px solid #4CAF50';
      break;
    case 'warning':
      notification.style.borderLeft = '4px solid #FF9800';
      break;
    case 'error':
      notification.style.borderLeft = '4px solid #F44336';
      break;
    default:
      notification.style.borderLeft = '4px solid #2196F3';
      break;
  }
  
  // 通知内容
  notification.innerHTML = `
    <div style="font-weight: 500; margin-bottom: 4px;">${title}</div>
    <div style="font-size: 14px; color: #666;">${message}</div>
  `;
  
  // 添加到容器
  container.appendChild(notification);
  
  // 创建淡出动画
  if (!document.getElementById('notification-style')) {
    const style = document.createElement('style');
    style.id = 'notification-style';
    style.textContent = `
      @keyframes notification-fade-in {
        from { opacity: 0; transform: translateX(20px); }
        to { opacity: 1; transform: translateX(0); }
      }
      @keyframes notification-fade-out {
        from { opacity: 1; transform: translateX(0); }
        to { opacity: 0; transform: translateX(20px); }
      }
    `;
    document.head.appendChild(style);
  }
  
  // 自动关闭通知
  // 移除setTimeout模拟延迟;
  }, 5000);
}

// 导出对象
window.WebSocketClient = WebSocketClient;
window.createDashboardSocket = createDashboardSocket;
window.createMonitoringSocket = createMonitoringSocket;
window.showNotification = showNotification; 