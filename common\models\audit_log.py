from sqlalchemy import (
    Column, Integer, String, TIMESTAMP, ForeignKey, JSON
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from common.database.base_class import Base

class AuditLog(Base):
    __tablename__ = 'audit_logs'
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    action = Column(String(100), nullable=False)
    target_type = Column(String(50))
    target_id = Column(Integer)
    ip_address = Column(String(50))
    details = Column(JSON)
    timestamp = Column(TIMESTAMP, server_default=func.now())
    
    user = relationship('User', back_populates='audit_logs')
