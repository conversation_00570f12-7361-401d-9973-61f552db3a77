# OmniLink Web UI Dockerfile

### 第一阶段: 构建前端静态文件 ###
FROM node:18-alpine AS build

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json (如果存在)
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制所有剩余的前端代码
COPY . .

# 构建生产版本
RUN npm run build

### 第二阶段: 使用Nginx提供服务 ###
FROM nginx:stable-alpine

# 将构建阶段生成的静态文件复制到Nginx的默认Web根目录
COPY --from=build /app/dist /usr/share/nginx/html

# 复制自定义的Nginx配置 (如果需要，可后续添加)
# COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露80端口
EXPOSE 80

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"] 