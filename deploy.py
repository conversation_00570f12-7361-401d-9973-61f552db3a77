#!/usr/bin/env python3
"""
OmniLink系统部署脚本
"""

import subprocess
import sys
import time

def run_command(cmd, description):
    """运行命令并处理错误"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, text=True, capture_output=True)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False

def main():
    print("🚀 开始部署OmniLink系统...")
    
    # 步骤1: 清理环境
    if not run_command("docker-compose down -v", "清理现有容器"):
        print("继续执行...")
    
    # 步骤2: 拉取基础镜像
    if not run_command("docker pull postgres:16-alpine", "拉取PostgreSQL镜像"):
        return False
    
    if not run_command("docker pull redis:6.2-alpine", "拉取Redis镜像"):
        return False
    
    # 步骤3: 构建应用镜像
    if not run_command("docker-compose build --no-cache main-server", "构建主服务器镜像"):
        return False
    
    if not run_command("docker-compose build --no-cache slave-server", "构建从服务器镜像"):
        return False
    
    # 步骤4: 启动服务
    if not run_command("docker-compose up -d", "启动所有服务"):
        return False
    
    print("\n⏳ 等待服务启动...")
    time.sleep(10)
    
    # 步骤5: 检查服务状态
    run_command("docker-compose ps", "检查服务状态")
    
    print("\n🎉 部署完成!")
    print("📍 访问地址:")
    print("   - Web界面: http://localhost:8000")
    print("   - API文档: http://localhost:8000/docs")
    print("   - 管理员账户: firefly / bro2fhz12")
    print("\n📋 常用命令:")
    print("   - 查看日志: docker-compose logs -f")
    print("   - 停止服务: docker-compose down")
    print("   - 重启服务: docker-compose restart")

if __name__ == "__main__":
    main() 