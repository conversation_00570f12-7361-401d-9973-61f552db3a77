#!/usr/bin/env node

const http = require('http');
const EventEmitter = require('events');

class ChromeMCPConnector extends EventEmitter {
    constructor(port = 12306) {
        super();
        this.port = port;
        this.connected = false;
    }

    connect() {
        console.log(`Connecting to Chrome MCP Server on port ${this.port}...`);
        
        const options = {
            hostname: '127.0.0.1',
            port: this.port,
            path: '/sse',
            method: 'GET',
            headers: {
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache'
            }
        };

        const req = http.request(options, (res) => {
            console.log(`Connected to Chrome MCP Server (Status: ${res.statusCode})`);
            this.connected = true;
            
            res.on('data', (chunk) => {
                const data = chunk.toString();
                if (data.trim()) {
                    console.log('Received:', data.trim());
                    this.emit('data', data);
                }
            });

            res.on('end', () => {
                console.log('Connection ended');
                this.connected = false;
            });
        });

        req.on('error', (err) => {
            console.error('Connection error:', err.message);
            this.connected = false;
            // 重连机制
            setTimeout(() => {
                console.log('Attempting to reconnect...');
                this.connect();
            }, 5000);
        });

        req.end();

        // 发送心跳
        setInterval(() => {
            if (this.connected) {
                console.log('Chrome MCP Connector - Heartbeat');
            }
        }, 30000);
    }
}

// 启动连接器
const connector = new ChromeMCPConnector();
connector.connect();

// 优雅退出
process.on('SIGINT', () => {
    console.log('\nShutting down Chrome MCP Connector...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\nShutting down Chrome MCP Connector...');
    process.exit(0);
});
