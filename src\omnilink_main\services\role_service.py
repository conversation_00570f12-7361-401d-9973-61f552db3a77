from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from typing import List, Optional
from fastapi import HTTPException, status

from common.models.role import Role
from common.models.user import User
from common.schemas.role_schema import RoleCreate, RoleUpdate

class RoleService:
    async def get_role_by_id(self, db: AsyncSession, *, id: int) -> Optional[Role]:
        return await db.get(Role, id)

    async def get_role_by_name(self, db: AsyncSession, *, name: str) -> Optional[Role]:
        statement = select(Role).where(Role.name == name)
        result = await db.execute(statement)
        return result.scalars().first()

    async def get_all_roles(self, db: AsyncSession, *, skip: int = 0, limit: int = 100) -> List[Role]:
        statement = select(Role).offset(skip).limit(limit)
        result = await db.execute(statement)
        return result.scalars().all()

    async def create_new_role(self, db: AsyncSession, *, role_in: RoleCreate) -> Role:
        existing_role = await self.get_role_by_name(db, name=role_in.name)
        if existing_role:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Role with this name already exists.")
        
        db_role = Role(**role_in.model_dump())
        db.add(db_role)
        await db.commit()
        await db.refresh(db_role)
        return db_role

    async def update_existing_role(self, db: AsyncSession, *, role_id: int, role_in: RoleUpdate) -> Role:
        db_role = await self.get_role_by_id(db, id=role_id)
        if not db_role:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Role not found.")
        if db_role.is_system_role:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="System roles cannot be modified.")
            
        update_data = role_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_role, field, value)
        
        await db.commit()
        await db.refresh(db_role)
        return db_role

    async def delete_existing_role(self, db: AsyncSession, *, role_id: int) -> Role:
        db_role = await self.get_role_by_id(db, id=role_id)
        if not db_role:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Role not found.")
        if db_role.is_system_role:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="System roles cannot be deleted.")
            
        await db.delete(db_role)
        await db.commit()
        return db_role

    async def assign_role_to_user(self, db: AsyncSession, *, user_id: int, role_id: int) -> User:
        from src.omnilink_main.services.user_service import user_service_instance
        user = await user_service_instance.get(db, id=user_id)
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
        
        role = await self.get_role_by_id(db, id=role_id)
        if not role:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Role not found")
        
        if role not in user.roles:
            user.roles.append(role)
            db.add(user)
            await db.commit()
            await db.refresh(user)
        return user

    async def revoke_role_from_user(self, db: AsyncSession, *, user_id: int, role_id: int) -> User:
        from src.omnilink_main.services.user_service import user_service_instance
        user = await user_service_instance.get(db, id=user_id)
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
        
        role = await self.get_role_by_id(db, id=role_id)
        if not role:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Role not found")
            
        if role in user.roles:
            user.roles.remove(role)
            db.add(user)
            await db.commit()
            await db.refresh(user)
        return user

# Create a single, reusable instance of the service
role_service_instance = RoleService()
