# 审计日志系统

本模块提供完整的审计日志记录和查询功能，用于跟踪和审计系统中的各种操作和事件。

## 主要功能

- **多种事件类型支持**：系统事件、用户事件、安全事件、资源操作等
- **灵活的存储后端**：支持数据库、SQLite、文件和内存存储
- **丰富的查询功能**：按时间、用户、事件类型、严重性等多维度查询
- **自定义事件处理**：支持注册自定义事件处理器
- **安全事件告警**：对安全相关事件自动记录和告警
- **生命周期管理**：自动清理过期审计记录

## 目录结构

```
audit/
  ├── __init__.py        - 模块初始化文件，导出公共接口
  ├── audit_events.py    - 审计事件类型和类别定义
  ├── audit_record.py    - 审计记录模型定义
  ├── audit_service.py   - 审计服务实现
  ├── audit_storage.py   - 审计存储管理器
  ├── examples.py        - 使用示例
  └── README.md          - 本文档
```

## 快速开始

### 1. 记录审计事件

```python
from ky.main_server.audit import log_event, AuditEventType

# 记录系统启动事件
log_event(
    event_type=AuditEventType.SYSTEM_STARTUP,
    message="系统启动成功",
    details={"version": "1.0.0", "environment": "production"}
)

# 记录用户登录事件
log_event(
    event_type=AuditEventType.USER_LOGIN,
    message="用户登录成功",
    user_id="user123",
    user_name="张三",
    user_ip="*************",
    details={"login_method": "password"}
)
```

### 2. 查询审计日志

```python
from ky.main_server.audit import query_logs, AuditEventType, AuditSeverity

# 基本查询 - 查询最近的系统事件
records, total = query_logs(
    event_types=[AuditEventType.SYSTEM_STARTUP, AuditEventType.SYSTEM_SHUTDOWN],
    limit=10,
    order_desc=True  # 最新的记录优先
)

print(f"查询到 {total} 条记录")
for record in records:
    print(f"{record.timestamp}: {record.message}")

# 高级查询 - 组合多个条件
records, total = query_logs(
    event_types=[AuditEventType.USER_LOGIN, AuditEventType.USER_LOGOUT],
    severities=[AuditSeverity.INFO, AuditSeverity.NOTICE],
    user_ids=["user123"],
    text_search="成功",
    limit=5
)
```

### 3. 自定义事件处理

```python
from ky.main_server.audit import AuditService, AuditEventType, AuditRecord

# 获取审计服务实例
service = AuditService.get_instance()

# 定义自定义处理器
def security_alert_handler(record: AuditRecord):
    if record.event_severity.value == "CRITICAL":
        # 发送紧急通知
        print(f"安全告警: {record.message}")
    
# 注册处理器
service.register_event_handler(
    AuditEventType.SECURITY_POLICY_VIOLATION, 
    security_alert_handler
)
```

## 配置

审计服务可以通过配置参数进行定制：

```python
from ky.main_server.audit import AuditService, StorageBackend

# 配置参数
config = {
    'source': 'main_server',           # 来源标识
    'storage_backend': 'DATABASE',     # 存储后端类型
    'db_file': 'audit_logs.db',        # SQLite文件路径
    'log_dir': 'audit_logs',           # 日志文件目录
    'retention_days': 90,              # 保留天数
    'auto_cleanup': True,              # 自动清理
    'cleanup_interval': 86400,         # 清理间隔（秒）
    'async_mode': True                 # 异步模式
}

# 初始化审计服务
audit_service = AuditService(config=config)
```

## 最佳实践

1. **及时记录重要操作**：系统启动/关闭、用户登录/登出、权限变更、敏感数据访问等
2. **详细记录上下文**：包括操作用户、IP地址、资源信息等
3. **适当设置保留期**：根据安全要求和存储容量设置合理的保留期
4. **定期审查安全事件**：定期检查安全相关的审计日志
5. **使用标签分类**：为重要事件添加标签，便于后续查询

## 扩展建议

1. **集成消息队列**：高并发环境下可考虑使用消息队列缓冲审计事件
2. **导出功能**：添加将审计日志导出为CSV或PDF的功能
3. **图表分析**：添加可视化分析功能，便于识别模式和趋势
4. **集成SIEM**：与安全信息和事件管理系统集成 