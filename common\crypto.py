"""
OmniLink全联通系统 - 加密解密模块
提供对称加密、非对称加密、散列和数字签名功能
"""

import os
import base64
import logging
import hmac
import hashlib
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from typing import Tuple, Optional
from cryptography.hazmat.primitives.ciphers.aead import AESGCM

logger = logging.getLogger(__name__)

class Encryption:
    """加密工具类，用于替换原有的加密实现"""
    
    @staticmethod
    def get_key():
        """从环境变量或配置文件获取密钥"""
        key = os.environ.get('ENCRYPTION_KEY')
        if not key:
            # 回退到配置文件
            try:
                from ..main_server.config import get_config
                key = get_config('ENCRYPTION_KEY')
            except ImportError:
                # 开发环境或测试环境使用默认密钥
                key = "ky_secure_key_2024"
        
        # 确保密钥长度为32字节
        while len(key.encode('utf-8')) < 32:
            key += key
        
        return key.encode('utf-8')[:32]
    
    @staticmethod
    def encrypt(data):
        """
        使用AES-CBC加密数据
        
        Args:
            data: 要加密的数据(字符串或字节)
            
        Returns:
            str: Base64编码的加密数据
        """
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        try:
            key = Encryption.get_key()
            iv = os.urandom(16)
            
            # 使用PKCS7填充
            padder = padding.PKCS7(128).padder()
            padded_data = padder.update(data) + padder.finalize()
            
            # 加密
            cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
            encryptor = cipher.encryptor()
            encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
            
            # 结合IV和加密数据并用Base64编码
            result = base64.b64encode(iv + encrypted_data).decode('utf-8')
            return result
        except Exception as e:
            logger.error(f"加密失败: {str(e)}")
            raise ValueError(f"加密失败: {str(e)}")
    
    @staticmethod
    def decrypt(encrypted_data):
        """
        解密AES-CBC加密的数据
        
        Args:
            encrypted_data: Base64编码的加密数据
            
        Returns:
            str: 解密后的字符串
        """
        try:
            # Base64解码
            data = base64.b64decode(encrypted_data)
            
            # 提取IV（前16字节）
            iv = data[:16]
            ciphertext = data[16:]
            
            key = Encryption.get_key()
            
            # 解密
            cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
            decryptor = cipher.decryptor()
            decrypted_padded = decryptor.update(ciphertext) + decryptor.finalize()
            
            # 去除填充
            unpadder = padding.PKCS7(128).unpadder()
            unpadded = unpadder.update(decrypted_padded) + unpadder.finalize()
            
            return unpadded.decode('utf-8')
        except Exception as e:
            logger.error(f"解密失败: {str(e)}")
            raise ValueError(f"解密失败: {str(e)}")
    
    @staticmethod
    def generate_token(user_id, expires):
        """
        生成JWT风格的令牌
        
        Args:
            user_id: 用户ID
            expires: 过期时间戳
            
        Returns:
            str: 令牌
        """
        import json
        import time
        
        # 创建载荷
        payload = {
            'sub': str(user_id),
            'exp': expires,
            'iat': int(time.time())
        }
        
        # 序列化并编码头部和载荷
        header = {'alg': 'HS256', 'typ': 'JWT'}
        header_json = json.dumps(header, separators=(',', ':')).encode()
        header_b64 = base64.urlsafe_b64encode(header_json).decode().rstrip('=')
        
        payload_json = json.dumps(payload, separators=(',', ':')).encode()
        payload_b64 = base64.urlsafe_b64encode(payload_json).decode().rstrip('=')
        
        # 生成签名
        key = Encryption.get_key()
        to_sign = f"{header_b64}.{payload_b64}".encode()
        signature = hmac.new(key, to_sign, hashlib.sha256).digest()
        signature_b64 = base64.urlsafe_b64encode(signature).decode().rstrip('=')
        
        # 组合令牌
        token = f"{header_b64}.{payload_b64}.{signature_b64}"
        return token
    
    @staticmethod
    def verify_token(token):
        """
        验证令牌
        
        Args:
            token: JWT令牌
            
        Returns:
            dict: 解码后的载荷，如果令牌无效则返回None
        """
        import json
        import time
        
        try:
            # 分解令牌
            header_b64, payload_b64, signature_b64 = token.split('.')
            
            # 验证签名
            key = Encryption.get_key()
            to_verify = f"{header_b64}.{payload_b64}".encode()
            signature = base64.urlsafe_b64decode(signature_b64 + '=' * (4 - len(signature_b64) % 4))
            calculated_signature = hmac.new(key, to_verify, hashlib.sha256).digest()
            
            if not hmac.compare_digest(signature, calculated_signature):
                return None
            
            # 解码载荷
            payload_json = base64.urlsafe_b64decode(payload_b64 + '=' * (4 - len(payload_b64) % 4))
            payload = json.loads(payload_json)
            
            # 检查过期时间
            if payload.get('exp', 0) < time.time():
                return None
            
            return payload
        except Exception as e:
            logger.error(f"验证令牌失败: {str(e)}")
            return None

class CryptoManager:
    """加密管理器"""
    
    def __init__(self):
        self._symmetric_key = None
        self._private_key = None
        self._public_key = None
        
    def generate_symmetric_key(self) -> bytes:
        """生成对称加密密钥"""
        return Fernet.generate_key()
    
    def set_symmetric_key(self, key: bytes):
        """设置对称加密密钥"""
        self._symmetric_key = key
    
    def encrypt_symmetric(self, data: bytes, key: Optional[bytes] = None) -> bytes:
        """对称加密"""
        try:
            key_to_use = key or self._symmetric_key
            if not key_to_use:
                raise ValueError("未设置加密密钥")
            
            fernet = Fernet(key_to_use)
            return fernet.encrypt(data)
        except Exception as e:
            logger.error(f"对称加密失败: {e}")
            raise
    
    def decrypt_symmetric(self, encrypted_data: bytes, key: Optional[bytes] = None) -> bytes:
        """对称解密"""
        try:
            key_to_use = key or self._symmetric_key
            if not key_to_use:
                raise ValueError("未设置解密密钥")
            
            fernet = Fernet(key_to_use)
            return fernet.decrypt(encrypted_data)
        except Exception as e:
            logger.error(f"对称解密失败: {e}")
            raise
    
    def generate_rsa_key_pair(self, key_size: int = 2048) -> Tuple[bytes, bytes]:
        """生成RSA密钥对"""
        try:
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=key_size,
            )
            
            # 序列化私钥
            private_pem = private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )
            
            # 序列化公钥
            public_key = private_key.public_key()
            public_pem = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
            return private_pem, public_pem
        except Exception as e:
            logger.error(f"生成RSA密钥对失败: {e}")
            raise
    
    def encrypt_rsa(self, data: bytes, public_key_pem: bytes) -> bytes:
        """RSA加密"""
        try:
            public_key = serialization.load_pem_public_key(public_key_pem)
            
            encrypted = public_key.encrypt(
                data,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            return encrypted
        except Exception as e:
            logger.error(f"RSA加密失败: {e}")
            raise
    
    def decrypt_rsa(self, encrypted_data: bytes, private_key_pem: bytes) -> bytes:
        """RSA解密"""
        try:
            private_key = serialization.load_pem_private_key(
                private_key_pem, 
                password=None
            )
            
            decrypted = private_key.decrypt(
                encrypted_data,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            return decrypted
        except Exception as e:
            logger.error(f"RSA解密失败: {e}")
            raise
    
    def hash_password(self, password: str, salt: Optional[bytes] = None) -> Tuple[bytes, bytes]:
        """密码散列"""
        try:
            if salt is None:
                salt = os.urandom(32)
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = kdf.derive(password.encode('utf-8'))
            return key, salt
        except Exception as e:
            logger.error(f"密码散列失败: {e}")
            raise
    
    def verify_password(self, password: str, hashed_password: bytes, salt: bytes) -> bool:
        """验证密码"""
        try:
            key, _ = self.hash_password(password, salt)
            return hmac.compare_digest(key, hashed_password)
        except Exception as e:
            logger.error(f"密码验证失败: {e}")
            return False
    
    def calculate_file_hash(self, file_path: str, algorithm: str = 'sha256') -> str:
        """计算文件散列值"""
        try:
            hash_obj = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
        except Exception as e:
            logger.error(f"计算文件散列失败: {e}")
            raise
    
    def encode_base64(self, data: bytes) -> str:
        """Base64编码"""
        return base64.b64encode(data).decode('utf-8')
    
    def decode_base64(self, encoded_data: str) -> bytes:
        """Base64解码"""
        return base64.b64decode(encoded_data.encode('utf-8'))

# 全局加密管理器实例
crypto_manager = CryptoManager()

# 便捷函数
def encrypt_data(data: bytes, key: Optional[bytes] = None) -> bytes:
    """加密数据"""
    return crypto_manager.encrypt_symmetric(data, key)

def decrypt_data(encrypted_data: bytes, key: Optional[bytes] = None) -> bytes:
    """解密数据"""
    return crypto_manager.decrypt_symmetric(encrypted_data, key)

def hash_password(password: str) -> Tuple[bytes, bytes]:
    """散列密码"""
    return crypto_manager.hash_password(password)

def verify_password(password: str, hashed_password: bytes, salt: bytes) -> bool:
    """验证密码"""
    return crypto_manager.verify_password(password, hashed_password, salt)

def calculate_file_hash(file_path: str) -> str:
    """计算文件散列值"""
    return crypto_manager.calculate_file_hash(file_path)

def decrypt_data(key: bytes, encrypted_data_hex: str) -> Optional[str]:
    """Decrypts data that was encrypted with encrypt_data."""
    try:
        encrypted_data = bytes.fromhex(encrypted_data_hex)
        return decrypt_data(encrypted_data, key).decode('utf-8')
    except Exception as e:
        logger.error(f"解密失败: {str(e)}")
        return None 
