from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Any
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from src.omnilink_main.dependencies.db import get_db
from common.models.user import User
from common.schemas.device_schema import Device as DeviceSchema, DeviceConnectionRequest, DeviceConnectionResponse
from common.schemas.api_schema import APIResponse
from src.omnilink_main.services.device_service import device_service_instance
from src.omnilink_main.services.connection_control_service import connection_control_service
from src.omnilink_main.dependencies.auth import get_current_active_user
from src.omnilink_main.core.exceptions import DeviceNotAvailableError, PermissionDeniedError, SlaveServerOfflineError

router = APIRouter()

class DeviceConnectionRequestModel(BaseModel):
    """设备连接请求模型"""
    user_contact: str = ""

@router.get("/", response_model=APIResponse[List[DeviceSchema]])
async def read_devices(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Retrieve devices available to the current user based on their permissions.
    """
    devices = await device_service_instance.get_devices_for_user(db=db, user=current_user)
    return APIResponse(data=devices)

@router.get("/available", response_model=APIResponse[List[DeviceSchema]])
async def read_available_devices(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Get available devices for the current user (专属客户端使用).
    """
    devices = await device_service_instance.get_available_devices_for_user(db=db, user=current_user)
    return APIResponse(data=devices)

@router.get("/{device_id}", response_model=APIResponse[DeviceSchema])
async def read_device(
    device_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Get a specific device by ID, if the user has permission to view it.
    """
    device = await device_service_instance.get_by_id(db, id=device_id)
    if not device:
        raise HTTPException(status_code=404, detail="Device not found")
    
    # 检查用户权限
    user_devices = await device_service_instance.get_devices_for_user(db=db, user=current_user)
    if device.id not in [d.id for d in user_devices]:
        raise HTTPException(status_code=403, detail="Permission denied")
    
    return APIResponse(data=device)

@router.get("/{device_id}/status", response_model=APIResponse[dict])
async def get_device_status(
    device_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Get device status (专属客户端使用).
    """
    device = await device_service_instance.get_by_id(db, id=device_id)
    if not device:
        raise HTTPException(status_code=404, detail="Device not found")
    
    # 获取连接信息
    connection_info = connection_control_service.get_connection_info(device_id)
    
    status_data = {
        "device_id": device_id,
        "status": device.status,
        "current_user_id": device.current_user_id,
        "current_user_contact": device.current_user_contact,
        "connected_at": device.connected_at.isoformat() if device.connected_at else None,
        "last_user_id": device.last_user_id,
        "last_user_contact": device.last_user_contact,
        "last_used_at": device.last_used_at.isoformat() if device.last_used_at else None,
        "is_connected": connection_info is not None,
        "connection_info": connection_info
    }
    
    return APIResponse(data=status_data)

@router.post("/{device_id}/connect", response_model=APIResponse[DeviceConnectionResponse])
async def connect_to_device(
    device_id: int,
    request_data: DeviceConnectionRequestModel,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Request to connect to a device (专属客户端使用).
    """
    try:
        # 使用用户联系方式，如果没有提供则使用用户邮箱
        user_contact = request_data.user_contact or current_user.email or current_user.phone or ""
        
        connection_response = await connection_control_service.request_device_connection(
            device_id=device_id,
            user_id=current_user.id,
            user_contact=user_contact,
            db=db
        )
        
        return APIResponse(data=connection_response)
        
    except DeviceNotAvailableError as e:
        raise HTTPException(status_code=409, detail=str(e))
    except PermissionDeniedError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except SlaveServerOfflineError as e:
        raise HTTPException(status_code=503, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Connection failed: {str(e)}")

@router.post("/{device_id}/disconnect", response_model=APIResponse[dict])
async def disconnect_from_device(
    device_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Request to disconnect from a device (专属客户端使用).
    """
    try:
        success = await connection_control_service.disconnect_device(
            device_id=device_id,
            user_id=current_user.id,
            db=db,
            reason="user_requested"
        )
        
        if success:
            return APIResponse(data={"message": "Device disconnected successfully"})
        else:
            raise HTTPException(status_code=400, detail="Failed to disconnect device")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Disconnection failed: {str(e)}")

# 管理员专用端点
@router.get("/admin/all", response_model=APIResponse[List[DeviceSchema]])
async def read_all_devices(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Get all devices (管理员视角).
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    devices = await device_service_instance.get_all(db)
    return APIResponse(data=devices)

@router.post("/admin/{device_id}/force-disconnect", response_model=APIResponse[dict])
async def force_disconnect_device(
    device_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Force disconnect a device (管理员功能).
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    try:
        success = await connection_control_service.disconnect_device(
            device_id=device_id,
            user_id=current_user.id,
            db=db,
            reason="admin_forced"
        )
        
        if success:
            return APIResponse(data={"message": "Device force disconnected successfully"})
        else:
            raise HTTPException(status_code=400, detail="Failed to force disconnect device")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Force disconnection failed: {str(e)}")