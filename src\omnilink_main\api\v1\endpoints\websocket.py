from fastapi import APIRouter, WebSocket, Depends, WebSocketDisconnect, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
import logging
from typing import Optional

from src.omnilink_main.communication.ws_manager import ws_manager
from src.omnilink_main.dependencies.db import get_db
from src.omnilink_main.services import slave_server_service
from common.models.slave_server import SlaveServer

logger = logging.getLogger(__name__)
router = APIRouter()

async def get_slave_from_token(token: str, db: AsyncSession) -> Optional[dict]:
    # The token from the slave is its API_KEY
    slave = await slave_server_service.get_by_api_key(db, api_key=token)
    if not slave:
        return None
    # Return a dict to avoid passing the whole ORM object around
    return {"id": str(slave.id), "server_id": slave.server_id}


@router.websocket("/ws/slave/{token}")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str,
    db: AsyncSession = Depends(get_db)
):
    """
    The WebSocket endpoint for slave servers to connect to.
    The token is the slave's unique API Key.
    """
    slave_info = await get_slave_from_token(token, db)
    if not slave_info:
        logger.warning(f"WebSocket connection rejected for invalid API key token.")
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return
    
    slave_id = slave_info["id"]
    await ws_manager.connect(slave_id, websocket)
    try:
        while True:
            # We keep the connection open and listen for any potential messages from the slave.
            # The slave's command_handler doesn't send messages, but this is good practice.
            data = await websocket.receive_text()
            logger.info(f"Received message from slave {slave_id}: {data}")
    except WebSocketDisconnect:
        ws_manager.disconnect(slave_id)
    except Exception as e:
        logger.error(f"Error in WebSocket connection for slave {slave_id}: {e}", exc_info=True)
        ws_manager.disconnect(slave_id)
        # Ensure the websocket is closed if an error occurs
        if not websocket.client_state == 'DISCONNECTED':
             await websocket.close(code=status.WS_1011_INTERNAL_ERROR) 