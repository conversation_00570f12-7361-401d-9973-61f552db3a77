"""
OmniLink全联通系统 - 通用常量定义
包含所有系统级别的枚举和常量
"""

from enum import Enum, IntEnum
from typing import Dict, Any

class UserAction(Enum):
    """用户操作类型"""
    LOGIN = "login"
    LOGOUT = "logout"
    ACCESS_DEVICE = "access_device"
    DISCONNECT_DEVICE = "disconnect_device"
    CREATE_GROUP = "create_group"
    MODIFY_GROUP = "modify_group"
    DELETE_GROUP = "delete_group"
    CREATE_USER = "create_user"
    MODIFY_USER = "modify_user"
    DELETE_USER = "delete_user"
    CHANGE_PERMISSION = "change_permission"

class UserRole(IntEnum):
    """用户角色级别"""
    GUEST = 0
    USER = 1
    OPERATOR = 2
    ADMIN = 3
    SUPER_ADMIN = 4

class DeviceType(Enum):
    """设备类型"""
    USB_STORAGE = "usb_storage"
    USB_PRINTER = "usb_printer"
    USB_WEBCAM = "usb_webcam"
    USB_KEYBOARD = "usb_keyboard"
    USB_MOUSE = "usb_mouse"
    USB_AUDIO = "usb_audio"
    USB_HID = "usb_hid"
    USB_SERIAL = "usb_serial"
    USB_GENERIC = "usb_generic"

class GroupType(Enum):
    """设备组类型"""
    DEPARTMENT = "department"
    PROJECT = "project"
    LOCATION = "location"
    CUSTOM = "custom"

class SlaveStatus(Enum):
    """从服务器状态"""
    ONLINE = "online"
    OFFLINE = "offline"
    CONNECTING = "connecting"
    ERROR = "error"
    MAINTENANCE = "maintenance"

class LogLevel(IntEnum):
    """日志级别"""
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40
    CRITICAL = 50

class ResourceType(Enum):
    """资源类型"""
    DEVICE = "device"
    GROUP = "group"
    USER = "user"
    SLAVE_SERVER = "slave_server"
    SYSTEM = "system"

class ActionType(Enum):
    """操作类型"""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    EXECUTE = "execute"

class LogResult(Enum):
    """操作结果"""
    SUCCESS = "success"
    FAILURE = "failure"
    PARTIAL = "partial"
    PENDING = "pending"

# 系统常量
SYSTEM_CONSTANTS = {
    "MAX_LOGIN_ATTEMPTS": 5,
    "SESSION_TIMEOUT": 3600,  # 1小时
    "DEVICE_DISCOVERY_INTERVAL": 30,  # 30秒
    "HEARTBEAT_INTERVAL": 60,  # 1分钟
    "MAX_CONCURRENT_DEVICES": 50,
    "DEFAULT_PAGE_SIZE": 20,
    "MAX_PAGE_SIZE": 100,
    "CACHE_TTL": 300,  # 5分钟
    "MAX_FILE_SIZE": 1024 * 1024 * 100,  # 100MB
    "SUPPORTED_LANGUAGES": ["zh-CN"],
    "DEFAULT_TIMEZONE": "Asia/Shanghai"
}

# 错误代码
ERROR_CODES = {
    "AUTH_FAILED": 1001,
    "PERMISSION_DENIED": 1002,
    "DEVICE_NOT_FOUND": 2001,
    "DEVICE_BUSY": 2002,
    "CONNECTION_FAILED": 3001,
    "NETWORK_ERROR": 3002,
    "INVALID_REQUEST": 4001,
    "INVALID_PARAMETER": 4002,
    "INTERNAL_ERROR": 5001,
    "SERVICE_UNAVAILABLE": 5002
}

# HTTP状态码映射
HTTP_STATUS_MESSAGES = {
    200: "请求成功",
    201: "创建成功",
    400: "请求参数错误",
    401: "身份验证失败",
    403: "权限不足",
    404: "资源不存在",
    409: "资源冲突",
    500: "服务器内部错误",
    503: "服务不可用"
}

def get_user_role_name(role: UserRole) -> str:
    """获取用户角色显示名称"""
    role_names = {
        UserRole.GUEST: "访客",
        UserRole.USER: "普通用户",
        UserRole.OPERATOR: "操作员",
        UserRole.ADMIN: "管理员",
        UserRole.SUPER_ADMIN: "超级管理员"
    }
    return role_names.get(role, "未知角色")

def get_device_type_name(device_type: DeviceType) -> str:
    """获取设备类型显示名称"""
    type_names = {
        DeviceType.USB_STORAGE: "USB存储设备",
        DeviceType.USB_PRINTER: "USB打印机",
        DeviceType.USB_WEBCAM: "USB摄像头",
        DeviceType.USB_KEYBOARD: "USB键盘",
        DeviceType.USB_MOUSE: "USB鼠标",
        DeviceType.USB_AUDIO: "USB音频设备",
        DeviceType.USB_HID: "USB人机接口设备",
        DeviceType.USB_SERIAL: "USB串口设备",
        DeviceType.USB_GENERIC: "USB通用设备"
    }
    return type_names.get(device_type, "未知设备")

def get_error_message(error_code: int) -> str:
    """根据错误代码获取错误消息"""
    error_messages = {
        1001: "身份验证失败",
        1002: "权限不足",
        2001: "设备不存在",
        2002: "设备正在使用中",
        3001: "连接失败",
        3002: "网络错误",
        4001: "请求格式错误",
        4002: "请求参数无效",
        5001: "服务器内部错误",
        5002: "服务暂时不可用"
    }
    return error_messages.get(error_code, f"未知错误（代码：{error_code}）")

# ... (其他可能存在的常量) 
