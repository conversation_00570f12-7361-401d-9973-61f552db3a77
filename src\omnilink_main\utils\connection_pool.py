"""
连接池模块

提供设备连接管理，跟踪和限制用户与USB设备的连接。
此模块维护活跃连接，确保资源合理分配，避免设备冲突。
支持连接限制、超时管理和连接状态监控。
"""
import os
import threading
import logging
import time
import json
import uuid
from enum import Enum
from typing import Dict, List, Set, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger('connection_pool')

class ConnectionState(Enum):
    """连接状态枚举"""
    INITIALIZING = "initializing"  # 初始化中
    ACTIVE = "active"              # 活跃中
    IDLE = "idle"                  # 空闲中
    DISCONNECTING = "disconnecting"  # 断开中
    CLOSED = "closed"              # 已关闭
    ERROR = "error"                # 错误状态

class ConnectionType(Enum):
    """连接类型枚举"""
    USB = "usb"          # USB设备连接
    VIRTUALHERE = "virtualhere"  # VirtualHere专用连接
    MAINTENANCE = "maintenance"  # 维护连接
    OTHER = "other"      # 其他类型连接

class ConnectionInfo:
    """连接信息类"""
    
    def __init__(self, connection_id: str, user_id: str, device_id: str, 
                 server_id: str, connection_type: ConnectionType, port: int):
        """
        初始化连接信息
        
        参数:
            connection_id: 连接唯一标识
            user_id: 用户ID
            device_id: 设备ID
            server_id: 服务器ID
            connection_type: 连接类型
            port: 使用的端口
        """
        self.connection_id = connection_id
        self.user_id = user_id
        self.device_id = device_id
        self.server_id = server_id
        self.port = port
        self.connection_type = connection_type
        self.state = ConnectionState.INITIALIZING
        self.created_at = time.time()
        self.last_activity = time.time()
        self.closed_at = None
        self.error = None
        self.metadata = {}  # 附加元数据
        
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        返回:
            Dict[str, Any]: 连接信息字典
        """
        return {
            'connection_id': self.connection_id,
            'user_id': self.user_id,
            'device_id': self.device_id,
            'server_id': self.server_id,
            'port': self.port,
            'connection_type': self.connection_type.value,
            'state': self.state.value,
            'created_at': self.created_at,
            'last_activity': self.last_activity,
            'closed_at': self.closed_at,
            'uptime': time.time() - self.created_at if self.state != ConnectionState.CLOSED else 
                      (self.closed_at or time.time()) - self.created_at,
            'error': self.error,
            'metadata': self.metadata
        }
    
    def update_activity(self) -> None:
        """更新活动时间"""
        self.last_activity = time.time()
    
    def set_state(self, state: ConnectionState, error: str = None) -> None:
        """
        设置连接状态
        
        参数:
            state: 新状态
            error: 错误信息（如有）
        """
        self.state = state
        if state == ConnectionState.ERROR and error:
            self.error = error
        if state == ConnectionState.CLOSED:
            self.closed_at = time.time()
    
    def set_metadata(self, key: str, value: Any) -> None:
        """
        设置元数据
        
        参数:
            key: 元数据键
            value: 元数据值
        """
        self.metadata[key] = value
    
    def is_idle(self, idle_timeout: int = 300) -> bool:
        """
        检查连接是否空闲
        
        参数:
            idle_timeout: 空闲超时时间（秒）
            
        返回:
            bool: 是否空闲
        """
        return (time.time() - self.last_activity) > idle_timeout
    
    def is_expired(self, max_lifetime: int = 21600) -> bool:
        """
        检查连接是否过期
        
        参数:
            max_lifetime: 最大生命周期（秒），默认6小时
            
        返回:
            bool: 是否过期
        """
        return (time.time() - self.created_at) > max_lifetime

class ConnectionPool:
    """连接池类，管理所有设备连接"""
    
    def __init__(self, storage_path: str = None, 
                 max_connections_per_user: int = 4,
                 max_connections_per_device: int = 1,
                 idle_timeout: int = 300,
                 max_lifetime: int = 21600):
        """
        初始化连接池
        
        参数:
            storage_path: 持久化存储路径
            max_connections_per_user: 每个用户最大连接数
            max_connections_per_device: 每个设备最大连接数
            idle_timeout: 空闲超时时间（秒）
            max_lifetime: 最大生命周期（秒）
        """
        self.connections: Dict[str, ConnectionInfo] = {}
        self.user_connections: Dict[str, Set[str]] = {}  # 用户ID -> 连接ID集合
        self.device_connections: Dict[str, Set[str]] = {}  # 设备ID -> 连接ID集合
        self.server_connections: Dict[str, Set[str]] = {}  # 服务器ID -> 连接ID集合
        
        self.max_connections_per_user = max_connections_per_user
        self.max_connections_per_device = max_connections_per_device
        self.idle_timeout = idle_timeout
        self.max_lifetime = max_lifetime
        
        self.lock = threading.RLock()
        self.storage_path = storage_path or os.path.expanduser("~/.ky/connection_pool.json")
        
        # 确保存储目录存在
        os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
        
        # 加载持久化连接
        self._load_connections()
        
        # 启动清理线程
        self.cleaner_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleaner_thread.start()
        
        logger.info(f"连接池初始化完成，当前有 {len(self.connections)} 个活跃连接")
    
    def _load_connections(self) -> None:
        """从存储中加载连接"""
        if not os.path.exists(self.storage_path):
            return
            
        try:
            with open(self.storage_path, 'r') as f:
                data = json.load(f)
                
            if not isinstance(data, dict) or 'connections' not in data:
                logger.warning("连接池存储文件格式无效")
                return
                
            loaded = 0
            for conn_data in data['connections']:
                try:
                    # 只恢复活跃或空闲连接
                    state = conn_data.get('state')
                    if state not in ('active', 'idle'):
                        continue
                        
                    # 检查是否过期
                    created_at = conn_data.get('created_at', 0)
                    last_activity = conn_data.get('last_activity', 0)
                    
                    if time.time() - created_at > self.max_lifetime:
                        logger.debug(f"跳过已过期连接: {conn_data.get('connection_id')}")
                        continue
                        
                    if time.time() - last_activity > self.idle_timeout:
                        logger.debug(f"跳过空闲连接: {conn_data.get('connection_id')}")
                        continue
                    
                    # 创建连接对象
                    connection = ConnectionInfo(
                        connection_id=conn_data.get('connection_id'),
                        user_id=conn_data.get('user_id'),
                        device_id=conn_data.get('device_id'),
                        server_id=conn_data.get('server_id'),
                        connection_type=ConnectionType(conn_data.get('connection_type', 'other')),
                        port=conn_data.get('port', 0)
                    )
                    
                    # 恢复状态和元数据
                    connection.state = ConnectionState(state)
                    connection.created_at = created_at
                    connection.last_activity = last_activity
                    connection.metadata = conn_data.get('metadata', {})
                    
                    # 添加到连接池
                    self.connections[connection.connection_id] = connection
                    
                    # 更新索引
                    self._update_indices(connection)
                    
                    loaded += 1
                except Exception as e:
                    logger.error(f"加载连接出错: {str(e)}")
            
            logger.info(f"已从存储中加载 {loaded} 个有效连接")
        except Exception as e:
            logger.error(f"加载连接池状态失败: {str(e)}")
    
    def _save_connections(self) -> None:
        """保存连接到存储"""
        try:
            # 只保存活跃和空闲连接
            active_connections = []
            for conn in self.connections.values():
                if conn.state in (ConnectionState.ACTIVE, ConnectionState.IDLE):
                    active_connections.append(conn.to_dict())
            
            with open(self.storage_path, 'w') as f:
                json.dump({
                    'connections': active_connections,
                    'updated_at': time.time()
                }, f, indent=2)
                
            logger.debug(f"已保存 {len(active_connections)} 个连接到存储")
        except Exception as e:
            logger.error(f"保存连接池状态失败: {str(e)}")
    
    def _update_indices(self, connection: ConnectionInfo) -> None:
        """
        更新索引
        
        参数:
            connection: 连接信息
        """
        # 更新用户索引
        if connection.user_id:
            if connection.user_id not in self.user_connections:
                self.user_connections[connection.user_id] = set()
            self.user_connections[connection.user_id].add(connection.connection_id)
        
        # 更新设备索引
        if connection.device_id:
            if connection.device_id not in self.device_connections:
                self.device_connections[connection.device_id] = set()
            self.device_connections[connection.device_id].add(connection.connection_id)
        
        # 更新服务器索引
        if connection.server_id:
            if connection.server_id not in self.server_connections:
                self.server_connections[connection.server_id] = set()
            self.server_connections[connection.server_id].add(connection.connection_id)
    
    def _remove_from_indices(self, connection: ConnectionInfo) -> None:
        """
        从索引中移除连接
        
        参数:
            connection: 连接信息
        """
        # 从用户索引移除
        if connection.user_id and connection.user_id in self.user_connections:
            self.user_connections[connection.user_id].discard(connection.connection_id)
            if not self.user_connections[connection.user_id]:
                del self.user_connections[connection.user_id]
        
        # 从设备索引移除
        if connection.device_id and connection.device_id in self.device_connections:
            self.device_connections[connection.device_id].discard(connection.connection_id)
            if not self.device_connections[connection.device_id]:
                del self.device_connections[connection.device_id]
        
        # 从服务器索引移除
        if connection.server_id and connection.server_id in self.server_connections:
            self.server_connections[connection.server_id].discard(connection.connection_id)
            if not self.server_connections[connection.server_id]:
                del self.server_connections[connection.server_id]
    
    def create_connection(self, user_id: str, device_id: str, server_id: str, 
                         connection_type: ConnectionType, port: int) -> Optional[ConnectionInfo]:
        """
        创建新连接
        
        参数:
            user_id: 用户ID
            device_id: 设备ID
            server_id: 服务器ID
            connection_type: 连接类型
            port: 使用的端口
            
        返回:
            Optional[ConnectionInfo]: 创建的连接信息，如果无法创建则返回None
        """
        with self.lock:
            # 检查用户连接限制
            user_conn_count = len(self.user_connections.get(user_id, set()))
            if user_conn_count >= self.max_connections_per_user:
                logger.warning(f"用户 {user_id} 已达最大连接数 {self.max_connections_per_user}")
                return None
            
            # 检查设备连接限制
            device_conn_count = len(self.device_connections.get(device_id, set()))
            if device_conn_count >= self.max_connections_per_device:
                logger.warning(f"设备 {device_id} 已被占用，当前连接数 {device_conn_count}")
                return None
            
            # 创建连接ID
            connection_id = str(uuid.uuid4())
            
            # 创建连接信息
            connection = ConnectionInfo(
                connection_id=connection_id,
                user_id=user_id,
                device_id=device_id,
                server_id=server_id,
                connection_type=connection_type,
                port=port
            )
            
            # 设置为活跃状态
            connection.set_state(ConnectionState.ACTIVE)
            
            # 添加到连接池
            self.connections[connection_id] = connection
            
            # 更新索引
            self._update_indices(connection)
            
            # 保存连接状态
            self._save_connections()
            
            logger.info(f"创建连接: user={user_id}, device={device_id}, server={server_id}, port={port}")
            return connection
    
    def get_connection(self, connection_id: str) -> Optional[ConnectionInfo]:
        """
        获取连接信息
        
        参数:
            connection_id: 连接ID
            
        返回:
            Optional[ConnectionInfo]: 连接信息，不存在则返回None
        """
        with self.lock:
            connection = self.connections.get(connection_id)
            if connection:
                # 更新活动时间
                connection.update_activity()
            return connection
    
    def update_connection_state(self, connection_id: str, state: ConnectionState, 
                               error: str = None) -> bool:
        """
        更新连接状态
        
        参数:
            connection_id: 连接ID
            state: 新状态
            error: 错误信息（如有）
            
        返回:
            bool: 是否成功更新
        """
        with self.lock:
            connection = self.connections.get(connection_id)
            if not connection:
                logger.warning(f"未找到连接: {connection_id}")
                return False
            
            # 更新状态和活动时间
            connection.set_state(state, error)
            connection.update_activity()
            
            # 如果连接已关闭，从索引中移除
            if state == ConnectionState.CLOSED:
                self._remove_from_indices(connection)
            
            # 保存连接状态
            self._save_connections()
            
            logger.info(f"更新连接状态: {connection_id} -> {state.value}")
            return True
    
    def close_connection(self, connection_id: str, reason: str = None) -> bool:
        """
        关闭连接
        
        参数:
            connection_id: 连接ID
            reason: 关闭原因
            
        返回:
            bool: 是否成功关闭
        """
        with self.lock:
            connection = self.connections.get(connection_id)
            if not connection:
                logger.warning(f"未找到连接: {connection_id}")
                return False
            
            # 更新状态
            connection.set_state(ConnectionState.CLOSED)
            if reason:
                connection.set_metadata('close_reason', reason)
            
            # 从索引中移除
            self._remove_from_indices(connection)
            
            # 保存连接状态
            self._save_connections()
            
            logger.info(f"关闭连接: {connection_id}, 原因: {reason or '未指定'}")
            return True
    
    def get_user_connections(self, user_id: str) -> List[ConnectionInfo]:
        """
        获取用户的所有连接
        
        参数:
            user_id: 用户ID
            
        返回:
            List[ConnectionInfo]: 连接列表
        """
        with self.lock:
            conn_ids = self.user_connections.get(user_id, set())
            return [self.connections[conn_id] for conn_id in conn_ids 
                   if conn_id in self.connections]
    
    def get_device_connections(self, device_id: str) -> List[ConnectionInfo]:
        """
        获取设备的所有连接
        
        参数:
            device_id: 设备ID
            
        返回:
            List[ConnectionInfo]: 连接列表
        """
        with self.lock:
            conn_ids = self.device_connections.get(device_id, set())
            return [self.connections[conn_id] for conn_id in conn_ids 
                   if conn_id in self.connections]
    
    def get_server_connections(self, server_id: str) -> List[ConnectionInfo]:
        """
        获取服务器的所有连接
        
        参数:
            server_id: 服务器ID
            
        返回:
            List[ConnectionInfo]: 连接列表
        """
        with self.lock:
            conn_ids = self.server_connections.get(server_id, set())
            return [self.connections[conn_id] for conn_id in conn_ids 
                   if conn_id in self.connections]
    
    def get_all_connections(self) -> List[ConnectionInfo]:
        """
        获取所有连接
        
        返回:
            List[ConnectionInfo]: 连接列表
        """
        with self.lock:
            return list(self.connections.values())
    
    def is_device_available(self, device_id: str) -> bool:
        """
        检查设备是否可用（未被占用）
        
        参数:
            device_id: 设备ID
            
        返回:
            bool: 设备是否可用
        """
        with self.lock:
            # 检查设备是否有活跃连接
            conn_ids = self.device_connections.get(device_id, set())
            for conn_id in conn_ids:
                if conn_id in self.connections:
                    conn = self.connections[conn_id]
                    if conn.state in (ConnectionState.ACTIVE, ConnectionState.INITIALIZING):
                        return False
            return True
    
    def cleanup_connections(self) -> Tuple[int, int, int]:
        """
        清理过期和空闲连接
        
        返回:
            Tuple[int, int, int]: (清理的空闲连接数, 清理的过期连接数, 清理的错误连接数)
        """
        with self.lock:
            idle_closed = 0
            expired_closed = 0
            error_closed = 0
            
            # 找出要清理的连接
            to_close = []
            for conn_id, conn in list(self.connections.items()):
                # 检查是否已关闭
                if conn.state == ConnectionState.CLOSED:
                    to_close.append((conn_id, "already_closed"))
                    continue
                
                # 检查错误状态
                if conn.state == ConnectionState.ERROR:
                    to_close.append((conn_id, "error"))
                    error_closed += 1
                    continue
                
                # 检查过期
                if conn.is_expired(self.max_lifetime):
                    to_close.append((conn_id, "expired"))
                    expired_closed += 1
                    continue
                
                # 检查空闲
                if conn.state == ConnectionState.ACTIVE and conn.is_idle(self.idle_timeout):
                    to_close.append((conn_id, "idle"))
                    idle_closed += 1
                    continue
            
            # 关闭连接
            for conn_id, reason in to_close:
                self.close_connection(conn_id, reason)
            
            # 移除已关闭的连接
            closed_count = 0
            for conn_id, conn in list(self.connections.items()):
                if conn.state == ConnectionState.CLOSED:
                    del self.connections[conn_id]
                    closed_count += 1
            
            if closed_count > 0:
                logger.info(f"清理了 {closed_count} 个已关闭连接")
            
            # 保存连接状态
            if to_close:
                self._save_connections()
            
            return idle_closed, expired_closed, error_closed
    
    def _cleanup_loop(self) -> None:
        """连接清理循环"""
        while True:
            try:
                # 每隔一段时间清理连接
                time.sleep(60)  # 一分钟清理一次
                
                idle, expired, error = self.cleanup_connections()
                if idle or expired or error:
                    logger.info(f"连接清理: {idle}空闲, {expired}过期, {error}错误")
            except Exception as e:
                logger.error(f"连接清理异常: {str(e)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取连接池统计信息
        
        返回:
            Dict[str, Any]: 统计信息
        """
        with self.lock:
            active_count = 0
            idle_count = 0
            error_count = 0
            
            for conn in self.connections.values():
                if conn.state == ConnectionState.ACTIVE:
                    active_count += 1
                elif conn.state == ConnectionState.IDLE:
                    idle_count += 1
                elif conn.state == ConnectionState.ERROR:
                    error_count += 1
            
            return {
                "total_connections": len(self.connections),
                "active_connections": active_count,
                "idle_connections": idle_count,
                "error_connections": error_count,
                "user_count": len(self.user_connections),
                "device_count": len(self.device_connections),
                "server_count": len(self.server_connections),
                "max_connections_per_user": self.max_connections_per_user,
                "max_connections_per_device": self.max_connections_per_device,
                "idle_timeout": self.idle_timeout,
                "max_lifetime": self.max_lifetime
            }


# 单例模式
_connection_pool = None

def get_connection_pool(storage_path: str = None, 
                      max_connections_per_user: int = 4,
                      max_connections_per_device: int = 1,
                      idle_timeout: int = 300,
                      max_lifetime: int = 21600) -> ConnectionPool:
    """
    获取连接池实例（单例模式）
    
    参数:
        storage_path: 持久化存储路径
        max_connections_per_user: 每个用户最大连接数
        max_connections_per_device: 每个设备最大连接数
        idle_timeout: 空闲超时时间（秒）
        max_lifetime: 最大生命周期（秒）
        
    返回:
        ConnectionPool: 连接池实例
    """
    global _connection_pool
    if _connection_pool is None:
        _connection_pool = ConnectionPool(
            storage_path=storage_path,
            max_connections_per_user=max_connections_per_user,
            max_connections_per_device=max_connections_per_device,
            idle_timeout=idle_timeout,
            max_lifetime=max_lifetime
        )
    return _connection_pool 
