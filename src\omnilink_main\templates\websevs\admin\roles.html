<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理 - Web管理服务</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .role-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .role-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .role-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--light-color);
        }
        
        .role-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .role-description {
            color: var(--gray-color);
            margin-bottom: 1rem;
        }
        
        .role-meta {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        
        .role-meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .permissions-section h4 {
            margin-bottom: 1rem;
            color: var(--dark-color);
        }
        
        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .permission-item {
            background-color: var(--light-color);
            border-radius: var(--border-radius);
            padding: 0.8rem;
            font-size: 0.9rem;
        }
        
        .permission-item.active {
            background-color: rgba(52, 168, 83, 0.1);
            border-left: 3px solid var(--secondary-color);
        }
        
        .search-bar {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .search-input {
            flex: 1;
            padding: 0.6rem 1rem;
            border: 1px solid var(--gray-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
        }
        
        .btn {
            padding: 0.6rem 1.2rem;
            border-radius: var(--border-radius);
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: background-color 0.2s;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #3367d6;
        }
        
        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outline:hover {
            background-color: rgba(66, 133, 244, 0.1);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #d32f2f;
        }
        
        .btn-warning {
            background-color: var(--tertiary-color);
            color: var(--dark-color);
        }
        
        .btn-warning:hover {
            background-color: #f2a600;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.8rem;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .modal-content {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--light-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-header h3 {
            margin: 0;
            color: var(--primary-color);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--gray-color);
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .modal-footer {
            padding: 1.5rem;
            border-top: 1px solid var(--light-color);
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-control {
            width: 100%;
            padding: 0.6rem;
            border: 1px solid var(--gray-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        
        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }
        
        .checkbox-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
            margin-top: 0.5rem;
            max-height: 200px;
            overflow-y: auto;
            padding: 0.5rem;
            border: 1px solid var(--light-color);
            border-radius: var(--border-radius);
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .badge-primary {
            background-color: rgba(66, 133, 244, 0.1);
            color: var(--primary-color);
        }
        
        .badge-count {
            background-color: var(--light-color);
            color: var(--dark-color);
            padding: 0.2rem 0.5rem;
            border-radius: 1rem;
            margin-left: 0.5rem;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <header>
        <h1>角色管理</h1>
        <nav>
            <ul>
                <li><a href="{{ url_for('websevs.admin.index') }}">首页</a></li>
                <li><a href="{{ url_for('websevs.admin.users') }}">用户管理</a></li>
                <li><a href="{{ url_for('websevs.admin.roles') }}">角色管理</a></li>
                <li><a href="{{ url_for('websevs.admin.permissions') }}">权限管理</a></li>
                <li><a href="{{ url_for('websevs.admin.logs') }}">系统日志</a></li>
                <li><a href="{{ url_for('websevs.admin.system') }}">系统配置</a></li>
                <li><a href="{{ url_for('websevs.admin.backup') }}">备份管理</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section>
            <div class="role-header">
                <h2>系统角色 <span class="badge-count">8</span></h2>
                <button class="btn btn-primary" id="createRoleBtn">
                    <span>+</span> 创建新角色
                </button>
            </div>
            
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="搜索角色...">
                <button class="btn btn-outline">搜索</button>
            </div>

            <!-- 角色列表 -->
            <div class="role-card">
                <div class="role-card-header">
                    <div class="role-name">超级管理员</div>
                    <div class="action-buttons">
                        <button class="btn btn-warning edit-role-btn">编辑</button>
                        <button class="btn btn-danger delete-role-btn">删除</button>
                    </div>
                </div>
                <div class="role-description">
                    拥有系统所有权限，可管理所有功能和资源。
                </div>
                <div class="role-meta">
                    <div class="role-meta-item">
                        <span>创建时间:</span>
                        <span>2023-06-20</span>
                    </div>
                    <div class="role-meta-item">
                        <span>用户数量:</span>
                        <span>2</span>
                    </div>
                    <div class="role-meta-item">
                        <span>权限数量:</span>
                        <span>24</span>
                    </div>
                </div>
                <div class="permissions-section">
                    <h4>拥有的权限 <span class="badge-count">24</span></h4>
                    <div class="permissions-grid">
                        <div class="permission-item active">用户管理</div>
                        <div class="permission-item active">角色管理</div>
                        <div class="permission-item active">权限管理</div>
                        <div class="permission-item active">设备管理</div>
                        <div class="permission-item active">系统配置</div>
                        <div class="permission-item active">日志查看</div>
                        <div class="permission-item active">备份管理</div>
                        <div class="permission-item active">服务器管理</div>
                    </div>
                </div>
            </div>

            <div class="role-card">
                <div class="role-card-header">
                    <div class="role-name">普通管理员</div>
                    <div class="action-buttons">
                        <button class="btn btn-warning edit-role-btn">编辑</button>
                        <button class="btn btn-danger delete-role-btn">删除</button>
                    </div>
                </div>
                <div class="role-description">
                    具有部分管理权限，可管理大部分资源，但不能修改系统核心配置。
                </div>
                <div class="role-meta">
                    <div class="role-meta-item">
                        <span>创建时间:</span>
                        <span>2023-06-20</span>
                    </div>
                    <div class="role-meta-item">
                        <span>用户数量:</span>
                        <span>3</span>
                    </div>
                    <div class="role-meta-item">
                        <span>权限数量:</span>
                        <span>18</span>
                    </div>
                </div>
                <div class="permissions-section">
                    <h4>拥有的权限 <span class="badge-count">18</span></h4>
                    <div class="permissions-grid">
                        <div class="permission-item active">用户管理</div>
                        <div class="permission-item">角色管理</div>
                        <div class="permission-item">权限管理</div>
                        <div class="permission-item active">设备管理</div>
                        <div class="permission-item">系统配置</div>
                        <div class="permission-item active">日志查看</div>
                        <div class="permission-item">备份管理</div>
                        <div class="permission-item active">服务器管理</div>
                    </div>
                </div>
            </div>

            <div class="role-card">
                <div class="role-card-header">
                    <div class="role-name">设备管理员</div>
                    <div class="action-buttons">
                        <button class="btn btn-warning edit-role-btn">编辑</button>
                        <button class="btn btn-danger delete-role-btn">删除</button>
                    </div>
                </div>
                <div class="role-description">
                    负责管理设备的接入、配置和共享状态。
                </div>
                <div class="role-meta">
                    <div class="role-meta-item">
                        <span>创建时间:</span>
                        <span>2023-06-21</span>
                    </div>
                    <div class="role-meta-item">
                        <span>用户数量:</span>
                        <span>5</span>
                    </div>
                    <div class="role-meta-item">
                        <span>权限数量:</span>
                        <span>8</span>
                    </div>
                </div>
                <div class="permissions-section">
                    <h4>拥有的权限 <span class="badge-count">8</span></h4>
                    <div class="permissions-grid">
                        <div class="permission-item">用户管理</div>
                        <div class="permission-item">角色管理</div>
                        <div class="permission-item">权限管理</div>
                        <div class="permission-item active">设备管理</div>
                        <div class="permission-item">系统配置</div>
                        <div class="permission-item active">日志查看</div>
                        <div class="permission-item">备份管理</div>
                        <div class="permission-item active">服务器管理</div>
                    </div>
                </div>
            </div>

            <div class="role-card">
                <div class="role-card-header">
                    <div class="role-name">普通用户</div>
                    <div class="action-buttons">
                        <button class="btn btn-warning edit-role-btn">编辑</button>
                        <button class="btn btn-danger delete-role-btn">删除</button>
                    </div>
                </div>
                <div class="role-description">
                    基本用户权限，可以使用共享设备和基本功能。
                </div>
                <div class="role-meta">
                    <div class="role-meta-item">
                        <span>创建时间:</span>
                        <span>2023-06-22</span>
                    </div>
                    <div class="role-meta-item">
                        <span>用户数量:</span>
                        <span>22</span>
                    </div>
                    <div class="role-meta-item">
                        <span>权限数量:</span>
                        <span>4</span>
                    </div>
                </div>
                <div class="permissions-section">
                    <h4>拥有的权限 <span class="badge-count">4</span></h4>
                    <div class="permissions-grid">
                        <div class="permission-item">用户管理</div>
                        <div class="permission-item">角色管理</div>
                        <div class="permission-item">权限管理</div>
                        <div class="permission-item active">设备连接</div>
                        <div class="permission-item">系统配置</div>
                        <div class="permission-item">日志查看</div>
                        <div class="permission-item">备份管理</div>
                        <div class="permission-item">服务器管理</div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 创建角色模态框 -->
    <div class="modal" id="roleModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>创建新角色</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="roleForm">
                    <div class="form-group">
                        <label for="roleName">角色名称</label>
                        <input type="text" id="roleName" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="roleDescription">角色描述</label>
                        <textarea id="roleDescription" class="form-control" required></textarea>
                    </div>
                    <div class="form-group">
                        <label>角色权限</label>
                        <div class="checkbox-list">
                            <div class="checkbox-item">
                                <input type="checkbox" id="perm1" name="permissions[]" value="user_management">
                                <label for="perm1">用户管理</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="perm2" name="permissions[]" value="role_management">
                                <label for="perm2">角色管理</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="perm3" name="permissions[]" value="permission_management">
                                <label for="perm3">权限管理</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="perm4" name="permissions[]" value="device_management">
                                <label for="perm4">设备管理</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="perm5" name="permissions[]" value="system_config">
                                <label for="perm5">系统配置</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="perm6" name="permissions[]" value="log_view">
                                <label for="perm6">日志查看</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="perm7" name="permissions[]" value="backup_management">
                                <label for="perm7">备份管理</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="perm8" name="permissions[]" value="server_management">
                                <label for="perm8">服务器管理</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="perm9" name="permissions[]" value="device_connect">
                                <label for="perm9">设备连接</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="perm10" name="permissions[]" value="profile_edit">
                                <label for="perm10">个人资料编辑</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="perm11" name="permissions[]" value="dashboard_view">
                                <label for="perm11">仪表盘查看</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="perm12" name="permissions[]" value="report_generation">
                                <label for="perm12">报表生成</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline modal-close-btn">取消</button>
                <button class="btn btn-primary" id="saveRoleBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal" id="deleteConfirmModal">
        <div class="modal-content" style="max-width: 400px">
            <div class="modal-header">
                <h3>确认删除</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>您确定要删除此角色吗？此操作不可逆，该角色下的所有用户将失去相应权限。</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline modal-close-btn">取消</button>
                <button class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>

    <footer>
        <p>&copy; 2023 Web管理服务系统 - 版本 1.0.0</p>
    </footer>

    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 创建角色按钮
            const createRoleBtn = document.getElementById('createRoleBtn');
            const roleModal = document.getElementById('roleModal');
            const modalCloseButtons = document.querySelectorAll('.modal-close, .modal-close-btn');
            const saveRoleBtn = document.getElementById('saveRoleBtn');
            
            // 编辑角色按钮
            const editRoleBtns = document.querySelectorAll('.edit-role-btn');
            
            // 删除角色按钮
            const deleteRoleBtns = document.querySelectorAll('.delete-role-btn');
            const deleteConfirmModal = document.getElementById('deleteConfirmModal');
            const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
            
            // 打开创建角色模态框
            createRoleBtn.addEventListener('click', function() {
                document.querySelector('#roleModal .modal-header h3').textContent = '创建新角色';
                document.getElementById('roleForm').reset();
                roleModal.style.display = 'flex';
            });
            
            // 关闭模态框
            modalCloseButtons.forEach(button => {
                button.addEventListener('click', function() {
                    roleModal.style.display = 'none';
                    deleteConfirmModal.style.display = 'none';
                });
            });
            
            // 点击模态框外部区域关闭
            window.addEventListener('click', function(event) {
                if (event.target === roleModal) {
                    roleModal.style.display = 'none';
                }
                if (event.target === deleteConfirmModal) {
                    deleteConfirmModal.style.display = 'none';
                }
            });
            
            // 保存角色
            saveRoleBtn.addEventListener('click', function() {
                const roleName = document.getElementById('roleName').value;
                const roleDescription = document.getElementById('roleDescription').value;
                
                if (!roleName || !roleDescription) {
                    alert('角色名称和描述不能为空');
                    return;
                }
                
                // 收集选中的权限
                const permissions = [];
                document.querySelectorAll('input[name="permissions[]"]:checked').forEach(checkbox => {
                    permissions.push(checkbox.value);
                });
                
                // 这里可以添加AJAX请求保存角色数据
                console.log({
                    name: roleName,
                    description: roleDescription,
                    permissions: permissions
                });
                
                alert('角色保存成功！');
                roleModal.style.display = 'none';
            });
            
            // 编辑角色
            editRoleBtns.forEach(button => {
                button.addEventListener('click', function() {
                    const roleCard = this.closest('.role-card');
                    const roleName = roleCard.querySelector('.role-name').textContent;
                    const roleDescription = roleCard.querySelector('.role-description').textContent.trim();
                    
                    // 设置模态框标题
                    document.querySelector('#roleModal .modal-header h3').textContent = '编辑角色';
                    
                    // 填充表单
                    document.getElementById('roleName').value = roleName;
                    document.getElementById('roleDescription').value = roleDescription;
                    
                    // 根据角色卡片中的权限项选中对应复选框
                    const activePermissions = roleCard.querySelectorAll('.permission-item.active');
                    document.querySelectorAll('input[name="permissions[]"]').forEach(checkbox => {
                        checkbox.checked = false;
                    });
                    
                    activePermissions.forEach(perm => {
                        const permText = perm.textContent.trim();
                        const checkbox = Array.from(document.querySelectorAll('input[name="permissions[]"]')).find(cb => {
                            return cb.nextElementSibling.textContent.trim() === permText;
                        });
                        if (checkbox) {
                            checkbox.checked = true;
                        }
                    });
                    
                    roleModal.style.display = 'flex';
                });
            });
            
            // 删除角色
            deleteRoleBtns.forEach(button => {
                button.addEventListener('click', function() {
                    deleteConfirmModal.style.display = 'flex';
                    
                    // 保存要删除的角色引用
                    confirmDeleteBtn.dataset.roleCard = this.closest('.role-card');
                });
            });
            
            // 确认删除
            confirmDeleteBtn.addEventListener('click', function() {
                const roleCard = document.querySelector(confirmDeleteBtn.dataset.roleCard);
                if (roleCard) {
                    // 这里可以添加AJAX请求删除角色
                    roleCard.remove();
                }
                
                deleteConfirmModal.style.display = 'none';
                alert('角色已删除');
            });
        });
    </script>
</body>
</html> 