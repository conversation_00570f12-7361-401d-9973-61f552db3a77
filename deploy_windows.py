#!/usr/bin/env python3
"""
Windows环境一键部署脚本
在Windows Docker Desktop环境下快速部署OmniLink主从服务器
"""

import subprocess
import time
import sys
import os
from pathlib import Path

def run_command(command: str, timeout: int = 300) -> bool:
    """运行命令并返回是否成功"""
    try:
        print(f"执行: {command}")
        result = subprocess.run(
            command,
            shell=True,
            timeout=timeout,
            cwd=Path(__file__).parent
        )
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print(f"❌ 命令超时: {command}")
        return False
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        return False

def main():
    print("=" * 80)
    print("OmniLink 主从服务器 Windows Docker 一键部署")
    print("=" * 80)
    
    # 检查Docker
    print("\n🔍 检查Docker环境...")
    if not run_command("docker --version", 30):
        print("❌ Docker不可用，请确保Docker Desktop已启动")
        sys.exit(1)
    print("✅ Docker环境正常")
    
    # 检查配置文件
    print("\n🔍 检查配置文件...")
    required_files = ["docker-compose.yaml", "app.env"]
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少文件: {file}")
            sys.exit(1)
    print("✅ 配置文件完整")
    
    # 清理旧容器
    print("\n🧹 清理旧容器...")
    run_command("docker-compose down --remove-orphans", 120)
    
    # 构建镜像
    print("\n🔨 构建Docker镜像...")
    if not run_command("docker-compose build --no-cache", 900):
        print("❌ 镜像构建失败")
        sys.exit(1)
    print("✅ 镜像构建成功")
    
    # 启动数据库
    print("\n🚀 启动数据库和Redis...")
    if not run_command("docker-compose up -d postgres-db redis", 180):
        print("❌ 数据库启动失败")
        sys.exit(1)
    
    print("等待数据库就绪...")
    time.sleep(30)
    
    # 启动主服务器
    print("\n🚀 启动主服务器...")
    if not run_command("docker-compose up -d main-server", 180):
        print("❌ 主服务器启动失败")
        sys.exit(1)
    
    print("等待主服务器就绪...")
    time.sleep(20)
    
    # 启动从服务器
    print("\n🚀 启动从服务器...")
    if not run_command("docker-compose up -d slave-server", 180):
        print("❌ 从服务器启动失败")
        sys.exit(1)
    
    print("\n✅ 所有服务启动成功！")
    
    # 显示状态
    print("\n🔍 服务状态:")
    run_command("docker-compose ps", 30)
    
    # 测试连通性
    print("\n🧪 测试服务连通性...")
    time.sleep(10)
    
    if run_command('curl -f http://localhost:8000/health', 30):
        print("✅ 主服务器健康检查通过")
    else:
        print("⚠️ 主服务器健康检查失败")
    
    if run_command('curl -f http://localhost:8001/health -H "Authorization: Bearer dev-api-key-12345"', 30):
        print("✅ 从服务器健康检查通过")
    else:
        print("⚠️ 从服务器健康检查失败")
    
    print("\n" + "=" * 80)
    print("🎉 OmniLink主从服务器部署完成！")
    print("=" * 80)
    print("\n📋 访问信息:")
    print("- 主服务器API文档: http://localhost:8000/docs")
    print("- 主服务器Web界面: http://localhost:8000")
    print("- 从服务器健康检查: http://localhost:8001/health")
    print("- 管理员账户: firefly / bro2fhz12")
    print("\n💡 管理命令:")
    print("- 停止服务: docker-compose down")
    print("- 查看日志: docker-compose logs -f")
    print("- 重启服务: docker-compose restart")
    print("=" * 80)

if __name__ == "__main__":
    main() 