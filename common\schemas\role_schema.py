from pydantic import BaseModel, Field
from typing import Optional, List

# Shared properties
class RoleBase(BaseModel):
    name: str = Field(..., min_length=2, max_length=50, description="Name of the role")
    description: Optional[str] = Field(None, max_length=255, description="Description of the role")

# Properties to receive on role creation
class RoleCreate(RoleBase):
    permissions: List[str] = Field(..., description="List of permission strings for the role")

# Properties to receive on role update
class RoleUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=2, max_length=50)
    description: Optional[str] = Field(None, max_length=255)
    permissions: Optional[List[str]] = None

# Properties shared by models stored in DB
class RoleInDBBase(RoleBase):
    id: int
    is_system_role: bool

    class Config:
        from_attributes = True

# Properties to return to the client
class RoleRead(RoleInDBBase):
    permissions: List[str]

class UserRoleAssignment(BaseModel):
    user_id: int
    role_id: int

class Role(RoleBase):
    id: int
    is_system_role: bool = False
    
    class Config:
        from_attributes = True

# Schema for assigning a role to a user
class RoleAssignmentRequest(BaseModel):
    role_id: int = Field(..., description="The ID of the role to assign")
