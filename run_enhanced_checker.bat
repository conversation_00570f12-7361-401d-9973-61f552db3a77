@echo off
chcp 65001 >nul
title 增强版项目完工检查器

echo.
echo ========================================
echo    增强版项目完工检查器
echo ========================================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    echo 请安装Python 3.7+并添加到系统PATH
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "config.ini" (
    echo ❌ 配置文件config.ini不存在
    echo 请确保config.ini文件在当前目录
    pause
    exit /b 1
)

REM 检查主程序文件
if not exist "enhanced_project_checker.py" (
    echo ❌ 主程序文件enhanced_project_checker.py不存在
    echo 请确保enhanced_project_checker.py文件在当前目录
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

REM 安装必要的依赖
echo 📦 检查并安装必要依赖...
python -c "import psutil" 2>nul
if errorlevel 1 (
    echo 正在安装psutil...
    pip install psutil
)

python -c "import configparser" 2>nul
if errorlevel 1 (
    echo 正在安装configparser...
    pip install configparser
)

echo.
echo 🚀 启动项目检查器...
echo.

REM 运行主程序
python enhanced_project_checker.py

REM 检查运行结果
if errorlevel 1 (
    echo.
    echo ❌ 检查过程中发生错误
    echo 请查看上方错误信息
) else (
    echo.
    echo ✅ 检查完成！
    echo 📄 报告已保存到docs/bgnr目录
)

echo.
echo 按任意键退出...
pause >nul 