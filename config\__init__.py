"""
配置包初始化文件

此文件使得 'ky.config' 目录可以被视为一个Python包，并可以统一管理配置的加载。
"""

import logging

logger = logging.getLogger(__name__)

# 尝试导入应用配置
try:
    # 假设 app_config.py 将位于 ky/config/ 目录下
    # 如果 AuthService 中的 from ky.config.app_config import get_app_config 有效，
    # 那么 app_config.py 应该在这里或者Python路径可以找到的地方。
    from .app_config import get_app_config
    logger.info("成功从 ky.config.app_config 导入 get_app_config。")
except ImportError:
    # 如果直接在 ky.config 下找不到 app_config.py，
    # get_app_config 可能来自其他模块，或者 app_config.py 尚未创建或位于其他路径
    # 我们将在 AuthService 中保留其原始导入方式，此处仅作记录
    logger.warning("未能从 ky.config.app_config 导入 get_app_config。将依赖其他地方的导入路径。")
    # 定义一个临时的 get_app_config 以避免 NameError，如果其他地方也没有成功导入的话
    # 但这只是权宜之计，真正的问题需要解决 app_config.py 的位置和内容
    def get_app_config(key: str, default: any = None) -> any:
        logger.error(f"紧急情况：get_app_config 未能正确加载，尝试获取键 '{key}' 将返回默认值 '{default}'。请检查 app_config.py 的配置！")
        return default

# 导入从服务器JWT配置
try:
    from .jwt_slave_config import (
        get_slave_jwt_secret,
        get_slave_jwt_algorithm,
        get_slave_access_token_expire_minutes,
        JWT_SLAVE_SECRET_KEY, # 也可直接导入常量
        JWT_SLAVE_ALGORITHM,
        JWT_SLAVE_ACCESS_TOKEN_EXPIRE_MINUTES
    )
    logger.info("成功导入从服务器JWT配置 (jwt_slave_config)。")
except ImportError as e:
    logger.error(f"导入从服务器JWT配置 (jwt_slave_config) 失败: {e}", exc_info=True)
    # 提供默认值或抛出更严重的异常，取决于应用需求
    def get_slave_jwt_secret() -> str: return "fallback-secret"
    def get_slave_jwt_algorithm() -> str: return "HS256"
    def get_slave_access_token_expire_minutes() -> int: return 5
    JWT_SLAVE_SECRET_KEY = "fallback-secret"
    JWT_SLAVE_ALGORITHM = "HS256"
    JWT_SLAVE_ACCESS_TOKEN_EXPIRE_MINUTES = 5
    logger.critical("从服务器JWT配置加载失败，已使用回退值！这可能导致安全风险或功能异常！")

# 导入数据库配置 (如果存在)
try:
    from .db_config import get_database_config # 假设 db_config.py 存在并有此函数
    logger.info("成功导入数据库配置 (db_config)。")
except ImportError:
    logger.warning("未能导入数据库配置 (ky.config.db_config)。如果需要数据库，请确保其配置正确。")
    def get_database_config() -> dict:
        logger.error("数据库配置 (get_database_config) 未加载。")
        return {}

# 可以在这里定义一个全局的配置加载函数，汇总所有配置
# def load_all_configs():
#     app_conf = {k: get_app_config(k) for k in ["DEBUG", "SECRET_KEY"]}
#     jwt_slave_conf = {
#         "secret": get_slave_jwt_secret(),
#         "algo": get_slave_jwt_algorithm(),
#         "expire_min": get_slave_access_token_expire_minutes()
#     }
#     db_conf = get_database_config()
#     return {"app": app_conf, "jwt_slave": jwt_slave_conf, "db": db_conf}

logger.info("ky.config 包初始化完成。")

__all__ = [
    "get_app_config", 
    "get_slave_jwt_secret", 
    "get_slave_jwt_algorithm", 
    "get_slave_access_token_expire_minutes",
    "JWT_SLAVE_SECRET_KEY",
    "JWT_SLAVE_ALGORITHM",
    "JWT_SLAVE_ACCESS_TOKEN_EXPIRE_MINUTES",
    "get_database_config" # 如果定义了
] 