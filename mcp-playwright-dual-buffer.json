{"mcpServers": {"playwright-primary": {"command": "npx", "args": ["@playwright/mcp@0.0.28", "--port", "8931", "--headless"], "env": {"PLAYWRIGHT_BROWSERS_PATH": "0", "PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD": "1"}}, "playwright-secondary": {"command": "npx", "args": ["@playwright/mcp@0.0.28", "--port", "8932", "--headless"], "env": {"PLAYWRIGHT_BROWSERS_PATH": "0", "PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD": "1"}}, "chrome-mcp-bridge": {"command": "node", "args": ["-e", "const http = require('http'); const server = http.createServer((req, res) => { res.writeHead(200, {'Content-Type': 'text/event-stream', 'Access-Control-Allow-Origin': '*'}); res.write('data: {\"type\":\"connection\",\"status\":\"connected\"}\n\n'); setInterval(() => { res.write('data: {\"type\":\"heartbeat\",\"timestamp\":' + Date.now() + '}\n\n'); }, 30000); }); server.listen(12307, () => console.log('MCP Bridge running on port 12307'));"]}}}