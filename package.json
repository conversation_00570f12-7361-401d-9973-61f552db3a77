{"name": "nex-project-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite --host --host 0.0.0.0", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vue/tsconfig": "^0.7.0", "axios": "^1.10.0", "date-fns": "^4.1.0", "element-plus": "^2.7.7", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "pinia": "^2.1.7", "vue": "^3.4.31", "vue-router": "^4.4.0"}, "devDependencies": {"@types/lodash": "^4.17.18", "@types/node": "^20.14.9", "@vitejs/plugin-vue": "^5.0.5", "typescript": "^5.5.3", "vite": "^5.3.3", "vue-tsc": "^2.0.26"}}