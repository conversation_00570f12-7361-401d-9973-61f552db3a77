from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from src.omnilink_main.core.config import settings
from src.omnilink_main.api.v1.api import api_router
from src.omnilink_main.ui import main_ui # This will be created next
from nicegui import ui

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# Set all CORS enabled origins
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

app.include_router(api_router, prefix=settings.API_V1_STR)

# Initialize and mount the NiceGUI UI
main_ui.init(app)
