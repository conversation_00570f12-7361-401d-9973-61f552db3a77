from sqlalchemy import (
    <PERSON>um<PERSON>, Integer, String, TIMESTAMP, Boolean, Table, ForeignKey, Text, JSON
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from common.database.base_class import Base
from .user import user_role_association

role_policy_association = Table(
    'role_policy_association',
    Base.metadata,
    Column('role_id', Integer, ForeignKey('roles.id', ondelete='CASCADE'), primary_key=True),
    Column('policy_rule_id', Integer, ForeignKey('policy_rules.id', ondelete='CASCADE'), primary_key=True)
)

class Role(Base):
    __tablename__ = 'roles'
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, unique=True)
    description = Column(Text)
    # 这个permissions字段可以用来存储与角色绑定的额外静态权限字符串，
    # 与动态的policy_rules形成互补。
    permissions = Column(JSON) 
    is_system_role = Column(<PERSON><PERSON><PERSON>, default=False)
    created_at = Column(TIMESTAMP, server_default=func.now())
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now())
    
    users = relationship('User', secondary=user_role_association, back_populates='roles')
    policies = relationship('PolicyRule', secondary=role_policy_association, back_populates='roles')
