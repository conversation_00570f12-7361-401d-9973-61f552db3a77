from pydantic import BaseModel
from typing import Optional, Any, Dict

from common.rules_engine.models import Filter # 导入我们之前定义的规则引擎的顶层模型

# --- Policy Rule Schemas ---

# Shared properties
class PolicyRuleBase(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = True
    # The rule definition itself, validated against the Filter model
    rule_definition: Optional[Filter] = None

# Properties to receive via API on creation
class PolicyRuleCreate(PolicyRuleBase):
    name: str
    rule_definition: Filter

# Properties to receive via API on update
class PolicyRuleUpdate(PolicyRuleBase):
    pass

# Properties shared by models stored in DB
class PolicyRuleInDBBase(PolicyRuleBase):
    id: int
    name: str
    is_active: bool
    rule_definition: Dict[str, Any] # In DB it's stored as JSON

    class Config:
        from_attributes = True

# Additional properties to return via API
class PolicyRule(PolicyRuleInDBBase):
    pass
