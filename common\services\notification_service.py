import asyncio
import logging
import smtplib
import socket
import json
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional, Dict, Tuple, Any

logger = logging.getLogger(__name__)

class NotificationService:
    """
    通用通知服务，用于发送邮件和短信。
    """
    def __init__(self, 
                 mail_config_path: Optional[str] = None, 
                 sms_config_path: Optional[str] = None):
        """
        初始化通知服务。

        参数:
            mail_config_path (Optional[str]): 邮件服务配置文件的路径 (JSON格式)。
                                             默认为 'config/keys/email_service_config.json'。
            sms_config_path (Optional[str]): 短信服务配置文件的路径 (JSON格式)。
                                            默认为 'config/keys/sms_service_config.json'。
        """
        self.mail_config: Optional[Dict] = None
        self.sms_config: Optional[Dict] = None

        default_mail_config_path = os.path.join('config', 'keys', 'email_service_config.json')
        actual_mail_config_path = mail_config_path or default_mail_config_path
        
        default_sms_config_path = os.path.join('config', 'keys', 'sms_service_config.json')
        actual_sms_config_path = sms_config_path or default_sms_config_path

        try:
            if os.path.exists(actual_mail_config_path):
                with open(actual_mail_config_path, 'r', encoding='utf-8') as f:
                    self.mail_config = json.load(f)
                logger.info(f"邮件配置已从 {actual_mail_config_path} 加载。")
            else:
                logger.warning(f"邮件配置文件 {actual_mail_config_path} 未找到，邮件功能可能不可用。")
        except Exception as e:
            logger.error(f"加载邮件配置 {actual_mail_config_path} 失败: {e}")

        try:
            if os.path.exists(actual_sms_config_path):
                with open(actual_sms_config_path, 'r', encoding='utf-8') as f:
                    self.sms_config = json.load(f)
                logger.info(f"短信配置已从 {actual_sms_config_path} 加载。")
            else:
                logger.warning(f"短信配置文件 {actual_sms_config_path} 未找到，短信功能可能不可用。")
        except Exception as e:
            logger.error(f"加载短信配置 {actual_sms_config_path} 失败: {e}")

    async def send_email(self, recipient: str, subject: str, body_text: str, body_html: Optional[str] = None) -> Tuple[bool, str]:
        """
        异步发送邮件。

        参数:
            recipient (str): 收件人邮箱地址。
            subject (str): 邮件主题。
            body_text (str): 邮件正文 (纯文本)。
            body_html (Optional[str]): 邮件正文 (HTML格式)。

        返回:
            Tuple[bool, str]: (是否成功, 消息)
        """
        if not self.mail_config:
            logger.error("邮件服务未配置，无法发送邮件。")
            return False, "邮件服务未配置"

        sender_email = self.mail_config.get('sender_email')
        smtp_server = self.mail_config.get('smtp_server')
        smtp_port = self.mail_config.get('smtp_port', 587) # 默认为587 (TLS)
        smtp_user = self.mail_config.get('smtp_user')
        smtp_password = self.mail_config.get('smtp_password')
        use_tls = self.mail_config.get('use_tls', True)
        use_ssl = self.mail_config.get('use_ssl', False)


        if not all([sender_email, smtp_server, smtp_user, smtp_password]):
            logger.error("邮件配置不完整 (缺少 sender_email, smtp_server, smtp_user, 或 smtp_password)。")
            return False, "邮件配置不完整"

        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = sender_email
        msg['To'] = recipient

        part_text = MIMEText(body_text, 'plain', 'utf-8')
        msg.attach(part_text)

        if body_html:
            part_html = MIMEText(body_html, 'html', 'utf-8')
            msg.attach(part_html)
        
        loop = asyncio.get_event_loop()
        try:
            if use_ssl:
                server = smtplib.SMTP_SSL(smtp_server, smtp_port, timeout=10)
            else:
                server = smtplib.SMTP(smtp_server, smtp_port, timeout=10)
            
            if use_tls and not use_ssl: # 只有在非SSL模式下才尝试STARTTLS
                await loop.run_in_executor(None, server.ehlo)
                await loop.run_in_executor(None, server.starttls)
                await loop.run_in_executor(None, server.ehlo)

            await loop.run_in_executor(None, server.login, smtp_user, smtp_password)
            await loop.run_in_executor(None, server.sendmail, sender_email, [recipient], msg.as_string())
            await loop.run_in_executor(None, server.quit)
            logger.info(f"邮件已成功发送至 {recipient}，主题: {subject}")
            return True, "邮件发送成功"
        except smtplib.SMTPException as e:
            logger.error(f"发送邮件至 {recipient} 失败 (SMTPException): {e}")
            return False, f"邮件发送失败: SMTP错误 {e.smtp_code} - {e.smtp_error}"
        except ConnectionRefusedError:
            logger.error(f"发送邮件至 {recipient} 失败: 连接被拒绝。检查SMTP服务器地址和端口。")
            return False, "邮件发送失败: 连接被拒绝"
        except socket.gaierror:
            logger.error(f"发送邮件至 {recipient} 失败: SMTP服务器地址无效。")
            return False, "邮件发送失败: SMTP服务器地址无效"
        except Exception as e:
            logger.error(f"发送邮件至 {recipient} 失败 (未知错误): {e}")
            return False, f"邮件发送失败: {str(e)}"


    async def send_sms(self, recipient_phone: str, message: str) -> Tuple[bool, str]:
        """
        异步发送短信。

        参数:
            recipient_phone (str): 收信人手机号码。
            message (str): 短信内容。

        返回:
            Tuple[bool, str]: (是否成功, 消息)
            
        Raises:
            NotImplementedError: 短信功能在项目中不需要实现
        """
        # 短信功能在项目规划中已标记为不需要实现
        # 此功能不在系统需求范围内，仅保留接口以维持架构完整性
        raise NotImplementedError(
            "短信发送功能未实现：此功能在项目规划中已标记为不需要。"
            "系统通知通过邮件和WebSocket方式进行，短信支持不在当前需求范围内。"
        )

    async def send_alert_from_data(self, recipient_config_key: str, alert_data: Dict[str, Any]) -> bool:
        """
        根据告警数据和配置的接收者密钥发送告警邮件。

        参数:
            recipient_config_key (str): 用于在邮件配置中查找收件人组的键。
            alert_data (Dict[str, Any]): 包含告警详情的字典。
                                         预期包含: 'alert_type_display', 'hostname', 'ip_address',
                                                    'timestamp_utc', 'message', 'details' (可选)。
        返回:
            bool: 如果至少成功发送了一封邮件则为True，否则为False。
        """
        if not self.mail_config:
            logger.error("邮件服务未配置，无法发送告警。")
            return False

        recipient_groups = self.mail_config.get("recipient_groups", {})
        recipients = recipient_groups.get(recipient_config_key)

        if not recipients:
            logger.warning(f"在邮件配置中未找到键为 '{recipient_config_key}' 的收件人组，或收件人列表为空。")
            # 尝试发送给默认的admin_email (如果配置了)
            default_admin_email = self.mail_config.get("default_admin_email")
            if default_admin_email:
                recipients = [default_admin_email]
                logger.info(f"将尝试发送告警给默认管理员邮箱: {default_admin_email}")
            else:
                logger.error(f"没有可用的收件人来发送关于 '{recipient_config_key}' 的告警。")
                return False
        
        if not isinstance(recipients, list):
            logger.error(f"收件人组 '{recipient_config_key}' 的配置格式不正确，应为列表。实际类型: {type(recipients)}")
            return False

        subject = f"[系统告警] {alert_data.get('alert_type_display', '未知类型')} - 服务器: {alert_data.get('hostname', '未知主机')}"
        
        body_lines = [
            f"告警类型: {alert_data.get('alert_type_display', 'N/A')}",
            f"服务器主机名: {alert_data.get('hostname', 'N/A')}",
            f"服务器IP: {alert_data.get('ip_address', 'N/A')}",
            f"时间 (UTC): {alert_data.get('timestamp_utc', 'N/A')}",
            f"\n消息详情:",
            f"{alert_data.get('message', '无具体消息。')}",
        ]

        details = alert_data.get('details')
        if isinstance(details, dict) and details:
            body_lines.append("\n附加详细信息:")
            for key, value in details.items():
                body_lines.append(f"  {key.replace('_', ' ').title()}: {value}")
        elif isinstance(details, str) and details:
            body_lines.append("\n附加详细信息:")
            body_lines.append(details)

        body_text = "\n".join(body_lines)
        # 简单生成HTML版本，可以根据需要进行美化
        body_html_lines = [f"<h2>{subject}</h2>"] + [f"<p>{line.replace(chr(10), '<br>')}</p>" for line in body_lines]
        body_html = "\n".join(body_html_lines)

        overall_success = True
        for recipient in recipients:
            if not recipient or not isinstance(recipient, str) or '@' not in recipient: # 基本的邮箱格式校验
                logger.warning(f"无效的收件人邮箱地址: {recipient}，已跳过。")
                overall_success = False
                continue
            
            success, msg = await self.send_email(recipient, subject, body_text, body_html)
            if not success:
                logger.error(f"向 {recipient} 发送告警邮件失败: {msg}")
                overall_success = False
            else:
                logger.info(f"已成功向 {recipient} 发送关于 '{alert_data.get('alert_type_display')}' 的告警邮件。")
        
        return overall_success

# 示例用法 (通常不会在这里直接运行)
async def main_example():
    # 配置路径应指向实际的配置文件
    # 在真实应用中，这些配置通常在应用启动时加载一次，服务实例会被注入或全局可用
    
    # 确保 config/keys 目录存在，并且在其中创建模板文件
    if not os.path.exists('config/keys'):
        os.makedirs('config/keys')

    email_template_path = 'config/keys/email_service_config.json.template'
    sms_template_path = 'config/keys/sms_service_config.json.template'

    if not os.path.exists(email_template_path):
        with open(email_template_path, 'w', encoding='utf-8') as f_email_template:
            json.dump({
                "sender_email": "<EMAIL>",
                "smtp_server": "smtp.example.com",
                "smtp_port": 587,
                "smtp_user": "your_username",
                "smtp_password": "your_password",
                "use_tls": True,
                "use_ssl": False
            }, f_email_template, indent=4)
        print(f"创建了邮件配置模板: {email_template_path}")

    if not os.path.exists(sms_template_path):
        with open(sms_template_path, 'w', encoding='utf-8') as f_sms_template:
            json.dump({
                "api_key": "YOUR_SMS_API_KEY",
                "api_secret": "YOUR_SMS_API_SECRET",
                "sender_id": "YourSenderID",
                "endpoint_url": "https://api.sms_provider.com/send"
            }, f_sms_template, indent=4)
        print(f"创建了短信配置模板: {sms_template_path}")
    
    # 假设你已经创建了 config/keys/email_service_config.json (基于模板)
    # notifier = NotificationService(mail_config_path='config/keys/email_service_config.json')
    
    # # 发送邮件示例
    # success, msg = await notifier.send_email(
    #     recipient="<EMAIL>",
    #     subject="测试邮件 from NotificationService",
    #     body_text="这是一封通过NotificationService发送的纯文本测试邮件。",
    #     body_html="<h1>测试邮件</h1><p>这是一封通过NotificationService发送的 <b>HTML</b> 测试邮件。</p>"
    # )
    # print(f"邮件发送结果: {success}, {msg}")

    # # 发送短信示例
    # success_sms, msg_sms = await notifier.send_sms(
    #     recipient_phone="+**********",
    #     message="来自NotificationService的测试短信。"
    # )
    # print(f"短信发送结果: {success_sms}, {msg_sms}")

if __name__ == '__main__':
    # # 配置日志记录器以便查看输出
    # logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # # 为了运行示例，你需要一个事件循环
    # # asyncio.run(main_example())
    pass 
