"""
端口池模块

提供动态端口分配管理，用于从服务器的远程访问。
此模块维护一个可用端口池，确保每个从服务器都有唯一的访问端口。
支持持久化存储和多实例共享。
"""
import os
import threading
import logging
import socket
import json
import time
import random
from typing import Optional, Set, List, Dict, Any

logger = logging.getLogger('port_pool')

class PortPool:
    """端口池，管理可用端口资源"""
    
    def __init__(self, start_port: int = 10000, end_port: int = 20000, storage_path: str = None):
        """
        初始化端口池
        
        参数:
            start_port: 起始端口
            end_port: 结束端口
            storage_path: 持久化存储路径
        """
        self.start_port = start_port
        self.end_port = end_port
        self.used_ports: Set[int] = set()
        self.lock = threading.Lock()
        self.storage_path = storage_path or os.path.expanduser("~/.ky/port_pool.json")
        
        # 确保存储目录存在
        os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
        
        # 加载已使用的端口
        self._load_used_ports()
        
        logger.info(f"初始化端口池: {start_port} - {end_port}, 已使用 {len(self.used_ports)} 个端口")
    
    def _load_used_ports(self) -> None:
        """从存储中加载已使用的端口"""
        if os.path.exists(self.storage_path):
            try:
                with open(self.storage_path, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, dict) and 'used_ports' in data:
                        self.used_ports = set(map(int, data['used_ports']))
                        logger.info(f"已从存储中加载 {len(self.used_ports)} 个使用中的端口")
            except Exception as e:
                logger.error(f"加载端口池状态失败: {str(e)}")
    
    def _save_used_ports(self) -> None:
        """保存已使用的端口到存储"""
        try:
            with open(self.storage_path, 'w') as f:
                json.dump({
                    'used_ports': list(self.used_ports),
                    'updated_at': time.time()
                }, f)
        except Exception as e:
            logger.error(f"保存端口池状态失败: {str(e)}")
    
    def is_port_available(self, port: int) -> bool:
        """
        检查端口是否可用
        
        参数:
            port: 要检查的端口
            
        返回:
            bool: 端口是否可用
        """
        if port in self.used_ports:
            return False
            
        try:
            # 尝试绑定端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(0.5)
            sock.bind(('', port))
            sock.close()
            return True
        except Exception:
            return False
    
    def get_available_port(self) -> Optional[int]:
        """
        获取一个可用端口
        
        返回:
            Optional[int]: 可用端口，如果没有则返回None
        """
        with self.lock:
            # 先尝试从未使用的端口中选择
            available_ports = set(range(self.start_port, self.end_port + 1)) - self.used_ports
            
            # 随机化端口选择，避免总是从头开始选择
            candidates = list(available_ports)
            random.shuffle(candidates)
            
            for port in candidates:
                if self.is_port_available(port):
                    self.used_ports.add(port)
                    self._save_used_ports()
                    logger.info(f"分配端口: {port}")
                    return port
            
            # 如果所有端口都被使用，尝试检查已使用端口是否有可用的
            for port in sorted(self.used_ports):
                if self.is_port_available(port):
                    logger.info(f"重用端口: {port}")
                    return port
        
        logger.warning("没有可用端口")
        return None
    
    def reserve_port(self, port: int) -> bool:
        """
        保留指定端口
        
        参数:
            port: 要保留的端口
            
        返回:
            bool: 是否成功保留
        """
        if port < self.start_port or port > self.end_port:
            logger.warning(f"端口 {port} 超出范围 ({self.start_port} - {self.end_port})")
            return False
            
        with self.lock:
            if port in self.used_ports:
                logger.warning(f"端口 {port} 已被使用")
                return False
                
            self.used_ports.add(port)
            self._save_used_ports()
            logger.info(f"保留端口: {port}")
            return True
    
    def release_port(self, port: int) -> bool:
        """
        释放指定端口
        
        参数:
            port: 要释放的端口
            
        返回:
            bool: 是否成功释放
        """
        with self.lock:
            if port in self.used_ports:
                self.used_ports.remove(port)
                self._save_used_ports()
                logger.info(f"释放端口: {port}")
                return True
            
            logger.warning(f"端口 {port} 未被使用，无需释放")
            return False
    
    def get_used_ports(self) -> List[int]:
        """
        获取所有已使用的端口
        
        返回:
            List[int]: 已使用的端口列表
        """
        with self.lock:
            return list(self.used_ports)
            
    def get_available_ports_count(self) -> int:
        """
        获取可用端口数量
        
        返回:
            int: 可用端口数量
        """
        with self.lock:
            return (self.end_port - self.start_port + 1) - len(self.used_ports)
    
    def check_all_ports(self) -> Dict[str, Any]:
        """
        检查所有端口的状态
        
        返回:
            Dict: 端口状态信息
        """
        with self.lock:
            # 检查已标记使用但实际可用的端口
            phantom_ports = []
            for port in sorted(self.used_ports):
                if self.is_port_available(port):
                    phantom_ports.append(port)
            
            # 检查未标记使用但实际被占用的端口
            potential_ports = set(range(self.start_port, self.end_port + 1)) - self.used_ports
            blocked_ports = []
            for port in sorted(list(potential_ports)[:20]):  # 仅检查前20个潜在端口
                if not self.is_port_available(port):
                    blocked_ports.append(port)
            
            return {
                "total_ports": self.end_port - self.start_port + 1,
                "used_ports": len(self.used_ports),
                "available_ports": self.get_available_ports_count(),
                "phantom_ports": phantom_ports,
                "blocked_ports_sample": blocked_ports
            }
    
    def cleanup(self) -> int:
        """
        清理无效的端口分配
        
        返回:
            int: 清理的端口数量
        """
        with self.lock:
            # 查找已分配但实际可用的端口
            to_release = []
            for port in sorted(self.used_ports):
                if self.is_port_available(port):
                    to_release.append(port)
            
            # 释放这些端口
            for port in to_release:
                self.used_ports.remove(port)
            
            # 保存更改
            if to_release:
                self._save_used_ports()
                logger.info(f"已清理 {len(to_release)} 个无效的端口分配")
            
            return len(to_release)
    
    def reserve_port_range(self, start: int, end: int) -> List[int]:
        """
        保留一个端口范围
        
        参数:
            start: 起始端口
            end: 结束端口
            
        返回:
            List[int]: 成功保留的端口列表
        """
        with self.lock:
            reserved = []
            for port in range(start, end + 1):
                if self.reserve_port(port):
                    reserved.append(port)
            
            return reserved
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取端口池状态
        
        返回:
            Dict: 状态信息
        """
        with self.lock:
            total = self.end_port - self.start_port + 1
            used = len(self.used_ports)
            available = total - used
            
            return {
                "start_port": self.start_port,
                "end_port": self.end_port,
                "total_ports": total,
                "used_ports": used,
                "available_ports": available,
                "usage_percent": round(used / total * 100, 2) if total > 0 else 0
            }

# 全局端口池实例
port_pool_instance = None

def get_port_pool(start_port: int = 10000, end_port: int = 20000, storage_path: str = None) -> PortPool:
    """
    获取端口池实例
    
    参数:
        start_port: 起始端口
        end_port: 结束端口
        storage_path: 持久化存储路径
        
    返回:
        PortPool: 端口池实例
    """
    global port_pool_instance
    if port_pool_instance is None:
        port_pool_instance = PortPool(start_port, end_port, storage_path)
    return port_pool_instance 

class PortPoolManager:
    """
    Manages a pool of available ports to be assigned for device sharing.
    """
    def __init__(self, start_port: int, end_port: int):
        if start_port > end_port:
            raise ValueError("Start port must be less than or equal to end port.")
        
        self.port_range = (start_port, end_port)
        self.available_ports: Set[int] = set(range(start_port, end_port + 1))
        self.used_ports: Set[int] = set()
        self.lock = threading.Lock()
        logger.info(f"Port Pool Manager initialized with range {start_port}-{end_port}.")

    def lease_port(self) -> Optional[int]:
        """
        Get an available port from the pool.
        """
        with self.lock:
            if not self.available_ports:
                logger.warning("No available ports in the pool.")
                return None
            
            port = self.available_ports.pop()
            self.used_ports.add(port)
            logger.info(f"Leased port {port}.")
            return port

    def release_port(self, port: int):
        """
        Return a port to the pool.
        """
        with self.lock:
            if port in self.used_ports:
                self.used_ports.remove(port)
                self.available_ports.add(port)
                logger.info(f"Released port {port}.")
            elif self.port_range[0] <= port <= self.port_range[1]:
                 # If port is in range but not in used_ports, just add it back.
                 # This can happen if a port is released that was never formally leased (e.g. on cleanup).
                 self.available_ports.add(port)
                 logger.warning(f"Port {port} was released but not in the used set. Added back to pool.")
            else:
                logger.error(f"Attempted to release port {port} which is outside the managed range.")

# Create a global instance, port range should be configured in settings.
# For now, using a common range for VirtualHere.
port_pool = PortPoolManager(start_port=7575, end_port=8000) 
