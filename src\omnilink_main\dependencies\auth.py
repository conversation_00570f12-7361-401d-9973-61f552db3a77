from fastapi import Depends, HTTPException, status
from fastapi.security import O<PERSON><PERSON>2<PERSON><PERSON><PERSON><PERSON>earer
from pydantic import ValidationError
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List

from common.models.user import User
from common.schemas.token_schema import TokenPayload
from src.omnilink_main.core.security import decode_token
from src.omnilink_main.core.redis import token_blacklist_service
from src.omnilink_main.dependencies.db import get_db
from src.omnilink_main.services.auth_service import auth_service_instance

reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl="/api/v1/auth/login"
)

async def get_current_token(token: str = Depends(reusable_oauth2)) -> str:
    """Dependency to get the raw token string."""
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, 
            detail="未认证"
        )
    return token

async def get_current_user(
    db: AsyncSession = Depends(get_db), token: str = Depends(get_current_token)
) -> User:
    try:
        payload = decode_token(token)
        if not payload or payload.get("type") != "access":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无效的令牌类型或载荷",
            )
        token_data = TokenPayload(**payload)
        
        jti = payload.get("jti")
        if not jti or await token_blacklist_service.is_blacklisted(jti):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已被撤销",
            )

    except (ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无法验证凭据",
        )
    
    # 使用认证服务获取用户信息（包含角色）
    user = await auth_service_instance.get_user_by_id(db, user_id=token_data.sub)
    if not user:
        raise HTTPException(status_code=404, detail="用户未找到")
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="用户已被禁用")
    return current_user


async def get_current_active_superuser(
    current_user: User = Depends(get_current_active_user),
) -> User:
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=400, detail="用户权限不足"
        )
    return current_user


class PermissionChecker:
    """权限检查器，用于验证用户是否具有所需权限"""
    
    def __init__(self, *required_permissions: str):
        self.required_permissions = set(required_permissions)

    async def __call__(self, user: User = Depends(get_current_active_user)):
        # 超级用户拥有所有权限
        if user.is_superuser:
            return

        # 收集用户的所有权限
        user_permissions = set()
        for role in user.roles:
            if role.permissions and isinstance(role.permissions, list):
                user_permissions.update(role.permissions)

        # 检查是否具有所需权限
        if not self.required_permissions.issubset(user_permissions):
            missing_permissions = self.required_permissions - user_permissions
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，缺少权限: {', '.join(missing_permissions)}",
            )


def require_permission(*permissions: str):
    """权限装饰器工厂函数"""
    return PermissionChecker(*permissions)
