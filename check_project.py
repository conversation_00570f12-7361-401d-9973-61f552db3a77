#!/usr/bin/env python3
"""项目状态检查脚本"""

import os
from pathlib import Path

def check_files():
    """检查关键文件是否存在"""
    root = Path(__file__).parent
    
    files_to_check = [
        "docker-compose.yaml",
        "app.env", 
        "src/omnilink_main/main.py",
        "src/omnilink_main/core/config.py",
        "slave_server/main.py",
        "deployment/dockerfiles/Dockerfile.main",
        "deployment/dockerfiles/Dockerfile.slave",
        "main_server/requirements.txt",
        "slave_server/requirements.txt"
    ]
    
    print("🔍 检查项目文件...")
    missing_files = []
    
    for file_path in files_to_check:
        full_path = root / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  缺失 {len(missing_files)} 个文件")
        return False
    else:
        print("\n🎉 所有关键文件检查通过!")
        return True

def main():
    print("OmniLink项目状态检查")
    print("="*40)
    
    if check_files():
        print("\n✅ 项目状态: 良好")
        print("📝 可以执行: python deploy.py")
    else:
        print("\n❌ 项目状态: 有问题")
        print("📝 请检查缺失的文件")

if __name__ == "__main__":
    main() 