# OmniLink全联通系统

## 项目概述

OmniLink全联通系统是一个基于主从服务器架构的USB设备远程访问解决方案，旨在提供安全、稳定、高效的远程USB设备管理和访问能力。系统由三大核心组件组成：主服务器、从服务器和专属客户端，共同构建完整的远程USB设备访问生态。

### 核心功能

- **集中式认证与权限管理**：统一管理所有用户账号和权限，支持多层级组织结构
- **USB设备虚拟化**：通过VirtualHere技术实现USB设备的远程连接能力
- **动态端口管理**：智能分配端口，建立客户端与从服务器的长效连接
- **多级安全保障**：TLS 1.3加密通信、双向验证、设备指纹验证等多重安全机制
- **全面日志审计**：记录所有关键操作和异常事件，支持追溯和分析
- **高可用性设计**：支持故障检测、自动恢复和数据备份

## 系统架构

OmniLink全联通系统采用主从服务器架构，主要组件包括：

### 主服务器

- **系统定位**：作为整体系统的控制中心，提供用户认证、权限管理和系统配置功能
- **技术架构**：基于高性能x86平台，使用Python编写，采用PostgreSQL存储数据
- **核心功能**：集中式认证、组织架构管理、从服务器管理、设备分组与授权、日志审计、Web管理界面
- **性能规格**：支持≤500用户并发操作，支持≤6台从服务器管理

### 从服务器

- **系统定位**：作为USB设备接入节点，负责设备管理和连接代理
- **技术架构**：轻量级Linux系统，集成VirtualHere商业版，使用SQLite本地缓存
- **核心功能**：USB设备虚拟化、设备自动发现、智能HUB处理、数据本地缓存、心跳检测机制
- **性能规格**：树莓派3B支持≤50个USB设备/节点，香橙派3B支持≤60个USB设备/节点

### 专属客户端

- **系统定位**：终端用户访问接口，提供USB设备连接和使用能力
- **技术架构**：基于Windows原生开发，兼容Windows 10/11和部分Windows 7系统
- **核心功能**：用户认证、设备浏览、一键连接、会话管理、状态监控、自动重连、多身份切换
- **性能规格**：启动时间<5秒，内存占用<200MB，支持同时连接≤4个USB设备

## 目录结构

OmniLink全联通系统的代码结构如下：

```
/
├── src/                  # 源代码
├── tests/                # 测试代码
├── docs/                 # 文档
├── deploy/               # 部署配置
├── scripts/              # 工具脚本
└── config/               # 配置文件
```

源代码按功能模块组织：

```
src/
├── common/               # 公共组件
├── auth/                 # 认证模块
├── device/               # 设备管理
├── network/              # 网络管理
├── monitor/              # 监控系统
├── security/             # 安全组件
├── ui/                   # 用户界面
└── api/                  # API服务
```

## 技术实现

### 主服务器技术栈

- **操作系统**：Ubuntu Server 20.04 LTS 或更高版本
- **编程语言**：Python 3.8+
- **Web框架**：FastAPI
- **数据库**：PostgreSQL 13+
- **缓存**：Redis 6+
- **Web服务器**：Nginx + Uvicorn

### 从服务器技术栈

- **操作系统**：Ubuntu Server 20.04 LTS
- **硬件平台**：树莓派3B/香橙派3B
- **编程语言**：Python 3.8+
- **数据存储**：SQLite 3.30+
- **USB管理**：VirtualHere 4.4.1+商业版

### 专属客户端技术栈

- **开发技术栈**：Python 3.8+, PyQt5
- **网络通信**：Requests, WebSocket
- **打包工具**：PyInstaller

## 安全特性

- 所有通信采用TLS 1.3加密
- 主从服务器间采用双向验证
- 用户权限严格按照组织层级管理
- 设备指纹验证防止未授权设备接入
- 全程日志审计保障操作可追溯

## 许可协议

本项目采用 MIT 许可证开源。

## 依赖安装与管理

本项目采用分层依赖管理模式，以便针对不同组件和环境灵活安装所需依赖。**自动化部署脚本已集成依赖安装功能。**

### 依赖文件说明

| 文件名                      | 适用场景         | 说明                                                         |
|-----------------------------|------------------|--------------------------------------------------------------|
| ky/requirements.txt         | 全局基础         | 所有环境必装的通用依赖，分层依赖通过`-r`引用此文件           |
| ky/requirements-server.txt  | 主服务器专属     | 部署或运行主服务器所需依赖，**由部署脚本自动安装**，需先安装ky/requirements.txt |
| ky/requirements-slave.txt   | 从服务器专属     | 部署或运行从服务器所需依赖，**由部署脚本自动安装**，需先安装ky/requirements.txt |
| ky/requirements-user.txt    | 专属客户端专属   | 编译、打包或运行专属客户端所需依赖，需先安装ky/requirements.txt |
| ky/requirements-web.txt     | Web后端API服务专属| 运行Web后端API服务所需依赖，需先安装ky/requirements.txt      |
| ky/requirements-dev.txt     | 开发/测试/CI/CD  | 开发、测试、文档、CI/CD等辅助工具，需先安装ky/requirements.txt |

### 自动化安装流程

推荐使用项目提供的自动化部署脚本进行安装，脚本会自动处理Python依赖和部分前端依赖的安装。

请根据您需要设置的环境，找到对应的部署脚本并执行：

1.  **Linux系统一键安装脚本 (`ky/scripts/install.sh`)**

    此脚本适用于在Linux系统上快速安装主服务器、从服务器或全部组件。脚本会创建独立用户、目录结构、虚拟环境，并自动安装Python依赖和Web前端依赖（如果安装主服务器或全部）。

    进入项目根目录并执行：
    ```bash
    # 安装主服务器
    sudo bash ky/scripts/install.sh master

    # 安装从服务器
    sudo bash ky/scripts/install.sh slave

    # 安装主服务器和从服务器（全部）
    sudo bash ky/scripts/install.sh all
    ```
    *注意：此脚本需要root权限运行 (`sudo`)。*

2.  **Python部署工具脚本 (`ky/tools/deployment/scripts/`)**

    这些Python脚本提供了更灵活的部署选项，可以在Python环境中直接运行，也支持自动化依赖安装。

    *   **主服务器部署 (`deploy_main_server.py`)**

        进入 `ky/tools/deployment/scripts/` 目录并执行（在激活的Python环境中）：
        ```bash
        python deploy_main_server.py
        # 可选参数请参考脚本帮助或文档
        ```

    *   **从服务器部署 (`deploy_sub_server.py`)**

        进入 `ky/tools/deployment/scripts/` 目录并执行（在激活的Python环境中）：
        ```bash
        python deploy_sub_server.py
        # 可选参数请参考脚本帮助或文档
        ```
    这些脚本将自动创建虚拟环境并在其中安装所需的Python依赖。

### 手动安装特定依赖 (仅适用于非自动化部署或开发环境)

如果您不需要执行完整的自动化部署脚本，或者只需要为开发/测试环境安装特定依赖，可以手动使用 `pip` 安装。

请确保您已经在虚拟环境中（推荐）并且已安装全局基础依赖 `ky/requirements.txt`。

进入 `ky` 目录并执行相应的命令：

*   **专属客户端环境**

    ```bash
    pip install -r requirements-user.txt
    ```

*   **Web后端API服务环境**

    ```bash
    pip install -r requirements-web.txt
    ```

*   **开发/测试/文档/CI工具依赖** (开发者/CI专用)

    ```bash
    pip install -r requirements-dev.txt
    ```

### 安装前端依赖 (Web管理界面的前端部分)

前端依赖使用 npm/yarn 管理，与Python依赖分离。自动化安装脚本（如 `install.sh`）会尝试安装，但您也可以手动安装。

进入前端项目目录并安装：

```bash
cd ky/main_server/frontend
npm install
# 或者 yarn install (如果使用yarn)
```

**重要提示**：

*   自动化部署脚本会在其创建的**虚拟环境**中安装Python依赖。
*   请确保您的系统中已正确安装 **Python 3.8+** 和 **pip**。
*   前端依赖安装需要 **Node.js** 和 **npm/yarn** 环境。自动化脚本会检查这些工具的存在性，如果未找到将跳过前端依赖安装。
*   `ky/scripts/install.sh` 脚本需要 **root权限** 运行。

```
MIT License

Copyright (c) 2024 OmniLink Project Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```
