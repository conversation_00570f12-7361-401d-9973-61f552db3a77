import redis.asyncio as redis
from src.omnilink_main.core.config import settings
import logging

logger = logging.getLogger(__name__)

class RedisClient:
    """
    A wrapper for the redis-py async client, providing a singleton
    connection pool and a simple interface for getting a client.
    """
    _pool: redis.ConnectionPool | None = None

    @classmethod
    def _create_pool(cls):
        """Creates the Redis connection pool from the settings."""
        if cls._pool is None:
            logger.info(f"Creating Redis connection pool for URL: {settings.REDIS_URL}")
            try:
                cls._pool = redis.ConnectionPool.from_url(
                    str(settings.REDIS_URL),
                    max_connections=10,
                    decode_responses=True  # Decode responses to strings by default
                )
            except Exception as e:
                logger.critical(f"Failed to create Redis connection pool: {e}", exc_info=True)
                raise
    
    @classmethod
    def get_client(cls) -> redis.Redis:
        """
        Gets a Redis client from the connection pool.
        Initializes the pool if it doesn't exist.
        """
        if cls._pool is None:
            cls._create_pool()
        
        if cls._pool is None:
             # This should not happen if _create_pool doesn't raise an exception
             raise RuntimeError("Redis connection pool is not available.")

        return redis.Redis(connection_pool=cls._pool)

    @classmethod
    async def close_pool(cls):
        """Closes the connection pool."""
        if cls._pool:
            logger.info("Closing Redis connection pool.")
            await cls._pool.disconnect()
            cls._pool = None

# Instantiate a client for easy access
redis_client = RedisClient.get_client()

async def get_redis_client() -> redis.Redis:
    """
    FastAPI dependency to get a Redis client.
    """
    return RedisClient.get_client()

async def test_redis_connection():
    """
    A simple function to test the Redis connection.
    Pings the Redis server.
    """
    client = RedisClient.get_client()
    try:
        await client.ping()
        logger.info("Redis connection successful.")
        return True
    except Exception as e:
        logger.error(f"Redis connection failed: {e}", exc_info=True)
        return False

class TokenBlacklistService:
    """
    Handles JWT blacklisting in Redis for user logouts.
    """
    _redis_client: redis.Redis = RedisClient.get_client()
    BLACKLIST_KEY_PREFIX = "jwt_blacklist:"

    def __init__(self):
        pass

    async def add_to_blacklist(self, jti: str, expires_in: int):
        """
        Adds a token's JTI to the blacklist with an expiration.
        
        Args:
            jti: The JWT ID (jti claim) of the token.
            expires_in: The number of seconds until the token naturally expires.
                        The blacklist entry will expire at the same time.
        """
        key = f"{self.BLACKLIST_KEY_PREFIX}{jti}"
        # Set the key with a value of "blacklisted" and an expiration time
        await self._redis_client.set(key, "blacklisted", ex=expires_in)
        logger.debug(f"JTI {jti} added to blacklist, expires in {expires_in} seconds.")

    async def is_blacklisted(self, jti: str) -> bool:
        """
        Checks if a token's JTI is in the blacklist.
        """
        key = f"{self.BLACKLIST_KEY_PREFIX}{jti}"
        is_blacklisted = await self._redis_client.get(key)
        return is_blacklisted is not None

# Instantiate the service for easy access
token_blacklist_service = TokenBlacklistService()
