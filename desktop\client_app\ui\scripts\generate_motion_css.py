#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Material Design 动效CSS生成器

根据动效定义生成CSS文件，包含关键帧动画和过渡效果
"""

import os
import json
from pathlib import Path
import re
import sys

# 设置路径
SCRIPT_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
PARENT_DIR = SCRIPT_DIR.parent

# 导入motion模块
sys.path.append(str(SCRIPT_DIR))
from motion import MotionSystem

# 定义路径
THEMES_DIR = PARENT_DIR / "themes"
MOTION_JSON_PATH = THEMES_DIR / "motion.json"
WEB_CSS_PATH = PARENT_DIR / "web" / "static" / "css" / "material-motion.css"

def ensure_dir_exists(file_path):
    """确保目录存在"""
    directory = os.path.dirname(file_path)
    if not os.path.exists(directory):
        os.makedirs(directory)

def generate_transitions_css(motion_data):
    """
    生成过渡效果CSS
    
    Args:
        motion_data: 动效JSON数据
        
    Returns:
        过渡效果CSS字符串
    """
    css = "/* Material Design 过渡效果 */\n\n"
    
    # 获取过渡数据
    transitions = motion_data.get("transitions", {})
    
    # 通用过渡类
    durations = motion_data.get("duration", {})
    easings = motion_data.get("easing", {})
    
    # 生成通用过渡类
    css += ":root {\n"
    for name, value in durations.items():
        css += f"  --md-duration-{name}: {value}ms;\n"
    css += "\n"
    for name, value in easings.items():
        css += f"  --md-easing-{name}: {value};\n"
    css += "}\n\n"
    
    # 添加通用过渡基类
    css += "/* 通用过渡基类 */\n"
    css += ".md-transition {\n"
    css += "  transition-property: all;\n"
    css += "  transition-duration: var(--md-duration-standard);\n"
    css += "  transition-timing-function: var(--md-easing-standard);\n"
    css += "}\n\n"
    
    # 添加元素特定过渡类
    for element, states in transitions.items():
        css += f"/* {element.capitalize()} 过渡 */\n"
        
        for state, props in states.items():
            duration = props.get("duration", durations.get("standard", 250))
            easing = props.get("easing", easings.get("standard", "cubic-bezier(0.2, 0.0, 0.0, 1.0)"))
            
            css += f".md-{element}-transition-{state} {{\n"
            css += f"  transition-property: all;\n"
            css += f"  transition-duration: {duration}ms;\n"
            css += f"  transition-timing-function: {easing};\n"
            css += "}\n\n"
    
    # 添加实用过渡类
    css += "/* 实用过渡类 */\n"
    for name, value in durations.items():
        css += f".md-transition-duration-{name} {{\n"
        css += f"  transition-duration: {value}ms;\n"
        css += "}\n\n"
    
    for name, value in easings.items():
        css += f".md-transition-easing-{name} {{\n"
        css += f"  transition-timing-function: {value};\n"
        css += "}\n\n"
    
    return css

def generate_css():
    """
    生成完整的Material Design动效CSS
    
    Returns:
        生成的CSS字符串
    """
    # 加载或生成动效JSON
    motion_data = MotionSystem.load_motion_json()
    
    # 生成CSS文件内容
    css = "/**\n"
    css += " * Material Design Motion System\n"
    css += " * 自动生成，请勿手动修改\n"
    css += " */\n\n"
    
    # 添加关键帧动画
    css += MotionSystem.generate_css_keyframes()
    
    # 添加过渡效果
    css += generate_transitions_css(motion_data)
    
    # 添加状态层动画
    css += "/* 状态层动画 */\n"
    css += ".md-state-layer {\n"
    css += "  position: absolute;\n"
    css += "  top: 0;\n"
    css += "  left: 0;\n"
    css += "  right: 0;\n"
    css += "  bottom: 0;\n"
    css += "  overflow: hidden;\n"
    css += "  pointer-events: none;\n"
    css += "  border-radius: inherit;\n"
    css += "}\n\n"
    
    css += ".md-state-layer::before {\n"
    css += "  content: '';\n"
    css += "  position: absolute;\n"
    css += "  top: 0;\n"
    css += "  left: 0;\n"
    css += "  width: 100%;\n"
    css += "  height: 100%;\n"
    css += "  background-color: currentColor;\n"
    css += "  opacity: 0;\n"
    css += "  transition: opacity var(--md-duration-standard) var(--md-easing-standard);\n"
    css += "}\n\n"
    
    css += ".md-state-hover:hover .md-state-layer::before {\n"
    css += "  opacity: 0.08;\n"
    css += "}\n\n"
    
    css += ".md-state-focus .md-state-layer::before,\n"
    css += ".md-state-focus-visible:focus-visible .md-state-layer::before {\n"
    css += "  opacity: 0.12;\n"
    css += "}\n\n"
    
    css += ".md-state-active:active .md-state-layer::before {\n"
    css += "  opacity: 0.12;\n"
    css += "}\n\n"
    
    css += ".md-state-dragged .md-state-layer::before {\n"
    css += "  opacity: 0.16;\n"
    css += "}\n\n"
    
    # 元素特定状态样式
    css += "/* 按钮状态样式 */\n"
    css += ".md-button {\n"
    css += "  position: relative;\n"
    css += "  overflow: hidden;\n"
    css += "}\n\n"
    
    css += ".md-button .md-state-layer::before {\n"
    css += "  transition: opacity var(--md-duration-press) var(--md-easing-standard);\n"
    css += "}\n\n"
    
    # 涟漪效果
    css += "/* 涟漪效果 */\n"
    css += "@keyframes mdRippleEnter {\n"
    css += "  0% {\n"
    css += "    transform: scale(0);\n"
    css += "    opacity: 0.1;\n"
    css += "  }\n"
    css += "  100% {\n"
    css += "    transform: scale(1);\n"
    css += "    opacity: 0.12;\n"
    css += "  }\n"
    css += "}\n\n"
    
    css += "@keyframes mdRippleExit {\n"
    css += "  0% {\n"
    css += "    opacity: 0.12;\n"
    css += "  }\n"
    css += "  100% {\n"
    css += "    opacity: 0;\n"
    css += "  }\n"
    css += "}\n\n"
    
    css += ".md-ripple {\n"
    css += "  position: absolute;\n"
    css += "  border-radius: 50%;\n"
    css += "  background-color: currentColor;\n"
    css += "  pointer-events: none;\n"
    css += "  transform-origin: center;\n"
    css += "}\n\n"
    
    css += ".md-ripple-enter {\n"
    css += "  animation: mdRippleEnter 225ms var(--md-easing-standard) forwards;\n"
    css += "}\n\n"
    
    css += ".md-ripple-exit {\n"
    css += "  animation: mdRippleExit 150ms var(--md-easing-accelerated) forwards;\n"
    css += "}\n\n"
    
    # 浏览器兼容性添加
    css = add_browser_compatibility(css)
    
    return css

def add_browser_compatibility(css):
    """
    添加浏览器兼容性前缀
    
    Args:
        css: 原始CSS字符串
        
    Returns:
        添加浏览器兼容性前缀后的CSS
    """
    # 添加关键帧动画浏览器前缀
    css = re.sub(r'@keyframes\s+([a-zA-Z0-9_-]+)\s*{', r'@-webkit-keyframes \1 {\n@keyframes \1 {', css)
    
    # 添加过渡和动画属性浏览器前缀
    css = re.sub(r'transition([^:]*):([^;]+);', r'-webkit-transition\1:\2;\ntransition\1:\2;', css)
    css = re.sub(r'animation([^:]*):([^;]+);', r'-webkit-animation\1:\2;\nanimation\1:\2;', css)
    
    # 添加transform属性浏览器前缀
    css = re.sub(r'transform:([^;]+);', r'-webkit-transform:\1;\n-ms-transform:\1;\ntransform:\1;', css)
    
    return css

def save_css(css, output_path=None):
    """
    保存CSS到文件
    
    Args:
        css: CSS内容
        output_path: 输出路径，默认为预定义的WEB_CSS_PATH
    """
    if output_path is None:
        output_path = WEB_CSS_PATH
    
    # 确保目录存在
    ensure_dir_exists(output_path)
    
    # 保存CSS文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(css)
    
    print(f"CSS文件已保存到: {output_path}")

def main():
    """主函数"""
    try:
        # 确保主题目录存在
        os.makedirs(THEMES_DIR, exist_ok=True)
        
        # 生成CSS
        css = generate_css()
        
        # 保存CSS文件
        save_css(css)
        
        print("动效CSS生成成功!")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main() 