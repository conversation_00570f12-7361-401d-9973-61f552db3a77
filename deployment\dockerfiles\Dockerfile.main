# Dockerfile for the Main Server (v2, using python:3.11-slim base)
# This Dockerfile uses a multi-stage build to create a slim and secure final image.

# Stage 1: Builder
# Use python:3.11-slim as the base, since it's confirmed to work on the host machine.
FROM python:3.11-slim AS builder

# Set environment variables to non-interactive
ENV DEBIAN_FRONTEND=noninteractive

# Install necessary build tools: curl and PostgreSQL client (for alembic)
# python3-pip is already included in the base image.
RUN apt-get update && \
    apt-get install -y --no-install-recommends curl libpq-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install uv, the fast Python package installer, and use it in the same RUN command
COPY ./main_server/requirements.txt .
RUN curl -LsSf https://astral.sh/uv/install.sh | sh && \
    /root/.local/bin/uv pip install --system --no-cache -r requirements.txt

# Set up the application directory
WORKDIR /app

# Stage 2: Final Image
# Use the same slim Python base image for the final product
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

WORKDIR /app

# Copy installed dependencies from the builder stage
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Install runtime-specific dependencies like the PostgreSQL client and netcat
RUN apt-get update && apt-get install -y --no-install-recommends libpq5 netcat-traditional && apt-get clean && rm -rf /var/lib/apt/lists/*

# Copy the application code
COPY ./src/ ./src/
COPY ./common/ ./common/
COPY ./main_server/ ./main_server/
COPY ./alembic/ ./alembic/
COPY alembic.ini .

# The application will be started by docker-compose, so no ENTRYPOINT/CMD is needed here.
EXPOSE 8000 