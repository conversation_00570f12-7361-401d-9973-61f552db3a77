from enum import Enum
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator
import re


class FilterOperator(str, Enum):
    """过滤条件操作符枚举"""
    EQUALS = "eq"
    NOT_EQUALS = "ne"
    GREATER_THAN = "gt"
    GREATER_EQUALS = "ge"
    LESS_THAN = "lt"
    LESS_EQUALS = "le"
    CONTAINS = "contains"
    NOT_CONTAINS = "not_contains"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"
    MATCHES = "matches"
    NOT_MATCHES = "not_matches"
    IN = "in"
    NOT_IN = "not_in"
    IS_NULL = "is_null"
    IS_NOT_NULL = "is_not_null"


class FilterLogic(str, Enum):
    """过滤条件组逻辑枚举"""
    AND = "and"
    OR = "or"
    NOT = "not"


class FilterCondition(BaseModel):
    """表示单个过滤条件的Pydantic模型"""
    field: str = Field(..., description="要过滤的字段路径，支持点表示法，如 'device.info.type'")
    operator: FilterOperator = Field(..., description="过滤操作符")
    value: Any = Field(None, description="用于比较的过滤值")
    case_sensitive: bool = Field(False, description="字符串比较是否区分大小写")

    @validator('value', pre=True, always=True)
    def check_value_for_operator(cls, v, values):
        op = values.get('operator')
        if op in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL]:
            if v is not None:
                raise ValueError(f"当操作符为 '{op.value}' 时，值必须为 None")
        elif op in [FilterOperator.IN, FilterOperator.NOT_IN]:
            if not isinstance(v, list):
                raise ValueError(f"当操作符为 '{op.value}' 时，值必须是一个列表")
        return v

class FilterGroup(BaseModel):
    """表示多个过滤条件组合的Pydantic模型"""
    logic: FilterLogic = Field(..., description="条件组的逻辑关系 (AND, OR, NOT)")
    conditions: List['FilterCondition'] = Field([], description="此组内的过滤条件列表")
    groups: List['FilterGroup'] = Field([], description="此组内的嵌套过滤条件组列表")

# 更新FilterGroup的前向引用
FilterGroup.update_forward_refs()

class Filter(BaseModel):
    """表示一个完整过滤器的Pydantic模型"""
    id: str = Field(..., description="过滤器的唯一ID")
    name: str = Field(..., description="过滤器的名称")
    description: str = Field("", description="过滤器的详细描述")
    filter_group: FilterGroup = Field(..., description="顶层过滤条件组")
    enabled: bool = Field(True, description="此过滤器是否启用")
    metadata: Dict[str, Any] = Field({}, description="其他元数据")


# --- Aliases for legacy or alternative representations ---
# To resolve import errors in other modules that expect a 'Schema' suffix.
FilterConditionSchema = FilterCondition
FilterGroupSchema = FilterGroup

