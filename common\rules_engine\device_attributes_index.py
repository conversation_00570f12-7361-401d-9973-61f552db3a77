#!/usr/bin/env python3
"""
设备属性索引

提供高效的设备属性查询功能，支持多种属性的快速检索
"""

import logging
import threading
from typing import Dict, List, Any, Optional, Set, Tuple, Union

logger = logging.getLogger(__name__)

class DeviceAttributeIndex:
    """设备属性索引类"""
    
    def __init__(self):
        """初始化设备属性索引"""
        self.lock = threading.RLock()
        
        # 字符串类型属性索引
        self.string_indices: Dict[str, Dict[str, Set[str]]] = {}  # 字段名 -> 字段值 -> 设备ID集合
        
        # 数字类型属性索引
        self.numeric_indices: Dict[str, Dict[float, Set[str]]] = {}  # 字段名 -> 字段值 -> 设备ID集合
        
        # 设备ID到属性值的映射，用于更新
        self.device_attributes: Dict[str, Dict[str, Any]] = {}  # 设备ID -> {字段名 -> 字段值}
        
        # 索引的字段列表
        self.indexed_fields: Set[str] = set()
    
    def add_index(self, field_name: str) -> None:
        """
        添加索引字段
        
        参数:
            field_name: 字段名
        """
        with self.lock:
            # 已经索引过的字段，不重复添加
            if field_name in self.indexed_fields:
                return
            
            # 添加到索引字段集合
            self.indexed_fields.add(field_name)
            
            # 为所有已有设备创建索引
            for device_id, attributes in self.device_attributes.items():
                if field_name in attributes:
                    self._index_attribute(device_id, field_name, attributes[field_name])
    
    def remove_index(self, field_name: str) -> None:
        """
        移除索引字段
        
        参数:
            field_name: 字段名
        """
        with self.lock:
            # 从索引字段集合中移除
            self.indexed_fields.discard(field_name)
            
            # 移除此字段的所有索引
            if field_name in self.string_indices:
                del self.string_indices[field_name]
            if field_name in self.numeric_indices:
                del self.numeric_indices[field_name]
    
    def add_device(self, device_id: str, attributes: Dict[str, Any]) -> None:
        """
        添加设备
        
        参数:
            device_id: 设备ID
            attributes: 设备属性
        """
        with self.lock:
            # 保存设备属性
            self.device_attributes[device_id] = attributes.copy()
            
            # 为所有索引字段建立索引
            for field_name in self.indexed_fields:
                if field_name in attributes:
                    self._index_attribute(device_id, field_name, attributes[field_name])
    
    def update_device(self, device_id: str, attributes: Dict[str, Any]) -> None:
        """
        更新设备
        
        参数:
            device_id: 设备ID
            attributes: 设备属性
        """
        with self.lock:
            if device_id not in self.device_attributes:
                # 设备不存在，直接添加
                self.add_device(device_id, attributes)
                return
            
            old_attributes = self.device_attributes[device_id]
            
            # 找出变更的字段
            for field_name in self.indexed_fields:
                old_value = old_attributes.get(field_name)
                new_value = attributes.get(field_name)
                
                # 值发生变化，更新索引
                if old_value != new_value:
                    # 从旧索引中移除
                    if old_value is not None:
                        self._unindex_attribute(device_id, field_name, old_value)
                    
                    # 添加到新索引
                    if new_value is not None:
                        self._index_attribute(device_id, field_name, new_value)
            
            # 更新保存的属性
            self.device_attributes[device_id] = attributes.copy()
    
    def remove_device(self, device_id: str) -> None:
        """
        移除设备
        
        参数:
            device_id: 设备ID
        """
        with self.lock:
            if device_id not in self.device_attributes:
                return
            
            attributes = self.device_attributes[device_id]
            
            # 从所有索引中移除
            for field_name in self.indexed_fields:
                if field_name in attributes:
                    self._unindex_attribute(device_id, field_name, attributes[field_name])
            
            # 移除保存的属性
            del self.device_attributes[device_id]
    
    def _index_attribute(self, device_id: str, field_name: str, value: Any) -> None:
        """
        为属性创建索引
        
        参数:
            device_id: 设备ID
            field_name: 字段名
            value: 字段值
        """
        if isinstance(value, (int, float)):
            # 处理数字类型
            if field_name not in self.numeric_indices:
                self.numeric_indices[field_name] = {}
            
            float_value = float(value)
            if float_value not in self.numeric_indices[field_name]:
                self.numeric_indices[field_name][float_value] = set()
            
            self.numeric_indices[field_name][float_value].add(device_id)
        elif isinstance(value, str):
            # 处理字符串类型
            if field_name not in self.string_indices:
                self.string_indices[field_name] = {}
            
            if value not in self.string_indices[field_name]:
                self.string_indices[field_name][value] = set()
            
            self.string_indices[field_name][value].add(device_id)
        elif isinstance(value, (list, tuple)):
            # 处理列表类型，为每个元素单独索引
            for item in value:
                if isinstance(item, (str, int, float)):
                    self._index_attribute(device_id, f"{field_name}_item", item)
        elif isinstance(value, dict):
            # 处理字典类型，为每个键值对单独索引
            for key, val in value.items():
                if isinstance(val, (str, int, float)):
                    self._index_attribute(device_id, f"{field_name}_{key}", val)
    
    def _unindex_attribute(self, device_id: str, field_name: str, value: Any) -> None:
        """
        移除属性索引
        
        参数:
            device_id: 设备ID
            field_name: 字段名
            value: 字段值
        """
        if isinstance(value, (int, float)):
            # 处理数字类型
            if field_name in self.numeric_indices:
                float_value = float(value)
                if float_value in self.numeric_indices[field_name]:
                    self.numeric_indices[field_name][float_value].discard(device_id)
                    
                    # 如果集合为空，移除该值的索引
                    if not self.numeric_indices[field_name][float_value]:
                        del self.numeric_indices[field_name][float_value]
        elif isinstance(value, str):
            # 处理字符串类型
            if field_name in self.string_indices:
                if value in self.string_indices[field_name]:
                    self.string_indices[field_name][value].discard(device_id)
                    
                    # 如果集合为空，移除该值的索引
                    if not self.string_indices[field_name][value]:
                        del self.string_indices[field_name][value]
        elif isinstance(value, (list, tuple)):
            # 处理列表类型
            for item in value:
                if isinstance(item, (str, int, float)):
                    self._unindex_attribute(device_id, f"{field_name}_item", item)
        elif isinstance(value, dict):
            # 处理字典类型
            for key, val in value.items():
                if isinstance(val, (str, int, float)):
                    self._unindex_attribute(device_id, f"{field_name}_{key}", val)
    
    def find_devices_by_attribute(self, field_name: str, value: Any) -> Set[str]:
        """
        根据属性查找设备
        
        参数:
            field_name: 字段名
            value: 字段值
            
        返回:
            Set[str]: 设备ID集合
        """
        with self.lock:
            if isinstance(value, (int, float)):
                # 处理数字类型
                if field_name not in self.numeric_indices:
                    return set()
                
                float_value = float(value)
                return self.numeric_indices[field_name].get(float_value, set()).copy()
            elif isinstance(value, str):
                # 处理字符串类型
                if field_name not in self.string_indices:
                    return set()
                
                return self.string_indices[field_name].get(value, set()).copy()
            else:
                # 不支持的类型
                return set()
    
    def find_devices_by_range(self, field_name: str, min_value: float, max_value: float) -> Set[str]:
        """
        根据数值范围查找设备
        
        参数:
            field_name: 字段名
            min_value: 最小值（包含）
            max_value: 最大值（包含）
            
        返回:
            Set[str]: 设备ID集合
        """
        with self.lock:
            if field_name not in self.numeric_indices:
                return set()
            
            result: Set[str] = set()
            
            # 查找范围内的所有值
            for value, device_ids in self.numeric_indices[field_name].items():
                if min_value <= value <= max_value:
                    result.update(device_ids)
            
            return result
    
    def find_devices_by_prefix(self, field_name: str, prefix: str) -> Set[str]:
        """
        根据字符串前缀查找设备
        
        参数:
            field_name: 字段名
            prefix: 前缀
            
        返回:
            Set[str]: 设备ID集合
        """
        with self.lock:
            if field_name not in self.string_indices:
                return set()
            
            result: Set[str] = set()
            
            # 查找所有以指定前缀开头的值
            for value, device_ids in self.string_indices[field_name].items():
                if value.startswith(prefix):
                    result.update(device_ids)
            
            return result
    
    def find_devices_by_suffix(self, field_name: str, suffix: str) -> Set[str]:
        """
        根据字符串后缀查找设备
        
        参数:
            field_name: 字段名
            suffix: 后缀
            
        返回:
            Set[str]: 设备ID集合
        """
        with self.lock:
            if field_name not in self.string_indices:
                return set()
            
            result: Set[str] = set()
            
            # 查找所有以指定后缀结尾的值
            for value, device_ids in self.string_indices[field_name].items():
                if value.endswith(suffix):
                    result.update(device_ids)
            
            return result
    
    def find_devices_by_contains(self, field_name: str, substring: str) -> Set[str]:
        """
        根据字符串包含关系查找设备
        
        参数:
            field_name: 字段名
            substring: 子字符串
            
        返回:
            Set[str]: 设备ID集合
        """
        with self.lock:
            if field_name not in self.string_indices:
                return set()
            
            result: Set[str] = set()
            
            # 查找所有包含指定子字符串的值
            for value, device_ids in self.string_indices[field_name].items():
                if substring in value:
                    result.update(device_ids)
            
            return result
    
    def get_attribute_values(self, field_name: str) -> List[Any]:
        """
        获取某个字段的所有唯一值
        
        参数:
            field_name: 字段名
            
        返回:
            List[Any]: 字段值列表
        """
        with self.lock:
            values = []
            
            # 收集字符串类型的值
            if field_name in self.string_indices:
                values.extend(self.string_indices[field_name].keys())
            
            # 收集数字类型的值
            if field_name in self.numeric_indices:
                values.extend(self.numeric_indices[field_name].keys())
            
            return values
    
    def get_device_count_by_value(self, field_name: str, value: Any) -> int:
        """
        获取具有某个字段值的设备数量
        
        参数:
            field_name: 字段名
            value: 字段值
            
        返回:
            int: 设备数量
        """
        return len(self.find_devices_by_attribute(field_name, value))
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取索引统计信息
        
        返回:
            Dict[str, Any]: 统计信息
        """
        with self.lock:
            stats = {
                "device_count": len(self.device_attributes),
                "indexed_fields": list(self.indexed_fields),
                "string_indices": {},
                "numeric_indices": {},
            }
            
            # 收集字符串索引统计
            for field_name, value_dict in self.string_indices.items():
                stats["string_indices"][field_name] = {
                    "unique_values": len(value_dict),
                    "total_references": sum(len(device_ids) for device_ids in value_dict.values())
                }
            
            # 收集数字索引统计
            for field_name, value_dict in self.numeric_indices.items():
                stats["numeric_indices"][field_name] = {
                    "unique_values": len(value_dict),
                    "total_references": sum(len(device_ids) for device_ids in value_dict.values()),
                    "min_value": min(value_dict.keys()) if value_dict else None,
                    "max_value": max(value_dict.keys()) if value_dict else None
                }
            
            return stats
    
    def clear(self) -> None:
        """清空所有索引"""
        with self.lock:
            self.string_indices.clear()
            self.numeric_indices.clear()
            self.device_attributes.clear()
            # 保留索引字段集合 