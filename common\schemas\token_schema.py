from pydantic import BaseModel, Field
from typing import Optional
import uuid

class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str

class RefreshToken(BaseModel):
    refresh_token: str

class TokenPayload(BaseModel):
    sub: Optional[int] = None
    jti: Optional[str] = None

class LoginRequest(BaseModel):
    username: str
    password: str

class TokenRequestForm(BaseModel):
    """
    This is not a schema, but a dependency that mimics the OAuth2PasswordRequestForm
    to allow for JSON-based token requests instead of form data.
    """
    grant_type: Optional[str] = Field(None, description="urn:ietf:params:oauth:grant-type:token-exchange")
    username: Optional[str] = None
    password: Optional[str] = None
    scope: str = ""
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
