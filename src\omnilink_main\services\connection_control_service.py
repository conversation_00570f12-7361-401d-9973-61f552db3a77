"""
连接控制服务
负责处理设备连接的完整生命周期：连接请求、端口分配、从服务器协调、连接记录、资源释放
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from common.models.device import Device
from common.models.user import User
from common.models.slave_server import SlaveServer
from common.schemas.device_schema import DeviceConnectionRequest, DeviceConnectionResponse
from src.omnilink_main.core.database import get_async_session
from src.omnilink_main.utils.port_pool import get_port_pool
from src.omnilink_main.communication.ws_manager import ws_manager
from src.omnilink_main.core.idle_session_manager import idle_session_manager
from src.omnilink_main.core.exceptions import DeviceNotAvailableError, PermissionDeniedError, SlaveServerOfflineError

logger = logging.getLogger(__name__)

class ConnectionControlService:
    """连接控制服务"""
    
    def __init__(self):
        self.active_connections: Dict[int, Dict[str, Any]] = {}  # device_id -> connection_info
        self.port_pool = get_port_pool()
        
    async def request_device_connection(
        self,
        device_id: int,
        user_id: int,
        user_contact: str,
        db: AsyncSession
    ) -> DeviceConnectionResponse:
        """
        处理设备连接请求
        
        Args:
            device_id: 设备ID
            user_id: 用户ID
            user_contact: 用户联系方式
            db: 数据库会话
            
        Returns:
            DeviceConnectionResponse: 连接响应信息
            
        Raises:
            DeviceNotAvailableError: 设备不可用
            PermissionDeniedError: 权限不足
            SlaveServerOfflineError: 从服务器离线
        """
        logger.info(f"Processing connection request for device {device_id} by user {user_id}")
        
        # 1. 验证设备状态
        device = await self._get_and_validate_device(db, device_id)
        
        # 2. 验证用户权限
        user = await self._get_and_validate_user(db, user_id)
        await self._validate_user_permissions(db, user, device)
        
        # 3. 检查设备可用性
        if device.status != "available":
            raise DeviceNotAvailableError(f"Device {device_id} is not available (status: {device.status})")
            
        if device.current_user_id is not None:
            raise DeviceNotAvailableError(f"Device {device_id} is already in use by user {device.current_user_id}")
        
        # 4. 验证从服务器状态
        slave_server = device.slave_server
        if not slave_server or slave_server.status != "online":
            raise SlaveServerOfflineError(f"Slave server for device {device_id} is offline")
        
        # 5. 分配端口
        allocated_port = self.port_pool.lease_port()
        if not allocated_port:
            raise DeviceNotAvailableError("No available ports for connection")
        
        try:
            # 6. 通知从服务器准备连接
            connection_info = await self._prepare_slave_connection(
                slave_server, device, allocated_port
            )
            
            # 7. 更新设备状态
            await self._update_device_connection_status(
                db, device, user_id, user_contact, allocated_port
            )
            
            # 8. 记录活动连接
            self._record_active_connection(device_id, user_id, allocated_port, connection_info)
            
            # 9. 启动会话监控
            idle_session_manager.start_session(device_id, user_id)
            
            # 10. 返回连接信息
            response = DeviceConnectionResponse(
                device_id=device_id,
                device_name=device.device_name,
                virtualhere_host=slave_server.ip_address,
                virtualhere_port=allocated_port,
                connection_token=connection_info.get("token"),
                expires_at=connection_info.get("expires_at")
            )
            
            logger.info(f"Successfully established connection for device {device_id}")
            return response
            
        except Exception as e:
            # 连接失败，释放端口
            self.port_pool.release_port(allocated_port)
            logger.error(f"Failed to establish connection for device {device_id}: {e}")
            raise
    
    async def disconnect_device(
        self,
        device_id: int,
        user_id: int,
        db: AsyncSession,
        reason: str = "user_requested"
    ) -> bool:
        """
        断开设备连接
        
        Args:
            device_id: 设备ID
            user_id: 用户ID
            db: 数据库会话
            reason: 断开原因
            
        Returns:
            bool: 是否成功断开
        """
        logger.info(f"Processing disconnection for device {device_id} by user {user_id}, reason: {reason}")
        
        # 1. 获取设备信息
        device = await db.get(Device, device_id)
        if not device:
            logger.warning(f"Device {device_id} not found for disconnection")
            return False
            
        # 2. 验证用户权限（只有当前用户或管理员可以断开）
        if device.current_user_id != user_id:
            user = await db.get(User, user_id)
            if not user or not user.is_superuser:
                logger.warning(f"User {user_id} not authorized to disconnect device {device_id}")
                return False
        
        # 3. 获取连接信息
        connection_info = self.active_connections.get(device_id)
        if not connection_info:
            logger.warning(f"No active connection found for device {device_id}")
        
        try:
            # 4. 通知从服务器停止共享
            if device.slave_server:
                await self._stop_slave_sharing(device.slave_server, device, reason)
            
            # 5. 释放端口
            if connection_info and connection_info.get("allocated_port"):
                self.port_pool.release_port(connection_info["allocated_port"])
            
            # 6. 更新设备状态
            await self._update_device_disconnection_status(db, device)
            
            # 7. 清理活动连接记录
            self._cleanup_active_connection(device_id)
            
            # 8. 结束会话监控
            idle_session_manager.end_session(device_id)
            
            logger.info(f"Successfully disconnected device {device_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error during disconnection of device {device_id}: {e}", exc_info=True)
            return False
    
    async def _get_and_validate_device(self, db: AsyncSession, device_id: int) -> Device:
        """获取并验证设备"""
        device = await db.get(Device, device_id)
        if not device:
            raise DeviceNotAvailableError(f"Device {device_id} not found")
        return device
    
    async def _get_and_validate_user(self, db: AsyncSession, user_id: int) -> User:
        """获取并验证用户"""
        user = await db.get(User, user_id)
        if not user:
            raise PermissionDeniedError(f"User {user_id} not found")
        if not user.is_active:
            raise PermissionDeniedError(f"User {user_id} is inactive")
        return user
    
    async def _validate_user_permissions(self, db: AsyncSession, user: User, device: Device):
        """验证用户权限"""
        # TODO: 实现基于规则引擎的权限验证
        # 目前简单验证：用户必须是激活状态
        if not user.is_active:
            raise PermissionDeniedError(f"User {user.id} does not have permission to access device {device.id}")
    
    async def _prepare_slave_connection(
        self,
        slave_server: SlaveServer,
        device: Device,
        allocated_port: int
    ) -> Dict[str, Any]:
        """准备从服务器连接"""
        command = {
            "action": "share_device",
            "device_path": device.device_id,
            "port": allocated_port,
            "command_id": f"connect-{device.id}-{datetime.utcnow().timestamp()}"
        }
        
        # 发送命令到从服务器
        response = await ws_manager.send_command_and_wait(
            str(slave_server.id), 
            command,
            timeout=30
        )
        
        if not response or response.get("status") != "success":
            raise SlaveServerOfflineError(f"Slave server failed to prepare connection: {response}")
        
        return {
            "token": response.get("connection_token"),
            "expires_at": response.get("expires_at"),
            "slave_response": response
        }
    
    async def _stop_slave_sharing(
        self,
        slave_server: SlaveServer,
        device: Device,
        reason: str
    ):
        """停止从服务器共享"""
        command = {
            "action": "unshare_device",
            "device_path": device.device_id,
            "reason": reason,
            "command_id": f"disconnect-{device.id}-{datetime.utcnow().timestamp()}"
        }
        
        try:
            await ws_manager.send_command(str(slave_server.id), command)
            logger.info(f"Sent unshare command to slave server for device {device.id}")
        except Exception as e:
            logger.error(f"Failed to send unshare command for device {device.id}: {e}")
    
    async def _update_device_connection_status(
        self,
        db: AsyncSession,
        device: Device,
        user_id: int,
        user_contact: str,
        allocated_port: int
    ):
        """更新设备连接状态"""
        device.status = "in_use"
        device.current_user_id = user_id
        device.current_user_contact = user_contact
        device.connected_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(device)
    
    async def _update_device_disconnection_status(self, db: AsyncSession, device: Device):
        """更新设备断开状态"""
        device.status = "available"
        device.last_user_id = device.current_user_id
        device.last_user_contact = device.current_user_contact
        device.last_used_at = datetime.utcnow()
        device.current_user_id = None
        device.current_user_contact = None
        device.connected_at = None
        
        await db.commit()
        await db.refresh(device)
    
    def _record_active_connection(
        self,
        device_id: int,
        user_id: int,
        allocated_port: int,
        connection_info: Dict[str, Any]
    ):
        """记录活动连接"""
        self.active_connections[device_id] = {
            "user_id": user_id,
            "allocated_port": allocated_port,
            "connected_at": datetime.utcnow(),
            "connection_info": connection_info
        }
    
    def _cleanup_active_connection(self, device_id: int):
        """清理活动连接记录"""
        if device_id in self.active_connections:
            del self.active_connections[device_id]
    
    def get_active_connections(self) -> Dict[int, Dict[str, Any]]:
        """获取所有活动连接"""
        return self.active_connections.copy()
    
    def get_connection_info(self, device_id: int) -> Optional[Dict[str, Any]]:
        """获取特定设备的连接信息"""
        return self.active_connections.get(device_id)

# 全局实例
connection_control_service = ConnectionControlService()

# 注册空闲会话超时回调
async def handle_session_timeout(device_id: int, user_id: int):
    """处理会话超时"""
    async with get_async_session() as db:
        await connection_control_service.disconnect_device(
            device_id, user_id, db, reason="session_timeout"
        )

idle_session_manager.register_timeout_callback(handle_session_timeout) 