from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
import logging

from common.database.base_class import Base # Import the centralized Base

logger = logging.getLogger(__name__)

# Use the globally configured database URL from settings
from src.omnilink_main.core.config import settings
DATABASE_URL = str(settings.SQLALCHEMY_DATABASE_URI)

db_engine = create_async_engine(DATABASE_URL, echo=False)

AsyncSessionLocal = sessionmaker(
    autocommit=False, autoflush=False, bind=db_engine, class_=AsyncSession
)

async def get_async_session() -> AsyncSession:
    """获取异步数据库会话"""
    async with AsyncSessionLocal() as session:
        yield session

async def init_db():
    logger.info("Initializing database...")
    async with db_engine.begin() as conn:
        # In development, you might want to drop and recreate tables
        # await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)
    logger.info("Database initialization finished.")
