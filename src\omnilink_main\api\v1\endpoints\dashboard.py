from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from src.omnilink_main.dependencies.db import get_db
from common.schemas.api_schema import APIResponse
from common.schemas.dashboard_schema import DashboardStats
from src.omnilink_main.services.dashboard_service import dashboard_service
from src.omnilink_main.dependencies.auth import PermissionChecker

router = APIRouter()

@router.get("/stats", response_model=APIResponse[DashboardStats], dependencies=[Depends(PermissionChecker("dashboard.read"))])
async def get_dashboard_stats(db: AsyncSession = Depends(get_db)):
    """
    Retrieve dashboard statistics.
    Requires 'dashboard.read' permission.
    """
    stats = await dashboard_service.get_stats(db)
    return APIResponse(data=stats)
