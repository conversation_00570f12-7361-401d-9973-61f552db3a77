# requirements-user.txt
# OmniLink 用于专属客户端的依赖
# 根据 oed.md 要求建立的专门依赖文件

# === 专属客户端专属依赖 ===
# 以下依赖仅客户端需要

# Advanced GUI Features
pyqt5-tools>=5.15.9 # Development tools for PyQt5

# Windows Specific Features
win10toast>=0.9; sys_platform == "win32" # Windows 10 toast notifications
keyboard==0.13.5 # Global hotkey support

# Auto-updater
requests-toolbelt>=1.0.0 # Advanced HTTP features for updates

# GUI Framework & Styling
pywinstyles==1.5 
PyQt5==5.15.9
PyQt5-Qt5==5.15.2
PyQt5-sip==12.13.0
PyQtWebEngine>=5.15.6,<6.0.0 # Web engine for Qt5
qt-material==1.3.9
qdarkstyle>=3.2.0 # Dark theme

# Core Libraries
python-dotenv==1.0.0
requests==2.31.0
websockets==12.0
PyYAML==6.0.1
loguru>=0.7.2,<1.0.0 # Logging framework
python-dateutil>=2.8.2,<3.0.0 # Date/time utilities
psutil==5.9.6

# Security & Authentication
PyJWT[crypto]>=2.8.0,<3.0.0 # JWT token handling
keyring>=24.0.0,<25.0.0 # Secure credential storage

# Windows Integration & System Utilities
pywin32==306
setproctitle>=1.3.2,<2.0.0 # Process title modification
pynput>=1.7.6,<2.0.0 # Input monitoring and control

# Communication Encryption/Compression
lz4>=4.3.2,<5.0.0 # Fast compression
pycryptodome>=3.20.0,<4.0.0 # Advanced cryptographic functions

# Packaging (Development/Build time)
PyInstaller>=6.0.0,<7.0.0 # Application packaging

# Network and Hardware Discovery (if needed)
zeroconf>=0.131.0 # Service discovery
netifaces>=0.11.0 # Network interface information
pystun3==0.1.0

# Configuration and Data Handling
configparser # Built-in, but explicit for clarity
json5>=0.9.0 # Enhanced JSON parsing

# Optional: System tray support
pystray>=0.19.0 # System tray icon support

# Optional: Advanced logging
concurrent-log-handler>=0.9.25 # Multi-process safe logging

# 开发工具
black==23.11.0
flake8==6.1.0
pytest==7.4.3
qt-material
