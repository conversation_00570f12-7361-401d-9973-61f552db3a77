import logging
from typing import Optional, Dict, Any
from datetime import timedelta, datetime # Added datetime

# Attempt to import IPReputationEntry and cachetools
try:
    from .models import IPReputationEntry
    _IP_REPUTATION_ENTRY_AVAILABLE = True
except ImportError:
    IPReputationEntry = None # type: ignore
    _IP_REPUTATION_ENTRY_AVAILABLE = False
    logging.warning("IPReputationEntry model not found. IPReputationService will use fallback logic for VPN/Proxy checks.")

try:
    from cachetools import TTLCache # Removed 'cached' as it's not used directly here
    _CACHE_AVAILABLE = True
except ImportError:
    TTLCache = None # type: ignore
    # cached = None # type: ignore # 'cached' decorator not used
    _CACHE_AVAILABLE = False
    logging.warning("cachetools library not found. IPReputationService will operate without caching for DB lookups.")

logger = logging.getLogger(__name__)

class IPReputationService:
    """
    Service for IP reputation and geolocation checks.
    Connects to IPReputationEntry model for local reputation data
    and can be extended for external services.
    """
    # _known_vpn_ips = {"*************", "************"} # Example VPN IPs - REMOVED
    # _known_proxy_ips = {"**********"} # Example Proxy IPs - REMOVED
    _private_ip_ranges = [
        ("10.0.0.0", "**************"),
        ("**********", "**************"),
        ("***********", "***************"),
        ("*********", "***************") # Loopback
    ]

    CACHE_TTL = timedelta(hours=1)
    # Initialize cache only if cachetools is available
    if _CACHE_AVAILABLE and TTLCache is not None: # Ensure TTLCache is not None before calling
        _ip_check_cache: Optional['TTLCache'] = TTLCache(maxsize=1024, ttl=CACHE_TTL.total_seconds())
    else:
        _ip_check_cache: Optional['TTLCache'] = None

    @staticmethod
    def _ip_to_int(ip: str) -> Optional[int]:
        "Converts an IPv4 address string to an integer."
        parts = ip.split('.')
        if len(parts) != 4:
            # Basic IPv6 check (very simplified, not for production validation)
            if ':' in ip: 
                # For now, we don't convert IPv6 to int for range checks in this basic service
                # but we should not immediately return None if it's potentially a valid IPv6 for other checks.
                # Let other methods handle IPv6 if they support it.
                return None # Still return None for int conversion as this is for IPv4 ranges
            return None
        try:
            return (int(parts[0]) << 24) + (int(parts[1]) << 16) + (int(parts[2]) << 8) + int(parts[3])
        except ValueError:
            return None

    @staticmethod
    def is_private_ip(ip_address: str) -> bool:
        """Checks if the given IP address is a private IP address."""
        logger.debug(f"IPReputationService: Checking if {ip_address} is private.")
        # Handle IPv6 private ranges (ULA, link-local) if necessary for full production readiness
        # For now, focusing on IPv4 as per existing _ip_to_int logic
        if ':' in ip_address: # Basic IPv6 check
            # Add IPv6 private IP checks if needed. For example:
            # from ipaddress import ip_address as ipaddr
            # if ipaddr(ip_address).is_private: return True
            # This service's _ip_to_int is for IPv4, so IPv6 won't match ranges below.
            return False # Assuming non-IPv4 is not private by this method's old logic

        ip_int = IPReputationService._ip_to_int(ip_address)
        if ip_int is None: # Not a valid IPv4 format for this simple checker
            return False 
        for range_start, range_end in IPReputationService._private_ip_ranges:
            start_int = IPReputationService._ip_to_int(range_start)
            end_int = IPReputationService._ip_to_int(range_end)
            if start_int is not None and end_int is not None and start_int <= ip_int <= end_int:
                return True
        return False

    @staticmethod
    def _fetch_ip_reputation_from_db(ip_address: str) -> Optional[IPReputationEntry]: # type: ignore
        """
        Internal method to fetch IP reputation from the database.
        This method is what will be cached.
        """
        if not _IP_REPUTATION_ENTRY_AVAILABLE or IPReputationEntry is None:
            return None
        try:
            entry = IPReputationEntry.get_or_none(
                (IPReputationEntry.ip_address == ip_address) &
                (IPReputationEntry.is_active == True) &
                ((IPReputationEntry.expires_at == None) | (IPReputationEntry.expires_at > datetime.utcnow()))
            )
            return entry # type: ignore
        except Exception as e:
            logger.error(f"Error querying IPReputationEntry for {ip_address}: {e}", exc_info=True)
            return None

    @staticmethod
    def is_known_vpn_or_proxy(ip_address: str) -> bool:
        """Checks if the IP is a known VPN or Proxy using the IPReputationEntry model with caching."""
        logger.debug(f"IPReputationService: Checking VPN/Proxy for {ip_address} using database.")

        if not _IP_REPUTATION_ENTRY_AVAILABLE or IPReputationEntry is None:
            logger.warning("IPReputationEntry model not available, cannot check DB for VPN/Proxy.")
            return False # Fallback: if model is not available, assume not VPN/Proxy

        cached_result = None
        if IPReputationService._ip_check_cache is not None:
            try:
                cached_result = IPReputationService._ip_check_cache.get(ip_address)
            except Exception as e: # Broad exception for unforeseen cache issues
                logger.warning(f"Cache get failed for {ip_address}: {e}. Will query DB.")


        if cached_result is not None:
            logger.debug(f"Cache hit for {ip_address}: VPN/Proxy status is {cached_result}.")
            return cached_result

        logger.debug(f"Cache miss for {ip_address}. Querying database.")
        entry = IPReputationService._fetch_ip_reputation_from_db(ip_address)
        
        is_vpn_or_proxy = False
        if entry:
            if entry.reputation_type.lower() in ['vpn', 'proxy']:
                is_vpn_or_proxy = True
        
        if IPReputationService._ip_check_cache is not None:
            try:
                IPReputationService._ip_check_cache[ip_address] = is_vpn_or_proxy
                logger.debug(f"Stored VPN/Proxy status for {ip_address} in cache: {is_vpn_or_proxy}")
            except Exception as e: # Broad exception for unforeseen cache issues
                 logger.warning(f"Cache set failed for {ip_address}: {e}.")

        return is_vpn_or_proxy

    @staticmethod
    def get_geolocation(ip_address: str) -> Optional[Dict[str, Any]]:
        """
        获取IP地址的地理位置数据。
        从数据库或集成的GeoIP服务获取信息。
        """
        logger.debug(f"IPReputationService: 获取IP {ip_address} 的地理位置信息")
        
        # 首先从数据库中获取地理位置数据（如果有）
        if _IP_REPUTATION_ENTRY_AVAILABLE and IPReputationEntry is not None:
            entry = IPReputationService._fetch_ip_reputation_from_db(ip_address)
            if entry:
                # 尝试从entry.geo_data JSON字段获取地理位置数据（如果有）
                if hasattr(entry, 'geo_data') and entry.geo_data:
                    try:
                        if isinstance(entry.geo_data, dict):
                            geo_data = entry.geo_data
                        else:
                            # 如果存储为JSON字符串
                            import json
                            geo_data = json.loads(entry.geo_data)
                        
                        # 验证geo_data包含所需字段
                        required_fields = ["latitude", "longitude", "city", "country"]
                        if all(field in geo_data for field in required_fields):
                            logger.debug(f"从数据库获取到IP {ip_address} 的地理位置数据")
                            return geo_data
                    except Exception as e:
                        logger.warning(f"解析IP {ip_address} 的数据库地理位置数据时出错: {e}")
                
                # 如果没有geo_data字段或解析失败，尝试从tags解析
                if entry.tags:
                    try:
                        tags_dict = dict(tag.split(':', 1) for tag in entry.tags.split(',') if ':' in tag)
                        geo_data = {}
                        if "country" in tags_dict: geo_data["country"] = tags_dict["country"]
                        if "city" in tags_dict: geo_data["city"] = tags_dict["city"]
                        if "asn" in tags_dict: geo_data["asn"] = tags_dict["asn"]
                        if "asn_org" in tags_dict: geo_data["asn_organization"] = tags_dict["asn_org"]
                        if "lat" in tags_dict: geo_data["latitude"] = float(tags_dict["lat"])
                        if "lon" in tags_dict: geo_data["longitude"] = float(tags_dict["lon"])
                        
                        if geo_data and ("country" in geo_data or "city" in geo_data):
                            logger.debug(f"从数据库标签中解析到IP {ip_address} 的地理位置数据")
                            # 填充缺失的标准字段
                            geo_data.setdefault("latitude", None)
                            geo_data.setdefault("longitude", None)
                            geo_data.setdefault("city", "未知城市")
                            geo_data.setdefault("country", "未知国家")
                            if "asn" in geo_data and "asn_organization" not in geo_data:
                                geo_data["asn_organization"] = f"ASN {geo_data['asn']} 的组织"
                            return geo_data
                    except Exception as e:
                        logger.warning(f"从标签解析IP {ip_address} 的地理位置数据时出错: {e}")
        
        # 尝试使用MaxMind GeoIP数据库（如果可用）
        try:
            import maxminddb
            geoip_db_path = None
            
            # 尝试从配置获取数据库路径
            try:
                from main_server.config import get_config
                config = get_config()
                geoip_db_path = config.get('geoip', {}).get('db_path')
            except ImportError:
                logger.debug("无法导入配置模块来获取GeoIP数据库路径")
            
            # 默认路径（如果配置中没有）
            if not geoip_db_path:
                import os
                possible_paths = [
                    "/etc/ky/geoip/GeoLite2-City.mmdb",
                    "C:\\ky\\geoip\\GeoLite2-City.mmdb",
                    os.path.join(os.path.dirname(__file__), "../../../data/geoip/GeoLite2-City.mmdb")
                ]
                
                for path in possible_paths:
                    if os.path.exists(path):
                        geoip_db_path = path
                        break
            
            if geoip_db_path and os.path.exists(geoip_db_path):
                with maxminddb.open_database(geoip_db_path) as reader:
                    result = reader.get(ip_address)
                    if result:
                        geo_data = {}
                        
                        # 提取国家信息
                        if 'country' in result:
                            geo_data['country'] = result['country'].get('names', {}).get('zh-CN') or result['country'].get('names', {}).get('en')
                        
                        # 提取城市信息
                        if 'city' in result:
                            geo_data['city'] = result['city'].get('names', {}).get('zh-CN') or result['city'].get('names', {}).get('en')
                        
                        # 提取经纬度
                        if 'location' in result:
                            geo_data['latitude'] = result['location'].get('latitude')
                            geo_data['longitude'] = result['location'].get('longitude')
                        
                        # 提取ASN信息（如果使用GeoLite2-ASN数据库或合并了ASN数据）
                        if 'autonomous_system' in result:
                            geo_data['asn'] = f"AS{result['autonomous_system'].get('number')}"
                            geo_data['asn_organization'] = result['autonomous_system'].get('organization')
                        
                        # 确保所有必需字段都有默认值
                        geo_data.setdefault('country', '未知国家')
                        geo_data.setdefault('city', '未知城市')
                        geo_data.setdefault('latitude', None)
                        geo_data.setdefault('longitude', None)
                        geo_data.setdefault('asn', 'ASN未知')
                        geo_data.setdefault('asn_organization', '未知组织')
                        
                        logger.debug(f"从MaxMind GeoIP数据库获取到IP {ip_address} 的地理位置数据")
                        return geo_data
            else:
                logger.debug(f"未找到MaxMind GeoIP数据库文件，尝试回退到硬编码数据")
        except ImportError:
            logger.debug("maxminddb库未安装，无法使用MaxMind GeoIP数据库")
        except Exception as e:
            logger.warning(f"使用MaxMind GeoIP获取IP {ip_address} 的地理位置数据时出错: {e}")
        
        # 回退到硬编码的示例数据（如果上述方法都失败）
        if ip_address == "*******": 
            return {"latitude": 40.7128, "longitude": -74.0060, "city": "纽约", "country": "美国", "asn": "AS15169", "asn_organization": "Google LLC"}
        if ip_address == "*******": 
            return {"latitude": 37.4220, "longitude": -122.0841, "city": "山景城", "country": "美国", "asn": "AS15169", "asn_organization": "Google LLC"}
        
        # 检查是否是已知的VPN/代理
        if IPReputationService.is_known_vpn_or_proxy(ip_address):
            db_entry = IPReputationService._fetch_ip_reputation_from_db(ip_address)
            asn_org = "已知VPN/代理服务商"
            asn_id = "AS-ANONYMIZER"
            
            if db_entry:
                if db_entry.source:
                    asn_org = db_entry.source
                if db_entry.tags:
                    try:
                        tags_dict = dict(tag.split(':', 1) for tag in db_entry.tags.split(',') if ':' in tag)
                        if "asn_org" in tags_dict: asn_org = tags_dict["asn_org"]
                        if "asn" in tags_dict: asn_id = tags_dict["asn"]
                    except Exception:
                        pass

            return {"latitude": None, "longitude": None, "city": "VPN/代理网络", "country": "未知", "asn": asn_id, "asn_organization": asn_org}

        # 检查是否是私有IP
        if IPReputationService.is_private_ip(ip_address):
            return {"latitude": None, "longitude": None, "city": "私有网络", "country": "内部", "asn": "内部", "asn_organization": "私有网络"}
        
        # 示例移动运营商IP
        if ip_address == "***************":
            return {"latitude": 34.0522, "longitude": -118.2437, "city": "洛杉矶", "country": "美国", "asn": "AS-MOBILECARRIER", "asn_organization": "示例移动运营商"}
        
        logger.debug(f"未找到IP {ip_address} 的特定地理位置信息，返回None")
        return None

    @staticmethod
    def get_asn_info(ip_address: str) -> Optional[Dict[str, Any]]:
        """
        获取IP地址的ASN（自治系统编号）信息
        
        Args:
            ip_address: 要查询的IP地址
            
        Returns:
            包含ASN信息的字典，如果无法获取则返回None
            返回字典包含：asn（AS编号）、organization（组织名称）、
            network（网络范围）等字段
        """
        logger.debug(f"IPReputationService: 获取IP {ip_address} 的ASN信息")
        
        # 首先尝试从地理位置数据中提取ASN信息
        geo_data = IPReputationService.get_geolocation(ip_address)
        if geo_data and ('asn' in geo_data or 'asn_organization' in geo_data):
            asn_info = {
                'asn': geo_data.get('asn', 'AS0'),
                'organization': geo_data.get('asn_organization', '未知组织'),
                'network': geo_data.get('network', '未知网络')
            }
            logger.debug(f"从地理位置数据中获取到IP {ip_address} 的ASN信息: {asn_info}")
            return asn_info
            
        # 尝试使用MaxMind ASN数据库（如果可用）
        try:
            import maxminddb
            asn_db_path = None
            
            # 尝试从配置获取ASN数据库路径
            try:
                from main_server.config import get_config
                config = get_config()
                asn_db_path = config.get('geoip', {}).get('asn_db_path')
            except ImportError:
                logger.debug("无法导入配置模块来获取ASN数据库路径")
            
            # 默认路径（如果配置中没有）
            if not asn_db_path:
                import os
                possible_paths = [
                    "/etc/ky/geoip/GeoLite2-ASN.mmdb",
                    "C:\\ky\\geoip\\GeoLite2-ASN.mmdb",
                    os.path.join(os.path.dirname(__file__), "../../../data/geoip/GeoLite2-ASN.mmdb")
                ]
                
                for path in possible_paths:
                    if os.path.exists(path):
                        asn_db_path = path
                        break
            
            if asn_db_path and os.path.exists(asn_db_path):
                with maxminddb.open_database(asn_db_path) as reader:
                    result = reader.get(ip_address)
                    if result:
                        asn_info = {
                            'asn': f"AS{result.get('autonomous_system_number', 0)}",
                            'organization': result.get('autonomous_system_organization', '未知组织'),
                            'network': result.get('network', '未知网络')
                        }
                        logger.debug(f"从MaxMind ASN数据库获取到IP {ip_address} 的ASN信息: {asn_info}")
                        return asn_info
        except ImportError:
            logger.warning("maxminddb模块不可用，无法使用MaxMind ASN数据库")
        except Exception as e:
            logger.error(f"从MaxMind ASN数据库获取IP {ip_address} 的ASN信息时出错: {e}", exc_info=True)
        
        # 如果本地方法都失败，尝试使用外部API
        try:
            import requests
            # 使用免费的ipapi.co服务（有请求限制）
            response = requests.get(f"https://ipapi.co/{ip_address}/json/", timeout=3)
            if response.status_code == 200:
                data = response.json()
                if 'asn' in data:
                    asn_info = {
                        'asn': data.get('asn', 'AS0'),
                        'organization': data.get('org', '未知组织'),
                        'network': data.get('network', '未知网络')
                    }
                    logger.debug(f"从ipapi.co获取到IP {ip_address} 的ASN信息: {asn_info}")
                    return asn_info
        except ImportError:
            logger.warning("requests模块不可用，无法使用外部API获取ASN信息")
        except Exception as e:
            logger.error(f"从外部API获取IP {ip_address} 的ASN信息时出错: {e}", exc_info=True)
        
        # 所有方法都失败，返回None
        logger.warning(f"无法获取IP {ip_address} 的ASN信息")
        return None

    @staticmethod
    def calculate_distance_km(geo1: Dict[str, Any], geo2: Dict[str, Any]) -> float:
        """ 
        Placeholder for Haversine distance calculation. 
        Requires actual lat/lon values.
        """
        # This method remains a placeholder or would need a proper math library / geo library
        if geo1 and geo2 and \
           isinstance(geo1.get("latitude"), (float, int)) and isinstance(geo1.get("longitude"), (float, int)) and \
           isinstance(geo2.get("latitude"), (float, int)) and isinstance(geo2.get("longitude"), (float, int)):
            
            lat1, lon1 = geo1["latitude"], geo1["longitude"]
            lat2, lon2 = geo2["latitude"], geo2["longitude"]
            
            # Simplified placeholder distance calculation - NOT GEOGRAPHICALLY ACCURATE
            # Replace with Haversine formula for production
            # import math
            # R = 6371 # Radius of Earth in kilometers
            # lat1_rad = math.radians(lat1)
            # lon1_rad = math.radians(lon1)
            # lat2_rad = math.radians(lat2)
            # lon2_rad = math.radians(lon2)
            # dlon = lon2_rad - lon1_rad
            # dlat = lat2_rad - lat1_rad
            # a = math.sin(dlat / 2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2)**2
            # c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
            # distance = R * c
            # return distance

            lat_diff_deg = abs(lat1 - lat2)
            lon_diff_deg = abs(lon1 - lon2)
            # Very rough approximation
            return (lat_diff_deg * 111) + (lon_diff_deg * 80) 
        return float('inf') 
