#!/usr/bin/env python3
"""
设备筛选引擎

提供高性能的多条件筛选引擎，支持复杂查询构建与高效执行
"""

import re
import logging
import threading
from typing import Dict, List, Any, Optional, Set, Callable, Union, Tuple
from datetime import datetime
from enum import Enum, auto
import time
from collections import defaultdict
import json

logger = logging.getLogger(__name__)

class FilterOperator(Enum):
    """筛选操作符枚举"""
    EQUALS = auto()               # 等于
    NOT_EQUALS = auto()           # 不等于
    GREATER_THAN = auto()         # 大于
    GREATER_THAN_EQUALS = auto()  # 大于等于
    LESS_THAN = auto()            # 小于
    LESS_THAN_EQUALS = auto()     # 小于等于
    CONTAINS = auto()             # 包含
    NOT_CONTAINS = auto()         # 不包含
    STARTS_WITH = auto()          # 开头是
    ENDS_WITH = auto()            # 结尾是
    MATCHES_REGEX = auto()        # 匹配正则
    IN = auto()                   # 在列表中
    NOT_IN = auto()               # 不在列表中
    EXISTS = auto()               # 字段存在
    NOT_EXISTS = auto()           # 字段不存在

    @classmethod
    def from_string(cls, op_str: str) -> 'FilterOperator':
        """
        从字符串获取操作符
        
        参数:
            op_str: 操作符字符串
            
        返回:
            FilterOperator: 操作符枚举值
        """
        mapping = {
            "eq": cls.EQUALS,
            "equals": cls.EQUALS,
            "neq": cls.NOT_EQUALS,
            "not_equals": cls.NOT_EQUALS,
            "gt": cls.GREATER_THAN,
            "greater_than": cls.GREATER_THAN,
            "gte": cls.GREATER_THAN_EQUALS,
            "greater_than_equals": cls.GREATER_THAN_EQUALS,
            "lt": cls.LESS_THAN,
            "less_than": cls.LESS_THAN,
            "lte": cls.LESS_THAN_EQUALS,
            "less_than_equals": cls.LESS_THAN_EQUALS,
            "contains": cls.CONTAINS,
            "not_contains": cls.NOT_CONTAINS,
            "starts_with": cls.STARTS_WITH,
            "ends_with": cls.ENDS_WITH,
            "matches_regex": cls.MATCHES_REGEX,
            "regex": cls.MATCHES_REGEX,
            "in": cls.IN,
            "not_in": cls.NOT_IN,
            "exists": cls.EXISTS,
            "not_exists": cls.NOT_EXISTS
        }
        op_str = op_str.lower()
        if op_str not in mapping:
            raise ValueError(f"不支持的筛选操作符: {op_str}")
        return mapping[op_str]

class FilterLogic(Enum):
    """筛选逻辑枚举"""
    AND = auto()  # 所有条件都满足
    OR = auto()   # 任一条件满足
    NOT = auto()  # 所有条件都不满足

    @classmethod
    def from_string(cls, logic_str: str) -> 'FilterLogic':
        """
        从字符串获取逻辑操作符
        
        参数:
            logic_str: 逻辑操作符字符串
            
        返回:
            FilterLogic: 逻辑操作符枚举值
        """
        logic_str = logic_str.upper()
        if logic_str == "AND":
            return cls.AND
        elif logic_str == "OR":
            return cls.OR
        elif logic_str == "NOT":
            return cls.NOT
        raise ValueError(f"不支持的筛选逻辑: {logic_str}")

class FilterCondition:
    """筛选条件类"""
    
    def __init__(self, 
                 field: str, 
                 operator: Union[FilterOperator, str], 
                 value: Any = None,
                 case_sensitive: bool = False):
        """
        初始化筛选条件
        
        参数:
            field: 字段名称
            operator: 操作符
            value: 比较值
            case_sensitive: 是否区分大小写（字符串比较时）
        """
        self.field = field
        self.operator = operator if isinstance(operator, FilterOperator) else FilterOperator.from_string(operator)
        self.value = value
        self.case_sensitive = case_sensitive
        self._compiled_regex = None
        
        # 预编译正则表达式
        if self.operator == FilterOperator.MATCHES_REGEX and isinstance(value, str):
            flags = 0 if case_sensitive else re.IGNORECASE
            try:
                self._compiled_regex = re.compile(value, flags)
            except re.error as e:
                logger.error(f"无效的正则表达式 '{value}': {e}")
                self._compiled_regex = None
    
    def matches(self, device: Dict[str, Any]) -> bool:
        """
        检查设备是否匹配此条件
        
        参数:
            device: 设备数据
            
        返回:
            bool: 是否匹配
        """
        # 检查字段是否存在
        if self.operator == FilterOperator.EXISTS:
            return self.field in device
        
        if self.operator == FilterOperator.NOT_EXISTS:
            return self.field not in device
        
        # 字段不存在时其他操作符返回False
        if self.field not in device:
            return False
        
        device_value = device[self.field]
        
        # 处理字符串值（区分大小写）
        if isinstance(device_value, str) and isinstance(self.value, str) and not self.case_sensitive:
            device_value = device_value.lower()
            compare_value = self.value.lower()
        else:
            compare_value = self.value
        
        # 根据操作符进行比较
        if self.operator == FilterOperator.EQUALS:
            return device_value == compare_value
        
        elif self.operator == FilterOperator.NOT_EQUALS:
            return device_value != compare_value
        
        elif self.operator == FilterOperator.GREATER_THAN:
            try:
                return device_value > compare_value
            except TypeError:
                return False
        
        elif self.operator == FilterOperator.GREATER_THAN_EQUALS:
            try:
                return device_value >= compare_value
            except TypeError:
                return False
        
        elif self.operator == FilterOperator.LESS_THAN:
            try:
                return device_value < compare_value
            except TypeError:
                return False
        
        elif self.operator == FilterOperator.LESS_THAN_EQUALS:
            try:
                return device_value <= compare_value
            except TypeError:
                return False
        
        elif self.operator == FilterOperator.CONTAINS:
            if isinstance(device_value, str) and isinstance(compare_value, str):
                return compare_value in device_value
            elif isinstance(device_value, (list, tuple, set)):
                return compare_value in device_value
            return False
        
        elif self.operator == FilterOperator.NOT_CONTAINS:
            if isinstance(device_value, str) and isinstance(compare_value, str):
                return compare_value not in device_value
            elif isinstance(device_value, (list, tuple, set)):
                return compare_value not in device_value
            return True
        
        elif self.operator == FilterOperator.STARTS_WITH:
            if isinstance(device_value, str) and isinstance(compare_value, str):
                return device_value.startswith(compare_value)
            return False
        
        elif self.operator == FilterOperator.ENDS_WITH:
            if isinstance(device_value, str) and isinstance(compare_value, str):
                return device_value.endswith(compare_value)
            return False
        
        elif self.operator == FilterOperator.MATCHES_REGEX:
            if not isinstance(device_value, str):
                return False
            if not self._compiled_regex:
                return False
            return bool(self._compiled_regex.search(device_value))
        
        elif self.operator == FilterOperator.IN:
            if not isinstance(compare_value, (list, tuple, set)):
                return False
            return device_value in compare_value
        
        elif self.operator == FilterOperator.NOT_IN:
            if not isinstance(compare_value, (list, tuple, set)):
                return True
            return device_value not in compare_value
        
        # 未知操作符
        logger.warning(f"未知的筛选操作符: {self.operator}")
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        返回:
            Dict[str, Any]: 条件字典
        """
        return {
            "field": self.field,
            "operator": self.operator.name,
            "value": self.value,
            "case_sensitive": self.case_sensitive
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FilterCondition':
        """
        从字典创建条件
        
        参数:
            data: 条件字典
            
        返回:
            FilterCondition: 条件对象
        """
        return cls(
            field=data["field"],
            operator=FilterOperator[data["operator"]] if isinstance(data["operator"], str) and data["operator"] in FilterOperator.__members__ else data["operator"],
            value=data["value"],
            case_sensitive=data.get("case_sensitive", False)
        )

class FilterGroup:
    """筛选条件组类"""
    
    def __init__(self, 
                 conditions: Optional[List[FilterCondition]] = None,
                 subgroups: Optional[List['FilterGroup']] = None,
                 logic: Union[FilterLogic, str] = FilterLogic.AND,
                 name: Optional[str] = None,
                 description: Optional[str] = None):
        """
        初始化筛选条件组
        
        参数:
            conditions: 条件列表
            subgroups: 子条件组列表
            logic: 条件组逻辑（AND/OR/NOT）
            name: 条件组名称
            description: 条件组描述
        """
        self.conditions = conditions or []
        self.subgroups = subgroups or []
        self.logic = logic if isinstance(logic, FilterLogic) else FilterLogic.from_string(logic)
        self.name = name
        self.description = description
    
    def matches(self, device: Dict[str, Any]) -> bool:
        """
        检查设备是否匹配此条件组
        
        参数:
            device: 设备数据
            
        返回:
            bool: 是否匹配
        """
        if not self.conditions and not self.subgroups:
            return True
        
        # 检查所有条件
        condition_results = [condition.matches(device) for condition in self.conditions]
        
        # 检查所有子组
        subgroup_results = [subgroup.matches(device) for subgroup in self.subgroups]
        
        # 组合条件和子组结果
        all_results = condition_results + subgroup_results
        
        # 如果没有任何结果，返回True
        if not all_results:
            return True
        
        # 根据逻辑组合结果
        if self.logic == FilterLogic.AND:
            return all(all_results)
        elif self.logic == FilterLogic.OR:
            return any(all_results)
        elif self.logic == FilterLogic.NOT:
            return not any(all_results)
        else:
            logger.warning(f"未知逻辑类型: {self.logic}")
            return False
    
    def add_condition(self, condition: FilterCondition) -> None:
        """
        添加条件
        
        参数:
            condition: 条件对象
        """
        self.conditions.append(condition)
    
    def add_subgroup(self, subgroup: 'FilterGroup') -> None:
        """
        添加子条件组
        
        参数:
            subgroup: 子条件组对象
        """
        self.subgroups.append(subgroup)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        返回:
            Dict[str, Any]: 条件组字典
        """
        return {
            "conditions": [condition.to_dict() for condition in self.conditions],
            "subgroups": [subgroup.to_dict() for subgroup in self.subgroups],
            "logic": self.logic.name,
            "name": self.name,
            "description": self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FilterGroup':
        """
        从字典创建条件组
        
        参数:
            data: 条件组字典
            
        返回:
            FilterGroup: 条件组对象
        """
        conditions = [FilterCondition.from_dict(condition_data) for condition_data in data.get("conditions", [])]
        
        # 递归创建子组
        subgroups = []
        for subgroup_data in data.get("subgroups", []):
            subgroups.append(cls.from_dict(subgroup_data))
        
        return cls(
            conditions=conditions,
            subgroups=subgroups,
            logic=FilterLogic[data["logic"]] if isinstance(data["logic"], str) and data["logic"] in FilterLogic.__members__ else data["logic"],
            name=data.get("name"),
            description=data.get("description")
        )

class FilterCache:
    """
    过滤器缓存类，用于存储过滤结果以提高性能
    """
    
    def __init__(self, max_size: int = 100, ttl: int = 300):
        """
        初始化过滤器缓存
        
        参数:
            max_size: 缓存最大条目数
            ttl: 缓存条目生存时间（秒）
        """
        self._cache: Dict[str, Any] = {}
        self._timestamps: Dict[str, float] = {}
        self._max_size = max_size
        self._ttl = ttl
        self._hits = 0
        self._misses = 0
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存项
        
        参数:
            key: 缓存键
            
        返回:
            Optional[Any]: 缓存值，如果不存在或已过期则返回None
        """
        with self._lock:
            # 检查键是否存在
            if key not in self._cache:
                self._misses += 1
                return None
            
            # 检查是否过期
            now = time.time()
            if now - self._timestamps[key] > self._ttl:
                # 删除过期项
                self._delete(key)
                self._misses += 1
                return None
            
            # 更新访问时间
            self._timestamps[key] = now
            self._hits += 1
            return self._cache[key]
    
    def put(self, key: str, value: Any) -> None:
        """
        添加或更新缓存项
        
        参数:
            key: 缓存键
            value: 缓存值
        """
        with self._lock:
            # 检查缓存是否已满
            if len(self._cache) >= self._max_size and key not in self._cache:
                # 删除最旧的项
                self._evict_oldest()
            
            # 添加或更新缓存项
            self._cache[key] = value
            self._timestamps[key] = time.time()
    
    def delete(self, key: str) -> None:
        """
        删除缓存项
        
        参数:
            key: 缓存键
        """
        with self._lock:
            self._delete(key)
    
    def _delete(self, key: str) -> None:
        """
        删除缓存项（内部方法，无锁）
        
        参数:
            key: 缓存键
        """
        if key in self._cache:
            del self._cache[key]
            del self._timestamps[key]
    
    def _evict_oldest(self) -> None:
        """
        驱逐最旧的缓存项（内部方法，无锁）
        """
        if not self._timestamps:
            return
        
        # 找到时间戳最小的键
        oldest_key = min(self._timestamps, key=self._timestamps.get)
        self._delete(oldest_key)
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._timestamps.clear()
    
    def invalidate_pattern(self, pattern: str) -> int:
        """
        使匹配模式的缓存项失效
        
        参数:
            pattern: 键模式
            
        返回:
            int: 失效的缓存项数量
        """
        count = 0
        with self._lock:
            # 查找所有匹配模式的键
            keys_to_delete = [k for k in self._cache.keys() if pattern in k]
            
            # 删除匹配的键
            for key in keys_to_delete:
                self._delete(key)
                count += 1
        
        return count
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        返回:
            Dict[str, Any]: 缓存统计信息
        """
        with self._lock:
            return {
                "size": len(self._cache),
                "max_size": self._max_size,
                "ttl": self._ttl,
                "hits": self._hits,
                "misses": self._misses,
                "hit_ratio": self._hits / (self._hits + self._misses) if (self._hits + self._misses) > 0 else 0
            }

class DeviceFilterEngine:
    """
    设备过滤引擎类，用于管理设备数据并执行过滤操作
    """
    
    def __init__(self, cache_size: int = 100, cache_ttl: int = 300):
        """
        初始化设备过滤引擎
        
        参数:
            cache_size: 缓存最大条目数
            cache_ttl: 缓存条目生存时间（秒）
        """
        # 设备数据存储，键为设备ID，值为设备数据
        self._devices: Dict[str, Dict[str, Any]] = {}
        
        # 设备层级关系，键为父设备ID，值为子设备ID集合
        self._device_hierarchy: Dict[str, Set[str]] = {}
        
        # 过滤结果缓存
        self._cache = FilterCache(max_size=cache_size, ttl=cache_ttl)
        
        # 线程锁，用于线程安全操作
        self._lock = threading.RLock()
    
    def add_device(self, device_id: str, device_data: Dict[str, Any]) -> bool:
        """
        添加设备
        
        参数:
            device_id: 设备ID
            device_data: 设备数据
            
        返回:
            bool: 添加是否成功
        """
        with self._lock:
            # 检查设备是否已存在
            if device_id in self._devices:
                logger.warning(f"添加设备失败，设备已存在: {device_id}")
                return False
            
            # 添加设备数据
            self._devices[device_id] = device_data.copy()
            
            # 处理设备层级关系
            parent_id = device_data.get("parent_id")
            if parent_id:
                if parent_id not in self._device_hierarchy:
                    self._device_hierarchy[parent_id] = set()
                self._device_hierarchy[parent_id].add(device_id)
            
            # 使相关缓存失效
            self._invalidate_related_cache(device_id)
            
            logger.info(f"成功添加设备: {device_id}")
            return True
    
    def update_device(self, device_id: str, device_data: Dict[str, Any]) -> bool:
        """
        更新设备数据
        
        参数:
            device_id: 设备ID
            device_data: 设备数据
            
        返回:
            bool: 更新是否成功
        """
        with self._lock:
            # 检查设备是否存在
            if device_id not in self._devices:
                logger.warning(f"更新设备失败，设备不存在: {device_id}")
                return False
            
            # 获取旧的父设备ID
            old_parent_id = self._devices[device_id].get("parent_id")
            
            # 获取新的父设备ID
            new_parent_id = device_data.get("parent_id")
            
            # 更新设备数据
            self._devices[device_id] = device_data.copy()
            
            # 更新设备层级关系
            if old_parent_id != new_parent_id:
                # 从旧父设备的子设备集合中移除
                if old_parent_id and old_parent_id in self._device_hierarchy:
                    self._device_hierarchy[old_parent_id].discard(device_id)
                    # 如果子设备集合为空，删除该父设备条目
                    if not self._device_hierarchy[old_parent_id]:
                        del self._device_hierarchy[old_parent_id]
                
                # 添加到新父设备的子设备集合中
                if new_parent_id:
                    if new_parent_id not in self._device_hierarchy:
                        self._device_hierarchy[new_parent_id] = set()
                    self._device_hierarchy[new_parent_id].add(device_id)
            
            # 使相关缓存失效
            self._invalidate_related_cache(device_id)
            
            logger.info(f"成功更新设备: {device_id}")
            return True
    
    def remove_device(self, device_id: str) -> bool:
        """
        移除设备
        
        参数:
            device_id: 设备ID
            
        返回:
            bool: 移除是否成功
        """
        with self._lock:
            # 检查设备是否存在
            if device_id not in self._devices:
                logger.warning(f"移除设备失败，设备不存在: {device_id}")
                return False
            
            # 获取父设备ID
            parent_id = self._devices[device_id].get("parent_id")
            
            # 从父设备的子设备集合中移除
            if parent_id and parent_id in self._device_hierarchy:
                self._device_hierarchy[parent_id].discard(device_id)
                # 如果子设备集合为空，删除该父设备条目
                if not self._device_hierarchy[parent_id]:
                    del self._device_hierarchy[parent_id]
            
            # 处理该设备的子设备
            if device_id in self._device_hierarchy:
                # 获取所有子设备ID
                child_ids = list(self._device_hierarchy[device_id])
                
                # 递归移除所有子设备
                for child_id in child_ids:
                    self.remove_device(child_id)
                
                # 删除该设备的子设备集合
                del self._device_hierarchy[device_id]
            
            # 删除设备数据
            del self._devices[device_id]
            
            # 使相关缓存失效
            self._invalidate_related_cache(device_id)
            
            logger.info(f"成功移除设备: {device_id}")
            return True
    
    def get_device(self, device_id: str) -> Optional[Dict[str, Any]]:
        """
        获取设备数据
        
        参数:
            device_id: 设备ID
            
        返回:
            Optional[Dict[str, Any]]: 设备数据，如果不存在则返回None
        """
        with self._lock:
            return self._devices.get(device_id)
    
    def get_device_children(self, device_id: str, recursive: bool = False) -> List[str]:
        """
        获取设备的子设备ID列表
        
        参数:
            device_id: 设备ID
            recursive: 是否递归获取所有子孙设备ID
            
        返回:
            List[str]: 子设备ID列表
        """
        with self._lock:
            # 获取直接子设备ID
            children = list(self._device_hierarchy.get(device_id, set()))
            
            # 如果需要递归获取所有子孙设备ID
            if recursive and children:
                # 使用集合避免重复
                all_children = set(children)
                
                # 递归获取每个子设备的子孙设备ID
                for child_id in children:
                    child_children = self.get_device_children(child_id, True)
                    all_children.update(child_children)
                
                return list(all_children)
            
            return children
    
    def filter_devices_with_condition(self, condition: FilterCondition, include_subtree: bool = True) -> List[Dict[str, Any]]:
        """
        使用单个过滤条件过滤设备
        
        参数:
            condition: 过滤条件
            include_subtree: 是否包含子设备树
            
        返回:
            List[Dict[str, Any]]: 符合过滤条件的设备列表
        """
        # 生成缓存键
        cache_key = f"condition:{condition.to_dict()}:{include_subtree}"
        
        # 尝试从缓存获取结果
        cached_result = self._cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        with self._lock:
            result = []
            
            # 遍历所有设备
            for device_id, device_data in self._devices.items():
                # 检查设备是否匹配条件
                if condition.match(device_data):
                    result.append(device_data.copy())
                    
                    # 如果包含子设备树，递归查找匹配的子设备
                    if include_subtree:
                        child_ids = self.get_device_children(device_id, True)
                        for child_id in child_ids:
                            if child_id in self._devices:
                                result.append(self._devices[child_id].copy())
            
            # 缓存结果
            self._cache.put(cache_key, result)
            
            return result
    
    def filter_devices_with_group(self, filter_group: FilterGroup, include_subtree: bool = True) -> List[Dict[str, Any]]:
        """
        使用过滤条件组过滤设备
        
        参数:
            filter_group: 过滤条件组
            include_subtree: 是否包含子设备树
            
        返回:
            List[Dict[str, Any]]: 符合过滤条件的设备列表
        """
        # 生成缓存键
        cache_key = f"group:{filter_group.to_dict()}:{include_subtree}"
        
        # 尝试从缓存获取结果
        cached_result = self._cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        with self._lock:
            # 如果过滤条件组是空的，返回所有设备
            if filter_group.is_empty():
                result = [device.copy() for device in self._devices.values()]
                self._cache.put(cache_key, result)
                return result
            
            # 如果是简单条件组（只有一个条件），直接使用单个条件过滤
            if filter_group.is_simple():
                return self.filter_devices_with_condition(filter_group.conditions[0], include_subtree)
            
            # 对于复杂条件组，遍历所有设备并使用条件组匹配
            result = []
            matched_device_ids = set()
            
            for device_id, device_data in self._devices.items():
                # 检查设备是否匹配条件组
                if filter_group.match(device_data):
                    result.append(device_data.copy())
                    matched_device_ids.add(device_id)
                    
                    # 如果包含子设备树，添加所有子设备
                    if include_subtree:
                        child_ids = self.get_device_children(device_id, True)
                        for child_id in child_ids:
                            if child_id in self._devices and child_id not in matched_device_ids:
                                result.append(self._devices[child_id].copy())
                                matched_device_ids.add(child_id)
            
            # 缓存结果
            self._cache.put(cache_key, result)
            
            return result
    
    def _invalidate_related_cache(self, device_id: str) -> None:
        """
        使与设备相关的缓存失效
        
        参数:
            device_id: 设备ID
        """
        # 使所有缓存失效，因为设备的变化可能影响任何过滤结果
        self._cache.clear()
    
    def clear_cache(self) -> None:
        """清除过滤结果缓存"""
        self._cache.clear()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        返回:
            Dict[str, Any]: 缓存统计信息
        """
        return self._cache.get_stats()
    
    def get_all_devices(self) -> List[Dict[str, Any]]:
        """
        获取所有设备数据
        
        返回:
            List[Dict[str, Any]]: 所有设备数据的列表
        """
        with self._lock:
            return [device.copy() for device in self._devices.values()]
    
    def get_device_count(self) -> int:
        """
        获取设备数量
        
        返回:
            int: 设备数量
        """
        with self._lock:
            return len(self._devices)
    
    def get_device_hierarchy_info(self) -> Dict[str, Any]:
        """
        获取设备层级关系信息
        
        返回:
            Dict[str, Any]: 设备层级关系信息
        """
        with self._lock:
            hierarchy_info = {}
            
            for parent_id, child_ids in self._device_hierarchy.items():
                hierarchy_info[parent_id] = list(child_ids)
            
            return {
                "hierarchy": hierarchy_info,
                "device_count": len(self._devices),
                "parent_count": len(self._device_hierarchy)
            } 