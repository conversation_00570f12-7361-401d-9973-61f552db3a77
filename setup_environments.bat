@echo off
chcp 65001 >nul
echo ====================================================================================================
echo                    OmniLink全联通系统 - 虚拟环境设置脚本
echo ====================================================================================================
echo.

echo 🚀 开始设置OmniLink系统的虚拟环境...
echo.

:: 设置基础路径
set "BASE_DIR=%~dp0"
set "KY_DIR=%BASE_DIR%ky"

:: 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python已安装，版本信息:
python --version
echo.

:: 创建虚拟环境目录
if not exist "%BASE_DIR%venvs" mkdir "%BASE_DIR%venvs"

echo 📦 创建虚拟环境...
echo.

:: 1. 主服务器虚拟环境
echo [1/4] 创建主服务器虚拟环境...
if exist "%BASE_DIR%venvs\main_server" (
    echo   - 主服务器虚拟环境已存在，跳过创建
) else (
    python -m venv "%BASE_DIR%venvs\main_server"
    echo   ✅ 主服务器虚拟环境创建完成
)

:: 2. 从服务器虚拟环境
echo [2/4] 创建从服务器虚拟环境...
if exist "%BASE_DIR%venvs\slave_server" (
    echo   - 从服务器虚拟环境已存在，跳过创建
) else (
    python -m venv "%BASE_DIR%venvs\slave_server"
    echo   ✅ 从服务器虚拟环境创建完成
)

:: 3. Web服务器虚拟环境
echo [3/4] 创建Web服务器虚拟环境...
if exist "%BASE_DIR%venvs\web_server" (
    echo   - Web服务器虚拟环境已存在，跳过创建
) else (
    python -m venv "%BASE_DIR%venvs\web_server"
    echo   ✅ Web服务器虚拟环境创建完成
)

:: 4. 专属客户端虚拟环境
echo [4/4] 创建专属客户端虚拟环境...
if exist "%BASE_DIR%venvs\user_client" (
    echo   - 专属客户端虚拟环境已存在，跳过创建
) else (
    python -m venv "%BASE_DIR%venvs\user_client"
    echo   ✅ 专属客户端虚拟环境创建完成
)

echo.
echo 🔧 安装依赖包...
echo.

:: 安装主服务器依赖
echo [1/4] 安装主服务器依赖...
call "%BASE_DIR%venvs\main_server\Scripts\activate.bat"
pip install --upgrade pip
pip install -r "%KY_DIR%\requirements-main.txt"
call deactivate
echo   ✅ 主服务器依赖安装完成

:: 安装从服务器依赖
echo [2/4] 安装从服务器依赖...
call "%BASE_DIR%venvs\slave_server\Scripts\activate.bat"
pip install --upgrade pip
pip install -r "%KY_DIR%\requirements-slave.txt"
call deactivate
echo   ✅ 从服务器依赖安装完成

:: 安装Web服务器依赖
echo [3/4] 安装Web服务器依赖...
call "%BASE_DIR%venvs\web_server\Scripts\activate.bat"
pip install --upgrade pip
pip install -r "%KY_DIR%\requirements-web.txt"
call deactivate
echo   ✅ Web服务器依赖安装完成

:: 安装专属客户端依赖
echo [4/4] 安装专属客户端依赖...
call "%BASE_DIR%venvs\user_client\Scripts\activate.bat"
pip install --upgrade pip
pip install -r "%KY_DIR%\requirements-user.txt"
call deactivate
echo   ✅ 专属客户端依赖安装完成

echo.
echo 📝 创建启动脚本...

:: 创建主服务器启动脚本
echo @echo off > "%BASE_DIR%start_main_server.bat"
echo chcp 65001 ^>nul >> "%BASE_DIR%start_main_server.bat"
echo echo 🚀 启动主服务器... >> "%BASE_DIR%start_main_server.bat"
echo call "%BASE_DIR%venvs\main_server\Scripts\activate.bat" >> "%BASE_DIR%start_main_server.bat"
echo cd /d "%KY_DIR%" >> "%BASE_DIR%start_main_server.bat"
echo python main_server\fastapi_main.py >> "%BASE_DIR%start_main_server.bat"
echo pause >> "%BASE_DIR%start_main_server.bat"

:: 创建从服务器启动脚本
echo @echo off > "%BASE_DIR%start_slave_server.bat"
echo chcp 65001 ^>nul >> "%BASE_DIR%start_slave_server.bat"
echo echo 🚀 启动从服务器... >> "%BASE_DIR%start_slave_server.bat"
echo call "%BASE_DIR%venvs\slave_server\Scripts\activate.bat" >> "%BASE_DIR%start_slave_server.bat"
echo cd /d "%KY_DIR%" >> "%BASE_DIR%start_slave_server.bat"
echo python slave_server\app.py >> "%BASE_DIR%start_slave_server.bat"
echo pause >> "%BASE_DIR%start_slave_server.bat"

:: 创建Web服务器启动脚本
echo @echo off > "%BASE_DIR%start_web_server.bat"
echo chcp 65001 ^>nul >> "%BASE_DIR%start_web_server.bat"
echo echo 🚀 启动Web服务器... >> "%BASE_DIR%start_web_server.bat"
echo call "%BASE_DIR%venvs\web_server\Scripts\activate.bat" >> "%BASE_DIR%start_web_server.bat"
echo cd /d "%KY_DIR%" >> "%BASE_DIR%start_web_server.bat"
echo python -m http.server 8080 >> "%BASE_DIR%start_web_server.bat"
echo pause >> "%BASE_DIR%start_web_server.bat"

:: 创建专属客户端启动脚本
echo @echo off > "%BASE_DIR%start_user_client.bat"
echo chcp 65001 ^>nul >> "%BASE_DIR%start_user_client.bat"
echo echo 🚀 启动专属客户端... >> "%BASE_DIR%start_user_client.bat"
echo call "%BASE_DIR%venvs\user_client\Scripts\activate.bat" >> "%BASE_DIR%start_user_client.bat"
echo cd /d "%KY_DIR%\USER" >> "%BASE_DIR%start_user_client.bat"
echo python main.py >> "%BASE_DIR%start_user_client.bat"
echo pause >> "%BASE_DIR%start_user_client.bat"

echo.
echo ====================================================================================================
echo                                   🎉 虚拟环境设置完成！
echo ====================================================================================================
echo.
echo 📁 虚拟环境位置:
echo   - 主服务器: %BASE_DIR%venvs\main_server
echo   - 从服务器: %BASE_DIR%venvs\slave_server
echo   - Web服务器: %BASE_DIR%venvs\web_server
echo   - 专属客户端: %BASE_DIR%venvs\user_client
echo.
echo 🚀 启动脚本:
echo   - 主服务器: start_main_server.bat
echo   - 从服务器: start_slave_server.bat
echo   - Web服务器: start_web_server.bat
echo   - 专属客户端: start_user_client.bat
echo.
echo 💡 使用说明:
echo   1. 双击对应的启动脚本即可运行相应服务
echo   2. 每个服务运行在独立的虚拟环境中，避免依赖冲突
echo   3. 如需手动激活虚拟环境，使用: call venvs\[服务名]\Scripts\activate.bat
echo.
pause 