#!/usr/bin/env python3
"""
设备筛选模板

提供可重用的设备筛选模板系统，支持模板的创建、保存、加载和应用
"""

import os
import json
import logging
import threading
from typing import Dict, List, Any, Optional, Set, Union
from datetime import datetime
import time
from collections import defaultdict

from ky.slave_server.device_sharing.filter.filter_models import FilterGroup
from ky.slave_server.device_sharing.filter.filter_engine import DeviceFilterEngine

logger = logging.getLogger(__name__)

class FilterTemplate:
    """设备筛选模板类"""
    
    def __init__(self, 
                 template_id: str,
                 name: str,
                 filter_group: FilterGroup,
                 created_at: Optional[datetime] = None,
                 updated_at: Optional[datetime] = None,
                 created_by: Optional[str] = None,
                 description: Optional[str] = None,
                 tags: Optional[List[str]] = None,
                 usage_count: int = 0,
                 is_favorite: bool = False,
                 metadata: Optional[Dict[str, Any]] = None):
        """
        初始化筛选模板
        
        参数:
            template_id: 模板唯一标识
            name: 模板名称
            filter_group: 筛选条件组
            created_at: 创建时间
            updated_at: 更新时间
            created_by: 创建者ID
            description: 模板描述
            tags: 标签列表
            usage_count: 使用次数
            is_favorite: 是否收藏
            metadata: 元数据
        """
        self.template_id = template_id
        self.name = name
        self.filter_group = filter_group
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
        self.created_by = created_by
        self.description = description or ""
        self.tags = tags or []
        self.usage_count = usage_count
        self.is_favorite = is_favorite
        self.metadata = metadata or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        返回:
            Dict[str, Any]: 字典表示
        """
        return {
            "template_id": self.template_id,
            "name": self.name,
            "filter_group": self.filter_group.to_dict(),
            "created_at": self.created_at.isoformat() if isinstance(self.created_at, datetime) else self.created_at,
            "updated_at": self.updated_at.isoformat() if isinstance(self.updated_at, datetime) else self.updated_at,
            "created_by": self.created_by,
            "description": self.description,
            "tags": self.tags,
            "usage_count": self.usage_count,
            "is_favorite": self.is_favorite,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FilterTemplate':
        """
        从字典创建模板
        
        参数:
            data: 字典数据
            
        返回:
            FilterTemplate: 筛选模板
        """
        # 处理日期字段
        if isinstance(data.get("created_at"), str):
            try:
                created_at = datetime.fromisoformat(data["created_at"])
            except ValueError:
                created_at = datetime.now()
        else:
            created_at = data.get("created_at", datetime.now())
        
        if isinstance(data.get("updated_at"), str):
            try:
                updated_at = datetime.fromisoformat(data["updated_at"])
            except ValueError:
                updated_at = datetime.now()
        else:
            updated_at = data.get("updated_at", datetime.now())
        
        # 处理FilterGroup
        filter_group_data = data.get("filter_group", {})
        if isinstance(filter_group_data, dict):
            filter_group = FilterGroup.from_dict(filter_group_data)
        else:
            filter_group = FilterGroup()
        
        return cls(
            template_id=data.get("template_id", ""),
            name=data.get("name", "未命名模板"),
            filter_group=filter_group,
            created_at=created_at,
            updated_at=updated_at,
            created_by=data.get("created_by"),
            description=data.get("description", ""),
            tags=data.get("tags", []),
            usage_count=data.get("usage_count", 0),
            is_favorite=data.get("is_favorite", False),
            metadata=data.get("metadata", {})
        )
    
    def increment_usage(self) -> int:
        """
        增加使用次数
        
        返回:
            int: 增加后的使用次数
        """
        self.usage_count += 1
        return self.usage_count


class FilterTemplateManager:
    """筛选模板管理器类"""
    
    def __init__(self, templates_dir: Optional[str] = None):
        """
        初始化模板管理器
        
        参数:
            templates_dir: 模板存储目录，默认为当前目录下的templates子目录
        """
        self.templates_dir = templates_dir or os.path.join(os.path.dirname(__file__), "templates")
        os.makedirs(self.templates_dir, exist_ok=True)
        
        # 模板字典，模板ID到模板对象的映射
        self.templates: Dict[str, FilterTemplate] = {}
        
        # 索引，用于快速查找
        self.tag_index: Dict[str, Set[str]] = defaultdict(set)  # 标签 -> 模板ID集合
        self.name_index: Dict[str, str] = {}  # 模板名称 -> 模板ID
        self.favorite_templates: Set[str] = set()  # 收藏的模板ID集合
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 加载模板
        self._load_templates()
    
    def _load_templates(self) -> None:
        """加载所有模板"""
        with self.lock:
            try:
                # 清空当前数据
                self.templates.clear()
                self._clear_indices()
                
                # 遍历目录中的所有.json文件
                for filename in os.listdir(self.templates_dir):
                    if not filename.endswith(".json"):
                        continue
                    
                    template_id = filename[:-5]  # 移除.json后缀
                    file_path = os.path.join(self.templates_dir, filename)
                    
                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            template_data = json.load(f)
                        
                        template = FilterTemplate.from_dict(template_data)
                        
                        # 确保ID一致
                        template.template_id = template_id
                        
                        # 添加到字典
                        self.templates[template_id] = template
                        
                        # 更新索引
                        self._index_template(template)
                        
                    except Exception as e:
                        logger.error(f"加载模板 {template_id} 失败: {e}")
            
            except Exception as e:
                logger.error(f"加载模板目录失败: {e}")
    
    def _save_template(self, template: FilterTemplate) -> bool:
        """
        保存模板到文件
        
        参数:
            template: 要保存的模板
            
        返回:
            bool: 保存是否成功
        """
        try:
            file_path = os.path.join(self.templates_dir, f"{template.template_id}.json")
            
            # 更新修改时间
            template.updated_at = datetime.now()
            
            # 转换为字典然后序列化为JSON
            template_data = template.to_dict()
            
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(template_data, f, ensure_ascii=False, indent=2)
            
            return True
        
        except Exception as e:
            logger.error(f"保存模板 {template.template_id} 失败: {e}")
            return False
    
    def _clear_indices(self) -> None:
        """清空所有索引"""
        self.tag_index.clear()
        self.name_index.clear()
        self.favorite_templates.clear()
    
    def _index_template(self, template: FilterTemplate) -> None:
        """
        为模板创建索引
        
        参数:
            template: 要索引的模板
        """
        # 索引标签
        for tag in template.tags:
            self.tag_index[tag].add(template.template_id)
        
        # 索引名称
        self.name_index[template.name] = template.template_id
        
        # 索引收藏状态
        if template.is_favorite:
            self.favorite_templates.add(template.template_id)
    
    def _remove_template_from_indices(self, template_id: str) -> None:
        """
        从索引中移除模板
        
        参数:
            template_id: 要移除的模板ID
        """
        if template_id not in self.templates:
            return
        
        template = self.templates[template_id]
        
        # 从标签索引中移除
        for tag in template.tags:
            if tag in self.tag_index:
                self.tag_index[tag].discard(template_id)
                # 如果此标签不再关联任何模板，移除此标签
                if not self.tag_index[tag]:
                    del self.tag_index[tag]
        
        # 从名称索引中移除
        if template.name in self.name_index and self.name_index[template.name] == template_id:
            del self.name_index[template.name]
        
        # 从收藏索引中移除
        self.favorite_templates.discard(template_id)
    
    def add_template(self, template: FilterTemplate) -> bool:
        """
        添加模板
        
        参数:
            template: 要添加的模板
            
        返回:
            bool: 添加是否成功
        """
        with self.lock:
            # 检查ID是否存在
            if template.template_id in self.templates:
                logger.warning(f"模板ID {template.template_id} 已存在")
                return False
            
            # 检查名称是否重复
            if template.name in self.name_index:
                logger.warning(f"模板名称 {template.name} 已存在")
                return False
            
            # 添加到字典
            self.templates[template.template_id] = template
            
            # 更新索引
            self._index_template(template)
            
            # 保存到文件
            if not self._save_template(template):
                # 保存失败，回滚
                del self.templates[template.template_id]
                self._remove_template_from_indices(template.template_id)
                return False
            
            return True
    
    def update_template(self, template: FilterTemplate) -> bool:
        """
        更新模板
        
        参数:
            template: 要更新的模板
            
        返回:
            bool: 更新是否成功
        """
        with self.lock:
            # 检查ID是否存在
            if template.template_id not in self.templates:
                logger.warning(f"模板ID {template.template_id} 不存在")
                return False
            
            # 检查名称是否重复（排除自身）
            old_template = self.templates[template.template_id]
            if template.name != old_template.name and template.name in self.name_index:
                logger.warning(f"模板名称 {template.name} 已存在")
                return False
            
            # 备份旧模板
            backup = self.templates[template.template_id]
            
            # 先从索引中移除旧模板
            self._remove_template_from_indices(template.template_id)
            
            # 更新模板
            self.templates[template.template_id] = template
            
            # 更新索引
            self._index_template(template)
            
            # 保存到文件
            if not self._save_template(template):
                # 保存失败，回滚
                self._remove_template_from_indices(template.template_id)
                self.templates[template.template_id] = backup
                self._index_template(backup)
                return False
            
            return True
    
    def delete_template(self, template_id: str) -> bool:
        """
        删除模板
        
        参数:
            template_id: 要删除的模板ID
            
        返回:
            bool: 删除是否成功
        """
        with self.lock:
            # 检查ID是否存在
            if template_id not in self.templates:
                logger.warning(f"模板ID {template_id} 不存在")
                return False
            
            # 备份模板，用于可能的回滚
            backup = self.templates[template_id]
            
            # 从索引中移除
            self._remove_template_from_indices(template_id)
            
            # 从字典中移除
            del self.templates[template_id]
            
            # 删除文件
            file_path = os.path.join(self.templates_dir, f"{template_id}.json")
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                return True
            
            except Exception as e:
                # 删除文件失败，回滚
                logger.error(f"删除模板文件 {template_id} 失败: {e}")
                self.templates[template_id] = backup
                self._index_template(backup)
                return False
    
    def get_template(self, template_id: str) -> Optional[FilterTemplate]:
        """
        获取模板
        
        参数:
            template_id: 模板ID
            
        返回:
            Optional[FilterTemplate]: 模板对象，不存在则返回None
        """
        with self.lock:
            return self.templates.get(template_id)
    
    def get_templates(self, 
                      tags: Optional[List[str]] = None,
                      sort_by: str = "name",
                      is_favorite: Optional[bool] = None) -> List[FilterTemplate]:
        """
        获取模板列表
        
        参数:
            tags: 标签筛选
            sort_by: 排序字段，可选值：name, usage_count, created_at, updated_at
            is_favorite: 是否筛选收藏的模板
            
        返回:
            List[FilterTemplate]: 模板列表
        """
        with self.lock:
            # 初始结果为所有模板
            result_ids = set(self.templates.keys())
            
            # 标签筛选
            if tags:
                tag_filtered_ids = set()
                for tag in tags:
                    tag_filtered_ids.update(self.tag_index.get(tag, set()))
                
                # 交集处理
                result_ids = result_ids.intersection(tag_filtered_ids) if tag_filtered_ids else set()
            
            # 收藏筛选
            if is_favorite is not None:
                if is_favorite:
                    result_ids = result_ids.intersection(self.favorite_templates)
                else:
                    result_ids = result_ids.difference(self.favorite_templates)
            
            # 转换为模板对象列表
            result = [self.templates[template_id] for template_id in result_ids]
            
            # 排序
            if sort_by == "name":
                result.sort(key=lambda t: t.name)
            elif sort_by == "usage_count":
                result.sort(key=lambda t: t.usage_count, reverse=True)
            elif sort_by == "created_at":
                result.sort(key=lambda t: t.created_at, reverse=True)
            elif sort_by == "updated_at":
                result.sort(key=lambda t: t.updated_at, reverse=True)
            
            return result
    
    def create_template(self,
                        name: str,
                        filter_group: FilterGroup,
                        created_by: Optional[str] = None,
                        description: Optional[str] = None,
                        tags: Optional[List[str]] = None,
                        metadata: Optional[Dict[str, Any]] = None) -> FilterTemplate:
        """
        创建新模板
        
        参数:
            name: 模板名称
            filter_group: 筛选条件组
            created_by: 创建者ID
            description: 模板描述
            tags: 标签列表
            metadata: 元数据
            
        返回:
            FilterTemplate: 创建的模板
        """
        with self.lock:
            # 检查名称是否重复
            if name in self.name_index:
                # 生成唯一名称
                base_name = name
                counter = 1
                while name in self.name_index:
                    name = f"{base_name} ({counter})"
                    counter += 1
            
            # 生成唯一ID
            template_id = str(int(time.time() * 1000))
            while template_id in self.templates:
                # 极少情况下可能冲突，增加随机后缀
                template_id = f"{template_id}_{int(time.time() * 1000) % 1000}"
            
            # 创建模板
            template = FilterTemplate(
                template_id=template_id,
                name=name,
                filter_group=filter_group,
                created_by=created_by,
                description=description,
                tags=tags,
                metadata=metadata
            )
            
            # 添加模板
            self.add_template(template)
            
            return template
    
    def toggle_favorite(self, template_id: str) -> Optional[bool]:
        """
        切换模板收藏状态
        
        参数:
            template_id: 模板ID
            
        返回:
            Optional[bool]: 切换后的收藏状态，不存在则返回None
        """
        with self.lock:
            if template_id not in self.templates:
                return None
            
            template = self.templates[template_id]
            
            # 切换状态
            if template.is_favorite:
                template.is_favorite = False
                self.favorite_templates.discard(template_id)
            else:
                template.is_favorite = True
                self.favorite_templates.add(template_id)
            
            # 保存更新
            self._save_template(template)
            
            return template.is_favorite
    
    def increment_usage(self, template_id: str) -> Optional[int]:
        """
        增加模板使用次数
        
        参数:
            template_id: 模板ID
            
        返回:
            Optional[int]: 增加后的使用次数，不存在则返回None
        """
        with self.lock:
            if template_id not in self.templates:
                return None
            
            template = self.templates[template_id]
            
            # 增加使用次数
            template.increment_usage()
            
            # 保存更新
            self._save_template(template)
            
            return template.usage_count
    
    def export_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """
        导出模板
        
        参数:
            template_id: 模板ID
            
        返回:
            Optional[Dict[str, Any]]: 导出的模板数据，不存在则返回None
        """
        with self.lock:
            template = self.get_template(template_id)
            if not template:
                return None
            
            return template.to_dict()
    
    def import_template(self, template_data: Dict[str, Any]) -> Optional[FilterTemplate]:
        """
        导入模板
        
        参数:
            template_data: 模板数据
            
        返回:
            Optional[FilterTemplate]: 导入的模板，失败则返回None
        """
        try:
            # 创建模板对象
            template = FilterTemplate.from_dict(template_data)
            
            # 如果ID为空或已存在，生成新ID
            if not template.template_id or template.template_id in self.templates:
                template.template_id = str(int(time.time() * 1000))
            
            # 如果名称已存在，调整名称
            if template.name in self.name_index:
                base_name = template.name
                counter = 1
                while template.name in self.name_index:
                    template.name = f"{base_name} (导入-{counter})"
                    counter += 1
            
            # 添加模板
            if self.add_template(template):
                return template
            else:
                return None
        
        except Exception as e:
            logger.error(f"导入模板失败: {e}")
            return None
    
    def get_all_tags(self) -> List[str]:
        """
        获取所有标签
        
        返回:
            List[str]: 标签列表
        """
        with self.lock:
            return list(self.tag_index.keys()) 