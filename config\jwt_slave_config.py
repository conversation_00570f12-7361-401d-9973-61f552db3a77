"""
从服务器JWT配置

定义用于从服务器认证的JWT相关配置，如密钥、算法和过期时间。
这些配置应与主服务器的从服务器令牌验证逻辑保持一致。
"""

# 强烈建议将实际的密钥存储在环境变量或安全的配置文件中，而不是硬编码在代码里。
# 例如: JWT_SLAVE_SECRET = os.getenv("JWT_SLAVE_SECRET", "your-default-slave-secret-key")

JWT_SLAVE_SECRET_KEY = "your-default-slave-secret-key-for-testing-only" # 生产环境中必须替换为强密钥
JWT_SLAVE_ALGORITHM = "HS256"
JWT_SLAVE_ACCESS_TOKEN_EXPIRE_MINUTES = 15  # 从服务器访问令牌有效期（分钟）
JWT_SLAVE_REFRESH_TOKEN_EXPIRE_DAYS = 7     # 从服务器刷新令牌有效期（天，如果需要刷新令牌机制）

# 如果从服务器也需要生成自己的令牌（例如，用于内部服务间通信），可以在此定义
# JWT_INTERNAL_SECRET_KEY = "another-secret-for-slave-internal-use"
# JWT_INTERNAL_ALGORITHM = "HS256"
# JWT_INTERNAL_EXPIRE_MINUTES = 60

# 令牌类型标识
TOKEN_TYPE_SLAVE_ACCESS = "slave_access"
TOKEN_TYPE_SLAVE_REFRESH = "slave_refresh" # 如果实现刷新令牌

# 预期的令牌签发者 (iss) 和受众 (aud)，用于更严格的验证 (可选)
# JWT_ISSUER = "urn:main_server"
# JWT_AUDIENCE_SLAVE = "urn:slave_server"

# 日志记录相关配置 (如果需要单独控制)
# LOG_LEVEL_JWT_SLAVE = "INFO"


def get_slave_jwt_secret() -> str:
    """获取从服务器JWT密钥。"""
    # 实际应用中，这里应该从环境变量或安全存储中加载密钥
    return JWT_SLAVE_SECRET_KEY

def get_slave_jwt_algorithm() -> str:
    """获取从服务器JWT算法。"""
    return JWT_SLAVE_ALGORITHM

def get_slave_access_token_expire_minutes() -> int:
    """获取从服务器访问令牌的有效期（分钟）。"""
    return JWT_SLAVE_ACCESS_TOKEN_EXPIRE_MINUTES


# 示例：如何被 AuthService 等服务引用
# from ky.config.jwt_slave_config import (
#     get_slave_jwt_secret,
#     get_slave_jwt_algorithm,
#     get_slave_access_token_expire_minutes
# )
#
# class AuthService:
#     def _generate_slave_token(self, slave_id: str):
#         secret = get_slave_jwt_secret()
#         algo = get_slave_jwt_algorithm()
#         expire_minutes = get_slave_access_token_expire_minutes()
#         # ... JWT generation logic ... 