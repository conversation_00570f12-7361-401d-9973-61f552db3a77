#!/usr/bin/env python3
"""
Docker构建测试脚本
验证OmniLink主从服务器的Docker容器构建和启动
"""

import subprocess
import time
import json
import logging
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('docker_test.log')
    ]
)
logger = logging.getLogger(__name__)

class DockerTestRunner:
    """Docker测试运行器"""
    
    def __init__(self):
        self.test_results = []
        self.project_root = Path(__file__).parent
        
    def run_command(self, command: str, timeout: int = 300) -> dict:
        """运行命令并返回结果"""
        try:
            logger.info(f"执行命令: {command}")
            
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.project_root
            )
            
            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "command": command
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "命令执行超时",
                "command": command
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": command
            }
    
    def test_docker_availability(self):
        """测试Docker是否可用"""
        logger.info("🔍 检查Docker环境...")
        
        # 检查Docker版本
        result = self.run_command("docker --version", timeout=30)
        
        if result["success"]:
            docker_version = result["stdout"].strip()
            logger.info(f"✅ Docker可用: {docker_version}")
            
            # 检查Docker Compose
            compose_result = self.run_command("docker-compose --version", timeout=30)
            if compose_result["success"]:
                compose_version = compose_result["stdout"].strip()
                logger.info(f"✅ Docker Compose可用: {compose_version}")
                
                self.test_results.append({
                    "test": "Docker环境检查",
                    "status": "PASS",
                    "message": f"Docker: {docker_version}, Compose: {compose_version}"
                })
                return True
            else:
                logger.error("❌ Docker Compose不可用")
                self.test_results.append({
                    "test": "Docker环境检查",
                    "status": "FAIL",
                    "message": "Docker Compose不可用"
                })
                return False
        else:
            logger.error("❌ Docker不可用")
            self.test_results.append({
                "test": "Docker环境检查",
                "status": "FAIL",
                "message": "Docker不可用"
            })
            return False
    
    def test_docker_compose_syntax(self):
        """测试Docker Compose配置语法"""
        logger.info("🔍 验证Docker Compose配置...")
        
        result = self.run_command("docker-compose config", timeout=60)
        
        if result["success"]:
            logger.info("✅ Docker Compose配置语法正确")
            self.test_results.append({
                "test": "Docker Compose语法",
                "status": "PASS",
                "message": "配置语法正确"
            })
            return True
        else:
            logger.error(f"❌ Docker Compose配置错误: {result.get('stderr', '')}")
            self.test_results.append({
                "test": "Docker Compose语法",
                "status": "FAIL",
                "message": f"配置错误: {result.get('stderr', '')}"
            })
            return False
    
    def test_image_build(self):
        """测试镜像构建"""
        logger.info("🔍 构建Docker镜像...")
        
        # 构建主服务器镜像
        logger.info("构建主服务器镜像...")
        main_result = self.run_command(
            "docker-compose build main-server", 
            timeout=600
        )
        
        if main_result["success"]:
            logger.info("✅ 主服务器镜像构建成功")
            main_status = "PASS"
        else:
            logger.error(f"❌ 主服务器镜像构建失败: {main_result.get('stderr', '')}")
            main_status = "FAIL"
        
        # 构建从服务器镜像
        logger.info("构建从服务器镜像...")
        slave_result = self.run_command(
            "docker-compose build slave-server", 
            timeout=600
        )
        
        if slave_result["success"]:
            logger.info("✅ 从服务器镜像构建成功")
            slave_status = "PASS"
        else:
            logger.error(f"❌ 从服务器镜像构建失败: {slave_result.get('stderr', '')}")
            slave_status = "FAIL"
        
        overall_success = main_result["success"] and slave_result["success"]
        
        self.test_results.append({
            "test": "Docker镜像构建",
            "status": "PASS" if overall_success else "FAIL",
            "message": f"主服务器: {main_status}, 从服务器: {slave_status}"
        })
        
        return overall_success
    
    def test_services_startup(self):
        """测试服务启动"""
        logger.info("🔍 启动Docker服务...")
        
        # 清理旧容器
        self.run_command("docker-compose down", timeout=120)
        
        # 启动数据库和Redis
        logger.info("启动基础服务...")
        base_result = self.run_command(
            "docker-compose up -d postgres-db redis", 
            timeout=180
        )
        
        if not base_result["success"]:
            logger.error("❌ 基础服务启动失败")
            self.test_results.append({
                "test": "服务启动",
                "status": "FAIL",
                "message": "基础服务启动失败"
            })
            return False
        
        # 等待数据库启动
        logger.info("等待数据库启动...")
        time.sleep(30)
        
        # 启动主服务器
        logger.info("启动主服务器...")
        main_result = self.run_command(
            "docker-compose up -d main-server", 
            timeout=180
        )
        
        if not main_result["success"]:
            logger.error("❌ 主服务器启动失败")
            self.test_results.append({
                "test": "服务启动",
                "status": "FAIL",
                "message": "主服务器启动失败"
            })
            return False
        
        # 等待主服务器启动
        time.sleep(20)
        
        # 启动从服务器
        logger.info("启动从服务器...")
        slave_result = self.run_command(
            "docker-compose up -d slave-server", 
            timeout=180
        )
        
        if slave_result["success"]:
            logger.info("✅ 所有服务启动成功")
            self.test_results.append({
                "test": "服务启动",
                "status": "PASS",
                "message": "所有服务启动成功"
            })
            return True
        else:
            logger.error("❌ 从服务器启动失败")
            self.test_results.append({
                "test": "服务启动",
                "status": "FAIL",
                "message": "从服务器启动失败"
            })
            return False
    
    def test_service_health(self):
        """测试服务健康状态"""
        logger.info("🔍 检查服务健康状态...")
        
        # 检查容器状态
        result = self.run_command("docker-compose ps", timeout=30)
        
        if result["success"]:
            output = result["stdout"]
            logger.info("容器状态:")
            logger.info(output)
            
            # 检查主服务器健康
            health_result = self.run_command(
                "curl -f http://localhost:8000/health || echo 'FAILED'", 
                timeout=30
            )
            
            if "FAILED" not in health_result.get("stdout", ""):
                logger.info("✅ 主服务器健康检查通过")
                main_health = "PASS"
            else:
                logger.error("❌ 主服务器健康检查失败")
                main_health = "FAIL"
            
            # 检查从服务器健康
            slave_health_result = self.run_command(
                "curl -f http://localhost:8001/health -H 'Authorization: Bearer dev-api-key-12345' || echo 'FAILED'", 
                timeout=30
            )
            
            if "FAILED" not in slave_health_result.get("stdout", ""):
                logger.info("✅ 从服务器健康检查通过")
                slave_health = "PASS"
            else:
                logger.error("❌ 从服务器健康检查失败")
                slave_health = "FAIL"
            
            overall_health = main_health == "PASS" and slave_health == "PASS"
            
            self.test_results.append({
                "test": "服务健康检查",
                "status": "PASS" if overall_health else "FAIL",
                "message": f"主服务器: {main_health}, 从服务器: {slave_health}"
            })
            
            return overall_health
        else:
            logger.error("❌ 无法获取容器状态")
            self.test_results.append({
                "test": "服务健康检查",
                "status": "FAIL",
                "message": "无法获取容器状态"
            })
            return False
    
    def test_api_endpoints(self):
        """测试API端点"""
        logger.info("🔍 测试API端点...")
        
        # 测试主服务器API文档
        docs_result = self.run_command(
            "curl -f http://localhost:8000/docs || echo 'FAILED'", 
            timeout=30
        )
        
        if "FAILED" not in docs_result.get("stdout", ""):
            logger.info("✅ API文档可访问")
            docs_status = "PASS"
        else:
            logger.error("❌ API文档不可访问")
            docs_status = "FAIL"
        
        # 测试从服务器设备端点
        devices_result = self.run_command(
            "curl -f http://localhost:8001/devices -H 'Authorization: Bearer dev-api-key-12345' || echo 'FAILED'", 
            timeout=30
        )
        
        if "FAILED" not in devices_result.get("stdout", ""):
            logger.info("✅ 从服务器设备端点可访问")
            devices_status = "PASS"
        else:
            logger.error("❌ 从服务器设备端点不可访问")
            devices_status = "FAIL"
        
        overall_api = docs_status == "PASS" and devices_status == "PASS"
        
        self.test_results.append({
            "test": "API端点测试",
            "status": "PASS" if overall_api else "FAIL",
            "message": f"API文档: {docs_status}, 设备端点: {devices_status}"
        })
        
        return overall_api
    
    def cleanup(self):
        """清理测试环境"""
        logger.info("🧹 清理测试环境...")
        
        result = self.run_command("docker-compose down", timeout=120)
        
        if result["success"]:
            logger.info("✅ 测试环境清理完成")
        else:
            logger.error("❌ 测试环境清理失败")
    
    def run_full_test(self):
        """运行完整测试"""
        logger.info("🚀 开始Docker构建和启动测试...")
        
        print("=" * 80)
        print("OmniLink Docker构建和启动测试")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        try:
            # 测试序列
            tests = [
                ("Docker环境检查", self.test_docker_availability),
                ("Docker Compose语法", self.test_docker_compose_syntax),
                ("Docker镜像构建", self.test_image_build),
                ("服务启动", self.test_services_startup),
                ("服务健康检查", self.test_service_health),
                ("API端点测试", self.test_api_endpoints)
            ]
            
            for test_name, test_func in tests:
                logger.info(f"\n--- {test_name} ---")
                
                try:
                    success = test_func()
                    if not success:
                        logger.warning(f"{test_name} 失败，但继续后续测试")
                except Exception as e:
                    logger.error(f"{test_name} 执行异常: {e}")
                    self.test_results.append({
                        "test": test_name,
                        "status": "ERROR",
                        "message": f"执行异常: {e}"
                    })
                
                time.sleep(2)
            
        finally:
            # 清理环境
            self.cleanup()
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("Docker测试结果报告")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r["status"] == "PASS")
        failed_tests = sum(1 for r in self.test_results if r["status"] == "FAIL")
        error_tests = sum(1 for r in self.test_results if r["status"] == "ERROR")
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"错误: {error_tests}")
        
        if total_tests > 0:
            success_rate = (passed_tests / total_tests) * 100
            print(f"成功率: {success_rate:.1f}%")
        else:
            success_rate = 0
        
        print("\n详细结果:")
        for result in self.test_results:
            status_icon = {
                "PASS": "✅",
                "FAIL": "❌", 
                "ERROR": "💥"
            }.get(result["status"], "❓")
            
            print(f"{status_icon} {result['test']}: {result['message']}")
        
        print("\n" + "=" * 80)
        
        if success_rate >= 80:
            print("🎉 Docker测试结果优秀！主从服务器容器化部署成功。")
        elif success_rate >= 60:
            print("⚠️  Docker测试结果一般，部分功能需要修复。")
        else:
            print("❌ Docker测试结果不理想，需要重大修复。")
        
        print("=" * 80)
        
        # 保存报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "error_tests": error_tests,
                "success_rate": success_rate
            },
            "test_results": self.test_results
        }
        
        with open("docker_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info("Docker测试报告已保存到 docker_test_report.json")

def main():
    """主函数"""
    runner = DockerTestRunner()
    runner.run_full_test()

if __name__ == "__main__":
    main() 