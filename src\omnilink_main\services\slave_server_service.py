from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import Optional, List
import secrets
from fastapi import HTT<PERSON>Ex<PERSON>, status

from common.models.slave_server import SlaveServer
from common.schemas.slave_server_schema import SlaveServerCreate, SlaveServerUpdate, SlaveServerRegister

class SlaveServerService:
    async def get_by_id(self, db: AsyncSession, *, id: int) -> Optional[SlaveServer]:
        return await db.get(SlaveServer, id)

    async def get_by_server_id(self, db: AsyncSession, *, server_id: str) -> Optional[SlaveServer]:
        statement = select(SlaveServer).where(SlaveServer.server_id == server_id)
        result = await db.execute(statement)
        return result.scalars().first()

    async def get_all(self, db: AsyncSession, *, skip: int = 0, limit: int = 100) -> List[SlaveServer]:
        statement = select(SlaveServer).offset(skip).limit(limit)
        result = await db.execute(statement)
        return result.scalars().all()

    async def register_or_update_slave(self, db: AsyncSession, *, server_in: SlaveServerRegister) -> SlaveServer:
        """
        Handles registration and heartbeats from slave servers.
        If the server (by server_id) exists, update its info.
        If not, create a new entry.
        """
        db_server = await self.get_by_server_id(db, server_id=server_in.server_id)
        if db_server:
            # It's an existing server checking in, update its state
            return await self.update(db, db_obj=db_server, obj_in=SlaveServerUpdate(**server_in.model_dump()))
        else:
            # It's a new server, create it
            create_schema = SlaveServerCreate(**server_in.model_dump())
            return await self.create(db, obj_in=create_schema)

    async def create(self, db: AsyncSession, *, obj_in: SlaveServerCreate) -> SlaveServer:
        db_server = await self.get_by_server_id(db, server_id=obj_in.server_id)
        if db_server:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="A slave server with this server_id already exists."
            )
        
        db_obj_data = obj_in.model_dump()
        # Generate a unique, secure API key for the new slave
        db_obj_data['api_key'] = secrets.token_urlsafe(32)
        db_obj = SlaveServer(**db_obj_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update(self, db: AsyncSession, *, db_obj: SlaveServer, obj_in: SlaveServerUpdate) -> SlaveServer:
        obj_data = obj_in.model_dump(exclude_unset=True)
        for field, value in obj_data.items():
            setattr(db_obj, field, value)
        
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def remove(self, db: AsyncSession, *, id: int) -> SlaveServer:
        db_server = await self.get_by_id(db, id=id)
        if not db_server:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Slave server not found")
        
        await db.delete(db_server)
        await db.commit()
        return db_server

    async def get_by_api_key(self, db: AsyncSession, api_key: str) -> Optional[SlaveServer]:
        statement = select(SlaveServer).where(SlaveServer.api_key == api_key)
        result = await db.execute(statement)
        return result.scalars().first()

slave_server_service = SlaveServerService()