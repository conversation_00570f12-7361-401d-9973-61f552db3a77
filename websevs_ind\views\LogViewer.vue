<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import axios from 'axios';
// ... (icon and other imports)

// --- Refs and Reactive State ---
const logs = ref([]);
const loading = ref(true);
const currentPage = ref(1);
const pageSize = ref(20);
const totalLogs = ref(0);
const filters = reactive({
  user_query: '',
  action_type: '',
  status: '',
  start_date: '',
  end_date: ''
});
const dates = ref([]); // For the date range picker

// --- API Call ---
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      username: filters.user_query || null,
      action_type: filters.action_type || null,
      status: filters.status || null,
      start_date: dates.value?.[0] || null,
      end_date: dates.value?.[1] || null,
    };
    
    // 清理空的参数
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === '') {
        delete params[key];
      }
    });

    const response = await axios.get('/api/v1/audit/search', { params });
    
    if (response.data && response.data.success) {
      logs.value = response.data.data.items || [];
      totalLogs.value = response.data.data.total || 0;
    } else {
      ElMessage.error('获取日志失败: 响应格式不正确');
      logs.value = [];
      totalLogs.value = 0;
    }
  } catch (error) {
    ElMessage.error('获取日志失败: ' + (error.response?.data?.message || '网络错误'));
    logs.value = [];
    totalLogs.value = 0;
  } finally {
    loading.value = false;
  }
};

// --- Watchers and Event Handlers ---
watch([currentPage, pageSize], fetchData);
watch(filters, fetchData, { deep: true });
watch(dates, (newDates) => {
  filters.start_date = newDates?.[0] || '';
  filters.end_date = newDates?.[1] || '';
  fetchData();
});

const handleSizeChange = (val) => {
  pageSize.value = val;
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// --- Lifecycle Hooks ---
onMounted(() => {
  fetchData();
});

// ... (rest of the script, e.g., chart options, formatters)
// The mock data generation function and its call in onMounted should be removed.
</script> 