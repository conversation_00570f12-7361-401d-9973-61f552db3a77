#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""连接指标模块

负责收集、计算和管理设备连接的性能指标
"""

import time
import threading
import logging
from typing import Dict, List, Optional, Any, Callable, Tuple
from enum import Enum
import json
import os
from collections import deque

# 配置日志
logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"        # 健康
    WARNING = "warning"        # 警告
    CRITICAL = "critical"      # 危险
    UNKNOWN = "unknown"        # 未知

class MetricType(Enum):
    """指标类型枚举"""
    COUNTER = "counter"        # 计数器，只增不减
    GAUGE = "gauge"            # 测量值，可增可减
    HISTOGRAM = "histogram"    # 直方图，用于统计分布

class ConnectionMetrics:
    """连接指标类
    
    收集、计算和管理设备连接的性能指标
    """
    
    def __init__(self, window_size: int = 100, health_check_thresholds: Optional[Dict[str, Any]] = None):
        """初始化连接指标
        
        Args:
            window_size: 滑动窗口大小，用于计算成功率
            health_check_thresholds: 健康检查阈值
        """
        self._lock = threading.RLock()
        self._window_size = window_size
        self._device_metrics: Dict[str, Dict[str, Any]] = {}  # device_id -> metrics
        self._device_connections: Dict[str, deque] = {}  # device_id -> connection results (1=success, 0=failure)
        self._health_check_thresholds = health_check_thresholds or {
            'success_rate_warning': 0.8,      # 成功率低于此值触发警告
            'success_rate_critical': 0.5,     # 成功率低于此值触发危险
            'failure_count_warning': 3,       # 连续失败次数触发警告
            'failure_count_critical': 5,      # 连续失败次数触发危险
            'reset_count_warning': 5,         # 重置次数触发警告
            'reset_count_critical': 10,       # 重置次数触发危险
            'latency_warning': 2000,          # 延迟(毫秒)触发警告
            'latency_critical': 5000,         # 延迟(毫秒)触发危险
        }
    
    def record_connection_attempt(self, device_id: str, success: bool, latency: Optional[float] = None, 
                                 metadata: Optional[Dict[str, Any]] = None):
        """记录连接尝试
        
        Args:
            device_id: 设备ID
            success: 是否成功
            latency: 连接延迟(毫秒)
            metadata: 元数据
        """
        with self._lock:
            # 确保设备指标存在
            if device_id not in self._device_metrics:
                self._device_metrics[device_id] = {
                    'attempts': 0,           # 连接尝试次数
                    'success': 0,            # 成功次数
                    'failures': 0,           # 失败次数
                    'resets': 0,             # 重置次数
                    'last_attempt': 0,       # 最后尝试时间
                    'last_success': 0,       # 最后成功时间
                    'last_failure': 0,       # 最后失败时间
                    'success_rate': 1.0,     # 成功率
                    'avg_latency': 0,        # 平均延迟(毫秒)
                    'min_latency': None,     # 最小延迟(毫秒)
                    'max_latency': None,     # 最大延迟(毫秒)
                    'consecutive_failures': 0,   # 连续失败次数
                    'consecutive_success': 0,    # 连续成功次数
                    'health_status': HealthStatus.UNKNOWN.value,  # 健康状态
                }
                
            # 确保设备连接记录存在
            if device_id not in self._device_connections:
                self._device_connections[device_id] = deque(maxlen=self._window_size)
                
            metrics = self._device_metrics[device_id]
            connection_results = self._device_connections[device_id]
            
            # 更新基本计数器
            metrics['attempts'] += 1
            metrics['last_attempt'] = time.time()
            
            if success:
                metrics['success'] += 1
                metrics['last_success'] = time.time()
                metrics['consecutive_failures'] = 0
                metrics['consecutive_success'] += 1
                connection_results.append(1)
            else:
                metrics['failures'] += 1
                metrics['last_failure'] = time.time()
                metrics['consecutive_failures'] += 1
                metrics['consecutive_success'] = 0
                connection_results.append(0)
                
            # 计算成功率
            if connection_results:
                metrics['success_rate'] = sum(connection_results) / len(connection_results)
                
            # 更新延迟指标
            if latency is not None:
                # 更新平均延迟
                if metrics['avg_latency'] == 0:
                    metrics['avg_latency'] = latency
                else:
                    metrics['avg_latency'] = (metrics['avg_latency'] * (metrics['attempts'] - 1) + latency) / metrics['attempts']
                    
                # 更新最小延迟
                if metrics['min_latency'] is None or latency < metrics['min_latency']:
                    metrics['min_latency'] = latency
                    
                # 更新最大延迟
                if metrics['max_latency'] is None or latency > metrics['max_latency']:
                    metrics['max_latency'] = latency
                    
            # 更新健康状态
            self._update_health_status(device_id)
            
            # 记录详细日志
            if metadata:
                log_message = f"设备 {device_id} 连接{'成功' if success else '失败'}"
                if latency is not None:
                    log_message += f"，延迟 {latency:.2f}ms"
                log_message += f"，成功率 {metrics['success_rate']:.2%}"
                
                if success:
                    logger.debug(log_message)
                else:
                    logger.warning(log_message)
    
    def record_connection_reset(self, device_id: str, reason: Optional[str] = None):
        """记录连接重置
        
        Args:
            device_id: 设备ID
            reason: 重置原因
        """
        with self._lock:
            # 确保设备指标存在
            if device_id not in self._device_metrics:
                self._device_metrics[device_id] = {
                    'attempts': 0,
                    'success': 0,
                    'failures': 0,
                    'resets': 0,
                    'last_attempt': 0,
                    'last_success': 0,
                    'last_failure': 0,
                    'success_rate': 1.0,
                    'avg_latency': 0,
                    'min_latency': None,
                    'max_latency': None,
                    'consecutive_failures': 0,
                    'consecutive_success': 0,
                    'health_status': HealthStatus.UNKNOWN.value,
                }
                
            metrics = self._device_metrics[device_id]
            metrics['resets'] += 1
            
            # 更新健康状态
            self._update_health_status(device_id)
            
            log_message = f"设备 {device_id} 连接重置，总计 {metrics['resets']} 次"
            if reason:
                log_message += f"，原因：{reason}"
                
            logger.warning(log_message)
    
    def get_device_metrics(self, device_id: str) -> Dict[str, Any]:
        """获取设备指标
        
        Args:
            device_id: 设备ID
            
        Returns:
            Dict[str, Any]: 设备指标，如果设备不存在则返回空字典
        """
        with self._lock:
            return self._device_metrics.get(device_id, {}).copy()
    
    def get_all_device_metrics(self) -> Dict[str, Dict[str, Any]]:
        """获取所有设备指标
        
        Returns:
            Dict[str, Dict[str, Any]]: 设备ID到指标的映射
        """
        with self._lock:
            return {device_id: metrics.copy() for device_id, metrics in self._device_metrics.items()}
    
    def reset_device_metrics(self, device_id: str):
        """重置设备指标
        
        Args:
            device_id: 设备ID
        """
        with self._lock:
            if device_id in self._device_metrics:
                del self._device_metrics[device_id]
            
            if device_id in self._device_connections:
                del self._device_connections[device_id]
                
            logger.info(f"设备 {device_id} 指标已重置")
    
    def reset_all_metrics(self):
        """重置所有设备指标"""
        with self._lock:
            self._device_metrics.clear()
            self._device_connections.clear()
            logger.info("所有设备指标已重置")
    
    def update_health_check_thresholds(self, thresholds: Dict[str, Any]):
        """更新健康检查阈值
        
        Args:
            thresholds: 阈值字典
        """
        with self._lock:
            self._health_check_thresholds.update(thresholds)
            
            # 更新所有设备的健康状态
            for device_id in self._device_metrics:
                self._update_health_status(device_id)
    
    def save_metrics(self, file_path: str) -> bool:
        """保存指标到文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 如果成功保存则返回True，否则返回False
        """
        try:
            with self._lock:
                data = {
                    'metrics': self._device_metrics,
                    'thresholds': self._health_check_thresholds,
                    'timestamp': time.time()
                }
                
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"已保存 {len(self._device_metrics)} 个设备指标到 {file_path}")
            return True
        except Exception as e:
            logger.error(f"保存指标失败: {e}")
            return False
    
    def load_metrics(self, file_path: str) -> bool:
        """从文件加载指标
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 如果成功加载则返回True，否则返回False
        """
        if not os.path.exists(file_path):
            logger.warning(f"指标文件 {file_path} 不存在")
            return False
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            with self._lock:
                self._device_metrics = data.get('metrics', {})
                
                if 'thresholds' in data:
                    self._health_check_thresholds = data['thresholds']
                    
                # 重新初始化连接记录
                self._device_connections.clear()
                for device_id, metrics in self._device_metrics.items():
                    success_rate = metrics.get('success_rate', 1.0)
                    conn_results = deque(maxlen=self._window_size)
                    
                    # 根据成功率填充连接记录
                    success_count = int(success_rate * self._window_size)
                    for _ in range(success_count):
                        conn_results.append(1)
                    for _ in range(self._window_size - success_count):
                        conn_results.append(0)
                        
                    self._device_connections[device_id] = conn_results
                
            logger.info(f"已从 {file_path} 加载 {len(self._device_metrics)} 个设备指标")
            return True
        except Exception as e:
            logger.error(f"加载指标失败: {e}")
            return False
    
    def _update_health_status(self, device_id: str):
        """更新设备健康状态
        
        Args:
            device_id: 设备ID
        """
        if device_id not in self._device_metrics:
            return
            
        metrics = self._device_metrics[device_id]
        thresholds = self._health_check_thresholds
        
        # 默认健康
        health_status = HealthStatus.HEALTHY
        
        # 检查成功率
        if metrics['success_rate'] < thresholds['success_rate_critical']:
            health_status = HealthStatus.CRITICAL
        elif metrics['success_rate'] < thresholds['success_rate_warning']:
            health_status = max(health_status, HealthStatus.WARNING)
            
        # 检查连续失败次数
        if metrics['consecutive_failures'] >= thresholds['failure_count_critical']:
            health_status = HealthStatus.CRITICAL
        elif metrics['consecutive_failures'] >= thresholds['failure_count_warning']:
            health_status = max(health_status, HealthStatus.WARNING)
            
        # 检查重置次数
        if metrics['resets'] >= thresholds['reset_count_critical']:
            health_status = HealthStatus.CRITICAL
        elif metrics['resets'] >= thresholds['reset_count_warning']:
            health_status = max(health_status, HealthStatus.WARNING)
            
        # 检查延迟
        if metrics['avg_latency'] > 0:
            if metrics['avg_latency'] >= thresholds['latency_critical']:
                health_status = HealthStatus.CRITICAL
            elif metrics['avg_latency'] >= thresholds['latency_warning']:
                health_status = max(health_status, HealthStatus.WARNING)
                
        # 更新健康状态
        metrics['health_status'] = health_status.value

class MetricsCollector:
    """指标收集器类
    
    负责收集和聚合多种指标
    """
    
    def __init__(self):
        """初始化指标收集器"""
        self._lock = threading.RLock()
        self._metrics: Dict[str, Dict[str, Any]] = {}  # metric_name -> metric_data
        self._metric_types: Dict[str, MetricType] = {}  # metric_name -> metric_type
        self._last_update: Dict[str, float] = {}  # metric_name -> timestamp
    
    def register_metric(self, name: str, metric_type: MetricType, initial_value: Any = None,
                       description: Optional[str] = None):
        """注册指标
        
        Args:
            name: 指标名称
            metric_type: 指标类型
            initial_value: 初始值
            description: 指标描述
        """
        with self._lock:
            if name in self._metrics:
                logger.warning(f"指标 {name} 已存在，将被覆盖")
                
            if metric_type == MetricType.COUNTER:
                initial_value = initial_value or 0
            elif metric_type == MetricType.GAUGE:
                initial_value = initial_value or 0.0
            elif metric_type == MetricType.HISTOGRAM:
                initial_value = initial_value or {}
                
            self._metrics[name] = {
                'value': initial_value,
                'description': description or "",
                'labels': {},
            }
            
            self._metric_types[name] = metric_type
            self._last_update[name] = time.time()
            
            logger.debug(f"已注册指标 {name}，类型 {metric_type.value}")
    
    def update_counter(self, name: str, increment: int = 1, labels: Optional[Dict[str, str]] = None):
        """更新计数器
        
        Args:
            name: 指标名称
            increment: 增量
            labels: 标签
        """
        with self._lock:
            if name not in self._metrics:
                self.register_metric(name, MetricType.COUNTER, 0)
                
            if self._metric_types[name] != MetricType.COUNTER:
                logger.error(f"指标 {name} 不是计数器类型")
                return
                
            metric = self._metrics[name]
            
            if labels:
                label_key = self._labels_to_key(labels)
                if 'labels' not in metric:
                    metric['labels'] = {}
                    
                if label_key not in metric['labels']:
                    metric['labels'][label_key] = {
                        'value': 0,
                        'labels': labels
                    }
                    
                metric['labels'][label_key]['value'] += increment
            else:
                metric['value'] += increment
                
            self._last_update[name] = time.time()
    
    def update_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """更新测量值
        
        Args:
            name: 指标名称
            value: 测量值
            labels: 标签
        """
        with self._lock:
            if name not in self._metrics:
                self.register_metric(name, MetricType.GAUGE, 0.0)
                
            if self._metric_types[name] != MetricType.GAUGE:
                logger.error(f"指标 {name} 不是测量值类型")
                return
                
            metric = self._metrics[name]
            
            if labels:
                label_key = self._labels_to_key(labels)
                if 'labels' not in metric:
                    metric['labels'] = {}
                    
                if label_key not in metric['labels']:
                    metric['labels'][label_key] = {
                        'value': 0.0,
                        'labels': labels
                    }
                    
                metric['labels'][label_key]['value'] = value
            else:
                metric['value'] = value
                
            self._last_update[name] = time.time()
    
    def update_histogram(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """更新直方图
        
        Args:
            name: 指标名称
            value: 数值
            labels: 标签
        """
        with self._lock:
            if name not in self._metrics:
                self.register_metric(name, MetricType.HISTOGRAM, {'count': 0, 'sum': 0.0, 'buckets': {}})
                
            if self._metric_types[name] != MetricType.HISTOGRAM:
                logger.error(f"指标 {name} 不是直方图类型")
                return
                
            metric = self._metrics[name]
            
            if labels:
                label_key = self._labels_to_key(labels)
                if 'labels' not in metric:
                    metric['labels'] = {}
                    
                if label_key not in metric['labels']:
                    metric['labels'][label_key] = {
                        'value': {'count': 0, 'sum': 0.0, 'buckets': {}},
                        'labels': labels
                    }
                    
                hist_data = metric['labels'][label_key]['value']
                hist_data['count'] += 1
                hist_data['sum'] += value
                
                # 更新桶
                for bucket in [1, 5, 10, 50, 100, 500, 1000, 5000, 10000]:
                    if bucket not in hist_data['buckets']:
                        hist_data['buckets'][bucket] = 0
                    if value <= bucket:
                        hist_data['buckets'][bucket] += 1
            else:
                hist_data = metric['value']
                hist_data['count'] += 1
                hist_data['sum'] += value
                
                # 更新桶
                for bucket in [1, 5, 10, 50, 100, 500, 1000, 5000, 10000]:
                    if bucket not in hist_data['buckets']:
                        hist_data['buckets'][bucket] = 0
                    if value <= bucket:
                        hist_data['buckets'][bucket] += 1
                
            self._last_update[name] = time.time()
    
    def get_metric(self, name: str) -> Optional[Dict[str, Any]]:
        """获取指标
        
        Args:
            name: 指标名称
            
        Returns:
            Optional[Dict[str, Any]]: 指标数据，如果不存在则返回None
        """
        with self._lock:
            if name not in self._metrics:
                return None
                
            metric = self._metrics[name].copy()
            metric['type'] = self._metric_types[name].value
            metric['last_update'] = self._last_update[name]
            
            return metric
    
    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """获取所有指标
        
        Returns:
            Dict[str, Dict[str, Any]]: 指标名称到指标数据的映射
        """
        with self._lock:
            result = {}
            for name, metric in self._metrics.items():
                result[name] = metric.copy()
                result[name]['type'] = self._metric_types[name].value
                result[name]['last_update'] = self._last_update[name]
                
            return result
    
    def reset_metric(self, name: str):
        """重置指标
        
        Args:
            name: 指标名称
        """
        with self._lock:
            if name not in self._metrics:
                return
                
            metric_type = self._metric_types[name]
            
            if metric_type == MetricType.COUNTER:
                initial_value = 0
            elif metric_type == MetricType.GAUGE:
                initial_value = 0.0
            elif metric_type == MetricType.HISTOGRAM:
                initial_value = {'count': 0, 'sum': 0.0, 'buckets': {}}
            
            self._metrics[name]['value'] = initial_value
            
            if 'labels' in self._metrics[name]:
                for label_key in self._metrics[name]['labels']:
                    self._metrics[name]['labels'][label_key]['value'] = initial_value
                    
            self._last_update[name] = time.time()
            
            logger.debug(f"已重置指标 {name}")
    
    def reset_all_metrics(self):
        """重置所有指标"""
        with self._lock:
            for name in self._metrics:
                self.reset_metric(name)
                
            logger.info("已重置所有指标")
    
    def remove_metric(self, name: str):
        """移除指标
        
        Args:
            name: 指标名称
        """
        with self._lock:
            if name in self._metrics:
                del self._metrics[name]
                
            if name in self._metric_types:
                del self._metric_types[name]
                
            if name in self._last_update:
                del self._last_update[name]
                
            logger.debug(f"已移除指标 {name}")
    
    def save_metrics(self, file_path: str) -> bool:
        """保存指标到文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 如果成功保存则返回True，否则返回False
        """
        try:
            with self._lock:
                data = {
                    'metrics': self._metrics,
                    'metric_types': {name: metric_type.value for name, metric_type in self._metric_types.items()},
                    'last_update': self._last_update,
                    'timestamp': time.time()
                }
                
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"已保存 {len(self._metrics)} 个指标到 {file_path}")
            return True
        except Exception as e:
            logger.error(f"保存指标失败: {e}")
            return False
    
    def load_metrics(self, file_path: str) -> bool:
        """从文件加载指标
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 如果成功加载则返回True，否则返回False
        """
        if not os.path.exists(file_path):
            logger.warning(f"指标文件 {file_path} 不存在")
            return False
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            with self._lock:
                self._metrics = data.get('metrics', {})
                
                # 转换指标类型
                metric_types = data.get('metric_types', {})
                self._metric_types = {name: MetricType(metric_type) for name, metric_type in metric_types.items()}
                
                self._last_update = data.get('last_update', {})
                
            logger.info(f"已从 {file_path} 加载 {len(self._metrics)} 个指标")
            return True
        except Exception as e:
            logger.error(f"加载指标失败: {e}")
            return False
    
    def _labels_to_key(self, labels: Dict[str, str]) -> str:
        """将标签转换为键
        
        Args:
            labels: 标签字典
            
        Returns:
            str: 标签键
        """
        return ';'.join(f"{k}:{v}" for k, v in sorted(labels.items())) 
