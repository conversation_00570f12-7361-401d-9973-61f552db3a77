import os
import subprocess
import time
import sys
import atexit
import requests

# --- Configuration ---
SLAVE_ID = "test_slave_007"
MAIN_SERVER_API_KEY = "test_api_key_for_e2e"
# Use sys.executable to ensure we use the same python interpreter
MOCK_VH_EXECUTABLE = f'"{sys.executable}" tests/mock_virtualhere.py'
MAIN_SERVER_URL = "http://localhost:8000"
API_URL = f"{MAIN_SERVER_URL}/api/v1"

processes = []

def cleanup():
    """Ensure all background processes are terminated on exit."""
    print("--- Cleaning up processes ---")
    for p in processes:
        if p.poll() is None:
            print(f"Terminating process {p.pid}...")
            p.terminate()
            try:
                p.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"Process {p.pid} did not terminate, killing.")
                p.kill()
    print("--- Cleanup complete ---")

atexit.register(cleanup)

def create_test_env_file():
    """Create a .env.test file for the slave server."""
    print("--- Creating .env.test file for slave server ---")
    with open("slave_server/.env.test", "w") as f:
        f.write(f'SLAVE_ID="{SLAVE_ID}"\n')
        f.write(f'MAIN_SERVER_API_KEY="{MAIN_SERVER_API_KEY}"\n')
        # The value must be quoted to handle potential spaces in sys.executable path
        f.write(f'VIRTUALHERE_PATH="{sys.executable} tests/mock_virtualhere.py"\n')
        f.write(f'MAIN_SERVER_URL="{MAIN_SERVER_URL}"\n')

def start_main_server():
    """Starts the main_server as a background process."""
    print("--- Starting Main Server ---")
    # For E2E test, we need a predictable way to get an auth token.
    # The easiest is to use the default superuser created by init_db.py
    # We need to ensure that user has a known API key.
    # We will assume `init_db` has been run and the first user is the test user.
    # A more robust solution would be to create a dedicated test user via API.
    main_server_process = subprocess.Popen(
        [sys.executable, "-m", "uvicorn", "main_server.main:app", "--host", "0.0.0.0", "--port", "8000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )
    processes.append(main_server_process)
    print(f"Main Server started with PID: {main_server_process.pid}")
    time.sleep(5)  # Give it a moment to start up
    return main_server_process

def start_slave_server():
    """Starts the slave_server as a background process, using the .env.test file."""
    print("--- Starting Slave Server ---")
    slave_env = os.environ.copy()
    slave_env["ENV_FILE"] = ".env.test"
    slave_server_process = subprocess.Popen(
        [sys.executable, "main.py"],
        cwd="slave_server",
        env=slave_env,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )
    processes.append(slave_server_process)
    print(f"Slave Server started with PID: {slave_server_process.pid}")
    time.sleep(5)  # Give it a moment to connect and report
    return slave_server_process

def run_tests():
    """Main test execution logic."""
    print("--- Running E2E Tests ---")
    headers = {"Authorization": f"Bearer {MAIN_SERVER_API_KEY}"}
    
    # Step 1: Verify slave connected and reported initial state
    print("\n>>> Test 1: Verify slave connection and initial report")
    response = requests.get(f"{API_URL}/slaves", headers=headers)
    assert response.status_code == 200
    slaves_list = response.json()
    print(f"GET /slaves response: {slaves_list}")
    assert any(s["slave_id"] == SLAVE_ID for s in slaves_list)
    print("PASS: Slave is connected and visible in the list.")
    
    # Step 2: Send a command to start a VH instance
    print(f"\n>>> Test 2: Send START_VH_INSTANCE command to {SLAVE_ID}")
    command_payload = {
        "action": "START_VH_INSTANCE",
        "device_path": "USB\\VID_1234&PID_5678\\SERIAL123",
        "port": 7575
    }
    response = requests.post(f"{API_URL}/slaves/{SLAVE_ID}/command", headers=headers, json=command_payload)
    print(f"POST /slaves/{SLAVE_ID}/command response: {response.status_code} {response.text}")
    assert response.status_code == 200
    print("PASS: Command sent successfully.")
    
    # Step 3: Verify the VH instance is running
    print("\n>>> Test 3: Verify VH instance is reported as running")
    time.sleep(11) # Wait for the next reporting cycle (10s interval + 1s buffer)
    response = requests.get(f"{API_URL}/slaves/{SLAVE_ID}", headers=headers)
    assert response.status_code == 200
    slave_details = response.json()
    print(f"GET /slaves/{SLAVE_ID} response: {slave_details}")
    vh_instances = slave_details.get("latest_report", {}).get("vh_instances", [])
    assert len(vh_instances) > 0
    assert vh_instances[0]["port"] == 7575
    assert vh_instances[0]["device_id"] == "USB\\VID_1234&PID_5678\\SERIAL123"
    print("PASS: VH instance is active and reported correctly.")
    
    print("\n--- ALL E2E TESTS PASSED ---")
    
def main():
    """Main test execution flow."""
    create_test_env_file()
    # For this test to work, we need a user with the known API key.
    # We'll need to handle this. For now, let's assume it exists.
    # A better way would be to create a test DB and user first.
    print("NOTE: This test assumes a user with API key 'test_api_key_for_e2e' exists.")
    print("You may need to run main_server/scripts/init_db.py and create_superuser.py,")
    print("or modify an existing user.")
    
    start_main_server()
    start_slave_server()
    
    try:
        run_tests()
    except Exception as e:
        print(f"\n!!!!!! An error occurred during tests: {e} !!!!!", file=sys.stderr)
        # Print server logs for debugging
        for p in processes:
            stdout, stderr = p.communicate()
            print(f"\n--- Logs for PID {p.pid} ---")
            print(f"-- STDOUT --\n{stdout.decode(errors='ignore')}")
            print(f"-- STDERR --\n{stderr.decode(errors='ignore')}")
        sys.exit(1)
    finally:
        # atexit handler will run for cleanup
        print("\n--- Test run finished ---")

if __name__ == "__main__":
    main()
