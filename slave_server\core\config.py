import os
from pydantic_settings import BaseSettings, SettingsConfigDict

# Determine which .env file to use. Default to .env if not specified.
env_file = os.getenv("ENV_FILE", ".env")

class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=env_file, 
        env_file_encoding='utf-8',
        extra='ignore'  # Ignore extra fields from .env file
    )

    # Core settings
    PROJECT_NAME: str = "OmniLink Slave Server"
    LOG_LEVEL: str = "INFO"
    SLAVE_ID: str
    
    # Slave server API settings
    SLAVE_SERVER_PORT: int = 8001
    SLAVE_SERVER_HOST: str = "0.0.0.0"

    # Main server connection settings
    MAIN_SERVER_URL: str = "http://localhost:8000"
    MAIN_SERVER_API_KEY: str

    # VirtualHere settings
    VIRTUALHERE_HOST: str = "127.0.0.1"
    VIRTUALHERE_PORT: int = 7575
    VIRTUALHERE_BINARY_PATH: str = "/app/vhusbdx86_64"
    
    # Device monitoring settings
    DEVICE_POLL_INTERVAL: int = 5

    # Heartbeat settings
    HEARTBEAT_INTERVAL: int = 30

    # Reconnect settings
    RECONNECT_INTERVAL: int = 10
    MAX_RECONNECT_ATTEMPTS: int = 5

    # Security settings
    API_KEY_HEADER: str = "Authorization"

    # System monitoring settings
    ENABLE_SYSTEM_MONITORING: bool = True
    SYSTEM_MONITOR_INTERVAL: int = 60

settings = Settings()
