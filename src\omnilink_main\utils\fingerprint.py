import hashlib
import json
import logging
import platform
import subprocess
import re
import uuid
from typing import Dict, List, Any, Optional
from pathlib import Path

logger = logging.getLogger('fingerprint')

class DeviceFingerprint:
    """设备指纹工具，用于收集和验证设备标识"""
    
    @staticmethod
    def get_mac_addresses() -> List[str]:
        """
        获取所有网络接口的MAC地址
        
        返回:
            list: MAC地址列表
        """
        mac_list = []
        
        try:
            # 尝试通过ip命令获取
            try:
                output = subprocess.check_output(
                    "ip link show | grep link/ether | awk '{print $2}'", 
                    shell=True
                ).decode()
                for line in output.strip().split('\n'):
                    if line:
                        mac_list.append(line.strip())
            except Exception as e:
                logger.warning(f"通过ip命令获取MAC地址失败: {str(e)}")
                
                # 尝试通过ifconfig命令获取
                try:
                    output = subprocess.check_output(
                        "ifconfig | grep -o -E '([0-9a-fA-F]{2}:){5}[0-9a-fA-F]{2}'", 
                        shell=True
                    ).decode()
                    for line in output.strip().split('\n'):
                        if line:
                            mac_list.append(line.strip())
                except Exception as e:
                    logger.warning(f"通过ifconfig命令获取MAC地址失败: {str(e)}")
            
            # 如果前两种方法都失败，尝试从/sys/class/net获取
            if not mac_list:
                try:
                    net_dirs = Path('/sys/class/net').glob('*')
                    for net_dir in net_dirs:
                        try:
                            with open(net_dir / 'address', 'r') as f:
                                mac = f.read().strip()
                                if mac and re.match(
                                    r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$', 
                                    mac
                                ):
                                    mac_list.append(mac)
                        except:
                            pass
                except Exception as e:
                    logger.warning(f"从/sys/class/net获取MAC地址失败: {str(e)}")
            
            # 如果上述方法都失败，尝试使用uuid模块（不太可靠）
            if not mac_list:
                try:
                    mac = ':'.join(re.findall('..', '%012x' % uuid.getnode()))
                    mac_list.append(mac)
                except Exception as e:
                    logger.error(f"通过uuid模块获取MAC地址失败: {str(e)}")
        except Exception as e:
            logger.error(f"获取MAC地址异常: {str(e)}")
        
        return mac_list
    
    @staticmethod
    def get_hardware_info() -> Dict[str, Any]:
        """
        获取硬件信息（该方法保留用于兼容，但不再用于指纹生成）
        
        返回:
            dict: 包含硬件信息的字典
        """
        hw_info = {
            'platform': 'Linux'
        }
        
        try:
            # 操作系统信息
            hw_info['os'] = platform.platform()
            
            # CPU信息
            hw_info['cpu'] = platform.processor()
            
            # 主机名
            hw_info['hostname'] = platform.node()
            
            # Linux平台获取主板和硬盘信息
            try:
                # 获取主板序列号
                try:
                    with open('/sys/class/dmi/id/board_serial', 'r') as f:
                        hw_info['motherboard'] = f.read().strip()
                except:
                    # 尝试使用dmidecode命令
                    try:
                        output = subprocess.check_output(
                            'dmidecode -s baseboard-serial-number', 
                            shell=True
                        ).decode()
                        hw_info['motherboard'] = output.strip()
                    except:
                        hw_info['motherboard'] = 'unknown'
                
                # 获取硬盘序列号
                try:
                    output = subprocess.check_output(
                        'lsblk -o NAME,SERIAL | grep -v loop | grep -v NAME', 
                        shell=True
                    ).decode()
                    disks = []
                    for line in output.strip().split('\n'):
                        parts = line.strip().split()
                        if len(parts) >= 2:
                            disks.append(parts[1])
                    hw_info['disk'] = ','.join(disks) if disks else 'unknown'
                except:
                    hw_info['disk'] = 'unknown'
            except Exception as e:
                logger.warning(f"获取Linux硬件信息失败: {str(e)}")
                hw_info['motherboard'] = 'unknown'
                hw_info['disk'] = 'unknown'
        except Exception as e:
            logger.error(f"获取硬件信息异常: {str(e)}")
        
        return hw_info
    
    @staticmethod
    def generate_fingerprint() -> Dict[str, Any]:
        """
        生成设备指纹，仅使用MAC地址
        
        基于需求文档修改：
        - 修订后的设备指纹算法（兼容树莓派3B硬件限制）
        - 仅使用MAC地址生成指纹
        - 若无法获取MAC地址，则禁止设备注册，不提供备选验证方式
        
        返回:
            dict: 包含指纹和原始数据的字典
        """
        try:
            # 获取MAC地址
            mac_addresses = DeviceFingerprint.get_mac_addresses()
            
            # 检查是否有MAC地址
            if not mac_addresses:
                logger.error("无法获取MAC地址，设备指纹生成失败")
                return {
                    'fingerprint': '',
                    'raw_data': {},
                    'success': False,
                    'error': '无法获取MAC地址，禁止设备注册'
                }
            
            # 使用第一个MAC地址生成指纹
            mac = mac_addresses[0]
            
            # 保存原始数据（仅MAC地址）
            fingerprint_data = {
                'mac': mac,
                'mac_list': mac_addresses,
            }
            
            # 生成指纹哈希
            fingerprint_hash = hashlib.sha256(mac.encode()).hexdigest()
            
            logger.info(f"已使用MAC地址生成设备指纹: {fingerprint_hash[:10]}...")
            
            # 返回指纹和原始数据
            return {
                'fingerprint': fingerprint_hash,
                'raw_data': fingerprint_data,
                'success': True
            }
        except Exception as e:
            logger.error(f"生成设备指纹失败: {str(e)}")
            return {
                'fingerprint': '',
                'raw_data': {},
                'success': False,
                'error': f'指纹生成出错: {str(e)}'
            }
    
    @staticmethod
    def verify_fingerprint(stored_fingerprint: str, current_fingerprint: str) -> bool:
        """
        验证设备指纹
        
        参数:
            stored_fingerprint: 存储的指纹
            current_fingerprint: 当前指纹
            
        返回:
            bool: 指纹是否匹配
        """
        # 简单比较指纹哈希
        return stored_fingerprint == current_fingerprint 
