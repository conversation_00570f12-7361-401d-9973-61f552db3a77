# Inspired by the script from: https://www.virtualhere.com/comment/7610#comment-7610
import os
import sys
import subprocess
from dataclasses import dataclass
from typing import List, Optional

@dataclass
class Device:
    """Represents a USB device connected to a VirtualHere server."""
    address: str
    vendor: str
    product: str
    in_use_by: Optional[str] = None

@dataclass
class Server:
    """Represents a VirtualHere server (hub) and its devices."""
    address: str
    hostname: str
    devices: List[Device]

class VirtualHereService:
    """
    Service for interacting with the local VirtualHere client.
    It uses the command-line interface as a robust fallback for Windows.
    """
    def __init__(self, vhui_path="C:\\Program Files\\VirtualHere Client\\vhui64.exe"):
        self.vhui_path = vhui_path
        if sys.platform == "win32" and not os.path.exists(self.vhui_path):
            print(f"Warning: VirtualHere executable not found at '{self.vhui_path}'")

    def _execute_command(self, command: str) -> Optional[str]:
        """
        Executes a command using the VirtualHere client CLI.
        Returns the response from the client.
        """
        if sys.platform != "win32":
            print("Error: This service implementation currently only supports Windows CLI.")
            return None
        
        try:
            process = subprocess.run(
                [self.vhui_path, "-t", command],
                capture_output=True,
                text=True,
                timeout=5,
                check=True
            )
            return process.stdout
        except FileNotFoundError:
            print(f"Error: Could not find VirtualHere executable at '{self.vhui_path}'.")
            return None
        except subprocess.TimeoutExpired:
            print(f"Error: Command '{command}' timed out.")
            return None
        except subprocess.CalledProcessError as e:
            print(f"Error executing command '{command}': {e.stderr}")
            return None
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            return None

    def list_servers_and_devices(self) -> List[Server]:
        """
        Lists all servers and their devices, parsing the output into structured objects.
        """
        raw_output = self._execute_command("LIST")
        if not raw_output:
            return []

        servers = []
        current_server = None
        lines = raw_output.strip().split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if "Auto-Find" in line or "Manual Hubs" in line:
                if current_server:
                    servers.append(current_server)
                try:
                    parts = line.split()
                    hostname = parts[2]
                    address = parts[3].strip('():')
                    current_server = Server(address=address, hostname=hostname, devices=[])
                except IndexError:
                    continue

            elif "Device" in line and current_server:
                try:
                    parts = line.split(',')
                    device_address = parts[0].split()[1].strip()
                    vendor = parts[1].strip()
                    product = parts[2].strip()
                    device = Device(address=device_address, vendor=vendor, product=product)
                    if "IN USE BY" in line:
                        in_use_by = line.split("IN USE BY")[1].strip().strip('()')
                        device.in_use_by = in_use_by
                    current_server.devices.append(device)
                except IndexError:
                    continue

        if current_server and current_server not in servers:
            servers.append(current_server)

        return servers

    def add_manual_hub(self, address: str) -> bool:
        """Adds a hub (server) manually."""
        response = self._execute_command(f"MANUAL HUB ADD,{address}")
        return response is not None and "OK" in response

    def use_device(self, address: str, password: Optional[str] = None) -> bool:
        """Tells the client to use a specific device."""
        command = f"USE,{address}"
        if password:
            command += f",{password}"
        response = self._execute_command(command)
        return response is not None and "OK" in response

    def stop_using_device(self, address: str) -> bool:
        """Tells the client to stop using a device."""
        response = self._execute_command(f"STOP USING,{address}")
        return response is not None and "OK" in response

# Singleton instance
virtualhere_service = VirtualHereService()