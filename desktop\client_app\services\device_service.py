"""
设备服务模块
负责设备的发现、权限验证、连接管理等功能
"""

import logging
import requests
from typing import Dict, List, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class DeviceService:
    """设备服务类 - 处理所有设备相关操作"""
    
    def __init__(self, api_base_url: str, auth_service):
        self.api_base_url = api_base_url.rstrip('/')
        self.auth_service = auth_service
        self.cached_devices = {}
        self.active_connections = {}  # device_id -> connection_info
        
    def get_headers(self) -> Dict[str, str]:
        """获取API请求头"""
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        if self.auth_service.is_authenticated():
            token = self.auth_service.get_token()
            headers['Authorization'] = f'Bearer {token}'
            
        return headers
    
    def get_available_devices(self) -> Dict[str, Any]:
        """
        获取当前用户可用的设备列表
        只返回用户有权限访问的设备
        """
        try:
            response = requests.get(
                f"{self.api_base_url}/api/v1/devices/available",
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                devices = data.get('devices', [])
                
                # 过滤设备 - 只显示用户有权限的设备
                filtered_devices = []
                for device in devices:
                    if self._check_device_permission(device):
                        filtered_devices.append(device)
                
                # 更新缓存
                self.cached_devices = {d['id']: d for d in filtered_devices}
                
                logger.info(f"获取到 {len(filtered_devices)} 个可用设备")
                return {
                    'success': True,
                    'devices': filtered_devices,
                    'total': len(filtered_devices)
                }
            elif response.status_code == 401:
                logger.warning("认证已过期，需要重新登录")
                self.auth_service.logout()
                return {'success': False, 'error': '认证已过期'}
            else:
                error_msg = response.json().get('detail', '获取设备列表失败')
                logger.error(f"获取设备列表失败: {error_msg}")
                return {'success': False, 'error': error_msg}
                
        except requests.RequestException as e:
            logger.error(f"网络请求失败: {e}")
            return {'success': False, 'error': '网络连接失败'}
        except Exception as e:
            logger.error(f"获取设备列表异常: {e}")
            return {'success': False, 'error': '系统错误'}
    
    def _check_device_permission(self, device: Dict) -> bool:
        """
        检查用户是否有权限访问指定设备
        这里实现客户端侧的权限验证逻辑
        """
        # 基本状态检查
        if device.get('status') not in ['available', 'in_use']:
            return False
        
        # 如果设备正在被使用，检查是否是当前用户
        if device.get('status') == 'in_use':
            current_user_id = self.auth_service.get_current_user_id()
            if device.get('current_user_id') != current_user_id:
                return False
        
        # 检查设备是否在用户的权限范围内
        # 这里可以根据用户角色、组织等进行更复杂的权限判断
        user_permissions = self.auth_service.get_user_permissions()
        
        # 超级管理员可以看到所有设备
        if 'admin.*' in user_permissions:
            return True
        
        # 检查设备特定权限
        if 'device.view' not in user_permissions:
            return False
        
        # 检查设备类型权限
        device_type = device.get('device_type', '')
        if device_type and f'device.{device_type}.access' in user_permissions:
            return True
        
        # 检查组织权限
        user_org_id = self.auth_service.get_user_org_id()
        device_org_id = device.get('organization_id')
        if user_org_id and device_org_id == user_org_id:
            return True
        
        # 默认权限检查
        return 'device.connect' in user_permissions
    
    def request_device_connection(self, device_id: str) -> Dict[str, Any]:
        """
        请求连接指定设备
        返回VirtualHere连接信息
        """
        try:
            # 检查设备是否可用
            device = self.cached_devices.get(device_id)
            if not device:
                return {'success': False, 'error': '设备不存在或不可用'}
            
            if device.get('status') == 'in_use':
                current_user_id = self.auth_service.get_current_user_id()
                if device.get('current_user_id') != current_user_id:
                    return {'success': False, 'error': '设备正在被其他用户使用'}
            
            # 发送连接请求
            payload = {
                'device_id': device_id,
                'client_info': {
                    'client_version': '1.0.0',
                    'os_info': self._get_os_info(),
                    'hardware_id': self.auth_service.get_hardware_id()
                }
            }
            
            response = requests.post(
                f"{self.api_base_url}/api/v1/devices/{device_id}/connect",
                json=payload,
                headers=self.get_headers(),
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                connection_info = data.get('connection_info', {})
                
                # 保存连接信息
                self.active_connections[device_id] = {
                    'device_id': device_id,
                    'device_name': device.get('description', '未知设备'),
                    'device_type': device.get('device_type', '未知类型'),
                    'vh_address': connection_info.get('vh_address'),
                    'vh_port': connection_info.get('vh_port'),
                    'session_id': connection_info.get('session_id'),
                    'connected_at': datetime.now(),
                    'slave_server': device.get('slave_server_name', '未知服务器')
                }
                
                logger.info(f"设备连接请求成功: {device_id}")
                return {
                    'success': True,
                    'connection_info': self.active_connections[device_id]
                }
            else:
                error_msg = response.json().get('detail', '连接请求失败')
                logger.error(f"设备连接请求失败: {error_msg}")
                return {'success': False, 'error': error_msg}
                
        except requests.RequestException as e:
            logger.error(f"连接请求网络错误: {e}")
            return {'success': False, 'error': '网络连接失败'}
        except Exception as e:
            logger.error(f"连接请求异常: {e}")
            return {'success': False, 'error': '系统错误'}
    
    def disconnect_device(self, device_id: str) -> Dict[str, Any]:
        """
        断开设备连接
        """
        try:
            connection_info = self.active_connections.get(device_id)
            if not connection_info:
                return {'success': False, 'error': '设备未连接'}
            
            # 发送断开请求
            payload = {
                'session_id': connection_info.get('session_id'),
                'reason': 'user_disconnect'
            }
            
            response = requests.post(
                f"{self.api_base_url}/api/v1/devices/{device_id}/disconnect",
                json=payload,
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                # 移除连接信息
                self.active_connections.pop(device_id, None)
                
                logger.info(f"设备断开连接成功: {device_id}")
                return {'success': True}
            else:
                error_msg = response.json().get('detail', '断开连接失败')
                logger.error(f"设备断开连接失败: {error_msg}")
                return {'success': False, 'error': error_msg}
                
        except requests.RequestException as e:
            logger.error(f"断开连接网络错误: {e}")
            # 即使网络请求失败，也要清理本地连接状态
            self.active_connections.pop(device_id, None)
            return {'success': False, 'error': '网络连接失败，但已清理本地连接状态'}
        except Exception as e:
            logger.error(f"断开连接异常: {e}")
            return {'success': False, 'error': '系统错误'}
    
    def get_active_connections(self) -> Dict[str, Dict]:
        """获取当前活跃的连接列表"""
        return self.active_connections.copy()
    
    def get_device_info(self, device_id: str) -> Optional[Dict]:
        """获取设备详细信息"""
        return self.cached_devices.get(device_id)
    
    def check_device_status(self, device_id: str) -> str:
        """检查设备当前状态"""
        try:
            response = requests.get(
                f"{self.api_base_url}/api/v1/devices/{device_id}/status",
                headers=self.get_headers(),
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('status', 'unknown')
            else:
                return 'error'
                
        except Exception as e:
            logger.error(f"检查设备状态失败: {e}")
            return 'unknown'
    
    def refresh_device_list(self) -> bool:
        """刷新设备列表"""
        result = self.get_available_devices()
        return result.get('success', False)
    
    def _get_os_info(self) -> Dict[str, str]:
        """获取操作系统信息"""
        import platform
        return {
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),
            'processor': platform.processor()
        }
    
    def get_connection_history(self, limit: int = 10) -> List[Dict]:
        """获取连接历史记录"""
        try:
            response = requests.get(
                f"{self.api_base_url}/api/v1/devices/connection-history",
                params={'limit': limit},
                headers=self.get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('history', [])
            else:
                logger.error("获取连接历史失败")
                return []
                
        except Exception as e:
            logger.error(f"获取连接历史异常: {e}")
            return []
    
    def report_connection_issue(self, device_id: str, issue_type: str, description: str) -> bool:
        """报告连接问题"""
        try:
            payload = {
                'device_id': device_id,
                'issue_type': issue_type,
                'description': description,
                'client_info': {
                    'version': '1.0.0',
                    'os_info': self._get_os_info()
                }
            }
            
            response = requests.post(
                f"{self.api_base_url}/api/v1/devices/report-issue",
                json=payload,
                headers=self.get_headers(),
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"报告连接问题失败: {e}")
            return False 