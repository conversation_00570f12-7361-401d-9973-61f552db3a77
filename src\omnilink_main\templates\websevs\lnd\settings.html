<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 从服务器</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .settings-container {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .settings-sidebar {
            width: 250px;
            flex-shrink: 0;
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1rem;
        }
        
        .settings-sidebar ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .settings-sidebar li {
            margin-bottom: 0.5rem;
        }
        
        .settings-sidebar a {
            display: block;
            padding: 0.75rem 1rem;
            color: var(--dark-color);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: all 0.2s ease;
        }
        
        .settings-sidebar a:hover {
            background-color: var(--light-color);
        }
        
        .settings-sidebar a.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .settings-content {
            flex: 1;
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1.5rem;
        }
        
        .settings-section {
            display: none;
        }
        
        .settings-section.active {
            display: block;
        }
        
        .settings-title {
            margin-top: 0;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--light-color);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--gray-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
        }
        
        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }
        
        .form-check input {
            margin-right: 0.5rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-secondary {
            background-color: var(--gray-color);
            color: white;
        }
        
        .save-settings {
            margin-top: 1.5rem;
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: var(--border-radius);
            display: none;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .settings-card {
            background-color: var(--light-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .settings-card h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
        }
        
        .settings-card p {
            margin: 0;
            font-size: 0.9rem;
            color: var(--gray-color);
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--gray-color);
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--secondary-color);
        }
        
        input:focus + .slider {
            box-shadow: 0 0 1px var(--secondary-color);
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <header>
        <h1>系统设置</h1>
        <nav>
            <ul>
                <li><a href="{{ url_for('websevs.lnd.index') }}">首页</a></li>
                <li><a href="{{ url_for('websevs.lnd.dashboard') }}">仪表盘</a></li>
                <li><a href="{{ url_for('websevs.lnd.devices') }}">设备管理</a></li>
                <li><a href="{{ url_for('websevs.lnd.settings') }}">系统设置</a></li>
                <li><a href="{{ url_for('websevs.lnd.diagnostics') }}">系统诊断</a></li>
                <li><a href="{{ url_for('websevs.lnd.virtualhere') }}">VirtualHere</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <div class="alert alert-success" id="successAlert">设置已成功保存！</div>
        <div class="alert alert-danger" id="errorAlert">保存设置时出错，请重试！</div>
        
        <div class="settings-container">
            <div class="settings-sidebar">
                <ul>
                    <li><a href="#general" class="active" data-target="general-settings">一般设置</a></li>
                    <li><a href="#network" data-target="network-settings">网络设置</a></li>
                    <li><a href="#security" data-target="security-settings">安全设置</a></li>
                    <li><a href="#virtualhere" data-target="virtualhere-settings">VirtualHere设置</a></li>
                    <li><a href="#notifications" data-target="notification-settings">通知设置</a></li>
                    <li><a href="#backup" data-target="backup-settings">备份与恢复</a></li>
                </ul>
            </div>
            
            <div class="settings-content">
                <!-- 一般设置 -->
                <div class="settings-section active" id="general-settings">
                    <h2 class="settings-title">一般设置</h2>
                    
                    <div class="form-group">
                        <label for="serverName">从服务器名称</label>
                        <input type="text" id="serverName" class="form-control" value="工作站-从服务器1">
                    </div>
                    
                    <div class="form-group">
                        <label for="description">服务器描述</label>
                        <textarea id="description" class="form-control" rows="3">用于共享设备的从服务器，位于研发部门。</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="timezone">时区</label>
                        <select id="timezone" class="form-control">
                            <option value="Asia/Shanghai">中国标准时间 (UTC+8)</option>
                            <option value="Asia/Tokyo">日本标准时间 (UTC+9)</option>
                            <option value="America/New_York">东部标准时间 (UTC-5)</option>
                            <option value="Europe/London">格林威治标准时间 (UTC+0)</option>
                            <option value="Europe/Paris">中欧标准时间 (UTC+1)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>系统日志级别</label>
                        <select class="form-control">
                            <option value="debug">调试 (Debug)</option>
                            <option value="info" selected>信息 (Info)</option>
                            <option value="warning">警告 (Warning)</option>
                            <option value="error">错误 (Error)</option>
                        </select>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="autoStart" checked>
                        <label for="autoStart">系统启动时自动启动服务</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="autoDetect" checked>
                        <label for="autoDetect">自动检测新USB设备</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="autoShare">
                        <label for="autoShare">自动共享新检测到的设备</label>
                    </div>
                </div>
                
                <!-- 网络设置 -->
                <div class="settings-section" id="network-settings">
                    <h2 class="settings-title">网络设置</h2>
                    
                    <div class="settings-card">
                        <h4>当前网络状态</h4>
                        <p>IP地址: ************* | MAC地址: 00:1A:2B:3C:4D:5E | 连接类型: 有线连接</p>
                    </div>
                    
                    <div class="form-group">
                        <label>网络配置</label>
                        <div class="form-check">
                            <input type="radio" name="networkMode" id="dhcp" checked>
                            <label for="dhcp">自动获取IP地址 (DHCP)</label>
                        </div>
                        <div class="form-check">
                            <input type="radio" name="networkMode" id="static">
                            <label for="static">使用静态IP地址</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="ipAddress">IP地址</label>
                        <input type="text" id="ipAddress" class="form-control" placeholder="*************" disabled>
                    </div>
                    
                    <div class="form-group">
                        <label for="subnetMask">子网掩码</label>
                        <input type="text" id="subnetMask" class="form-control" placeholder="*************" disabled>
                    </div>
                    
                    <div class="form-group">
                        <label for="gateway">默认网关</label>
                        <input type="text" id="gateway" class="form-control" placeholder="***********" disabled>
                    </div>
                    
                    <div class="form-group">
                        <label for="dns">DNS服务器</label>
                        <input type="text" id="dns" class="form-control" placeholder="*******, ***************" disabled>
                    </div>
                    
                    <div class="form-group">
                        <label for="mainServer">主服务器地址</label>
                        <input type="text" id="mainServer" class="form-control" value="***********00">
                    </div>
                    
                    <div class="form-group">
                        <label for="mainServerPort">主服务器端口</label>
                        <input type="number" id="mainServerPort" class="form-control" value="8080">
                    </div>
                </div>
                
                <!-- 安全设置 -->
                <div class="settings-section" id="security-settings">
                    <h2 class="settings-title">安全设置</h2>
                    
                    <div class="form-group">
                        <label for="oldPassword">当前密码</label>
                        <input type="password" id="oldPassword" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label for="newPassword">新密码</label>
                        <input type="password" id="newPassword" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label for="confirmPassword">确认新密码</label>
                        <input type="password" id="confirmPassword" class="form-control">
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="sshAccess" checked>
                        <label for="sshAccess">允许SSH远程访问</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="firewall" checked>
                        <label for="firewall">启用防火墙</label>
                    </div>
                    
                    <div class="form-group">
                        <label>允许的IP地址</label>
                        <textarea class="form-control" rows="3" placeholder="输入允许访问的IP地址，每行一个">***********/24
********</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>自动锁定设置</label>
                        <select class="form-control">
                            <option value="never">从不</option>
                            <option value="5">5分钟不活动后</option>
                            <option value="15" selected>15分钟不活动后</option>
                            <option value="30">30分钟不活动后</option>
                            <option value="60">1小时不活动后</option>
                        </select>
                    </div>
                </div>
                
                <!-- VirtualHere设置 -->
                <div class="settings-section" id="virtualhere-settings">
                    <h2 class="settings-title">VirtualHere设置</h2>
                    
                    <div class="settings-card">
                        <h4>VirtualHere状态</h4>
                        <p>状态: 运行中 | 版本: 4.3.5 | 许可证: 已激活 (20设备)</p>
                    </div>
                    
                    <div class="form-group">
                        <label>VirtualHere服务</label>
                        <div class="form-check" style="display: flex; align-items: center; justify-content: space-between;">
                            <label>启用VirtualHere服务</label>
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="vhPort">服务端口</label>
                        <input type="number" id="vhPort" class="form-control" value="7575">
                    </div>
                    
                    <div class="form-group">
                        <label for="vhPassword">服务访问密码</label>
                        <input type="password" id="vhPassword" class="form-control" value="********">
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="vhAutoStart" checked>
                        <label for="vhAutoStart">系统启动时自动启动VirtualHere</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="vhHub" checked>
                        <label for="vhHub">向Hub服务器注册</label>
                    </div>
                    
                    <div class="form-group">
                        <label for="vhLicense">VirtualHere许可证密钥</label>
                        <input type="text" id="vhLicense" class="form-control" value="XXXX-XXXX-XXXX-XXXX-XXXX">
                    </div>
                    
                    <div class="form-group">
                        <label for="vhConfig">VirtualHere配置文件</label>
                        <textarea id="vhConfig" class="form-control" rows="5" placeholder="高级配置"># VirtualHere配置
ServerName=工作站-从服务器1
ReverseSSHServer=
HideKeyboard=1
...</textarea>
                    </div>
                </div>
                
                <!-- 通知设置 -->
                <div class="settings-section" id="notification-settings">
                    <h2 class="settings-title">通知设置</h2>
                    
                    <div class="form-check" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 1rem;">
                        <label>启用电子邮件通知</label>
                        <label class="switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label for="smtpServer">SMTP服务器</label>
                        <input type="text" id="smtpServer" class="form-control" value="smtp.example.com">
                    </div>
                    
                    <div class="form-group">
                        <label for="smtpPort">SMTP端口</label>
                        <input type="number" id="smtpPort" class="form-control" value="587">
                    </div>
                    
                    <div class="form-group">
                        <label for="smtpUser">SMTP用户名</label>
                        <input type="text" id="smtpUser" class="form-control" value="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label for="smtpPassword">SMTP密码</label>
                        <input type="password" id="smtpPassword" class="form-control" value="********">
                    </div>
                    
                    <div class="form-group">
                        <label for="notifyEmail">通知接收邮箱</label>
                        <input type="email" id="notifyEmail" class="form-control" value="<EMAIL>">
                    </div>
                    
                    <h3>通知事件</h3>
                    
                    <div class="form-check">
                        <input type="checkbox" id="notifyConnect" checked>
                        <label for="notifyConnect">新设备连接</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="notifyDisconnect" checked>
                        <label for="notifyDisconnect">设备断开连接</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="notifyError" checked>
                        <label for="notifyError">系统错误</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="notifyUpdate">
                        <label for="notifyUpdate">系统更新</label>
                    </div>
                </div>
                
                <!-- 备份与恢复 -->
                <div class="settings-section" id="backup-settings">
                    <h2 class="settings-title">备份与恢复</h2>
                    
                    <div class="settings-card">
                        <h4>上次备份</h4>
                        <p>时间: 2023-07-14 15:30 | 大小: 2.3 MB | 状态: 成功</p>
                    </div>
                    
                    <div class="form-group">
                        <label>自动备份</label>
                        <select class="form-control">
                            <option value="never">从不</option>
                            <option value="daily" selected>每天</option>
                            <option value="weekly">每周</option>
                            <option value="monthly">每月</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>备份时间</label>
                        <input type="time" class="form-control" value="03:00">
                    </div>
                    
                    <div class="form-group">
                        <label>保留备份数量</label>
                        <input type="number" class="form-control" value="7">
                    </div>
                    
                    <div class="form-group">
                        <label>备份存储位置</label>
                        <input type="text" class="form-control" value="/var/backups/virtualhere/">
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="backupConfig" checked>
                        <label for="backupConfig">系统配置</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="backupLogs" checked>
                        <label for="backupLogs">系统日志</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" id="backupVH" checked>
                        <label for="backupVH">VirtualHere配置</label>
                    </div>
                    
                    <div style="margin-top: 1.5rem;">
                        <button class="btn btn-primary">立即备份</button>
                        <button class="btn btn-secondary" style="margin-left: 0.5rem;">恢复备份</button>
                        <button class="btn btn-secondary" style="margin-left: 0.5rem;">恢复出厂设置</button>
                    </div>
                </div>
                
                <div class="save-settings">
                    <button class="btn btn-secondary">取消</button>
                    <button class="btn btn-primary" id="saveSettings">保存设置</button>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <p>&copy; 2023 Web管理服务系统 - 版本 1.0.0</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 设置导航
            const navLinks = document.querySelectorAll('.settings-sidebar a');
            const settingsSections = document.querySelectorAll('.settings-section');
            
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有活动类
                    navLinks.forEach(item => item.classList.remove('active'));
                    settingsSections.forEach(section => section.classList.remove('active'));
                    
                    // 添加活动类到当前项
                    this.classList.add('active');
                    document.getElementById(this.getAttribute('data-target')).classList.add('active');
                });
            });
            
            // 网络设置中的DHCP/静态IP切换
            const dhcpRadio = document.getElementById('dhcp');
            const staticRadio = document.getElementById('static');
            const ipFields = document.querySelectorAll('#ipAddress, #subnetMask, #gateway, #dns');
            
            dhcpRadio.addEventListener('change', function() {
                if (this.checked) {
                    ipFields.forEach(field => field.disabled = true);
                }
            });
            
            staticRadio.addEventListener('change', function() {
                if (this.checked) {
                    ipFields.forEach(field => field.disabled = false);
                }
            });
            
            // 保存设置
            const saveButton = document.getElementById('saveSettings');
            const successAlert = document.getElementById('successAlert');
            const errorAlert = document.getElementById('errorAlert');
            
            saveButton.addEventListener('click', function() {
                // 模拟保存操作
                const saveSuccess = Math.random() > 0.2; // 80%的成功率
                
                if (saveSuccess) {
                    // 显示成功消息
                    successAlert.style.display = 'block';
                    errorAlert.style.display = 'none';
                    
                    setTimeout(() => {
                        successAlert.style.display = 'none';
                    }, 3000);
                } else {
                    // 显示错误消息
                    errorAlert.style.display = 'block';
                    successAlert.style.display = 'none';
                    
                    setTimeout(() => {
                        errorAlert.style.display = 'none';
                    }, 3000);
                }
            });
        });
    </script>
</body>
</html> 