"""
迁移服务测试

测试迁移服务的基本功能，包括迁移流程管理、状态监控和回滚处理。
"""

import os
import unittest
import tempfile
import shutil
import json
import logging
import time
import socket
from datetime import datetime
from unittest.mock import patch, Mock

from ..migration_service import MigrationService, MigrationError, ServerCommunicator

# 禁用测试日志
logging.disable(logging.CRITICAL)

class TestMigrationService(unittest.TestCase):
    """迁移服务测试用例"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建测试配置
        self.test_config = {
            'ssl_verify': False,
            'timeout': 5,
            'requirements': {
                'os': [('Linux', '4.4'), ('Windows', '10')],
                'min_cpu_cores': 2,
                'min_memory': 2048
            }
        }
        
        # 创建测试服务器信息
        self.source_server = {
            'id': 'source_server',
            'name': 'Source Server',
            'host': '*************',
            'port': 22,
            'api_url': 'https://*************:8443/api',
            'auth_type': 'basic',
            'username': 'test',
            'password': 'test123'
        }
        
        self.target_server = {
            'id': 'target_server',
            'name': 'Target Server',
            'host': '*************',
            'port': 22,
            'api_url': 'https://*************:8443/api',
            'auth_type': 'basic',
            'username': 'test',
            'password': 'test123'
        }
        
        # 创建迁移服务
        self.migration_service = MigrationService(
            data_dir=self.temp_dir,
            config=self.test_config
        )
        
        # 修补服务器通信组件
        self._patch_server_communicator()
    
    def tearDown(self):
        """测试后清理"""
        # 停止所有迁移
        self._stop_all_migrations()
        
        # 删除临时目录
        shutil.rmtree(self.temp_dir)
    
    def _patch_server_communicator(self):
        """修补服务器通信组件"""
        # 修补测试连接方法
        self.migration_service.server_communicator.test_connection = Mock(return_value=(True, None))
        
        # 修补获取服务器信息方法
        self.migration_service.server_communicator.get_server_info = Mock(
            return_value=(True, {
                'system': {
                    'os_type': 'Linux',
                    'os_version': '4.4',
                    'cpu_cores': 4,
                    'memory_total': 8192
                },
                'storage': {
                    'data_size': 1024 * 1024 * 1024,  # 1GB
                    'available_space': 1024 * 1024 * 1024 * 10  # 10GB
                }
            })
        )
        
        # 修补启动服务方法
        self.migration_service.server_communicator.start_service = Mock(return_value=(True, None))
        
        # 修补停止服务方法
        self.migration_service.server_communicator.stop_service = Mock(return_value=(True, None))
        
        # 修补获取服务状态方法
        self.migration_service.server_communicator.get_service_status = Mock(
            return_value=(True, {
                'status': 'running',
                'uptime': 3600,
                'memory_usage': 512
            })
        )
    
    def _stop_all_migrations(self):
        """停止所有迁移"""
        if self.migration_service.active_migration:
            self.migration_service.stop_migration(force=True)
    
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.migration_service)
        self.assertEqual(self.migration_service.data_dir, self.temp_dir)
        self.assertEqual(self.migration_service.config, self.test_config)
        self.assertIsNone(self.migration_service.active_migration)
        self.assertEqual(len(self.migration_service.migration_history), 0)
    
    def test_start_migration(self):
        """测试开始迁移"""
        # 开始迁移
        migration_id = self.migration_service.start_migration(
            self.source_server,
            self.target_server
        )
        
        # 验证结果
        self.assertIsNotNone(migration_id)
        self.assertIsNotNone(self.migration_service.active_migration)
        
        # 确保active_migration不为None
        if self.migration_service.active_migration:
            self.assertEqual(self.migration_service.active_migration['id'], migration_id)
            self.assertEqual(self.migration_service.active_migration['source'], self.source_server)
            self.assertEqual(self.migration_service.active_migration['target'], self.target_server)
            self.assertEqual(self.migration_service.active_migration['state'], 'initialized')
            self.assertEqual(self.migration_service.active_migration['progress'], 0)
        
        # 验证历史记录
        self.assertEqual(len(self.migration_service.migration_history), 1)
        self.assertEqual(self.migration_service.migration_history[0]['id'], migration_id)
    
    def test_start_multiple_migrations(self):
        """测试开始多个迁移"""
        # 开始第一个迁移
        migration_id1 = self.migration_service.start_migration(
            self.source_server,
            self.target_server
        )
        
        # 验证第一个迁移
        self.assertIsNotNone(migration_id1)
        self.assertIsNotNone(self.migration_service.active_migration)
        
        # 尝试开始第二个迁移，应该失败
        with self.assertRaises(MigrationError):
            self.migration_service.start_migration(
                self.source_server,
                self.target_server
            )
    
    def test_get_migration_status(self):
        """测试获取迁移状态"""
        # 开始迁移
        migration_id = self.migration_service.start_migration(
            self.source_server,
            self.target_server
        )
        
        # 获取状态
        status = self.migration_service.get_migration_status()
        
        # 验证状态
        self.assertIsNotNone(status)
        if status:
            self.assertEqual(status['id'], migration_id)
            self.assertEqual(status['state'], 'initialized')
        
        # 获取指定ID的状态
        status2 = self.migration_service.get_migration_status(migration_id)
        
        # 验证状态
        self.assertIsNotNone(status2)
        if status2:
            self.assertEqual(status2['id'], migration_id)
    
    def test_stop_migration(self):
        """测试停止迁移"""
        # 开始迁移
        migration_id = self.migration_service.start_migration(
            self.source_server,
            self.target_server
        )
        
        # 停止迁移
        result = self.migration_service.stop_migration()
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证停止标志
        self.assertTrue(self.migration_service._stop_flag.is_set())
    
    def test_add_migration_log(self):
        """测试添加迁移日志"""
        # 开始迁移
        migration_id = self.migration_service.start_migration(
            self.source_server,
            self.target_server
        )
        
        # 添加日志
        self.migration_service._add_migration_log("测试日志", 'info')
        
        # 验证日志
        self.assertIsNotNone(self.migration_service.active_migration)
        if self.migration_service.active_migration:
            self.assertEqual(len(self.migration_service.active_migration['logs']), 2)  # 初始化日志+测试日志
            self.assertEqual(self.migration_service.active_migration['logs'][1]['message'], "测试日志")
            self.assertEqual(self.migration_service.active_migration['logs'][1]['level'], "info")
    
    def test_save_checkpoint(self):
        """测试保存检查点"""
        # 开始迁移
        migration_id = self.migration_service.start_migration(
            self.source_server,
            self.target_server
        )
        
        # 保存检查点
        checkpoint_data = {'key': 'value'}
        self.migration_service._save_checkpoint('test_checkpoint', checkpoint_data)
        
        # 验证检查点
        self.assertIsNotNone(self.migration_service.active_migration)
        if self.migration_service.active_migration:
            self.assertIn('test_checkpoint', self.migration_service.active_migration['checkpoints'])
            self.assertEqual(
                self.migration_service.active_migration['checkpoints']['test_checkpoint']['data'],
                checkpoint_data
            )
    
    def test_get_checkpoint(self):
        """测试获取检查点"""
        # 开始迁移
        migration_id = self.migration_service.start_migration(
            self.source_server,
            self.target_server
        )
        
        # 保存检查点
        checkpoint_data = {'key': 'value'}
        self.migration_service._save_checkpoint('test_checkpoint', checkpoint_data)
        
        # 获取检查点
        data = self.migration_service._get_checkpoint('test_checkpoint')
        
        # 验证数据
        self.assertEqual(data, checkpoint_data)
        
        # 获取不存在的检查点
        data2 = self.migration_service._get_checkpoint('nonexistent')
        
        # 验证数据
        self.assertIsNone(data2)
    
    def test_update_migration_state(self):
        """测试更新迁移状态"""
        # 开始迁移
        migration_id = self.migration_service.start_migration(
            self.source_server,
            self.target_server
        )
        
        # 更新状态
        self.migration_service._update_migration_state('preparing')
        
        # 验证状态
        self.assertIsNotNone(self.migration_service.active_migration)
        if self.migration_service.active_migration:
            self.assertEqual(self.migration_service.active_migration['state'], 'preparing')
        
        # 更新为失败状态
        self.migration_service._update_migration_state('failed', '测试错误')
        
        # 验证状态
        self.assertIsNone(self.migration_service.active_migration)  # 失败后应该清除活动迁移
        
        # 验证历史记录
        self.assertEqual(self.migration_service.migration_history[0]['state'], 'failed')
        self.assertIsNotNone(self.migration_service.migration_history[0]['completed_at'])
    
    def test_check_server_connection(self):
        """测试检查服务器连接"""
        # 修改模拟返回值
        self.migration_service.server_communicator.test_connection = Mock(return_value=(True, None))
        
        # 检查连接
        result = self.migration_service._check_server_connection(self.source_server)
        
        # 验证结果
        self.assertTrue(result)
        
        # 修改模拟返回值
        self.migration_service.server_communicator.test_connection = Mock(return_value=(False, "连接失败"))
        
        # 检查连接
        result = self.migration_service._check_server_connection(self.source_server)
        
        # 验证结果
        self.assertFalse(result)
    
    def test_check_server_requirements(self):
        """测试检查服务器要求"""
        # 模拟服务器信息
        self.migration_service.server_communicator.get_server_info = Mock(
            return_value=(True, {
                'system': {
                    'os_type': 'Linux',
                    'os_version': '4.4',
                    'cpu_cores': 4,
                    'memory_total': 8192
                }
            })
        )
        
        # 检查要求
        result = self.migration_service._check_server_requirements(self.target_server)
        
        # 验证结果
        self.assertTrue(result)
        
        # 模拟不满足CPU要求
        self.migration_service.server_communicator.get_server_info = Mock(
            return_value=(True, {
                'system': {
                    'os_type': 'Linux',
                    'os_version': '4.4',
                    'cpu_cores': 1,  # 不满足最低要求
                    'memory_total': 8192
                }
            })
        )
        
        # 检查要求
        result = self.migration_service._check_server_requirements(self.target_server)
        
        # 验证结果
        self.assertFalse(result)
    
    def test_migration_process(self):
        """测试迁移处理流程"""
        # 模拟准备方法
        self.migration_service._prepare_migration = Mock()
        self.migration_service._export_data = Mock()
        self.migration_service._transfer_data = Mock()
        self.migration_service._import_data = Mock()
        self.migration_service._validate_migration = Mock()
        self.migration_service._switch_services = Mock()
        self.migration_service._finalize_migration = Mock()
        
        # 开始迁移
        migration_id = self.migration_service.start_migration(
            self.source_server,
            self.target_server
        )
        
        # 让迁移线程运行一段时间
        time.sleep(1)
        
        # 验证方法调用
        self.migration_service._prepare_migration.assert_called()
        self.migration_service._export_data.assert_called()
        self.migration_service._transfer_data.assert_called()
        self.migration_service._import_data.assert_called()
        self.migration_service._validate_migration.assert_called()
        self.migration_service._switch_services.assert_called()
        self.migration_service._finalize_migration.assert_called()


class TestServerCommunicator(unittest.TestCase):
    """服务器通信组件测试用例"""
    
    def setUp(self):
        """测试前准备"""
        # 创建通信组件
        self.communicator = ServerCommunicator(ssl_verify=False, timeout=1)
        
        # 创建测试服务器信息
        self.server_info = {
            'id': 'test_server',
            'name': 'Test Server',
            'host': '127.0.0.1',
            'port': 22,
            'api_url': 'https://127.0.0.1:8443/api',
            'auth_type': 'basic',
            'username': 'test',
            'password': 'test123'
        }
    
    @patch('socket.socket')
    def test_test_connection(self, mock_socket):
        """测试连接测试功能"""
        # 模拟套接字连接成功
        mock_socket_instance = Mock()
        mock_socket.return_value = mock_socket_instance
        
        # 测试连接
        result, error = self.communicator.test_connection(self.server_info)
        
        # 验证结果
        self.assertTrue(result)
        self.assertIsNone(error)
        
        # 验证套接字方法调用
        mock_socket_instance.connect.assert_called_with((self.server_info['host'], self.server_info['port']))
        mock_socket_instance.close.assert_called()
    
    @patch('socket.socket')
    def test_test_connection_failure(self, mock_socket):
        """测试连接失败"""
        # 模拟套接字连接失败
        mock_socket_instance = Mock()
        mock_socket_instance.connect.side_effect = socket.error("连接拒绝")
        mock_socket.return_value = mock_socket_instance
        
        # 测试连接
        result, error = self.communicator.test_connection(self.server_info)
        
        # 验证结果
        self.assertFalse(result)
        self.assertIsNotNone(error)
    
    def test_get_auth_header_none(self):
        """测试获取无认证头部"""
        # 创建无认证服务器信息
        server_info = {
            'id': 'test_server',
            'auth_type': 'none'
        }
        
        # 获取认证头部
        headers = self.communicator._get_auth_header(server_info)
        
        # 验证结果
        self.assertIsNone(headers)
    
    def test_get_auth_header_basic(self):
        """测试获取基本认证头部"""
        # 创建基本认证服务器信息
        server_info = {
            'id': 'test_server',
            'auth_type': 'basic',
            'username': 'test',
            'password': 'test123'
        }
        
        # 获取认证头部
        headers = self.communicator._get_auth_header(server_info)
        
        # 验证结果
        self.assertIsNotNone(headers)
        if headers:
            self.assertIn('Authorization', headers)
            self.assertTrue(headers['Authorization'].startswith('Basic '))
    
    def test_get_auth_header_token(self):
        """测试获取令牌认证头部"""
        # 创建令牌认证服务器信息
        server_info = {
            'id': 'test_server',
            'auth_type': 'token',
            'token': 'abc123'
        }
        
        # 获取认证头部
        headers = self.communicator._get_auth_header(server_info)
        
        # 验证结果
        self.assertIsNotNone(headers)
        if headers:
            self.assertIn('Authorization', headers)
            self.assertTrue(headers['Authorization'].startswith('Bearer '))


if __name__ == '__main__':
    unittest.main() 
