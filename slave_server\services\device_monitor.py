import asyncio
import logging
import re
from typing import Dict, Any, Callable, Optional, Set

from slave_server.core.config import settings
from slave_server.services.virtualhere_api_client import VirtualHereApiClient

logger = logging.getLogger(__name__)

class DeviceMonitorService:
    """
    Monitors device connection and disconnection events by polling the
    VirtualHere TCP API. This approach is platform-agnostic.
    """

    def __init__(self):
        """Initializes the device monitor service."""
        self._devices: Dict[str, Dict[str, Any]] = {} # Keyed by device_path
        self.known_device_paths: Set[str] = set()
        self.initialized: bool = False
        self.on_device_added: Optional[Callable[[Dict], None]] = None
        self.on_device_removed: Optional[Callable[[Dict], None]] = None
        self._polling_task: Optional[asyncio.Task] = None
        self.api_client = VirtualHereApiClient(
            host=settings.VIRTUALHERE_HOST, 
            port=settings.VIRTUALHERE_PORT
        )

    def start(self):
        """Starts the device monitoring service."""
        logger.info(f"Starting device monitor polling...")
        self._polling_task = asyncio.create_task(self._poll_devices())
        logger.info("Device monitor started.")

    def stop(self):
        """Stops the device monitoring service."""
        logger.info("Stopping device monitor...")
        if self._polling_task:
            self._polling_task.cancel()
        logger.info("Device monitor stopped.")
        
    async def _poll_devices(self):
        """Periodically checks for device changes."""
        logger.info(f"Device polling started. Interval: {settings.DEVICE_POLL_INTERVAL} seconds.")
        while True:
            try:
                await self._check_devices()
            except asyncio.CancelledError:
                logger.info("Device polling task cancelled.")
                break
            except Exception as e:
                logger.error(f"An unexpected error occurred during device polling: {e}", exc_info=True)
            
            await asyncio.sleep(settings.DEVICE_POLL_INTERVAL)

    async def _check_devices(self):
        """Checks for new or removed devices by querying VirtualHere API."""
        raw_output = await self._discover_devices_via_api()
        # If the API call fails, raw_output can be empty. Don't proceed.
        if not raw_output:
            logger.debug("Received no device data from VirtualHere API, skipping check.")
            # If we were initialized and now we get no data, it could mean the VH server went down.
            # We should clear existing devices.
            if self.initialized and self.known_device_paths:
                 logger.warning("VirtualHere API is unreachable or has no devices. Clearing all known devices.")
                 removed_ids = list(self.known_device_paths)
                 for removed_id in removed_ids:
                     self._remove_device(removed_id)
                 self.known_device_paths.clear()

            return

        current_devices_list = self._parse_vh_list_output(raw_output)
        
        current_devices = {dev['device_path']: dev for dev in current_devices_list}
        current_device_paths = set(current_devices.keys())
        
        if not self.initialized:
            logger.info("First device scan completed.")
            self.known_device_paths = current_device_paths
            self._devices = current_devices
            self.initialized = True
            # Trigger 'add' for all initial devices
            for dev_path in self.known_device_paths:
                self._add_device(dev_path, current_devices.get(dev_path))
            return

        new_device_paths = current_device_paths - self.known_device_paths
        removed_device_paths = self.known_device_paths - current_device_paths

        if new_device_paths or removed_device_paths:
            self._devices = current_devices

        for new_path in new_device_paths:
            self._add_device(new_path, self.get_device_info(new_path))

        for removed_path in removed_device_paths:
            self._remove_device(removed_path)

        self.known_device_paths = current_device_paths

    def _parse_vh_list_output(self, vh_output: str) -> list[dict[str, Any]]:
        """
        Parses the text output from 'VirtualHere -c "LIST"' into a list of device dicts.
        
        Example Input:
        -------------------
        Auto-Find: Found.
        
        USB Hub(1):
          (In-use by: You) Device (Kingston DataTraveler 3.0, Address: HUB_3.2)
        USB Hub(2):
          Device (SanDisk Cruzer Blade, Address: HUB_4.1, VID:PID=0781:5567)
        -------------------
        """
        devices = []
        # Regex to capture device name, address, and optional VID:PID
        device_regex = re.compile(r"Device \((.*?),\s*Address:\s*([\w\._-]+)(?:,\s*VID:PID=([0-9a-fA-F]{4}:[0-9a-fA-F]{4}))?\)")
        
        for line in vh_output.splitlines():
            match = device_regex.search(line.strip())
            if match:
                model, address, vid_pid_str = match.groups()
                vid, pid = (vid_pid_str.split(':') if vid_pid_str else (None, None))
                
                device_info = {
                    "device_path": address,  # Authoritative unique ID from VH
                    "model": model.strip(),
                    "vendor_id": vid,
                    "product_id": pid,
                    "manufacturer": None, # VH list doesn't provide this directly
                    "description": model.strip(), # Use model as description
                    "status": "Available",
                }
                devices.append(device_info)
        return devices

    async def _discover_devices_via_api(self) -> str:
        """
        Discovers connected USB devices by connecting to the VirtualHere TCP API.
        Returns the raw string output from the 'LIST' command.
        """
        try:
            if not await self.api_client.connect():
                logger.warning("Could not connect to VirtualHere API service. Is it running?")
                return ""
            
            raw_list = await self.api_client.list_devices_raw()
            return raw_list if raw_list is not None else ""
        
        except Exception as e:
            logger.error(f"An exception occurred during VirtualHere API discovery: {e}", exc_info=True)
            return ""
        finally:
            if self.api_client:
                await self.api_client.disconnect()

    def _add_device(self, device_path: str, device_info: Optional[dict]):
        if not device_info:
            logger.warning(f"Attempted to add device with path '{device_path}' but info was missing.")
            return

        if device_path not in self._devices:
            self._devices[device_path] = device_info
            logger.info(f"Device added: {device_info.get('model')} (Path: {device_path})")
            if self.on_device_added:
                self.on_device_added(device_info)

    def _remove_device(self, device_path: str):
        removed_device = self._devices.pop(device_path, None)
        if removed_device:
            logger.info(f"Device removed: {removed_device.get('model')} (Path: {device_path})")
            if self.on_device_removed:
                self.on_device_removed(removed_device)
        return removed_device

    def get_device_info(self, device_path: str) -> Optional[Dict[str, Any]]:
        """
        Retrieves formatted information for a specific device from the internal cache.
        """
        return self._devices.get(device_path)

    def get_devices(self) -> Dict[str, Dict]:
        """
        Returns the dictionary of currently connected and tracked devices.
        This is an alias for get_all_devices for backward compatibility.
        """
        return self._devices

    def get_all_devices(self) -> Dict[str, Dict]:
        """Returns the dictionary of currently connected and tracked devices."""
        return self._devices 