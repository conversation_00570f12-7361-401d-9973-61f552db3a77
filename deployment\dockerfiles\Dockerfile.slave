# Dockerfile for the Slave Server (v2, using python:3.11-slim base)
# This Dockerfile packages the slave server application along with the VirtualHere server.

# Use the existing Python 3.11 slim image
FROM python:3.11-slim

# Set the working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    unzip \
    libudev-dev \
    libusb-1.0-0-dev \
    udev \
    && rm -rf /var/lib/apt/lists/*

# Download and install VirtualHere Server for Linux
RUN wget -O /app/vhusbdx86_64 https://www.virtualhere.com/sites/default/files/usbserver/vhusbdx86_64 \
    && chmod +x /app/vhusbdx86_64

# Create VirtualHere configuration file
RUN echo "ServerName=OmniLink-Slave" > /app/config.ini \
    && echo "AutoFind=1" >> /app/config.ini \
    && echo "ClientTimeout=60" >> /app/config.ini

# Copy requirements file
COPY slave_server/requirements.txt /app/requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY common/ /app/common/
COPY slave_server/ /app/slave_server/
COPY config/ /app/config/

# Create log directory
RUN mkdir -p /var/log

# Create startup script
RUN echo '#!/bin/bash\n\
# Start VirtualHere server\n\
/app/vhusbdx86_64 -b -c /app/config.ini &\n\
\n\
# Wait for VirtualHere to start\n\
sleep 5\n\
\n\
# Start slave server\n\
cd /app\n\
python -m slave_server.main\n\
' > /app/start_slave.sh && chmod +x /app/start_slave.sh

# Expose ports
EXPOSE 8001 7575

# Set environment variables
ENV PYTHONPATH=/app
ENV LOG_LEVEL=INFO

# Start command
CMD ["/app/start_slave.sh"] 