#!/bin/bash
# Start script for the Slave Server container

# Exit immediately if a command exits with a non-zero status.
set -e

# Start the VirtualHere server in the background
# The -b flag runs it as a daemon.
echo "Starting VirtualHere server in background..."
./vhusbdx86_64 -b
echo "VirtualHere server started."

# Wait a couple of seconds to ensure VirtualHere is fully up and running
sleep 2

# Set the PYTHONPATH to include the project's source directories
export PYTHONPATH="/app:/app/src:/app/common"

# Start the slave server application using python
echo "Starting OmniLink Slave Server..."
python3.11 /app/slave_server/main.py 