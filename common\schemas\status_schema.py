from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class StatusResponse(BaseModel):
    status: str = "ok"

class DeviceStatus(BaseModel):
    """Represents the status of a single physical USB device."""
    vendor_id: Optional[str]
    product_id: Optional[str]
    manufacturer: Optional[str]
    model: Optional[str]
    device_path: Optional[str] = Field(None, description="The unique device path provided by the OS, if available.")

class VHInstanceStatus(BaseModel):
    """Represents the status of a single VirtualHere instance."""
    device_id: str = Field(..., description="The internal ID used to track the device being shared.")
    port: int
    pid: int

class SlaveStatusReport(BaseModel):
    """The complete status report sent by the slave server."""
    slave_id: str # This would be a unique identifier for the slave server itself
    devices: List[DeviceStatus]
    vh_instances: List[VHInstanceStatus]

class CPUMetrics(BaseModel):
    percent: float 