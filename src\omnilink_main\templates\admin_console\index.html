<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主从服务器系统 - 统一管理控制台</title>
    <!-- Material Design Web Components -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Material+Icons&display=block" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet" />
    <!-- Material Design 3 Styles -->
    <link href="{{ url_for('static', filename='css/material-components-web.min.css') }}" rel="stylesheet">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin_material.css') }}">
    <!-- Chart.js 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body class="mdc-typography">
    <div class="app-container">
        <!-- 顶部应用栏 -->
        <header class="mdc-top-app-bar mdc-top-app-bar--fixed">
            <div class="mdc-top-app-bar__row">
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-start">
                    <button class="material-icons mdc-top-app-bar__navigation-icon mdc-icon-button" id="menu-button">menu</button>
                    <span class="mdc-top-app-bar__title">主从服务器管理系统</span>
                </section>
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-end" role="toolbar">
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="refresh-button" aria-label="刷新">refresh</button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="theme-toggle" aria-label="切换主题">dark_mode</button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="user-menu-button" aria-label="用户菜单">account_circle</button>
                    
                    <!-- 用户菜单 -->
                    <div class="mdc-menu mdc-menu-surface" id="user-menu">
                        <ul class="mdc-list" role="menu" aria-hidden="true" aria-orientation="vertical" tabindex="-1">
                            <li class="mdc-list-item" role="menuitem">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="mdc-list-item__text">个人设置</span>
                            </li>
                            <li class="mdc-list-divider" role="separator"></li>
                            <li class="mdc-list-item" role="menuitem" id="logout-button" data-logout-url="/auth/logout">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="mdc-list-item__text">退出登录</span>
                            </li>
                        </ul>
                    </div>
                </section>
            </div>
        </header>

        <!-- 主内容区域 -->
        <div class="app-content mdc-top-app-bar--fixed-adjust">
            <div class="mdc-drawer-app-content">
                <!-- 侧边导航抽屉 -->
                <aside class="mdc-drawer mdc-drawer--dismissible">
                    <div class="mdc-drawer__content">
                        <nav class="mdc-list">
                            <a class="mdc-list-item mdc-list-item--activated" href="{{ url_for('admin_console.index') }}" aria-current="page">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">dashboard</span>
                                <span class="mdc-list-item__text">仪表盘</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_console.group_management') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">dns</span>
                                <span class="mdc-list-item__text">分组管理</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_console.migration_wizard') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">swap_horiz</span>
                                <span class="mdc-list-item__text">迁移向导</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_console.alerts') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">notifications</span>
                                <span class="mdc-list-item__text">告警设置</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_console.health_monitor') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">favorite</span>
                                <span class="mdc-list-item__text">健康监控</span>
                            </a>
                            
                            <hr class="mdc-list-divider">
                            <h6 class="mdc-list-group__subheader">系统</h6>
                            
                            <a class="mdc-list-item" href="{{ url_for('admin_console.system_settings') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">settings</span>
                                <span class="mdc-list-item__text">系统设置</span>
                            </a>
                        </nav>
                    </div>
                </aside>

                <!-- 主内容 -->
                <main class="main-content">
                    <div class="page-content">
                        <h1 class="mdc-typography--headline4">系统仪表盘</h1>
                        
                        <!-- 状态卡片 -->
                        <div class="dashboard-cards">
                            <div class="mdc-card dashboard-card server-card">
                                <div class="mdc-card__primary-action">
                                    <div class="card-content">
                                        <div class="card-icon-container">
                                            <span class="material-icons card-icon">dns</span>
                                        </div>
                                        <div class="card-data">
                                            <h2 class="mdc-typography--headline5">从服务器</h2>
                                            <div class="card-numbers">
                                                <span class="primary-value" id="online-slave-servers-card">-</span>
                                                <div class="secondary-value">
                                                    <span id="slaveOnline_secondary">-</span> 在线
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mdc-card dashboard-card device-card">
                                <div class="mdc-card__primary-action">
                                    <div class="card-content">
                                        <div class="card-icon-container">
                                            <span class="material-icons card-icon">usb</span>
                                        </div>
                                        <div class="card-data">
                                            <h2 class="mdc-typography--headline5">USB设备</h2>
                                            <div class="card-numbers">
                                                <span class="primary-value" id="connected-devices-card">-</span>
                                                <div class="secondary-value">
                                                    <span id="deviceActive_secondary">-</span> 活跃
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mdc-card dashboard-card user-card">
                                <div class="mdc-card__primary-action">
                                    <div class="card-content">
                                        <div class="card-icon-container">
                                            <span class="material-icons card-icon">group</span>
                                        </div>
                                        <div class="card-data">
                                            <h2 class="mdc-typography--headline5">用户</h2>
                                            <div class="card-numbers">
                                                <span class="primary-value" id="active-users-card">-</span>
                                                <div class="secondary-value">
                                                    <span id="userActive_secondary">-</span> 活跃
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mdc-card dashboard-card alert-card">
                                <div class="mdc-card__primary-action">
                                    <div class="card-content">
                                        <div class="card-icon-container">
                                            <span class="material-icons card-icon">notifications</span>
                                        </div>
                                        <div class="card-data">
                                            <h2 class="mdc-typography--headline5">告警</h2>
                                            <div class="card-numbers">
                                                <span class="primary-value" id="critical-alerts-card">-</span>
                                                <div class="secondary-value">
                                                    <span id="criticalAlerts_secondary">-</span> 严重
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- NEW: Real-time Resource Cards -->
                             <div class="mdc-card dashboard-card resource-card cpu-card">
                                <div class="mdc-card__primary-action">
                                    <div class="card-content">
                                        <div class="card-icon-container">
                                            <span class="material-icons card-icon">memory</span> <!-- Icon representing CPU -->
                                        </div>
                                        <div class="card-data">
                                            <h2 class="mdc-typography--headline6">CPU 使用率</h2>
                                            <div class="card-numbers">
                                                <span class="primary-value" id="cpuUsage">-%</span>
                                                 <div class="secondary-value" id="cpuLoadAvg">负载: -</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mdc-card dashboard-card resource-card memory-card">
                                <div class="mdc-card__primary-action">
                                    <div class="card-content">
                                        <div class="card-icon-container">
                                            <span class="material-symbols-outlined card-icon">memory_alt</span>
                                        </div>
                                        <div class="card-data">
                                            <h2 class="mdc-typography--headline6">内存使用率</h2>
                                            <div class="card-numbers">
                                                <span class="primary-value" id="memoryUsage">-%</span>
                                                <div class="secondary-value" id="memoryAbsolute">总量: - GB</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                             <div class="mdc-card dashboard-card resource-card network-card">
                                <div class="mdc-card__primary-action">
                                    <div class="card-content">
                                        <div class="card-icon-container">
                                            <span class="material-icons card-icon">network_check</span>
                                        </div>
                                        <div class="card-data">
                                            <h2 class="mdc-typography--headline6">网络状态</h2>
                                            <div class="card-numbers network-stats">
                                                 <span class="primary-value" id="networkLatency">延迟: - ms</span>
                                                 <div class="secondary-value">
                                                      <span title="发送速率" id="networkTxRate">↑ - KB/s</span> |
                                                      <span title="接收速率" id="networkRxRate">↓ - KB/s</span>
                                                 </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Add Disk Usage Card if needed -->

                        </div>

                        <!-- 系统状态图表 -->
                        <div class="dashboard-charts">
                            <div class="mdc-card chart-card">
                                <div class="mdc-card__primary-action">
                                    <div class="chart-header">
                                        <h2 class="mdc-typography--headline6">
                                            <span class="material-icons">show_chart</span>
                                            服务器健康状态趋势
                                        </h2>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="healthTrendChart"></canvas>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mdc-card chart-card">
                                <div class="mdc-card__primary-action">
                                    <div class="chart-header">
                                        <h2 class="mdc-typography--headline6">
                                            <span class="material-icons">pie_chart</span>
                                            设备连接状态
                                        </h2>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="deviceStatusChart"></canvas>
                                    </div>
                                </div>
                            </div>

                             <!-- NEW: Network Usage Chart -->
                             <div class="mdc-card chart-card">
                                <div class="mdc-card__primary-action">
                                    <div class="chart-header">                                        <h2 class="mdc-typography--headline6">                                            <span class="material-icons">swap_vert</span>                                            网络流量趋势 (Tx/Rx)                                        </h2>                                    </div>
                                    <div class="chart-container">                                        <canvas id="networkUsageChart"></canvas>                                    </div>                                </div>
                            </div>
                            <!-- NEW: Resource Usage Charts (Optional - could combine with health) -->
                             <div class="mdc-card chart-card">                                <div class="mdc-card__primary-action">                                    <div class="chart-header">                                        <h2 class="mdc-typography--headline6">                                            <span class="material-icons">insights</span>                                            资源使用率 (CPU/内存)                                        </h2>                                    </div>                                    <div class="chart-container">                                        <canvas id="resourceUsageChart"></canvas>                                    </div>                                </div>                            </div>

                        </div>

                        <!-- 最近事件和告警 -->
                        <div class="dashboard-tables">
                            <div class="mdc-card table-card">
                                <div class="mdc-card__primary-action">
                                    <div class="table-header">
                                        <h2 class="mdc-typography--headline6">
                                            <span class="material-icons">history</span>
                                            最近系统事件
                                        </h2>
                                    </div>
                                    <div class="table-container">
                                        <div class="mdc-layout-grid">
                                            <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-6-desktop mdc-layout-grid__cell--span-12-tablet">
                                                <div class="mdc-card dashboard-card">
                                                    <div class="mdc-card__content">
                                                        <h6 class="mdc-typography--subtitle1">最近事件</h6>
                                                        <ul class="mdc-list mdc-list--two-line" id="recentEventsList">
                                                            <!-- 事件将通过JS加载 -->
                                                            <li class="mdc-list-item"><span class="mdc-list-item__text">加载中...</span></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-6-desktop mdc-layout-grid__cell--span-12-tablet">
                                                <div class="mdc-card dashboard-card">
                                                    <div class="mdc-card__content">
                                                        <h6 class="mdc-typography--subtitle1">活动告警</h6>
                                                        <ul class="mdc-list mdc-list--two-line" id="activeAlertsList">
                                                            <!-- 告警将通过JS加载 -->
                                                            <li class="mdc-list-item"><span class="mdc-list-item__text">加载中...</span></li>
                                                        </ul>
                                    </div>
                                </div>
                            </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Material Design Web Components JS -->
    <script src="{{ url_for('static', filename='js/material-components-web.min.js') }}"></script>
    <!-- WebSocket客户端 -->
    <script src="/static/js/websocket_client.js"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/admin_material.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize MDC components
            const topAppBar = new mdc.topAppBar.MDCTopAppBar(document.querySelector('.mdc-top-app-bar'));
            const drawer = new mdc.drawer.MDCDrawer(document.querySelector('.mdc-drawer'));
            document.querySelector('#menu-button').addEventListener('click', () => { drawer.open = !drawer.open; });
            const userMenu = new mdc.menu.MDCMenu(document.querySelector('#user-menu'));
            document.querySelector('#user-menu-button').addEventListener('click', () => { userMenu.open = !userMenu.open; });
            document.querySelector('#logout-button').addEventListener('click', () => { window.location.href = "{{ url_for('auth.logout') }}"; });

            // --- Start of New/Refactored Dashboard Logic ---

            // Helper function to update text content of an element
            function updateElementText(id, value, defaultValue = '-') {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = (value !== null && value !== undefined && value !== '') ? String(value) : defaultValue;
                } else {
                    // console.warn(`Element with ID ${id} not found for value: ${value}`);
                }
            }
            
            // Function to update summary cards
            function updateSummaryCards(summaryData) {
                document.getElementById('active-users-card').textContent = summaryData.active_users !== undefined ? summaryData.active_users : 'N/A';
                document.getElementById('connected-devices-card').textContent = summaryData.connected_devices !== undefined ? summaryData.connected_devices : 'N/A';
                document.getElementById('online-slave-servers-card').textContent = summaryData.online_slave_servers !== undefined ? summaryData.online_slave_servers : 'N/A';
                document.getElementById('critical-alerts-card').textContent = summaryData.critical_alerts !== undefined ? summaryData.critical_alerts : 'N/A';

                // Clear or update secondary values as needed - for now, they might show old/stale data or be cleared.
                // document.getElementById('slaveOnline_secondary').textContent = '-'; 
                // document.getElementById('deviceActive_secondary').textContent = '-';
                // document.getElementById('userActive_secondary').textContent = '-';
                // document.getElementById('criticalAlerts_secondary').textContent = '-';
                
                // For new resource cards (CPU, Memory, Network) - Assuming API might provide these keys
                document.getElementById('cpuUsage').textContent = summaryData.cpu_usage_percent !== undefined ? `${summaryData.cpu_usage_percent.toFixed(1)}%` : 'N/A';
                document.getElementById('cpuLoadAvg').textContent = summaryData.cpu_load_average && Array.isArray(summaryData.cpu_load_average) ? `负载: ${summaryData.cpu_load_average.join(', ')}`: '负载: N/A';
                document.getElementById('memoryUsage').textContent = summaryData.memory_usage_percent !== undefined ? `${summaryData.memory_usage_percent.toFixed(1)}%` : 'N/A';
                document.getElementById('memoryAbsolute').textContent = summaryData.total_memory_gb !== undefined ? `总量: ${summaryData.total_memory_gb.toFixed(1)} GB` : '总量: N/A';
                document.getElementById('networkLatency').textContent = summaryData.network_latency_ms !== undefined ? `延迟: ${summaryData.network_latency_ms} ms` : '延迟: N/A';
                document.getElementById('networkTxRate').textContent = summaryData.network_tx_kbps !== undefined ? `↑ ${summaryData.network_tx_kbps.toFixed(1)} KB/s` : '↑ N/A';
                document.getElementById('networkRxRate').textContent = summaryData.network_rx_kbps !== undefined ? `↓ ${summaryData.network_rx_kbps.toFixed(1)} KB/s` : '↓ N/A';
            }

            // Function to fetch summary data
            async function fetchSummaryData() {
                try {
                    const response = await fetch('/api/admin/system_summary'); // API endpoint changed
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status} for /api/admin/system_summary`);
                    }
                    const data = await response.json();
                    updateSummaryCards(data);
                    console.log("仪表盘摘要数据已更新:", data);
                    if (data.message) { // Log message from API (e.g. about mock data)
                        const snackbar = new mdc.snackbar.MDCSnackbar(document.querySelector('.mdc-snackbar'));
                        if(snackbar && data.message.includes("模拟数据")){
                            snackbar.labelText = "提示: 当前仪表盘摘要数据显示的是模拟数据。";
                            snackbar.open();
                        }
                        console.warn("来自API的消息:", data.message);
                    }
                } catch (error) {
                    console.error('无法获取仪表盘摘要数据:', error);
                    updateSummaryCards({ /* Default to N/A on error */
                        active_users: 'N/A', connected_devices: 'N/A', online_slave_servers: 'N/A', critical_alerts: 'N/A',
                        cpu_usage_percent: 'N/A', cpu_load_average: [], memory_usage_percent: 'N/A', total_memory_gb: 'N/A',
                        network_latency_ms: 'N/A', network_tx_kbps: 'N/A', network_rx_kbps: 'N/A'
                    });
                }
            }

            function loadRecentEvents() {
                console.log("Fetching recent events...");
                fetch('/api/admin/dashboard/recent_events')
                    .then(response => {
                        if (!response.ok) throw new Error(`HTTP error! status: ${response.status} for /api/admin/dashboard/recent_events`);
                        return response.json();
                    })
                    .then(events => {
                        console.log("Recent events data received:", events);
                        const listElement = document.getElementById('recentEventsList');
                        if (listElement) {
                            listElement.innerHTML = ''; 
                            if (events && events.length > 0) {
                                events.slice(0, 5).forEach(event => {
                                    const item = document.createElement('li');
                                    item.className = 'mdc-list-item';
                                    item.innerHTML = `
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__text">
                                            <span class="mdc-list-item__primary-text">${event.description || '未知事件'}</span>
                                            <span class="mdc-list-item__secondary-text">
                                                ${new Date(event.timestamp).toLocaleString()} - ${event.source || '未知来源'}
                                            </span>
                                        </span>`;
                                    listElement.appendChild(item);
                                });
                } else {
                                listElement.innerHTML = '<li class="mdc-list-item"><span class="mdc-list-item__text">无最近事件</span></li>';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching recent events:', error);
                        const listElement = document.getElementById('recentEventsList');
                        if (listElement) listElement.innerHTML = '<li class="mdc-list-item"><span class="mdc-list-item__text">无法加载最近事件</span></li>';
                    });
            }

            function loadActiveAlerts() {
                console.log("Fetching active alerts...");
                fetch('/api/admin/dashboard/active_alerts')
                    .then(response => {
                        if (!response.ok) throw new Error(`HTTP error! status: ${response.status} for /api/admin/dashboard/active_alerts`);
                        return response.json();
                    })
                    .then(alerts => {
                        console.log("Active alerts data received:", alerts);
                        const listElement = document.getElementById('activeAlertsList');
                        if (listElement) {
                            listElement.innerHTML = '';
                            if (alerts && alerts.length > 0) {
                                alerts.slice(0, 5).forEach(alert => {
                                    const item = document.createElement('li');
                                    item.className = 'mdc-list-item';
                                    const severityColor = getAlertSeverityColor(alert.severity);
                                    const severityIcon = getAlertSeverityIcon(alert.severity);
                                    item.innerHTML = `
                                        <span class="mdc-list-item__ripple"></span>
                                        <i class="material-icons mdc-list-item__graphic" aria-hidden="true" style="color: ${severityColor};">${severityIcon}</i>
                                        <span class="mdc-list-item__text">
                                            <span class="mdc-list-item__primary-text">${alert.title || '未知告警'} (${alert.severity || '未知级别'})</span>
                                            <span class="mdc-list-item__secondary-text">
                                                ${new Date(alert.timestamp).toLocaleString()} - ${alert.source || '未知来源'}
                                            </span>
                                        </span>`;
                                    listElement.appendChild(item);
                                });
                            } else {
                                listElement.innerHTML = '<li class="mdc-list-item"><span class="mdc-list-item__text">无活动告警</span></li>';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching active alerts:', error);
                        const listElement = document.getElementById('activeAlertsList');
                        if (listElement) listElement.innerHTML = '<li class="mdc-list-item"><span class="mdc-list-item__text">无法加载活动告警</span></li>';
                    });
            }

            function getAlertSeverityColor(severity) {
                severity = String(severity || '').toLowerCase();
                if (severity === 'critical' || severity === 'high') return 'var(--mdc-theme-error, #b00020)';
                if (severity === 'warning' || severity === 'medium') return 'var(--mdc-theme-warning, #ffa000)';
                if (severity === 'info' || severity === 'low') return 'var(--mdc-theme-info, #2196f3)';
                return 'var(--mdc-theme-text-secondary-on-background, rgba(0,0,0,0.54))';
            }

            function getAlertSeverityIcon(severity) {
                severity = String(severity || '').toLowerCase();
                if (severity === 'critical' || severity === 'high') return 'error_outline';
                if (severity === 'warning' || severity === 'medium') return 'warning_amber';
                if (severity === 'info' || severity === 'low') return 'info_outline';
                return 'help_outline';
            }
            
            // Chart instances (assuming they are initialized later or structure is created by initCharts)
            let healthTrendChartInstance, resourceUsageChartInstance;

            function initCharts() {
                console.log("Initializing chart structures...");
                const healthCtx = document.getElementById('healthTrendChart')?.getContext('2d');
                if (healthCtx && !healthTrendChartInstance) {
                    healthTrendChartInstance = new Chart(healthCtx, {
                        type: 'line',
                        data: { labels: [], datasets: [
                            { label: 'CPU使用率 (%)', data: [], borderColor: '#1976D2', tension: 0.3, yAxisID: 'yPercentage' },
                            { label: '内存使用率 (%)', data: [], borderColor: '#7B1FA2', tension: 0.3, yAxisID: 'yPercentage' }
                        ]},
                        options: { responsive: true, maintainAspectRatio: false, scales: { x: { type: 'time', time: { unit: 'minute' } }, yPercentage: { beginAtZero: true, max: 100, title: {display: true, text: '使用率 (%)'} } } }
                    });
                }

                const resourceCtx = document.getElementById('resourceUsageChart')?.getContext('2d');
                if (resourceCtx && !resourceUsageChartInstance) {
                    resourceUsageChartInstance = new Chart(resourceCtx, {
                        type: 'line',
                        data: { labels: [], datasets: [
                            { label: 'CPU 使用率 (%)', data: [], borderColor: 'rgb(255, 159, 64)', tension: 0.1, yAxisID: 'yPercentage' },
                            { label: '内存使用率 (%)', data: [], borderColor: 'rgb(153, 102, 255)', tension: 0.1, yAxisID: 'yPercentage' }
                        ]},
                        options: { responsive: true, maintainAspectRatio: false, scales: { x: { type: 'time', time: { unit: 'minute' } }, yPercentage: { beginAtZero: true, max: 100, title: {display: true, text: '使用率 (%)'} } } }
                    });
                }
                // The actual data for charts will be populated by WebSocket or a dedicated API call if `updateCharts` is enhanced.
            }

            // Main function to load initial data and setup UI components
            function initializeDashboard() {
                fetchSummaryData(); 
            loadRecentEvents();
                loadActiveAlerts();
                initCharts();
            }
            
            initializeDashboard(); // Load data and init charts

            // WebSocket Initialization (assuming createDashboardSocket is defined elsewhere or globally)
            if (typeof createDashboardSocket === 'function') {
                const dashboardSocket = createDashboardSocket({
                    onConnected: () => {
                        console.log('仪表盘WebSocket已连接');
                        if (dashboardSocket && typeof dashboardSocket.requestData === 'function') {
                            dashboardSocket.requestData();
                        }
                    },
                    onDisconnected: (info) => console.log('仪表盘WebSocket已断开', info),
                    onError: (error) => {
                        console.error('仪表盘WebSocket错误:', error);
                        if (typeof showNotification === 'function') {
                            showNotification('连接错误', '仪表盘数据连接出现错误...', 'warning');
                    }
                }
            });
            
                dashboardSocket.connect().then(connected => {
                    if (connected) {
                        console.log('仪表盘WebSocket连接成功');
                    } else {
                        console.warn('仪表盘WebSocket连接失败');
                        if (typeof showNotification === 'function') {
                            showNotification('连接失败', '无法连接到仪表盘实时数据服务。', 'error');
                    }
                }
            });
                window.dashboardSocket = dashboardSocket; // Make it globally accessible if needed
            } else {
                console.warn('createDashboardSocket function is not defined. WebSocket will not be initialized.');
            }
            // --- End of New/Refactored Dashboard Logic ---
        });
    </script>
</body>
</html> 