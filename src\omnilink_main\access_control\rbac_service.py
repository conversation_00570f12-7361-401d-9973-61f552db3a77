from functools import lru_cache
from src.omnilink_main.services.user_service import UserService
from common.models.user import User
from common.models.role import Role

class RBACService:
    def __init__(self, user_service: UserService):
        self.user_service = user_service

    @lru_cache(maxsize=128)
    def _get_user_permissions(self, user_id: int) -> set[str]:
        """
        获取并缓存用户的权限集合。
        """
        user = self.user_service.get_user_by_id(user_id)
        if not user or not user.is_active:
            return set()
        
        permissions = set()
        for role in user.roles:
            for perm in role.permissions:
                permissions.add(perm.name)
        return permissions

    def has_permission(self, user: User, permission_name: str) -> bool:
        """
        检查用户是否具有特定权限。
        """
        if not user or not user.is_active:
            return False
        
        if user.is_superuser:
            return True
            
        user_permissions = self._get_user_permissions(user.id)
        return permission_name in user_permissions 
