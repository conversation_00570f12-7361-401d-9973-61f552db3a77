from fastapi import APIRouter, Depends, status
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession

from common.schemas.api_schema import APIResponse
from common.schemas.role_schema import RoleRead, RoleCreate, RoleUpdate
from src.omnilink_main.services.role_service import role_service_instance
from src.omnilink_main.dependencies.db import get_db
from src.omnilink_main.dependencies.auth import PermissionChecker

router = APIRouter()

@router.post("/", response_model=APIResponse[RoleRead], status_code=status.HTTP_201_CREATED, dependencies=[Depends(PermissionChecker('roles.create'))])
async def create_role(
    *, 
    db: AsyncSession = Depends(get_db), 
    role_in: RoleCreate
) -> APIResponse[RoleRead]:
    """
    Create a new role.
    (Permission: 'roles.create')
    """
    role = await role_service_instance.create_new_role(db=db, role_in=role_in)
    return APIResponse(data=role)

@router.get("/", response_model=APIResponse[List[RoleRead]], dependencies=[Depends(PermissionChecker('roles.view'))])
async def read_roles(
    *, 
    db: AsyncSession = Depends(get_db), 
    skip: int = 0, 
    limit: int = 100
) -> APIResponse[List[RoleRead]]:
    """
    Retrieve roles.
    (Permission: 'roles.view')
    """
    roles = await role_service_instance.get_all_roles(db, skip=skip, limit=limit)
    return APIResponse(data=roles)

@router.get("/{role_id}", response_model=APIResponse[RoleRead], dependencies=[Depends(PermissionChecker('roles.view'))])
async def read_role(
    *, 
    db: AsyncSession = Depends(get_db), 
    role_id: int
) -> APIResponse[RoleRead]:
    """
    Get a specific role by id.
    (Permission: 'roles.view')
    """
    role = await role_service_instance.get_role_by_id(db, id=role_id)
    if not role:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Role not found")
    return APIResponse(data=role)

@router.put("/{role_id}", response_model=APIResponse[RoleRead], dependencies=[Depends(PermissionChecker('roles.edit'))])
async def update_role(
    *, 
    db: AsyncSession = Depends(get_db), 
    role_id: int, 
    role_in: RoleUpdate
) -> APIResponse[RoleRead]:
    """
    Update a role.
    (Permission: 'roles.edit')
    """
    updated_role = await role_service_instance.update_existing_role(db=db, role_id=role_id, role_in=role_in)
    return APIResponse(data=updated_role)

@router.delete("/{role_id}", response_model=APIResponse[RoleRead], dependencies=[Depends(PermissionChecker('roles.delete'))])
async def delete_role(
    *, 
    db: AsyncSession = Depends(get_db), 
    role_id: int
) -> APIResponse[RoleRead]:
    """
    Delete a role.
    (Permission: 'roles.delete')
    """
    deleted_role = await role_service_instance.delete_existing_role(db=db, role_id=role_id)
    return APIResponse(data=deleted_role)
