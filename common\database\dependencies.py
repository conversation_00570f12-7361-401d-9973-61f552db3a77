from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession
from src.omnilink_main.core.database import AsyncSessionLocal

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency that provides a SQLAlchemy async session.
    This ensures that the session is properly closed after the request is finished.
    """
    async with AsyncSessionLocal() as session:
        yield session
