from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List

class AlertBase(BaseModel):
    message: str
    severity: str # 例如: 'info', 'warning', 'error', 'critical'
    source_type: str # 例如: 'main_server', 'slave_server', 'device'
    source_id: Optional[str] = None
    status: str # 例如: 'new', 'acknowledged', 'resolved', 'ignored'

class AlertResponse(AlertBase):
    id: int
    created_at: datetime
    updated_at: datetime
    acknowledged_at: Optional[datetime] = None
    acknowledged_by_user_id: Optional[int] = Field(None, description="确认此警报的用户ID")
    resolved_at: Optional[datetime] = None
    resolved_by_user_id: Optional[int] = Field(None, description="解决此警报的用户ID")
    notes: Optional[str] = Field(None, description="确认或处理过程中的备注") # 通用备注字段
    resolution_notes: Optional[str] = Field(None, description="解决警报时的详细说明")
    assignee_id: Optional[int] = Field(None, description="当前分配处理此警报的用户ID")
    device_serial_number: Optional[str] = Field(None, description="关联设备序列号 (如果适用)")
    slave_server_id: Optional[int] = Field(None, description="关联从服务器ID (如果适用)")

    class Config:
        from_attributes = True

class AlertListResponse(BaseModel):
    items: List[AlertResponse]
    total: int
    page: int
    size: int

class AlertAcknowledgeRequest(BaseModel):
    notes: Optional[str] = Field(None, description="确认警报时的备注信息", max_length=500)

class AlertResolveRequest(BaseModel):
    resolution_notes: str = Field(..., description="解决警报时的详细说明", min_length=1, max_length=1000)

class AlertAssignRequest(BaseModel):
    assignee_user_id: int = Field(..., description="要分配处理此警报的用户ID")
    notes: Optional[str] = Field(None, description="分配备注", max_length=500)

class AlertCreateForDevice(BaseModel):
    """用于从设备或从服务器上报警报的内部模型"""
    message: str = Field(..., max_length=1000)
    severity: str # critical, error, warning, info
    device_serial_number: Optional[str] = None
    slave_id: Optional[int] = None # 用于从服务器上报
    details: Optional[dict] = None # 额外上下文信息 
