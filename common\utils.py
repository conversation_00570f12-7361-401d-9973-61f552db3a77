"""
通用工具模块

提供系统信息获取、数据格式化、文件操作等常用工具函数。
"""

import os
import sys
import platform
import psutil
import hashlib
import json
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from pathlib import Path

logger = logging.getLogger(__name__)

def get_system_info() -> Dict[str, Any]:
    """
    获取系统信息
    
    返回:
        Dict[str, Any]: 系统信息字典
    """
    try:
        # 基础系统信息
        info = {
            'platform': platform.system(),
            'platform_release': platform.release(),
            'platform_version': platform.version(),
            'architecture': platform.machine(),
            'hostname': platform.node(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'python_implementation': platform.python_implementation(),
        }
        
        # CPU信息
        info['cpu_count'] = psutil.cpu_count()
        info['cpu_count_logical'] = psutil.cpu_count(logical=True)
        info['cpu_percent'] = psutil.cpu_percent(interval=1)
        
        # 内存信息
        memory = psutil.virtual_memory()
        info['memory_total'] = memory.total
        info['memory_available'] = memory.available
        info['memory_percent'] = memory.percent
        info['memory_used'] = memory.used
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        info['disk_total'] = disk.total
        info['disk_used'] = disk.used
        info['disk_free'] = disk.free
        info['disk_percent'] = (disk.used / disk.total) * 100
        
        # 网络信息
        network = psutil.net_io_counters()
        info['network_bytes_sent'] = network.bytes_sent
        info['network_bytes_recv'] = network.bytes_recv
        
        # 启动时间
        boot_time = psutil.boot_time()
        info['boot_time'] = datetime.fromtimestamp(boot_time).isoformat()
        info['uptime_seconds'] = datetime.now().timestamp() - boot_time
        
        return info
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        # 返回基础信息
        return {
            'platform': platform.system(),
            'version': platform.version(),
            'machine': platform.machine(),
            'python_version': platform.python_version(),
            'error': str(e)
        }

def format_bytes(bytes_value: int, decimal_places: int = 2) -> str:
    """
    格式化字节数为人类可读格式
    
    参数:
        bytes_value: 字节数
        decimal_places: 小数位数
        
    返回:
        str: 格式化后的字符串
    """
    if bytes_value == 0:
        return "0 B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
    
    for unit in units:
        if bytes_value < 1024.0:
            return f"{bytes_value:.{decimal_places}f} {unit}"
        bytes_value /= 1024.0
    
    return f"{bytes_value:.{decimal_places}f} {units[-1]}"

def format_duration(seconds: Union[int, float]) -> str:
    """
    格式化时间间隔为人类可读格式
    
    参数:
        seconds: 秒数
        
    返回:
        str: 格式化后的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    elif seconds < 86400:
        hours = seconds / 3600
        return f"{hours:.1f}小时"
    else:
        days = seconds / 86400
        return f"{days:.1f}天"

def calculate_file_hash(file_path: Union[str, Path], algorithm: str = 'sha256') -> str:
    """
    计算文件哈希值
    
    参数:
        file_path: 文件路径
        algorithm: 哈希算法 ('md5', 'sha1', 'sha256', 'sha512')
        
    返回:
        str: 哈希值
    """
    hash_func = hashlib.new(algorithm)
    
    try:
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_func.update(chunk)
        return hash_func.hexdigest()
    except Exception as e:
        logger.error(f"计算文件哈希失败 {file_path}: {e}")
        raise

def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """
    安全的JSON解析
    
    参数:
        json_str: JSON字符串
        default: 解析失败时的默认值
        
    返回:
        Any: 解析结果或默认值
    """
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError) as e:
        logger.warning(f"JSON解析失败: {e}")
        return default

def safe_json_dumps(obj: Any, default: str = "null") -> str:
    """
    安全的JSON序列化
    
    参数:
        obj: 要序列化的对象
        default: 序列化失败时的默认值
        
    返回:
        str: JSON字符串或默认值
    """
    try:
        return json.dumps(obj, ensure_ascii=False, default=str)
    except (TypeError, ValueError) as e:
        logger.warning(f"JSON序列化失败: {e}")
        return default

def ensure_dir(dir_path: Union[str, Path]) -> Path:
    """
    确保目录存在，如果不存在则创建
    
    参数:
        dir_path: 目录路径
        
    返回:
        Path: 目录路径对象
    """
    path = Path(dir_path)
    path.mkdir(parents=True, exist_ok=True)
    return path

def get_file_size(file_path: Union[str, Path]) -> int:
    """
    获取文件大小
    
    参数:
        file_path: 文件路径
        
    返回:
        int: 文件大小（字节）
    """
    try:
        return Path(file_path).stat().st_size
    except (OSError, FileNotFoundError):
        return 0

def is_port_available(port: int, host: str = 'localhost') -> bool:
    """
    检查端口是否可用
    
    参数:
        port: 端口号
        host: 主机地址
        
    返回:
        bool: 端口是否可用
    """
    import socket
    
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            return result != 0
    except Exception:
        return False

def find_available_port(start_port: int = 8000, end_port: int = 9000, host: str = 'localhost') -> Optional[int]:
    """
    查找可用端口
    
    参数:
        start_port: 起始端口
        end_port: 结束端口
        host: 主机地址
        
    返回:
        Optional[int]: 可用端口号，如果没有找到则返回None
    """
    for port in range(start_port, end_port + 1):
        if is_port_available(port, host):
            return port
    return None

def truncate_string(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    截断字符串
    
    参数:
        text: 原始字符串
        max_length: 最大长度
        suffix: 截断后缀
        
    返回:
        str: 截断后的字符串
    """
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix

def parse_size_string(size_str: str) -> int:
    """
    解析大小字符串为字节数
    
    参数:
        size_str: 大小字符串，如 "1GB", "500MB", "1024KB"
        
    返回:
        int: 字节数
    """
    size_str = size_str.upper().strip()
    
    units = {
        'B': 1,
        'KB': 1024,
        'MB': 1024 ** 2,
        'GB': 1024 ** 3,
        'TB': 1024 ** 4,
        'PB': 1024 ** 5
    }
    
    for unit, multiplier in units.items():
        if size_str.endswith(unit):
            try:
                number = float(size_str[:-len(unit)])
                return int(number * multiplier)
            except ValueError:
                break
    
    # 如果没有单位，假设是字节
    try:
        return int(size_str)
    except ValueError:
        raise ValueError(f"无法解析大小字符串: {size_str}")

def get_process_info(pid: Optional[int] = None) -> Dict[str, Any]:
    """
    获取进程信息
    
    参数:
        pid: 进程ID，如果为None则获取当前进程信息
        
    返回:
        Dict[str, Any]: 进程信息
    """
    try:
        process = psutil.Process(pid) if pid else psutil.Process()
        
        return {
            'pid': process.pid,
            'name': process.name(),
            'status': process.status(),
            'cpu_percent': process.cpu_percent(),
            'memory_percent': process.memory_percent(),
            'memory_info': process.memory_info()._asdict(),
            'create_time': datetime.fromtimestamp(process.create_time()).isoformat(),
            'num_threads': process.num_threads(),
            'cmdline': process.cmdline()
        }
    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
        logger.error(f"获取进程信息失败: {e}")
        return {'error': str(e)}

# 导出主要功能
__all__ = [
    'get_system_info',
    'format_bytes',
    'format_duration',
    'calculate_file_hash',
    'safe_json_loads',
    'safe_json_dumps',
    'ensure_dir',
    'get_file_size',
    'is_port_available',
    'find_available_port',
    'truncate_string',
    'parse_size_string',
    'get_process_info'
] 
