{"mcpServers": {"streamable-mcp-server": {"type": "http", "url": "http://127.0.0.1:12306/mcp", "timeout": 30000, "description": "Streamable MCP Server for web automation and content retrieval"}, "playwright-mcp": {"type": "http", "url": "http://127.0.0.1:8931/mcp", "timeout": 30000, "description": "Playwright MCP Server for browser automation"}}, "serverCommands": {"streamable-mcp-server": {"start": "node streamable-mcp-server --port 12306", "stop": "pkill -f streamable-mcp-server"}, "playwright-mcp": {"start": "npx @playwright/mcp@0.0.26 --port 8931 --headless", "stop": "pkill -f playwright"}}}