<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>迁移向导 - 统一管理控制台</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin_console.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边导航栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <!-- 导航内容与index.html相同 -->
                <div class="position-sticky pt-3">
                    <div class="px-3 py-4 text-white">
                        <h5>主从服务器管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.index') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.group_management') }}">
                                <i class="fas fa-server me-2"></i>分组管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('admin_console.migration_wizard') }}">
                                <i class="fas fa-exchange-alt me-2"></i>迁移向导
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.alerts') }}">
                                <i class="fas fa-bell me-2"></i>告警设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.health_monitor') }}">
                                <i class="fas fa-heartbeat me-2"></i>健康监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" id="helpBtn">
                                <i class="fas fa-question-circle me-2"></i>帮助
                            </a>
                        </li>
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>系统</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.system_settings') }}">
                                <i class="fas fa-cog me-2"></i>系统设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>退出
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">系统迁移向导</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshBtn">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="newMigrationBtn">
                                <i class="fas fa-plus"></i> 新建迁移任务
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 迁移任务列表 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-history me-2"></i>迁移任务历史
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th>任务ID</th>
                                        <th>名称</th>
                                        <th>源服务器</th>
                                        <th>目标服务器</th>
                                        <th>开始时间</th>
                                        <th>状态</th>
                                        <th>进度</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="migrationTasksList">
                                    <tr>
                                        <td colspan="8" class="text-center">暂无任务历史</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 迁移向导或详情 -->
                <div id="migrationWizardContainer" style="display: none;">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <span>
                                <i class="fas fa-magic me-2"></i>迁移向导
                            </span>
                            <button type="button" class="btn-close" id="closeMigrationWizard" aria-label="Close"></button>
                        </div>
                        <div class="card-body">
                            <!-- 分步导航 -->
                            <ul class="nav nav-pills nav-justified mb-4" id="migrationWizardSteps">
                                <li class="nav-item">
                                    <a class="nav-link active" id="step1-tab" data-bs-toggle="pill" href="#step1">1. 基本配置</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link disabled" id="step2-tab" data-bs-toggle="pill" href="#step2">2. 选择组件</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link disabled" id="step3-tab" data-bs-toggle="pill" href="#step3">3. 数据导出</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link disabled" id="step4-tab" data-bs-toggle="pill" href="#step4">4. 数据导入</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link disabled" id="step5-tab" data-bs-toggle="pill" href="#step5">5. 验证结果</a>
                                </li>
                            </ul>

                            <!-- 步骤内容 -->
                            <div class="tab-content" id="migrationWizardContent">
                                <!-- 步骤1：基本配置 -->
                                <div class="tab-pane fade show active" id="step1" role="tabpanel">
                                    <h4 class="mb-4">迁移基本配置</h4>
                                    <form id="migrationBasicForm">
                                        <div class="mb-3">
                                            <label for="migrationName" class="form-label">迁移任务名称</label>
                                            <input type="text" class="form-control" id="migrationName" required placeholder="请输入便于识别的任务名称">
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="sourceServer" class="form-label">源服务器</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">IP</span>
                                                    <input type="text" class="form-control" id="sourceServerIP" placeholder="如: *************" required>
                                                    <span class="input-group-text">端口</span>
                                                    <input type="number" class="form-control" id="sourceServerPort" placeholder="如: 8080" required>
                                                </div>
                                                <div class="form-text">当前服务器将作为源服务器</div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="targetServer" class="form-label">目标服务器</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">IP</span>
                                                    <input type="text" class="form-control" id="targetServerIP" placeholder="如: *************" required>
                                                    <span class="input-group-text">端口</span>
                                                    <input type="number" class="form-control" id="targetServerPort" placeholder="如: 8080" required>
                                                </div>
                                                <div class="form-text">需要确保目标服务器可访问且已启动</div>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">迁移方式</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="migrationType" id="migrationType1" value="full" checked>
                                                    <label class="form-check-label" for="migrationType1">
                                                        全量迁移（覆盖目标服务器所有数据）
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="migrationType" id="migrationType2" value="incremental">
                                                    <label class="form-check-label" for="migrationType2">
                                                        增量迁移（只迁移变更内容）
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">迁移选项</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="backupBeforeMigration" checked>
                                                    <label class="form-check-label" for="backupBeforeMigration">
                                                        迁移前备份
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="verifyAfterMigration" checked>
                                                    <label class="form-check-label" for="verifyAfterMigration">
                                                        迁移后验证
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="autoRollbackOnFailure" checked>
                                                    <label class="form-check-label" for="autoRollbackOnFailure">
                                                        失败时自动回滚
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <button type="button" class="btn btn-outline-secondary" id="cancelMigrationBtn">取消</button>
                                            <button type="submit" class="btn btn-primary" id="step1NextBtn">下一步</button>
                                        </div>
                                    </form>
                                </div>

                                <!-- 步骤2：选择组件 -->
                                <div class="tab-pane fade" id="step2" role="tabpanel">
                                    <h4 class="mb-4">选择要迁移的组件</h4>
                                    <form id="migrationComponentsForm">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="card mb-3">
                                                    <div class="card-header">数据与配置</div>
                                                    <div class="card-body">
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="checkbox" id="componentDatabase" checked>
                                                            <label class="form-check-label" for="componentDatabase">
                                                                数据库
                                                            </label>
                                                            <small class="form-text text-muted d-block">包括用户、从服务器、设备和权限信息</small>
                                                        </div>
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="checkbox" id="componentConfigurations" checked>
                                                            <label class="form-check-label" for="componentConfigurations">
                                                                系统配置
                                                            </label>
                                                            <small class="form-text text-muted d-block">包括全局设置、端口分配和服务参数</small>
                                                        </div>
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="checkbox" id="componentLogs">
                                                            <label class="form-check-label" for="componentLogs">
                                                                日志数据
                                                            </label>
                                                            <small class="form-text text-muted d-block">包括系统日志、审计日志和操作记录</small>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="componentStatistics">
                                                            <label class="form-check-label" for="componentStatistics">
                                                                统计数据
                                                            </label>
                                                            <small class="form-text text-muted d-block">包括使用统计、性能指标和历史趋势</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card mb-3">
                                                    <div class="card-header">组织与管理</div>
                                                    <div class="card-body">
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="checkbox" id="componentGroups" checked>
                                                            <label class="form-check-label" for="componentGroups">
                                                                从服务器分组
                                                            </label>
                                                            <small class="form-text text-muted d-block">包括分组结构、策略和标签</small>
                                                        </div>
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="checkbox" id="componentPartitions" checked>
                                                            <label class="form-check-label" for="componentPartitions">
                                                                分区配置
                                                            </label>
                                                            <small class="form-text text-muted d-block">包括分区定义和组分配</small>
                                                        </div>
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="checkbox" id="componentAlerts" checked>
                                                            <label class="form-check-label" for="componentAlerts">
                                                                告警配置
                                                            </label>
                                                            <small class="form-text text-muted d-block">包括告警规则、通知渠道和阈值</small>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="componentSchedules">
                                                            <label class="form-check-label" for="componentSchedules">
                                                                计划任务
                                                            </label>
                                                            <small class="form-text text-muted d-block">包括定时任务和批处理作业</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-check mb-3 mt-2">
                                            <input class="form-check-input" type="checkbox" id="selectAllComponents">
                                            <label class="form-check-label" for="selectAllComponents">
                                                <strong>全选</strong>
                                            </label>
                                        </div>

                                        <div class="d-flex justify-content-between">
                                            <button type="button" class="btn btn-outline-secondary" id="step2PrevBtn">上一步</button>
                                            <button type="submit" class="btn btn-primary" id="step2NextBtn">下一步</button>
                                        </div>
                                    </form>
                                </div>

                                <!-- 步骤3：数据导出 -->
                                <div class="tab-pane fade" id="step3" role="tabpanel">
                                    <h4 class="mb-4">数据导出</h4>
                                    <div class="alert alert-info mb-4">
                                        <i class="fas fa-info-circle me-2"></i>系统将从源服务器导出所选组件的数据。此过程可能需要几分钟时间，请耐心等待。
                                    </div>

                                    <div class="card mb-4">
                                        <div class="card-header">导出状态</div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">总体进度</label>
                                                <div class="progress">
                                                    <div id="exportProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">当前组件</label>
                                                <p class="form-control-plaintext" id="currentExportComponent">等待开始...</p>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">状态信息</label>
                                                <div class="form-control" style="height: 150px; overflow-y: auto;" id="exportStatusLog">
                                                    <div class="text-muted">准备开始数据导出...</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-outline-secondary" id="step3PrevBtn" disabled>上一步</button>
                                        <button type="button" class="btn btn-primary" id="startExportBtn">开始导出</button>
                                        <button type="button" class="btn btn-primary" id="step3NextBtn" style="display: none;">下一步</button>
                                    </div>
                                </div>

                                <!-- 步骤4：数据导入 -->
                                <div class="tab-pane fade" id="step4" role="tabpanel">
                                    <h4 class="mb-4">数据导入</h4>
                                    <div class="alert alert-info mb-4">
                                        <i class="fas fa-info-circle me-2"></i>系统将向目标服务器导入已导出的数据。此过程可能需要几分钟时间，请耐心等待。
                                    </div>

                                    <div class="card mb-4">
                                        <div class="card-header">导入状态</div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">总体进度</label>
                                                <div class="progress">
                                                    <div id="importProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">当前组件</label>
                                                <p class="form-control-plaintext" id="currentImportComponent">等待开始...</p>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">状态信息</label>
                                                <div class="form-control" style="height: 150px; overflow-y: auto;" id="importStatusLog">
                                                    <div class="text-muted">准备开始数据导入...</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-outline-secondary" id="step4PrevBtn" disabled>上一步</button>
                                        <button type="button" class="btn btn-primary" id="startImportBtn">开始导入</button>
                                        <button type="button" class="btn btn-primary" id="step4NextBtn" style="display: none;">下一步</button>
                                    </div>
                                </div>

                                <!-- 步骤5：验证结果 -->
                                <div class="tab-pane fade" id="step5" role="tabpanel">
                                    <h4 class="mb-4">迁移结果验证</h4>

                                    <div class="alert alert-success mb-4" id="migrationSuccessAlert" style="display: none;">
                                        <i class="fas fa-check-circle me-2"></i>迁移已成功完成！所有组件已成功迁移到目标服务器。
                                    </div>

                                    <div class="alert alert-warning mb-4" id="migrationWarningAlert" style="display: none;">
                                        <i class="fas fa-exclamation-triangle me-2"></i>迁移已完成，但有一些警告。请查看下方详细信息。
                                    </div>

                                    <div class="alert alert-danger mb-4" id="migrationErrorAlert" style="display: none;">
                                        <i class="fas fa-times-circle me-2"></i>迁移过程中发生错误。请查看下方详细信息。
                                    </div>

                                    <div class="card mb-4">
                                        <div class="card-header">验证结果</div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th>组件</th>
                                                            <th>状态</th>
                                                            <th>结果</th>
                                                            <th>详细信息</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="validationResultsTable">
                                                        <tr>
                                                            <td colspan="4" class="text-center">验证尚未开始</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card mb-4">
                                        <div class="card-header">迁移摘要</div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            迁移任务ID
                                                            <span id="summaryTaskId">-</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            开始时间
                                                            <span id="summaryStartTime">-</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            结束时间
                                                            <span id="summaryEndTime">-</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            总耗时
                                                            <span id="summaryDuration">-</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="col-md-6">
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            成功组件
                                                            <span class="badge bg-success rounded-pill" id="summarySuccessCount">0</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            警告组件
                                                            <span class="badge bg-warning rounded-pill" id="summaryWarningCount">0</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            失败组件
                                                            <span class="badge bg-danger rounded-pill" id="summaryErrorCount">0</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            总迁移数据量
                                                            <span id="summaryDataSize">-</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-outline-secondary" id="step5PrevBtn" disabled>上一步</button>
                                        <div>
                                            <button type="button" class="btn btn-outline-primary me-2" id="downloadReportBtn">
                                                <i class="fas fa-download me-1"></i>下载报告
                                            </button>
                                            <button type="button" class="btn btn-primary" id="finishMigrationBtn">完成</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/admin_console.js') }}"></script>
</body>
</html> 