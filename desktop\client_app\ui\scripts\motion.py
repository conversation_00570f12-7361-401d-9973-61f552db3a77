#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Material Design 动效系统基础模块

提供动画类型和缓动类型的枚举，以及加载动效定义的功能
"""

import os
import json
from enum import Enum, auto
from pathlib import Path

# 设置路径
SCRIPT_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
PARENT_DIR = SCRIPT_DIR.parent
THEMES_DIR = PARENT_DIR / "themes"
MOTION_JSON_PATH = THEMES_DIR / "motion.json"

class AnimationType(Enum):
    """动画类型枚举"""
    FADE_IN = auto()
    FADE_OUT = auto()
    SLIDE_IN = auto()
    SLIDE_OUT = auto()
    SCALE_IN = auto()
    SCALE_OUT = auto()
    RIPPLE = auto()
    STATE = auto()
    TRANSITION = auto()
    ELEVATION = auto()
    SWIPE = auto()
    EXPAND_COLLAPSE = auto()
    ROTATE = auto()

class EasingType(Enum):
    """缓动类型枚举"""
    STANDARD = auto()
    ACCELERATED = auto()
    DECELERATED = auto()
    LINEAR = auto()
    EMPHASIZED = auto()
    EMPHASIZED_ACCELERATE = auto()
    EMPHASIZED_DECELERATE = auto()

class MotionSystem:
    """Material Design动效系统"""
    
    def __init__(self, motion_path=None):
        """
        初始化动效系统
        
        Args:
            motion_path: 动效定义JSON文件路径，默认为MOTION_JSON_PATH
        """
        self.motion_path = motion_path or MOTION_JSON_PATH
        self.motion_data = {}
        
    def load_motion_json(self):
        """
        加载动效定义JSON文件
        
        Returns:
            加载的动效数据字典
        """
        if not os.path.exists(self.motion_path):
            # 如果文件不存在，创建默认动效定义
            self.motion_data = self.create_default_motion_data()
            # 确保目录存在
            os.makedirs(os.path.dirname(self.motion_path), exist_ok=True)
            # 保存默认动效定义
            with open(self.motion_path, 'w', encoding='utf-8') as f:
                json.dump(self.motion_data, f, indent=2)
        else:
            # 加载现有动效定义
            with open(self.motion_path, 'r', encoding='utf-8') as f:
                self.motion_data = json.load(f)
        
        return self.motion_data
    
    def create_default_motion_data(self):
        """
        创建默认动效定义
        
        Returns:
            默认动效数据字典
        """
        return {
            "duration": {
                "standard": 300,
                "short": 150,
                "long": 500,
                "extra_long": 700
            },
            "easing": {
                "standard": "cubic-bezier(0.4, 0.0, 0.2, 1)",
                "accelerated": "cubic-bezier(0.4, 0.0, 1.0, 1.0)",
                "decelerated": "cubic-bezier(0.0, 0.0, 0.2, 1.0)",
                "linear": "cubic-bezier(0.0, 0.0, 1.0, 1.0)",
                "emphasized": "cubic-bezier(0.2, 0.0, 0.0, 1.0)",
                "emphasized_accelerate": "cubic-bezier(0.3, 0.0, 0.8, 0.15)",
                "emphasized_decelerate": "cubic-bezier(0.05, 0.7, 0.1, 1.0)"
            },
            "animations": {
                "fade_in": {
                    "type": "FADE_IN",
                    "duration": "standard",
                    "easing": "standard",
                    "properties": {
                        "opacity_start": 0,
                        "opacity_end": 1
                    }
                },
                "fade_out": {
                    "type": "FADE_OUT",
                    "duration": "standard",
                    "easing": "accelerated",
                    "properties": {
                        "opacity_start": 1,
                        "opacity_end": 0
                    }
                },
                "slide_in_left": {
                    "type": "SLIDE_IN",
                    "duration": "standard",
                    "easing": "standard",
                    "properties": {
                        "direction": "left",
                        "distance": "100%"
                    }
                },
                "slide_in_right": {
                    "type": "SLIDE_IN",
                    "duration": "standard",
                    "easing": "standard",
                    "properties": {
                        "direction": "right",
                        "distance": "100%"
                    }
                },
                "slide_in_up": {
                    "type": "SLIDE_IN",
                    "duration": "standard",
                    "easing": "standard",
                    "properties": {
                        "direction": "up",
                        "distance": "100%"
                    }
                },
                "slide_in_down": {
                    "type": "SLIDE_IN",
                    "duration": "standard",
                    "easing": "standard",
                    "properties": {
                        "direction": "down",
                        "distance": "100%"
                    }
                },
                "slide_out_left": {
                    "type": "SLIDE_OUT",
                    "duration": "standard",
                    "easing": "accelerated",
                    "properties": {
                        "direction": "left",
                        "distance": "100%"
                    }
                },
                "slide_out_right": {
                    "type": "SLIDE_OUT",
                    "duration": "standard",
                    "easing": "accelerated",
                    "properties": {
                        "direction": "right",
                        "distance": "100%"
                    }
                },
                "slide_out_up": {
                    "type": "SLIDE_OUT",
                    "duration": "standard",
                    "easing": "accelerated",
                    "properties": {
                        "direction": "up",
                        "distance": "100%"
                    }
                },
                "slide_out_down": {
                    "type": "SLIDE_OUT",
                    "duration": "standard",
                    "easing": "accelerated",
                    "properties": {
                        "direction": "down",
                        "distance": "100%"
                    }
                },
                "scale_in": {
                    "type": "SCALE_IN",
                    "duration": "standard",
                    "easing": "standard",
                    "properties": {
                        "scale_start": 0.8,
                        "scale_end": 1
                    }
                },
                "scale_out": {
                    "type": "SCALE_OUT",
                    "duration": "standard",
                    "easing": "accelerated",
                    "properties": {
                        "scale_start": 1,
                        "scale_end": 0.8
                    }
                },
                "ripple": {
                    "type": "RIPPLE",
                    "duration": "standard",
                    "easing": "standard",
                    "properties": {
                        "color": "rgba(255, 255, 255, 0.12)",
                        "scale_start": 0,
                        "scale_end": 1
                    }
                },
                "state_hover": {
                    "type": "STATE",
                    "duration": "short",
                    "easing": "standard",
                    "properties": {
                        "opacity": 0.08
                    }
                },
                "state_focus": {
                    "type": "STATE",
                    "duration": "short",
                    "easing": "standard",
                    "properties": {
                        "opacity": 0.12
                    }
                },
                "state_pressed": {
                    "type": "STATE",
                    "duration": "short",
                    "easing": "standard",
                    "properties": {
                        "opacity": 0.12
                    }
                },
                "state_dragged": {
                    "type": "STATE",
                    "duration": "short",
                    "easing": "standard",
                    "properties": {
                        "opacity": 0.16,
                        "scale": 1.05,
                        "elevation_increase": 1
                    }
                }
            },
            "transitions": {
                "default": {
                    "properties": ["all"],
                    "duration": "standard",
                    "easing": "standard"
                },
                "elevation": {
                    "properties": ["box-shadow"],
                    "duration": "standard",
                    "easing": "standard"
                },
                "color": {
                    "properties": ["color", "background-color", "border-color", "fill", "stroke"],
                    "duration": "standard",
                    "easing": "standard"
                },
                "size": {
                    "properties": ["width", "height", "margin", "padding"],
                    "duration": "standard",
                    "easing": "standard"
                },
                "position": {
                    "properties": ["top", "right", "bottom", "left", "transform"],
                    "duration": "standard",
                    "easing": "standard"
                },
                "visibility": {
                    "properties": ["opacity", "visibility"],
                    "duration": "short",
                    "easing": "standard"
                }
            }
        }
    
    def get_easing_curve(self, easing_type_name):
        """
        获取缓动曲线CSS表达式
        
        Args:
            easing_type_name: 缓动类型名称
            
        Returns:
            缓动曲线CSS表达式
        """
        return self.motion_data.get("easing", {}).get(easing_type_name, "cubic-bezier(0.4, 0.0, 0.2, 1)")
    
    def get_duration_ms(self, duration_name):
        """
        获取持续时间毫秒值
        
        Args:
            duration_name: 持续时间名称
            
        Returns:
            持续时间毫秒值
        """
        return self.motion_data.get("duration", {}).get(duration_name, 300)
    
    def get_animation(self, animation_name):
        """
        获取动画定义
        
        Args:
            animation_name: 动画名称
            
        Returns:
            动画定义字典
        """
        return self.motion_data.get("animations", {}).get(animation_name, {})
    
    def get_transition(self, transition_name):
        """
        获取过渡定义
        
        Args:
            transition_name: 过渡名称
            
        Returns:
            过渡定义字典
        """
        return self.motion_data.get("transitions", {}).get(transition_name, {})
    
    def generate_qt_animation_class(self):
        """
        生成QT动画类代码
        
        Returns:
            生成的Python代码字符串
        """
        code = ''
        # 主动画类
        code += 'class MaterialAnimations:\n'
        code += '    """Material Design动画实现类"""\n'
        code += '    \n'
        code += '    def __init__(self, target_widget):\n'
        code += '        """初始化动画类\n'
        code += '        \n'
        code += '        Args:\n'
        code += '            target_widget: 目标控件\n'
        code += '        """\n'
        code += '        self.target = target_widget\n'
        code += '    \n'
        
        # 淡入方法
        code += '    def fade_in(self, duration=None, easing_type=EasingType.STANDARD):\n'
        code += '        """创建淡入动画\n'
        code += '        \n'
        code += '        Args:\n'
        code += '            duration: 持续时间（毫秒），默认为标准持续时间\n'
        code += '            easing_type: 缓动类型\n'
        code += '            \n'
        code += '        Returns:\n'
        code += '            创建的动画对象\n'
        code += '        """\n'
        code += '        if duration is None:\n'
        code += '            duration = DURATION_STANDARD\n'
        code += '        \n'
        code += '        # 创建透明度效果\n'
        code += '        opacity_effect = QGraphicsOpacityEffect(self.target)\n'
        code += '        self.target.setGraphicsEffect(opacity_effect)\n'
        code += '        opacity_effect.setOpacity(0.0)\n'
        code += '        \n'
        code += '        # 创建淡入动画\n'
        code += '        animation = QPropertyAnimation(opacity_effect, b"opacity")\n'
        code += '        animation.setDuration(duration)\n'
        code += '        animation.setStartValue(0.0)\n'
        code += '        animation.setEndValue(1.0)\n'
        code += '        animation.setEasingCurve(EASING_CURVE_MAP[easing_type])\n'
        code += '        \n'
        code += '        return animation\n'
        code += '    \n'
        
        # 淡出方法
        code += '    def fade_out(self, duration=None, easing_type=EasingType.ACCELERATED):\n'
        code += '        """创建淡出动画\n'
        code += '        \n'
        code += '        Args:\n'
        code += '            duration: 持续时间（毫秒），默认为标准持续时间\n'
        code += '            easing_type: 缓动类型\n'
        code += '            \n'
        code += '        Returns:\n'
        code += '            创建的动画对象\n'
        code += '        """\n'
        code += '        if duration is None:\n'
        code += '            duration = DURATION_STANDARD\n'
        code += '        \n'
        code += '        # 创建透明度效果（如果不存在）\n'
        code += '        effect = self.target.graphicsEffect()\n'
        code += '        if not effect or not isinstance(effect, QGraphicsOpacityEffect):\n'
        code += '            effect = QGraphicsOpacityEffect(self.target)\n'
        code += '            self.target.setGraphicsEffect(effect)\n'
        code += '            effect.setOpacity(1.0)\n'
        code += '        \n'
        code += '        # 创建淡出动画\n'
        code += '        animation = QPropertyAnimation(effect, b"opacity")\n'
        code += '        animation.setDuration(duration)\n'
        code += '        animation.setStartValue(1.0)\n'
        code += '        animation.setEndValue(0.0)\n'
        code += '        animation.setEasingCurve(EASING_CURVE_MAP[easing_type])\n'
        code += '        \n'
        code += '        return animation\n'
        code += '    \n'
        
        # 滑入方法
        code += '    def slide_in(self, direction=Qt.RightToLeft, duration=None, easing_type=EasingType.STANDARD):\n'
        code += '        """创建滑入动画\n'
        code += '        \n'
        code += '        Args:\n'
        code += '            direction: 方向，使用Qt方向常量\n'
        code += '            duration: 持续时间（毫秒），默认为标准持续时间\n'
        code += '            easing_type: 缓动类型\n'
        code += '            \n'
        code += '        Returns:\n'
        code += '            创建的动画对象\n'
        code += '        """\n'
        code += '        if duration is None:\n'
        code += '            duration = DURATION_STANDARD\n'
        code += '        \n'
        code += '        # 获取控件几何位置\n'
        code += '        geometry = self.target.geometry()\n'
        code += '        parent = self.target.parent()\n'
        code += '        parent_width = parent.width() if parent else 0\n'
        code += '        parent_height = parent.height() if parent else 0\n'
        code += '        \n'
        code += '        # 根据方向确定起始位置\n'
        code += '        start_geometry = QRect(geometry)\n'
        code += '        \n'
        code += '        if direction == Qt.RightToLeft:\n'
        code += '            start_geometry.moveLeft(parent_width)\n'
        code += '        elif direction == Qt.LeftToRight:\n'
        code += '            start_geometry.moveRight(0)\n'
        code += '        elif direction == Qt.BottomToTop:\n'
        code += '            start_geometry.moveTop(parent_height)\n'
        code += '        elif direction == Qt.TopToBottom:\n'
        code += '            start_geometry.moveBottom(0)\n'
        code += '        \n'
        code += '        # 设置起始位置\n'
        code += '        self.target.setGeometry(start_geometry)\n'
        code += '        \n'
        code += '        # 创建滑入动画\n'
        code += '        animation = QPropertyAnimation(self.target, b"geometry")\n'
        code += '        animation.setDuration(duration)\n'
        code += '        animation.setStartValue(start_geometry)\n'
        code += '        animation.setEndValue(geometry)\n'
        code += '        animation.setEasingCurve(EASING_CURVE_MAP[easing_type])\n'
        code += '        \n'
        code += '        return animation\n'
        code += '    \n'
        
        # 滑出方法
        code += '    def slide_out(self, direction=Qt.RightToLeft, duration=None, easing_type=EasingType.ACCELERATED):\n'
        code += '        """创建滑出动画\n'
        code += '        \n'
        code += '        Args:\n'
        code += '            direction: 方向，使用Qt方向常量\n'
        code += '            duration: 持续时间（毫秒），默认为标准持续时间\n'
        code += '            easing_type: 缓动类型\n'
        code += '            \n'
        code += '        Returns:\n'
        code += '            创建的动画对象\n'
        code += '        """\n'
        code += '        if duration is None:\n'
        code += '            duration = DURATION_STANDARD\n'
        code += '        \n'
        code += '        # 获取控件几何位置\n'
        code += '        start_geometry = self.target.geometry()\n'
        code += '        end_geometry = QRect(start_geometry)\n'
        code += '        parent = self.target.parent()\n'
        code += '        parent_width = parent.width() if parent else 0\n'
        code += '        parent_height = parent.height() if parent else 0\n'
        code += '        \n'
        code += '        # 根据方向确定结束位置\n'
        code += '        if direction == Qt.RightToLeft:\n'
        code += '            end_geometry.moveRight(0)\n'
        code += '        elif direction == Qt.LeftToRight:\n'
        code += '            end_geometry.moveLeft(parent_width)\n'
        code += '        elif direction == Qt.BottomToTop:\n'
        code += '            end_geometry.moveBottom(0)\n'
        code += '        elif direction == Qt.TopToBottom:\n'
        code += '            end_geometry.moveTop(parent_height)\n'
        code += '        \n'
        code += '        # 创建滑出动画\n'
        code += '        animation = QPropertyAnimation(self.target, b"geometry")\n'
        code += '        animation.setDuration(duration)\n'
        code += '        animation.setStartValue(start_geometry)\n'
        code += '        animation.setEndValue(end_geometry)\n'
        code += '        animation.setEasingCurve(EASING_CURVE_MAP[easing_type])\n'
        code += '        \n'
        code += '        return animation\n'
        code += '    \n'
        
        # 缩放方法
        code += '    def scale(self, start_scale=0.8, end_scale=1.0, duration=None, easing_type=EasingType.STANDARD):\n'
        code += '        """创建缩放动画\n'
        code += '        \n'
        code += '        Args:\n'
        code += '            start_scale: 起始缩放比例\n'
        code += '            end_scale: 结束缩放比例\n'
        code += '            duration: 持续时间（毫秒），默认为标准持续时间\n'
        code += '            easing_type: 缓动类型\n'
        code += '            \n'
        code += '        Returns:\n'
        code += '            创建的动画组\n'
        code += '        """\n'
        code += '        if duration is None:\n'
        code += '            duration = DURATION_STANDARD\n'
        code += '        \n'
        code += '        # 获取控件尺寸\n'
        code += '        original_size = self.target.size()\n'
        code += '        original_pos = self.target.pos()\n'
        code += '        \n'
        code += '        # 计算缩放尺寸\n'
        code += '        start_width = int(original_size.width() * start_scale)\n'
        code += '        start_height = int(original_size.height() * start_scale)\n'
        code += '        end_width = int(original_size.width() * end_scale)\n'
        code += '        end_height = int(original_size.height() * end_scale)\n'
        code += '        \n'
        code += '        # 计算位置偏移，使缩放以中心点为基准\n'
        code += '        start_x = original_pos.x() + (original_size.width() - start_width) // 2\n'
        code += '        start_y = original_pos.y() + (original_size.height() - start_height) // 2\n'
        code += '        end_x = original_pos.x() + (original_size.width() - end_width) // 2\n'
        code += '        end_y = original_pos.y() + (original_size.height() - end_height) // 2\n'
        code += '        \n'
        code += '        # 创建尺寸动画\n'
        code += '        size_animation = QPropertyAnimation(self.target, b"size")\n'
        code += '        size_animation.setDuration(duration)\n'
        code += '        size_animation.setStartValue(QSize(start_width, start_height))\n'
        code += '        size_animation.setEndValue(QSize(end_width, end_height))\n'
        code += '        size_animation.setEasingCurve(EASING_CURVE_MAP[easing_type])\n'
        code += '        \n'
        code += '        # 创建位置动画\n'
        code += '        pos_animation = QPropertyAnimation(self.target, b"pos")\n'
        code += '        pos_animation.setDuration(duration)\n'
        code += '        pos_animation.setStartValue(QPoint(start_x, start_y))\n'
        code += '        pos_animation.setEndValue(QPoint(end_x, end_y))\n'
        code += '        pos_animation.setEasingCurve(EASING_CURVE_MAP[easing_type])\n'
        code += '        \n'
        code += '        # 创建动画组\n'
        code += '        animation_group = QParallelAnimationGroup()\n'
        code += '        animation_group.addAnimation(size_animation)\n'
        code += '        animation_group.addAnimation(pos_animation)\n'
        code += '        \n'
        code += '        return animation_group\n'
        code += '    \n'
        
        # 缩放入场方法
        code += '    def scale_in(self, duration=None, easing_type=EasingType.STANDARD):\n'
        code += '        """创建缩放入场动画\n'
        code += '        \n'
        code += '        Args:\n'
        code += '            duration: 持续时间（毫秒），默认为标准持续时间\n'
        code += '            easing_type: 缓动类型\n'
        code += '            \n'
        code += '        Returns:\n'
        code += '            创建的动画组\n'
        code += '        """\n'
        code += '        return self.scale(0.8, 1.0, duration, easing_type)\n'
        code += '    \n'
        
        # 缩放出场方法
        code += '    def scale_out(self, duration=None, easing_type=EasingType.ACCELERATED):\n'
        code += '        """创建缩放出场动画\n'
        code += '        \n'
        code += '        Args:\n'
        code += '            duration: 持续时间（毫秒），默认为标准持续时间\n'
        code += '            easing_type: 缓动类型\n'
        code += '            \n'
        code += '        Returns:\n'
        code += '            创建的动画组\n'
        code += '        """\n'
        code += '        return self.scale(1.0, 0.8, duration, easing_type)\n'
        code += '    \n'
        
        # 组合动画方法
        code += '    def combine(self, animations):\n'
        code += '        """组合多个动画为并行动画组\n'
        code += '        \n'
        code += '        Args:\n'
        code += '            animations: 动画列表\n'
        code += '            \n'
        code += '        Returns:\n'
        code += '            创建的动画组\n'
        code += '        """\n'
        code += '        group = QParallelAnimationGroup()\n'
        code += '        for animation in animations:\n'
        code += '            group.addAnimation(animation)\n'
        code += '        return group\n'
        code += '    \n'
        
        # 序列动画方法
        code += '    def sequence(self, animations):\n'
        code += '        """组合多个动画为序列动画组\n'
        code += '        \n'
        code += '        Args:\n'
        code += '            animations: 动画列表\n'
        code += '            \n'
        code += '        Returns:\n'
        code += '            创建的动画组\n'
        code += '        """\n'
        code += '        group = QSequentialAnimationGroup()\n'
        code += '        for animation in animations:\n'
        code += '            group.addAnimation(animation)\n'
        code += '        return group\n'
        
        return code

# 测试代码
if __name__ == "__main__":
    motion_system = MotionSystem()
    motion_data = motion_system.load_motion_json()
    print("动效数据已加载:", motion_data.keys())
    print("标准持续时间:", motion_system.get_duration_ms("standard"), "毫秒")
    print("标准缓动曲线:", motion_system.get_easing_curve("standard")) 