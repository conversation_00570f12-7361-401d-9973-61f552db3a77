#!/usr/bin/env python3
"""
快速Windows测试脚本
验证OmniLink主从服务器核心功能和代码完整性
"""

import os
import sys
import json
import importlib.util
from pathlib import Path
from datetime import datetime

def check_file_exists(file_path: str) -> bool:
    """检查文件是否存在"""
    return Path(file_path).exists()

def analyze_python_file(file_path: str) -> dict:
    """分析Python文件内容"""
    if not check_file_exists(file_path):
        return {"exists": False}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        return {
            "exists": True,
            "line_count": len(lines),
            "has_imports": any("import" in line for line in lines[:30]),
            "has_classes": "class " in content,
            "has_functions": "def " in content or "async def" in content,
            "has_docstrings": '"""' in content,
            "has_todos": "TODO" in content.upper(),
            "has_async": "async " in content,
            "file_size": len(content),
            "import_count": len([line for line in lines if line.strip().startswith(('import ', 'from '))]),
            "class_count": content.count('class '),
            "function_count": content.count('def ') + content.count('async def')
        }
    except Exception as e:
        return {"exists": True, "error": str(e)}

def main():
    print("=" * 80)
    print("OmniLink 主从服务器快速功能验证")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 核心代码文件验证
    core_files = {
        "主服务器主程序": "src/omnilink_main/main.py",
        "空闲会话管理器": "src/omnilink_main/core/idle_session_manager.py",
        "连接控制服务": "src/omnilink_main/services/connection_control_service.py",
        "从服务器端点": "src/omnilink_main/api/v1/endpoints/slave_servers.py",
        "设备端点": "src/omnilink_main/api/v1/endpoints/devices.py",
        "从服务器主程序": "slave_server/main.py",
        "从服务器配置": "slave_server/core/config.py",
        "WebSocket管理器": "src/omnilink_main/communication/ws_manager.py",
        "端口池管理": "src/omnilink_main/utils/port_pool.py"
    }
    
    print("\n🔍 核心代码文件验证:")
    print("-" * 60)
    
    total_files = len(core_files)
    existing_files = 0
    total_lines = 0
    total_classes = 0
    total_functions = 0
    
    for name, file_path in core_files.items():
        analysis = analyze_python_file(file_path)
        
        if analysis["exists"] and "error" not in analysis:
            existing_files += 1
            total_lines += analysis["line_count"]
            total_classes += analysis.get("class_count", 0)
            total_functions += analysis.get("function_count", 0)
            
            status = "✅"
            details = f"({analysis['line_count']} 行, {analysis.get('class_count', 0)} 类, {analysis.get('function_count', 0)} 函数)"
            
            if analysis.get("has_todos"):
                details += " ⚠️ 含TODO"
            
        elif analysis["exists"]:
            status = "❌"
            details = f"(读取错误: {analysis.get('error', '未知')})"
        else:
            status = "❌"
            details = "(文件不存在)"
        
        print(f"{status} {name:<20}: {details}")
    
    # 数据库模型验证
    print(f"\n🗄️ 数据库模型验证:")
    print("-" * 60)
    
    model_files = {
        "用户模型": "common/models/user.py",
        "设备模型": "common/models/device.py",
        "从服务器模型": "common/models/slave_server.py",
        "组织模型": "common/models/organization.py",
        "角色模型": "common/models/role.py",
        "审计日志模型": "common/models/audit_log.py",
        "策略模型": "common/models/policy.py"
    }
    
    model_count = 0
    for name, file_path in model_files.items():
        if check_file_exists(file_path):
            model_count += 1
            print(f"✅ {name}")
        else:
            print(f"❌ {name}: 文件不存在")
    
    # API Schema验证
    print(f"\n📋 API Schema验证:")
    print("-" * 60)
    
    schema_files = {
        "设备Schema": "common/schemas/device_schema.py",
        "用户Schema": "common/schemas/user_schema.py",
        "从服务器Schema": "common/schemas/slave_server_schema.py",
        "API响应Schema": "common/schemas/api_schema.py",
        "状态Schema": "common/schemas/status_schema.py",
        "令牌Schema": "common/schemas/token_schema.py"
    }
    
    schema_count = 0
    for name, file_path in schema_files.items():
        if check_file_exists(file_path):
            schema_count += 1
            print(f"✅ {name}")
        else:
            print(f"❌ {name}: 文件不存在")
    
    # 部署配置验证
    print(f"\n🐳 部署配置验证:")
    print("-" * 60)
    
    deployment_files = {
        "Docker Compose": "docker-compose.yaml",
        "环境配置": "app.env",
        "主服务器Dockerfile": "deployment/dockerfiles/Dockerfile.main",
        "从服务器Dockerfile": "deployment/dockerfiles/Dockerfile.slave",
        "生产部署脚本": "deploy_production.py",
        "通信测试脚本": "test_main_slave_communication.py"
    }
    
    deployment_count = 0
    for name, file_path in deployment_files.items():
        if check_file_exists(file_path):
            deployment_count += 1
            print(f"✅ {name}")
        else:
            print(f"❌ {name}: 文件不存在")
    
    # 关键功能实现检查
    print(f"\n⚙️ 关键功能实现检查:")
    print("-" * 60)
    
    # 检查空闲会话管理器
    idle_session_file = "src/omnilink_main/core/idle_session_manager.py"
    if check_file_exists(idle_session_file):
        with open(idle_session_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        features = []
        if "class IdleSessionManager" in content:
            features.append("会话管理类")
        if "start_session" in content:
            features.append("会话启动")
        if "end_session" in content:
            features.append("会话结束")
        if "_cleanup_loop" in content:
            features.append("清理循环")
        if "timeout" in content.lower():
            features.append("超时处理")
        
        print(f"✅ 空闲会话管理器: {', '.join(features)}")
    else:
        print("❌ 空闲会话管理器: 文件不存在")
    
    # 检查连接控制服务
    connection_service_file = "src/omnilink_main/services/connection_control_service.py"
    if check_file_exists(connection_service_file):
        with open(connection_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        features = []
        if "class ConnectionControlService" in content:
            features.append("连接控制类")
        if "request_device_connection" in content:
            features.append("连接请求")
        if "disconnect_device" in content:
            features.append("设备断开")
        if "port_pool" in content:
            features.append("端口管理")
        if "ws_manager" in content:
            features.append("WebSocket集成")
        
        print(f"✅ 连接控制服务: {', '.join(features)}")
    else:
        print("❌ 连接控制服务: 文件不存在")
    
    # 检查从服务器实现
    slave_main_file = "slave_server/main.py"
    if check_file_exists(slave_main_file):
        with open(slave_main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        features = []
        if "class SlaveServerAgent" in content:
            features.append("从服务器代理")
        if "_heartbeat_loop" in content:
            features.append("心跳循环")
        if "VirtualHereService" in content:
            features.append("VirtualHere集成")
        if "psutil" in content:
            features.append("系统监控")
        if "websockets" in content:
            features.append("WebSocket通信")
        
        print(f"✅ 从服务器实现: {', '.join(features)}")
    else:
        print("❌ 从服务器实现: 文件不存在")
    
    # 生成总结报告
    print(f"\n📊 验证总结:")
    print("=" * 60)
    
    file_completeness = (existing_files / total_files) * 100
    model_completeness = (model_count / len(model_files)) * 100
    schema_completeness = (schema_count / len(schema_files)) * 100
    deployment_completeness = (deployment_count / len(deployment_files)) * 100
    
    print(f"核心代码文件: {existing_files}/{total_files} ({file_completeness:.1f}%)")
    print(f"数据库模型: {model_count}/{len(model_files)} ({model_completeness:.1f}%)")
    print(f"API Schema: {schema_count}/{len(schema_files)} ({schema_completeness:.1f}%)")
    print(f"部署配置: {deployment_count}/{len(deployment_files)} ({deployment_completeness:.1f}%)")
    print(f"总代码行数: ~{total_lines:,} 行")
    print(f"总类数: {total_classes}")
    print(f"总函数数: {total_functions}")
    
    overall_score = (file_completeness + model_completeness + schema_completeness + deployment_completeness) / 4
    
    print(f"\n🎯 总体完成度: {overall_score:.1f}%")
    
    if overall_score >= 90:
        print("🎉 优秀！项目实现非常完整，可以立即进行Docker部署。")
        print("\n📋 建议下一步操作:")
        print("1. 确保Docker Desktop已启动")
        print("2. 运行: python windows_docker_deploy.py")
        print("3. 访问 http://localhost:8000/docs 查看API文档")
        print("4. 使用 firefly/bro2fhz12 登录管理界面")
    elif overall_score >= 80:
        print("✅ 良好！项目实现基本完整，可以尝试部署。")
        print("建议修复缺失文件后进行Docker部署。")
    elif overall_score >= 70:
        print("⚠️ 一般！项目实现有一定完整性，但存在较多缺失。")
        print("建议补充缺失的关键文件后再进行部署。")
    else:
        print("❌ 不足！项目实现不完整，需要大量补充。")
        print("建议先完善核心功能实现。")
    
    # OED.md合规性检查
    print(f"\n📋 OED.md合规性检查:")
    print("-" * 60)
    
    oed_requirements = [
        ("IdleSessionManager", "src/omnilink_main/core/idle_session_manager.py"),
        ("ConnectionControlService", "src/omnilink_main/services/connection_control_service.py"),
        ("端口池管理", "src/omnilink_main/utils/port_pool.py"),
        ("WebSocket通信", "src/omnilink_main/communication/ws_manager.py"),
        ("VirtualHere集成", "slave_server/services/vh_service.py"),
        ("设备监控", "slave_server/services/device_monitor.py"),
        ("Docker部署", "docker-compose.yaml"),
        ("环境配置", "app.env")
    ]
    
    oed_compliance = 0
    for requirement, file_path in oed_requirements:
        if check_file_exists(file_path):
            print(f"✅ {requirement}")
            oed_compliance += 1
        else:
            print(f"❌ {requirement}: 缺失")
    
    oed_score = (oed_compliance / len(oed_requirements)) * 100
    print(f"\nOED.md合规性: {oed_compliance}/{len(oed_requirements)} ({oed_score:.1f}%)")
    
    print("\n" + "=" * 80)
    print("验证完成！")
    
    if overall_score >= 85 and oed_score >= 85:
        print("\n🚀 系统已准备就绪，可以进行Docker部署和功能测试！")
        print("建议运行: python windows_docker_deploy.py")
    
    print("=" * 80)
    
    # 保存验证报告
    report = {
        "timestamp": datetime.now().isoformat(),
        "summary": {
            "overall_score": overall_score,
            "file_completeness": file_completeness,
            "model_completeness": model_completeness,
            "schema_completeness": schema_completeness,
            "deployment_completeness": deployment_completeness,
            "oed_compliance": oed_score,
            "total_lines": total_lines,
            "total_classes": total_classes,
            "total_functions": total_functions
        },
        "recommendation": "立即进行Docker部署" if overall_score >= 85 else "需要修复缺失组件"
    }
    
    with open("quick_validation_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("详细验证报告已保存到 quick_validation_report.json")

if __name__ == "__main__":
    main() 