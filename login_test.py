import requests
import json

def test_login():
    url = "http://127.0.0.1:8000/api/v1/auth/token"
    data = {
        "username": "admin",
        "password": "password"
    }
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    try:
        response = requests.post(url, data=data, headers=headers)
        response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
        
        print("Status Code:", response.status_code)
        try:
            response_json = response.json()
            print("Response JSON:", json.dumps(response_json, indent=2))
        except json.JSONDecodeError:
            print("Response content is not valid JSON:")
            print(response.text)

    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    test_login() 