<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康监控 - 主从服务器管理系统</title>
    
    <!-- Material Design Web Components -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Material Design Components CSS -->
    <link rel="stylesheet" href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css">
    
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="/static/css/admin_material.css">
    
    <!-- Chart.js 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
</head>
<body class="mdc-typography">
    <div class="app-container">
        <!-- 顶部应用栏 -->
        <header class="mdc-top-app-bar mdc-top-app-bar--fixed">
            <div class="mdc-top-app-bar__row">
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-start">
                    <button class="material-icons mdc-top-app-bar__navigation-icon mdc-icon-button" id="menu-button">menu</button>
                    <span class="mdc-top-app-bar__title">健康监控</span>
                </section>
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-end" role="toolbar">
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="refresh-button" aria-label="刷新">refresh</button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="theme-toggle" aria-label="切换主题">dark_mode</button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="user-menu-button" aria-label="用户菜单">account_circle</button>
                    
                    <!-- 用户菜单 -->
                    <div class="mdc-menu mdc-menu-surface" id="user-menu">
                        <ul class="mdc-list" role="menu" aria-hidden="true" aria-orientation="vertical" tabindex="-1">
                            <li class="mdc-list-item" role="menuitem">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="mdc-list-item__text">个人设置</span>
                            </li>
                            <li class="mdc-list-divider" role="separator"></li>
                            <li class="mdc-list-item" role="menuitem" id="logout-button" data-logout-url="/auth/logout">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="mdc-list-item__text">退出登录</span>
                            </li>
                        </ul>
                    </div>
                </section>
            </div>
        </header>
        
        <div class="app-content mdc-top-app-bar--fixed-adjust">
            <!-- 侧边导航抽屉 -->
            <aside class="mdc-drawer mdc-drawer--dismissible">
                <div class="mdc-drawer__content">
                    <nav class="mdc-list">
                        <a class="mdc-list-item" href="/admin/dashboard">
                            <span class="material-icons mdc-list-item__graphic">dashboard</span>
                            <span class="mdc-list-item__text">控制面板</span>
                        </a>
                        <a class="mdc-list-item" href="/admin/group_management">
                            <span class="material-icons mdc-list-item__graphic">groups</span>
                            <span class="mdc-list-item__text">群组管理</span>
                        </a>
                        <a class="mdc-list-item" href="/admin/migration_wizard">
                            <span class="material-icons mdc-list-item__graphic">switch_access_shortcut</span>
                            <span class="mdc-list-item__text">迁移向导</span>
                        </a>
                        <a class="mdc-list-item" href="/admin/alerts">
                            <span class="material-icons mdc-list-item__graphic">notifications</span>
                            <span class="mdc-list-item__text">告警信息</span>
                        </a>
                        <a class="mdc-list-item mdc-list-item--activated" href="/admin/health_monitor" aria-current="page">
                            <span class="material-icons mdc-list-item__graphic">monitoring</span>
                            <span class="mdc-list-item__text">健康监控</span>
                        </a>
                        
                        <hr class="mdc-list-divider">
                        
                        <h6 class="mdc-list-group__subheader">系统</h6>
                        <a class="mdc-list-item" href="/admin/settings">
                            <span class="material-icons mdc-list-item__graphic">settings</span>
                            <span class="mdc-list-item__text">系统设置</span>
                        </a>
                        <a class="mdc-list-item" href="/admin/logs">
                            <span class="material-icons mdc-list-item__graphic">receipt_long</span>
                            <span class="mdc-list-item__text">系统日志</span>
                        </a>
                    </nav>
                </div>
            </aside>
            
            <!-- 主内容区域 -->
            <div class="mdc-drawer-app-content">
                <main class="main-content">
                    <div class="page-content">
                        <h1 class="mdc-typography--headline4">系统健康监控</h1>
                        
                        <!-- 系统状态卡片 -->
                        <div class="dashboard-cards">
                            <!-- CPU卡片 -->
                            <div class="mdc-card dashboard-card">
                                <div class="mdc-card__primary-action">
                                    <div class="card-content">
                                        <div class="card-data">
                                            <h2 class="mdc-typography--headline6">CPU使用率</h2>
                                            <div class="card-numbers">
                                                <span id="cpuUsage" class="primary-value">0%</span>
                                            </div>
                                            <div class="gauge-container">
                                                <canvas id="cpuGauge" height="100"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 内存卡片 -->
                            <div class="mdc-card dashboard-card">
                                <div class="mdc-card__primary-action">
                                    <div class="card-content">
                                        <div class="card-data">
                                            <h2 class="mdc-typography--headline6">内存使用率</h2>
                                            <div class="card-numbers">
                                                <span id="memoryUsage" class="primary-value">0%</span>
                                            </div>
                                            <div class="gauge-container">
                                                <canvas id="memoryGauge" height="100"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 磁盘卡片 -->
                            <div class="mdc-card dashboard-card">
                                <div class="mdc-card__primary-action">
                                    <div class="card-content">
                                        <div class="card-data">
                                            <h2 class="mdc-typography--headline6">磁盘使用率</h2>
                                            <div class="card-numbers">
                                                <span id="diskUsage" class="primary-value">0%</span>
                                            </div>
                                            <div class="gauge-container">
                                                <canvas id="diskGauge" height="100"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 网络卡片 -->
                            <div class="mdc-card dashboard-card">
                                <div class="mdc-card__primary-action">
                                    <div class="card-content">
                                        <div class="card-data">
                                            <h2 class="mdc-typography--headline6">网络流量</h2>
                                            <div class="card-numbers">
                                                <span id="networkTraffic" class="primary-value">0 Mbps</span>
                                            </div>
                                            <div class="gauge-container">
                                                <canvas id="networkGauge" height="100"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 从服务器状态表格 -->
                        <div class="mdc-card table-card">
                            <div class="mdc-card__primary-action">
                                <div class="table-header">
                                    <span class="material-icons">dns</span>
                                    <h2 class="mdc-typography--headline6">从服务器状态</h2>
                                </div>
                                <div class="table-container">
                                    <table class="mdc-data-table__table">
                                        <thead>
                                            <tr class="mdc-data-table__header-row">
                                                <th class="mdc-data-table__header-cell">服务器ID</th>
                                                <th class="mdc-data-table__header-cell">状态</th>
                                                <th class="mdc-data-table__header-cell">CPU使用率</th>
                                                <th class="mdc-data-table__header-cell">内存使用率</th>
                                                <th class="mdc-data-table__header-cell">磁盘使用率</th>
                                                <th class="mdc-data-table__header-cell">设备连接数</th>
                                                <th class="mdc-data-table__header-cell">最后心跳时间</th>
                                            </tr>
                                        </thead>
                                        <tbody id="serversTable">
                                            <tr class="mdc-data-table__row">
                                                <td class="mdc-data-table__cell" colspan="7">加载中...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 历史趋势图 -->
                        <div class="mdc-card chart-card">
                            <div class="mdc-card__primary-action">
                                <div class="chart-header">
                                    <span class="material-icons">insights</span>
                                    <h2 class="mdc-typography--headline6">系统资源历史趋势</h2>
                                </div>
                                <div class="chart-container" style="height: 300px;">
                                    <canvas id="resourceTrendChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>
    
    <!-- Material Design Components JS -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>
    
    <!-- WebSocket客户端 -->
    <script src="/static/js/websocket_client.js"></script>
    
    <!-- 自定义脚本 -->
    <script src="/static/js/admin_material.js"></script>
    
    <!-- 监控页面脚本 -->
    <script>
        // 仪表盘对象存储
        window.gaugeCharts = {};
        
        // 初始化监控WebSocket
        let monitorSocket = null;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化Material组件
            initializeMDC();
            
            // 初始化仪表盘
            initGauges();
            
            // 初始化趋势图
            initTrendChart();
            
            // 初始化监控Socket连接
            initializeMonitoringSocket();
            
            // 加载初始数据
            loadInitialData();
        });
        
        // 初始化仪表盘
        function initGauges() {
            // CPU仪表盘
            const cpuGaugeCtx = document.getElementById('cpuGauge').getContext('2d');
            window.gaugeCharts.cpu = new Chart(cpuGaugeCtx, {
                type: 'doughnut',
                data: {
                    datasets: [{
                        data: [0, 100],
                        backgroundColor: [
                            '#1976D2',
                            '#F5F5F5'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    cutout: '70%',
                    circumference: 180,
                    rotation: -90,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    animation: {
                        animateRotate: true,
                        animateScale: false
                    }
                }
            });
            
            // 内存仪表盘
            const memoryGaugeCtx = document.getElementById('memoryGauge').getContext('2d');
            window.gaugeCharts.memory = new Chart(memoryGaugeCtx, {
                type: 'doughnut',
                data: {
                    datasets: [{
                        data: [0, 100],
                        backgroundColor: [
                            '#009688',
                            '#F5F5F5'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    cutout: '70%',
                    circumference: 180,
                    rotation: -90,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    animation: {
                        animateRotate: true,
                        animateScale: false
                    }
                }
            });
            
            // 磁盘仪表盘
            const diskGaugeCtx = document.getElementById('diskGauge').getContext('2d');
            window.gaugeCharts.disk = new Chart(diskGaugeCtx, {
                type: 'doughnut',
                data: {
                    datasets: [{
                        data: [0, 100],
                        backgroundColor: [
                            '#FF9800',
                            '#F5F5F5'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    cutout: '70%',
                    circumference: 180,
                    rotation: -90,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    animation: {
                        animateRotate: true,
                        animateScale: false
                    }
                }
            });
            
            // 网络仪表盘
            const networkGaugeCtx = document.getElementById('networkGauge').getContext('2d');
            window.gaugeCharts.network = new Chart(networkGaugeCtx, {
                type: 'doughnut',
                data: {
                    datasets: [{
                        data: [0, 100],
                        backgroundColor: [
                            '#673AB7',
                            '#F5F5F5'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    cutout: '70%',
                    circumference: 180,
                    rotation: -90,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    animation: {
                        animateRotate: true,
                        animateScale: false
                    }
                }
            });
        }
        
        // 初始化趋势图
        function initTrendChart() {
            const ctx = document.getElementById('resourceTrendChart').getContext('2d');
            window.resourceTrendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array(24).fill('').map((_, i) => `${i}:00`),
                    datasets: [
                        {
                            label: 'CPU使用率',
                            data: Array(24).fill(0).map(() => Math.floor(Math.random() * 60) + 20),
                            borderColor: '#1976D2',
                            backgroundColor: 'rgba(25, 118, 210, 0.1)',
                            tension: 0.3,
                            fill: false
                        },
                        {
                            label: '内存使用率',
                            data: Array(24).fill(0).map(() => Math.floor(Math.random() * 40) + 30),
                            borderColor: '#009688',
                            backgroundColor: 'rgba(0, 150, 136, 0.1)',
                            tension: 0.3,
                            fill: false
                        },
                        {
                            label: '磁盘使用率',
                            data: Array(24).fill(0).map(() => Math.floor(Math.random() * 30) + 40),
                            borderColor: '#FF9800',
                            backgroundColor: 'rgba(255, 152, 0, 0.1)',
                            tension: 0.3,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '使用率 (%)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '时间'
                            }
                        }
                    }
                }
            });
        }
        
        // 初始化监控Socket连接
        function initializeMonitoringSocket() {
            monitorSocket = createMonitoringSocket({
                onConnected: () => {
                    console.log('监控WebSocket连接成功');
                    showNotification('连接成功', '已成功连接到监控服务', 'success');
                    document.getElementById('connection-status').textContent = '已连接';
                    document.getElementById('connection-status').classList.remove('status-offline');
                    document.getElementById('connection-status').classList.add('status-online');
                },
                onDisconnected: (info) => {
                    console.log('监控WebSocket连接断开', info);
                    showNotification('连接断开', '与监控服务的连接已断开，正在尝试重新连接', 'warning');
                    document.getElementById('connection-status').textContent = '已断开';
                    document.getElementById('connection-status').classList.remove('status-online');
                    document.getElementById('connection-status').classList.add('status-offline');
                },
                onData: (data) => {
                    updateMonitoringUI(data);
                },
                onError: (error) => {
                    console.error('监控WebSocket错误', error);
                    showNotification('连接错误', '监控服务连接出错，请刷新页面重试', 'error');
                }
            });
            
            // 建立连接
            monitorSocket.connect();
            
            // 设置页面退出时关闭连接
            window.addEventListener('beforeunload', () => {
                if (monitorSocket) {
                    monitorSocket.disconnect();
                }
            });
        }
        
        // 加载初始数据
        function loadInitialData() {
            // 从API加载系统状态
            fetch('/api/monitoring/system')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateSystemStatus(data.data);
                    }
                })
                .catch(error => {
                    console.error('获取系统状态失败:', error);
                    showNotification('数据加载失败', '无法获取系统状态数据', 'error');
                });
            
            // 从API加载服务器状态
            fetch('/api/monitoring/servers')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateServersStatus(data.data);
                    }
                })
                .catch(error => {
                    console.error('获取服务器状态失败:', error);
                    showNotification('数据加载失败', '无法获取服务器状态数据', 'error');
                });
            
            // 从API加载历史数据 - CPU
            fetch('/api/monitoring/history/cpu?duration=day')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateHistoryChart(data.data, 0);
                    }
                })
                .catch(error => {
                    console.error('获取CPU历史数据失败:', error);
                });
            
            // 从API加载历史数据 - 内存
            fetch('/api/monitoring/history/memory?duration=day')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateHistoryChart(data.data, 1);
                    }
                })
                .catch(error => {
                    console.error('获取内存历史数据失败:', error);
                });
            
            // 从API加载历史数据 - 磁盘
            fetch('/api/monitoring/history/disk?duration=day')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateHistoryChart(data.data, 2);
                    }
                })
                .catch(error => {
                    console.error('获取磁盘历史数据失败:', error);
                });
        }
        
        // 更新系统状态UI
        function updateSystemStatus(data) {
            // 更新CPU仪表盘
            const cpuGauge = Gauge.Collection.get(document.getElementById('cpuGauge'));
            if (cpuGauge) {
                cpuGauge.set(data.cpu.percent);
                document.getElementById('cpuUsage').textContent = data.cpu.percent.toFixed(1) + '%';
                document.getElementById('cpu-cores').textContent = data.cpu.cores;
            }
            
            // 更新内存仪表盘
            const memoryGauge = Gauge.Collection.get(document.getElementById('memoryGauge'));
            if (memoryGauge) {
                memoryGauge.set(data.memory.percent);
                document.getElementById('memoryUsage').textContent = data.memory.percent.toFixed(1) + '%';
                document.getElementById('memory-total').textContent = formatBytes(data.memory.total);
                document.getElementById('memory-used').textContent = formatBytes(data.memory.used);
            }
            
            // 更新磁盘仪表盘
            const diskGauge = Gauge.Collection.get(document.getElementById('diskGauge'));
            if (diskGauge) {
                diskGauge.set(data.disk.percent);
                document.getElementById('diskUsage').textContent = data.disk.percent.toFixed(1) + '%';
                document.getElementById('disk-total').textContent = formatBytes(data.disk.total);
                document.getElementById('disk-free').textContent = formatBytes(data.disk.free);
            }
            
            // 更新网络仪表盘
            const networkGauge = Gauge.Collection.get(document.getElementById('networkGauge'));
            if (networkGauge) {
                // 计算网络利用率（简化示例）
                const networkUsage = Math.min(100, Math.floor(Math.random() * 100));
                networkGauge.set(networkUsage);
                document.getElementById('networkTraffic').textContent = networkUsage.toFixed(1) + '%';
                document.getElementById('network-sent').textContent = formatBytes(data.network.bytes_sent);
                document.getElementById('network-received').textContent = formatBytes(data.network.bytes_recv);
            }
            
            // 更新最后更新时间
            const lastUpdated = new Date(data.timestamp);
            document.getElementById('last-updated').textContent = `最后更新: ${lastUpdated.toLocaleString()}`;
        }
        
        // 更新服务器状态表格
        function updateServersStatus(servers) {
            const tableBody = document.getElementById('serversTable');
            if (!tableBody) return;
            
            // 清空表格
            tableBody.innerHTML = '';
            
            if (servers.length === 0) {
                // 无数据时显示提示
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="7" class="text-center">没有可用的服务器数据</td>';
                tableBody.appendChild(row);
                return;
            }
            
            // 添加服务器数据
            servers.forEach(server => {
                const row = document.createElement('tr');
                
                // 设置状态类
                let statusClass = '';
                let statusText = '';
                
                switch (server.status) {
                    case 'online':
                        statusClass = 'status-online';
                        statusText = '在线';
                        break;
                    case 'warning':
                        statusClass = 'status-warning';
                        statusText = '警告';
                        break;
                    case 'offline':
                        statusClass = 'status-offline';
                        statusText = '离线';
                        break;
                    default:
                        statusClass = 'status-unknown';
                        statusText = '未知';
                        break;
                }
                
                row.innerHTML = `
                    <td>${server.name}</td>
                    <td>${server.ip}</td>
                    <td><span class="status-indicator ${statusClass}">${statusText}</span></td>
                    <td>${server.cpu_percent}%</td>
                    <td>${server.memory_percent}%</td>
                    <td>${server.disk_percent}%</td>
                    <td>
                        <button class="mdc-icon-button material-icons" title="查看详情" onclick="viewServerDetails('${server.id}')">visibility</button>
                    </td>
                `;
                
                tableBody.appendChild(row);
            });
        }
        
        // 更新历史趋势图
        function updateHistoryChart(data, datasetIndex) {
            if (!window.resourceTrendChart) return;
            
            const chart = window.resourceTrendChart;
            const points = data.points || [];
            
            // 转换数据点
            const values = points.map(point => {
                return typeof point.value === 'object' ? 0 : point.value;
            });
            
            // 更新对应的数据集
            chart.data.datasets[datasetIndex].data = values;
            chart.update();
        }
        
        // 显示服务器详情对话框
        function viewServerDetails(serverId) {
            // 获取服务器详情
            fetch(`/api/monitoring/server/${serverId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const server = data.data;
                        
                        // 构建详情HTML
                        let detailsHtml = `
                            <div class="server-details">
                                <h2>${server.name}</h2>
                                <div class="detail-row">
                                    <span class="detail-label">ID:</span>
                                    <span class="detail-value">${server.id}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">IP地址:</span>
                                    <span class="detail-value">${server.ip}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">类型:</span>
                                    <span class="detail-value">${server.type === 'main' ? '主服务器' : '从服务器'}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">状态:</span>
                                    <span class="detail-value status-indicator ${getStatusClass(server.status)}">${getStatusText(server.status)}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">运行时间:</span>
                                    <span class="detail-value">${server.uptime || 'N/A'}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">最后在线:</span>
                                    <span class="detail-value">${new Date(server.last_seen).toLocaleString()}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">CPU使用率:</span>
                                    <span class="detail-value">${server.cpu_percent}%</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">内存使用率:</span>
                                    <span class="detail-value">${server.memory_percent}%</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">磁盘使用率:</span>
                                    <span class="detail-value">${server.disk_percent}%</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">连接设备数:</span>
                                    <span class="detail-value">${server.connected_devices}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">活跃用户数:</span>
                                    <span class="detail-value">${server.active_users}</span>
                                </div>
                            </div>
                        `;
                        
                        // 显示对话框
                        const dialog = document.getElementById('server-details-dialog');
                        const dialogContent = document.getElementById('server-details-content');
                        
                        dialogContent.innerHTML = detailsHtml;
                        dialog.classList.add('open');
                        
                        // 添加关闭按钮事件
                        document.getElementById('close-dialog-button').onclick = () => {
                            dialog.classList.remove('open');
                        };
                    } else {
                        showNotification('获取失败', '无法获取服务器详情', 'error');
                    }
                })
                .catch(error => {
                    console.error('获取服务器详情失败:', error);
                    showNotification('获取失败', '无法获取服务器详情', 'error');
                });
        }
        
        // 获取状态类名
        function getStatusClass(status) {
            switch (status) {
                case 'online': return 'status-online';
                case 'warning': return 'status-warning';
                case 'offline': return 'status-offline';
                default: return 'status-unknown';
            }
        }
        
        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 'online': return '在线';
                case 'warning': return '警告';
                case 'offline': return '离线';
                default: return '未知';
            }
        }
        
        // 格式化字节数为可读格式
        function formatBytes(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
            
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }
        
        // 手动刷新数据
        function refreshData() {
            loadInitialData();
            showNotification('刷新成功', '已重新加载监控数据', 'success');
        }
        
        // 全局更新UI函数，被WebSocket调用
        window.updateMonitoringUI = function(data) {
            if (data.system) {
                updateSystemStatus(data.system);
            }
            
            if (data.servers) {
                updateServersStatus(data.servers);
            }
        };
        
        // 全局更新服务器状态函数，被WebSocket调用
        window.updateServersStatus = function(data) {
            updateServersStatus(data);
        };
    </script>
</body>
</html> 