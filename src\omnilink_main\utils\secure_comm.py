import lz4.frame
from Crypto.Cipher import ChaCha20_Poly1305
from Crypto.Protocol.KDF import HKDF
from Crypto.Hash import SHA256
import os
import json

# This should be loaded securely, not hardcoded directly if possible,
# or at least obfuscated during build/packaging.
MASTER_KEY_SEED = b"firefly"
HKDF_CONTEXT = b"OmniLinkAppCommV1"
NONCE_SIZE = 12  # ChaCha20_Poly1305 nonce size
TAG_SIZE = 16    # ChaCha20_Poly1305 tag size

def derive_key(session_salt: bytes, master_seed: bytes = MASTER_KEY_SEED, context: bytes = HKDF_CONTEXT) -> bytes:
    """
    Derives a 32-byte encryption key using HKDF.
    """
    if not isinstance(session_salt, bytes) or not session_salt:
        raise ValueError("session_salt must be non-empty bytes.")
    if not isinstance(master_seed, bytes) or not master_seed:
        raise ValueError("master_seed must be non-empty bytes.")
        
    key = HKDF(master_seed, 32, session_salt, SHA256, context=context)
    return key

def encrypt_message(data_payload: dict, key: bytes) -> bytes:
    """
    Serializes, compresses, and encrypts a dictionary payload.
    Returns bytes: nonce + tag + ciphertext.
    """
    if not isinstance(data_payload, dict):
        raise TypeError("data_payload must be a dictionary.")
    if not isinstance(key, bytes) or len(key) != 32:
        raise ValueError("key must be 32 bytes long.")

    try:
        message_bytes = json.dumps(data_payload).encode('utf-8')
        compressed_data = lz4.frame.compress(message_bytes)
        
        cipher = ChaCha20_Poly1305.new(key=key) # Auto-generates nonce
        ciphertext, tag = cipher.encrypt_and_digest(compressed_data)
        
        return cipher.nonce + tag + ciphertext
    except Exception as e:
        # Log a more specific error in a real application
        raise RuntimeError(f"Encryption process failed: {e}")

def decrypt_message(encrypted_payload: bytes, key: bytes) -> dict:
    """
    Decrypts, decompresses, and deserializes an encrypted byte payload into a dictionary.
    Expects bytes in format: nonce + tag + ciphertext.
    """
    if not isinstance(encrypted_payload, bytes):
        raise TypeError("encrypted_payload must be bytes.")
    if not isinstance(key, bytes) or len(key) != 32:
        raise ValueError("key must be 32 bytes long.")

    expected_min_len = NONCE_SIZE + TAG_SIZE
    if len(encrypted_payload) < expected_min_len:
        raise ValueError(f"Encrypted payload is too short. Minimum length: {expected_min_len} bytes.")
        
    nonce = encrypted_payload[:NONCE_SIZE]
    tag = encrypted_payload[NONCE_SIZE:NONCE_SIZE + TAG_SIZE]
    ciphertext = encrypted_payload[NONCE_SIZE + TAG_SIZE:]
    
    cipher = ChaCha20_Poly1305.new(key=key, nonce=nonce)
    
    try:
        decrypted_compressed_data = cipher.decrypt_and_verify(ciphertext, tag)
        decompressed_data = lz4.frame.decompress(decrypted_compressed_data)
        message_dict = json.loads(decompressed_data.decode('utf-8'))
        if not isinstance(message_dict, dict):
            raise ValueError("Decrypted payload is not a valid JSON dictionary.")
        return message_dict
    except ValueError as e:
        # This can be due to bad key, tampered message, or malformed payload after decryption
        raise ValueError(f"Decryption failed or message tampered: {e}")
    except Exception as e:
        # Log a more specific error
        raise RuntimeError(f"Decryption process failed: {e}")

# Example Usage (for testing purposes, not to be run directly in production like this)
if __name__ == '__main__':
    # This example assumes client and server have the same session_salt
    # In a real app, session_salt must be securely established per session.
    example_session_salt = os.urandom(16) 

    # Client side
    client_key = derive_key(example_session_salt)
    original_message = {"action": "echo", "data": "Hello, secure world!", "value": 12345}
    print(f"Original: {original_message}")
    
    encrypted = encrypt_message(original_message, client_key)
    print(f"Encrypted (first 30 bytes): {encrypted[:30]}... (len: {len(encrypted)})")
    
    # Server side (simulated)
    server_key = derive_key(example_session_salt) # Server uses the same salt
    if client_key != server_key:
        print("CRITICAL ERROR: Keys do not match despite same salt. Check KDF.")

    try:
        decrypted = decrypt_message(encrypted, server_key)
        print(f"Decrypted: {decrypted}")
        assert decrypted == original_message
        print("Encryption/Decryption test PASSED!")
    except Exception as e:
        print(f"Encryption/Decryption test FAILED: {e}")

    # Test tampering
    tampered_encrypted = bytearray(encrypted)
    if len(tampered_encrypted) > NONCE_SIZE + TAG_SIZE + 5:
        tampered_encrypted[NONCE_SIZE + TAG_SIZE + 5] = tampered_encrypted[NONCE_SIZE + TAG_SIZE + 5] ^ 0xFF # Flip a bit
        print("\nTesting tampered message...")
        try:
            decrypted_tampered = decrypt_message(bytes(tampered_encrypted), server_key)
            print(f"Decrypted tampered (should not happen): {decrypted_tampered}")
            print("Tampering test FAILED: Decryption succeeded unexpectedly.")
        except ValueError as e:
            print(f"Tampering test PASSED: Decryption failed as expected ({e})")
        except Exception as e:
            print(f"Tampering test FAILED with unexpected error: {e}")
    else:
        print("Skipping tampering test, encrypted message too short.")

    # Test with bad key
    bad_salt = os.urandom(16)
    bad_key = derive_key(bad_salt)
    print("\nTesting with bad key...")
    try:
        decrypted_bad_key = decrypt_message(encrypted, bad_key)
        print(f"Decrypted with bad key (should not happen): {decrypted_bad_key}")
        print("Bad key test FAILED: Decryption succeeded unexpectedly.")
    except ValueError as e:
        print(f"Bad key test PASSED: Decryption failed as expected ({e})")
    except Exception as e:
        print(f"Bad key test FAILED with unexpected error: {e}") 
