/**
 * 从服务器健康监控系统样式
 */

/* 全局样式 */
body {
    background-color: #f8f9fa;
}

/* 卡片样式增强 */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1rem;
    border: none;
    border-radius: 0.375rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 0.75rem 1rem;
}

/* 状态卡片样式 */
.card.bg-success, .card.bg-warning, .card.bg-danger, .card.bg-secondary {
    transition: transform 0.2s;
}

.card.bg-success:hover, .card.bg-warning:hover, .card.bg-danger:hover, .card.bg-secondary:hover {
    transform: translateY(-5px);
}

/* 服务器列表样式 */
.list-group-item {
    border-left: none;
    border-right: none;
    padding: 0.75rem 1rem;
    transition: background-color 0.2s;
}

.list-group-item.active {
    background-color: #f0f7ff;
    border-color: #dee2e6;
    color: #000;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

/* 进度条样式优化 */
.progress {
    height: 0.5rem;
    background-color: #e9ecef;
    border-radius: 1rem;
    overflow: hidden;
}

.progress-bar {
    border-radius: 1rem;
}

/* 表格样式美化 */
.table th {
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table-sm td, .table-sm th {
    padding: 0.5rem;
}

/* 图表区域美化 */
.chart-container {
    position: relative;
    margin: auto;
    height: 250px;
    margin-bottom: 1.5rem;
}

canvas {
    border-radius: 0.25rem;
    background-color: #fff;
    padding: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .card-header .d-flex input {
        margin-top: 0.5rem;
        width: 100% !important;
    }
    
    /* 改善移动端图表显示 */
    .col-md-6 {
        margin-bottom: 1.5rem;
    }
    
    /* 移动端状态卡片调整 */
    .row.mb-4 .col-md-3 {
        margin-bottom: 1rem;
    }
}

/* 状态徽章样式增强 */
.badge {
    padding: 0.35em 0.65em;
    font-weight: 500;
}

/* 加载指示器样式 */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* 导航栏样式 */
header {
    background-color: #fff;
}

header h1 {
    margin-bottom: 0;
}

/* 按钮美化 */
.btn-outline-primary, .btn-outline-secondary {
    border-width: 1px;
}

.btn-outline-primary:hover, .btn-outline-secondary:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

/* 点击波纹效果 */
.list-group-item {
    position: relative;
    overflow: hidden;
}

.list-group-item::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(0, 0, 0, 0.1);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.list-group-item:active::after {
    opacity: 1;
    width: 100%;
    height: 100%;
    border-radius: 0;
    transform: scale(0, 0) translate(-50%, -50%);
    transition: transform 0.3s, opacity 0.3s;
}

/* 搜索框样式 */
#serverSearch {
    border-radius: 1.5rem;
    padding-left: 1rem;
    border: 1px solid #ced4da;
    transition: box-shadow 0.15s ease-in-out;
}

#serverSearch:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    border-color: #86b7fe;
}

/* 刷新按钮旋转动画 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.refreshing {
    animation: spin 1s linear infinite;
}

/* 改进的详情视图样式 */
#serverDetail {
    min-height: 200px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
} 