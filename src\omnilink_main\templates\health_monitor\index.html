<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>从服务器健康监控</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/health_monitor.css') }}">
    <!-- 引入Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container-fluid">
        <header class="py-3 mb-3 border-bottom">
            <div class="container-fluid d-flex align-items-center justify-content-between">
                <h1 class="h4 mb-0">从服务器健康监控系统</h1>
                <div class="d-flex">
                    <button id="refreshBtn" class="btn btn-sm btn-outline-primary me-2">
                        <i class="bi bi-arrow-clockwise"></i> 刷新数据
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="groupDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            分组显示
                        </button>
                        <ul class="dropdown-menu" id="groupList" aria-labelledby="groupDropdown">
                            <li><a class="dropdown-item active" href="#" data-group="all">所有服务器</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </header>

        <!-- 状态概览区域 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <h5 class="card-title">健康</h5>
                        <p class="card-text display-4" id="healthyCount">0</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-warning">
                    <div class="card-body">
                        <h5 class="card-title">警告</h5>
                        <p class="card-text display-4" id="warningCount">0</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-danger">
                    <div class="card-body">
                        <h5 class="card-title">严重</h5>
                        <p class="card-text display-4" id="criticalCount">0</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-secondary">
                    <div class="card-body">
                        <h5 class="card-title">未知</h5>
                        <p class="card-text display-4" id="unknownCount">0</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 服务器列表和详情区域 -->
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">服务器列表</h5>
                            <input type="text" id="serverSearch" class="form-control form-control-sm w-50" placeholder="搜索服务器...">
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush" id="serverList">
                            <!-- 服务器列表将通过JavaScript动态加载 -->
                            <div class="d-flex justify-content-center py-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0" id="detailTitle">服务器详情</h5>
                    </div>
                    <div class="card-body" id="serverDetail">
                        <div class="text-center py-5 text-muted">
                            <i class="bi bi-info-circle fs-1"></i>
                            <p class="mt-3">请从左侧选择一个服务器查看详细信息</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 健康趋势图表区域 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">健康趋势</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <canvas id="cpuChart" height="200"></canvas>
                            </div>
                            <div class="col-md-6 mb-4">
                                <canvas id="memoryChart" height="200"></canvas>
                            </div>
                            <div class="col-md-6 mb-4">
                                <canvas id="diskChart" height="200"></canvas>
                            </div>
                            <div class="col-md-6 mb-4">
                                <canvas id="networkChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JS和自定义JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/health_monitor.js') }}"></script>
</body>
</html> 