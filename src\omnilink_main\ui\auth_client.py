import httpx
from nicegui import app, ui
from typing import <PERSON>ple, Callable
from functools import wraps
from ..core.config import settings

# In a real app, this should come from a config file.
# Since the UI and API are in the same process, we can use a relative URL.
# Use relative URL to avoid CORS issues in browser
BASE_URL = settings.API_V1_STR

async def login(username: str, password: str) -> Tuple[bool, str]:
    """
    Attempts to log in the user by calling the backend API.
    Stores the token in the browser's local storage on success.
    Returns (success, message).
    """
    try:
        async with httpx.AsyncClient() as client:
            # Note: FastAPI's OAuth2PasswordRequestForm expects form data, not JSON
            response = await client.post(
                f"{BASE_URL}/auth/login",
                data={"username": username, "password": password}
            )

            if response.status_code == 200:
                response_data = response.json()
                # API returns data wrapped in APIResponse format
                if response_data.get("success") and "data" in response_data:
                    token_data = response_data["data"]
                    access_token = token_data.get("access_token")
                    # Store the token in the browser's local storage
                    await app.storage.user.set('access_token', access_token)
                    return True, response_data.get("message", "Login successful!")
                else:
                    return False, response_data.get("message", "Login failed")
            else:
                try:
                    error_data = response.json()
                    return False, error_data.get("detail", "Unknown login error")
                except:
                    return False, f"Login failed with status {response.status_code}"

    except httpx.RequestError as e:
        return False, f"Could not connect to the server: {e}"

async def get_token() -> str | None:
    """Retrieves the access token from browser storage."""
    return await app.storage.user.get('access_token')

async def is_authenticated() -> bool:
    """Checks if a user is authenticated by verifying the presence of a token."""
    token = await get_token()
    return token is not None

def protected_page(func: Callable) -> Callable:
    """Decorator to protect a page, redirecting to '/' if not authenticated."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        if not await is_authenticated():
            ui.navigate.to('/')
            ui.notify('Please log in to access this page.', color='warning')
            return

        result = await func(*args, **kwargs)
        return result
    return wrapper

async def logout() -> None:
    """Logs the user out by clearing the token from storage."""
    await app.storage.user.clear()
    ui.navigate.to('/')

