#!/usr/bin/env python3
"""
OmniLink项目构建和部署脚本
此脚本自动化整个项目的构建、测试和部署流程
"""

import os
import sys
import subprocess
import logging
import time
import json
from pathlib import Path
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build_deploy.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class OmniLinkDeployment:
    """OmniLink项目部署管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.docker_compose_file = self.project_root / "docker-compose.yaml"
        self.env_file = self.project_root / "app.env"
        
    def check_prerequisites(self) -> bool:
        """检查部署前置条件"""
        logger.info("检查部署前置条件...")
        
        # 检查Docker是否可用
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, check=True)
            logger.info(f"Docker版本: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("Docker未安装或无法访问")
            return False
            
        # 检查Docker Compose是否可用
        try:
            result = subprocess.run(['docker-compose', '--version'], 
                                  capture_output=True, text=True, check=True)
            logger.info(f"Docker Compose版本: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("Docker Compose未安装或无法访问")
            return False
            
        # 检查必要文件是否存在
        required_files = [
            self.docker_compose_file,
            self.env_file,
            self.project_root / "deployment/dockerfiles/Dockerfile.main",
            self.project_root / "deployment/dockerfiles/Dockerfile.slave"
        ]
        
        for file_path in required_files:
            if not file_path.exists():
                logger.error(f"必要文件不存在: {file_path}")
                return False
                
        logger.info("所有前置条件检查通过")
        return True
    
    def clean_environment(self) -> bool:
        """清理现有环境"""
        logger.info("清理现有Docker环境...")
        
        try:
            # 停止并移除现有容器
            subprocess.run(['docker-compose', 'down', '-v'], 
                         cwd=self.project_root, check=True)
            logger.info("现有容器已停止并移除")
            
            # 清理未使用的Docker资源
            subprocess.run(['docker', 'system', 'prune', '-f'], 
                         check=True)
            logger.info("Docker系统清理完成")
            
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"环境清理失败: {e}")
            return False
    
    def build_images(self) -> bool:
        """构建Docker镜像"""
        logger.info("开始构建Docker镜像...")
        
        try:
            # 构建主服务器镜像
            logger.info("构建主服务器镜像...")
            subprocess.run(['docker-compose', 'build', '--no-cache', 'main-server'], 
                         cwd=self.project_root, check=True)
            
            # 构建从服务器镜像
            logger.info("构建从服务器镜像...")
            subprocess.run(['docker-compose', 'build', '--no-cache', 'slave-server'], 
                         cwd=self.project_root, check=True)
            
            logger.info("所有镜像构建完成")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"镜像构建失败: {e}")
            return False
    
    def start_services(self) -> bool:
        """启动所有服务"""
        logger.info("启动所有服务...")
        
        try:
            # 启动数据库和Redis服务
            logger.info("启动基础设施服务...")
            subprocess.run(['docker-compose', 'up', '-d', 'postgres-db', 'redis'], 
                         cwd=self.project_root, check=True)
            
            # 等待数据库就绪
            logger.info("等待数据库服务就绪...")
            time.sleep(10)
            
            # 启动主服务器
            logger.info("启动主服务器...")
            subprocess.run(['docker-compose', 'up', '-d', 'main-server'], 
                         cwd=self.project_root, check=True)
            
            # 等待主服务器就绪
            logger.info("等待主服务器就绪...")
            time.sleep(15)
            
            # 启动从服务器
            logger.info("启动从服务器...")
            subprocess.run(['docker-compose', 'up', '-d', 'slave-server'], 
                         cwd=self.project_root, check=True)
            
            logger.info("所有服务启动完成")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"服务启动失败: {e}")
            return False
    
    def check_service_health(self) -> bool:
        """检查服务健康状态"""
        logger.info("检查服务健康状态...")
        
        try:
            # 获取容器状态
            result = subprocess.run(['docker-compose', 'ps'], 
                                  cwd=self.project_root, 
                                  capture_output=True, text=True, check=True)
            
            logger.info("容器状态:")
            logger.info(result.stdout)
            
            # 检查主服务器API
            import requests
            try:
                response = requests.get('http://localhost:8000/api/v1/status', timeout=10)
                if response.status_code == 200:
                    logger.info("主服务器API响应正常")
                else:
                    logger.warning(f"主服务器API响应异常: {response.status_code}")
            except requests.RequestException as e:
                logger.warning(f"无法连接到主服务器API: {e}")
            
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"健康检查失败: {e}")
            return False
    
    def show_deployment_info(self):
        """显示部署信息"""
        logger.info("="*60)
        logger.info("OmniLink系统部署完成!")
        logger.info("="*60)
        logger.info("服务访问地址:")
        logger.info("  - 主服务器API: http://localhost:8000")
        logger.info("  - Web管理界面: http://localhost:8000")
        logger.info("  - API文档: http://localhost:8000/docs")
        logger.info("  - PostgreSQL: localhost:5432")
        logger.info("  - Redis: localhost:6379")
        logger.info("  - VirtualHere: localhost:7575")
        logger.info("="*60)
        logger.info("默认管理员账户:")
        logger.info("  - 用户名: firefly")
        logger.info("  - 密码: bro2fhz12")
        logger.info("="*60)
        logger.info("常用命令:")
        logger.info("  - 查看日志: docker-compose logs -f")
        logger.info("  - 停止服务: docker-compose down")
        logger.info("  - 重启服务: docker-compose restart")
        logger.info("="*60)
    
    def deploy(self) -> bool:
        """执行完整部署流程"""
        logger.info("开始OmniLink系统部署...")
        
        steps = [
            ("检查前置条件", self.check_prerequisites),
            ("清理环境", self.clean_environment),
            ("构建镜像", self.build_images),
            ("启动服务", self.start_services),
            ("健康检查", self.check_service_health)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"执行步骤: {step_name}")
            if not step_func():
                logger.error(f"步骤失败: {step_name}")
                return False
            logger.info(f"步骤完成: {step_name}")
        
        self.show_deployment_info()
        return True

def main():
    """主函数"""
    deployment = OmniLinkDeployment()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "clean":
            deployment.clean_environment()
        elif command == "build":
            deployment.build_images()
        elif command == "start":
            deployment.start_services()
        elif command == "health":
            deployment.check_service_health()
        elif command == "deploy":
            deployment.deploy()
        else:
            print("可用命令: clean, build, start, health, deploy")
    else:
        # 默认执行完整部署
        deployment.deploy()

if __name__ == "__main__":
    main() 