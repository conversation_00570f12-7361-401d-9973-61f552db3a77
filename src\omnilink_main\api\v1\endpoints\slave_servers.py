from fastapi import APIRouter, Depends, status, WebSocket, WebSocketDisconnect, HTTPException
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
import json
import logging
import asyncio

from src.omnilink_main.dependencies.db import get_db
from common.schemas.api_schema import APIResponse
from common.schemas.slave_server_schema import (
    SlaveServer as SlaveServerSchema, 
    SlaveServerCreate, 
    SlaveServerUpdate,
    SlaveServerRegister
)
from src.omnilink_main.services.slave_server_service import slave_server_service
from src.omnilink_main.services.device_service import device_service
from src.omnilink_main.dependencies.auth import PermissionChecker, get_current_user
from common.models.user import User
from common.models.slave_server import SlaveServer
from src.omnilink_main.communication.ws_manager import ws_manager

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/register", response_model=APIResponse[SlaveServerSchema])
async def register_slave_server(
    *,
    db: AsyncSession = Depends(get_db),
    server_in: SlaveServerRegister,
) -> APIResponse[SlaveServerSchema]:
    """
    Register a new slave server or update an existing one (heartbeat).
    This endpoint is intended to be called by the slave servers themselves.
    """
    server = await slave_server_service.register_or_update_slave(db=db, server_in=server_in)
    logger.info(f"Slave server registered/updated: {server_in.server_id}")
    return APIResponse(data=server)

@router.post("/", response_model=APIResponse[SlaveServerSchema], status_code=status.HTTP_201_CREATED, dependencies=[Depends(PermissionChecker('slaves.create'))])
async def create_slave_server(
    *,
    db: AsyncSession = Depends(get_db),
    server_in: SlaveServerCreate,
) -> APIResponse[SlaveServerSchema]:
    """
    Create a new slave server manually. (Admin action)
    """
    server = await slave_server_service.create(db=db, obj_in=server_in)
    return APIResponse(data=server)

@router.get("/", response_model=APIResponse[List[SlaveServerSchema]], dependencies=[Depends(PermissionChecker('slaves.view'))])
async def read_slave_servers(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
) -> APIResponse[List[SlaveServerSchema]]:
    """
    Retrieve slave servers.
    """
    servers = await slave_server_service.get_all(db, skip=skip, limit=limit)
    return APIResponse(data=servers)

@router.get("/{server_id}", response_model=APIResponse[SlaveServerSchema], dependencies=[Depends(PermissionChecker('slaves.view'))])
async def read_slave_server(
    *,
    server_id: int,
    db: AsyncSession = Depends(get_db),
) -> APIResponse[SlaveServerSchema]:
    """
    Get slave server by ID.
    """
    server = await slave_server_service.get_by_id(db, id=server_id)
    if not server:
        raise HTTPException(status_code=404, detail="Slave server not found")
    return APIResponse(data=server)

@router.put("/{server_id}", response_model=APIResponse[SlaveServerSchema], dependencies=[Depends(PermissionChecker('slaves.edit'))])
async def update_slave_server(
    *,
    server_id: int,
    server_in: SlaveServerUpdate,
    db: AsyncSession = Depends(get_db),
) -> APIResponse[SlaveServerSchema]:
    """
    Update a slave server.
    """
    db_server = await slave_server_service.get_by_id(db, id=server_id)
    if not db_server:
        raise HTTPException(status_code=404, detail="Slave server not found")
    server = await slave_server_service.update(db=db, db_obj=db_server, obj_in=server_in)
    return APIResponse(data=server)

@router.delete("/{server_id}", response_model=APIResponse[SlaveServerSchema], dependencies=[Depends(PermissionChecker('slaves.delete'))])
async def delete_slave_server(
    *,
    server_id: int,
    db: AsyncSession = Depends(get_db),
) -> APIResponse[SlaveServerSchema]:
    """
    Delete a slave server.
    """
    server = await slave_server_service.remove(db, id=server_id)
    return APIResponse(data=server)

@router.websocket("/{slave_id}/ws")
async def slave_websocket_endpoint(websocket: WebSocket, slave_id: str):
    """从服务器WebSocket连接端点"""
    try:
        # 连接WebSocket
        await ws_manager.connect(slave_id, websocket)
        logger.info(f"Slave server {slave_id} connected via WebSocket")
        
        # 处理消息循环
        while True:
            try:
                # 接收从服务器的消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理不同类型的消息
                message_type = message.get('type')
                
                if message_type == 'heartbeat':
                    # 处理心跳消息
                    await handle_heartbeat(slave_id, message.get('data', {}))
                    logger.debug(f"Received heartbeat from slave {slave_id}")
                    
                elif message_type == 'device_change':
                    # 处理设备变化通知
                    await handle_device_change(slave_id, message)
                    logger.info(f"Device change notification from slave {slave_id}: {message.get('action')}")
                    
                elif message_type == 'command_response':
                    # 处理命令响应
                    await handle_command_response(slave_id, message)
                    logger.debug(f"Command response from slave {slave_id}")
                    
                else:
                    logger.warning(f"Unknown message type from slave {slave_id}: {message_type}")
                    
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON received from slave {slave_id}: {data}")
            except Exception as e:
                logger.error(f"Error processing message from slave {slave_id}: {e}")
                
    except WebSocketDisconnect:
        logger.info(f"Slave server {slave_id} disconnected")
    except Exception as e:
        logger.error(f"WebSocket error for slave {slave_id}: {e}")
    finally:
        # 清理连接
        ws_manager.disconnect(slave_id)

async def handle_heartbeat(slave_id: str, heartbeat_data: dict):
    """处理从服务器心跳"""
    try:
        async with get_db() as db:
            # 根据slave_id查找从服务器
            slave_server = await slave_server_service.get_by_server_id(db, slave_id)
            if not slave_server:
                logger.warning(f"Slave server {slave_id} not found in database")
                return
            
            # 更新从服务器状态
            update_data = {
                "status": heartbeat_data.get("status", "online"),
                "last_seen": datetime.utcnow(),
                "load_average": heartbeat_data.get("load_average", 0.0),
                "memory_usage": heartbeat_data.get("memory_usage", 0.0),
                "device_count": heartbeat_data.get("device_count", 0)
            }
            
            await slave_server_service.update(db, db_obj=slave_server, obj_in=update_data)
            logger.debug(f"Updated heartbeat for slave {slave_id}")
            
    except Exception as e:
        logger.error(f"Error handling heartbeat from slave {slave_id}: {e}", exc_info=True)

async def handle_device_change(slave_id: str, message: dict):
    """处理设备变化通知"""
    try:
        async with get_db() as db:
            action = message.get('action')
            device_info = message.get('device')
            
            # 获取从服务器信息
            slave_server = await slave_server_service.get_by_server_id(db, slave_id)
            if not slave_server:
                logger.warning(f"Slave server {slave_id} not found for device change")
                return
            
            if action == 'added':
                # 在数据库中添加新设备
                device_data = {
                    "device_id": device_info.get("device_path"),
                    "vendor_id": device_info.get("vendor_id"),
                    "product_id": device_info.get("product_id"),
                    "serial_number": device_info.get("serial_number"),
                    "device_name": device_info.get("model"),
                    "device_type": device_info.get("device_type", "unknown"),
                    "status": "available",
                    "slave_server_id": slave_server.id
                }
                
                await device_service.create_or_update_device(db, device_data)
                logger.info(f"Device added on slave {slave_id}: {device_info.get('model')}")
                
            elif action == 'removed':
                # 在数据库中移除设备
                device_path = device_info.get("device_path")
                if device_path:
                    await device_service.remove_device_by_path(db, slave_server.id, device_path)
                    logger.info(f"Device removed on slave {slave_id}: {device_info.get('model')}")
                    
    except Exception as e:
        logger.error(f"Error handling device change from slave {slave_id}: {e}", exc_info=True)

async def handle_command_response(slave_id: str, message: dict):
    """处理命令响应"""
    try:
        command_id = message.get('command_id')
        success = message.get('success')
        data = message.get('data', {})
        error = message.get('error')
        
        # 通过WebSocket管理器处理命令响应
        response_data = {
            "success": success,
            "data": data,
            "error": error,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 通知等待该命令响应的调用方
        await ws_manager.handle_command_response(slave_id, command_id, response_data)
        logger.debug(f"Command {command_id} response from slave {slave_id}: success={success}")
        
    except Exception as e:
        logger.error(f"Error handling command response from slave {slave_id}: {e}", exc_info=True)

@router.post("/{slave_id}/command")
async def send_command_to_slave(
    slave_id: str,
    command: dict,
    current_user: User = Depends(get_current_user)
):
    """向从服务器发送命令"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="权限不足")
    
    try:
        response = await ws_manager.send_command_and_wait(slave_id, command, timeout=30)
        return {"message": f"Command sent to slave {slave_id}", "command": command, "response": response}
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except asyncio.TimeoutError:
        raise HTTPException(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail="Command timeout")
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))