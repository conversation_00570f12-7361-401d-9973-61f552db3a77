from nicegui import ui
from .. import auth_client # Will create this next

def create() -> None:
    """Creates the login page."""
    
    async def handle_login():
        success, message = await auth_client.login(username.value, password.value)
        if success:
            ui.navigate.to('/home')
        else:
            ui.notify(message, color='negative')

    with ui.column().classes('absolute-center items-center'):
        ui.label('OmniLink').classes('text-5xl font-bold text-primary mb-8')
        with ui.card().classes('w-96'):
            with ui.card_section():
                username = ui.input('Username').on('keydown.enter', handle_login)
                password = ui.input('Password', password=True, password_toggle_button=True).on('keydown.enter', handle_login)
            with ui.card_actions().classes('justify-end'):
                ui.button('Log in', on_click=handle_login)
