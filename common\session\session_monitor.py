#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
会话监控模块

此模块提供会话监控功能，支持实时监控会话状态、性能和健康状况，
并能检测会话异常、生成会话统计报告和触发警报。
"""

import logging
import time
import threading
import json
import os
from enum import Enum
from typing import Dict, List, Set, Optional, Any, Callable, Tuple
from datetime import datetime, timedelta

from .session_manager import SessionManager, Session, SessionState, SessionEvent

logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    """警报级别枚举"""
    INFO = "info"          # 信息级别
    WARNING = "warning"    # 警告级别
    ERROR = "error"        # 错误级别
    CRITICAL = "critical"  # 严重级别

class AlertType(Enum):
    """警报类型枚举"""
    HIGH_LATENCY = "high_latency"        # 高延迟
    CONNECTION_LOST = "connection_lost"  # 连接丢失
    RECONNECT_FAILED = "reconnect_failed"  # 重连失败
    FREQUENT_DISCONNECT = "frequent_disconnect"  # 频繁断开
    ABNORMAL_BEHAVIOR = "abnormal_behavior"  # 异常行为
    RESOURCE_LIMIT = "resource_limit"    # 资源限制
    SECURITY_CONCERN = "security_concern"  # 安全问题

class SessionMonitor:
    """会话监控器"""
    
    def __init__(self, session_manager: SessionManager, 
                 alert_handlers: Optional[Dict[AlertLevel, List[Callable[[Dict[str, Any]], None]]]] = None,
                 check_interval: int = 10,
                 stats_interval: int = 300,
                 report_dir: Optional[str] = None,
                 latency_threshold_warning: float = 200.0,  # 毫秒
                 latency_threshold_critical: float = 500.0, # 毫秒
                 disconnect_threshold: int = 3,  # 3次/小时
                 detailed_logging: bool = False):
        """
        初始化会话监控器
        
        参数:
            session_manager: 会话管理器实例
            alert_handlers: 警报处理器字典，按警报级别分组
            check_interval: 检查间隔(秒)
            stats_interval: 统计间隔(秒)
            report_dir: 报告目录
            latency_threshold_warning: 延迟警告阈值(毫秒)
            latency_threshold_critical: 延迟严重阈值(毫秒)
            disconnect_threshold: 断开连接阈值(次/小时)
            detailed_logging: 是否启用详细日志
        """
        self.session_manager = session_manager
        self.alert_handlers = alert_handlers or {level: [] for level in AlertLevel}
        
        self.check_interval = check_interval
        self.stats_interval = stats_interval
        self.report_dir = report_dir or os.path.expanduser("~/.ky/session_reports")
        os.makedirs(self.report_dir, exist_ok=True)
        
        self.latency_threshold_warning = latency_threshold_warning
        self.latency_threshold_critical = latency_threshold_critical
        self.disconnect_threshold = disconnect_threshold
        self.detailed_logging = detailed_logging
        
        # 会话统计信息
        self.active_sessions = 0
        self.suspended_sessions = 0
        self.reconnecting_sessions = 0
        self.failed_sessions = 0
        self.total_sessions = 0
        
        # 性能指标
        self.avg_latency = 0.0
        self.peak_latency = 0.0
        self.successful_reconnects = 0
        self.failed_reconnects = 0
        
        # 会话健康状况历史记录
        self.session_health_history: Dict[str, List[Dict[str, Any]]] = {}
        
        # 上次检查时的会话状态
        self.previous_states: Dict[str, SessionState] = {}
        
        # 会话断开记录
        self.disconnect_history: Dict[str, List[float]] = {}
        
        # 运行标志
        self.running = False
        self.monitor_thread = None
        self.stats_thread = None
        
        # 注册会话事件处理
        self.session_manager.register_session_listener(self._handle_session_event)
        
        logger.info("会话监控器初始化完成")
    
    def start(self) -> None:
        """启动监控"""
        if self.running:
            logger.warning("会话监控器已经在运行")
            return
        
        self.running = True
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        # 启动统计线程
        self.stats_thread = threading.Thread(target=self._stats_loop)
        self.stats_thread.daemon = True
        self.stats_thread.start()
        
        logger.info("会话监控器已启动")
    
    def stop(self) -> None:
        """停止监控"""
        if not self.running:
            logger.warning("会话监控器未运行")
            return
        
        self.running = False
        
        # 等待线程结束
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        
        if self.stats_thread and self.stats_thread.is_alive():
            self.stats_thread.join(timeout=5.0)
        
        logger.info("会话监控器已停止")
    
    def register_alert_handler(self, level: AlertLevel, 
                              handler: Callable[[Dict[str, Any]], None]) -> None:
        """
        注册警报处理器
        
        参数:
            level: 警报级别
            handler: 处理器函数，接收警报信息字典
        """
        if level not in self.alert_handlers:
            self.alert_handlers[level] = []
        
        if handler not in self.alert_handlers[level]:
            self.alert_handlers[level].append(handler)
    
    def unregister_alert_handler(self, level: AlertLevel, 
                                handler: Callable[[Dict[str, Any]], None]) -> bool:
        """
        注销警报处理器
        
        参数:
            level: 警报级别
            handler: 处理器函数
            
        返回:
            bool: 是否成功注销
        """
        if level in self.alert_handlers and handler in self.alert_handlers[level]:
            self.alert_handlers[level].remove(handler)
            return True
        return False
    
    def get_session_stats(self) -> Dict[str, Any]:
        """
        获取当前会话统计信息
        
        返回:
            Dict[str, Any]: 统计信息字典
        """
        return {
            "active_sessions": self.active_sessions,
            "suspended_sessions": self.suspended_sessions,
            "reconnecting_sessions": self.reconnecting_sessions,
            "failed_sessions": self.failed_sessions,
            "total_sessions": self.total_sessions,
            "avg_latency": self.avg_latency,
            "peak_latency": self.peak_latency,
            "successful_reconnects": self.successful_reconnects,
            "failed_reconnects": self.failed_reconnects,
            "timestamp": time.time()
        }
    
    def get_session_health(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话健康状况
        
        参数:
            session_id: 会话ID
            
        返回:
            Optional[Dict[str, Any]]: 健康状况信息，如果会话不存在则返回None
        """
        session = self.session_manager.get_session(session_id)
        if not session:
            return None
        
        # 计算健康分数
        health_score = self._calculate_health_score(session)
        
        # 获取断开连接历史
        disconnect_count = len(self.disconnect_history.get(session_id, []))
        
        return {
            "session_id": session_id,
            "health_score": health_score,
            "state": session.state.value,
            "latency": session.metrics.avg_latency,
            "uptime": time.time() - session.created_at,
            "disconnect_count": disconnect_count,
            "reconnect_attempts": session.metrics.reconnect_attempts,
            "timestamp": time.time()
        }
    
    def get_all_sessions_health(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有会话的健康状况
        
        返回:
            Dict[str, Dict[str, Any]]: 会话ID到健康状况的映射
        """
        result = {}
        for session in self.session_manager.sessions.values():
            health = self.get_session_health(session.session_id)
            if health:
                result[session.session_id] = health
        return result
    
    def generate_health_report(self) -> Dict[str, Any]:
        """
        生成健康报告
        
        返回:
            Dict[str, Any]: 健康报告
        """
        sessions_health = self.get_all_sessions_health()
        
        # 计算平均健康分数
        health_scores = [h["health_score"] for h in sessions_health.values()]
        avg_health_score = sum(health_scores) / len(health_scores) if health_scores else 0
        
        # 分类会话健康状况
        healthy_sessions = [sid for sid, h in sessions_health.items() if h["health_score"] >= 80]
        warning_sessions = [sid for sid, h in sessions_health.items() if 50 <= h["health_score"] < 80]
        critical_sessions = [sid for sid, h in sessions_health.items() if h["health_score"] < 50]
        
        report = {
            "timestamp": time.time(),
            "overall_health": avg_health_score,
            "total_sessions": len(sessions_health),
            "healthy_sessions": len(healthy_sessions),
            "warning_sessions": len(warning_sessions),
            "critical_sessions": len(critical_sessions),
            "session_stats": self.get_session_stats(),
            "top_issues": self._get_top_issues(sessions_health)
        }
        
        # 保存报告
        self._save_health_report(report)
        
        return report
    
    def _get_top_issues(self, sessions_health: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        获取主要问题
        
        参数:
            sessions_health: 会话健康状况字典
            
        返回:
            List[Dict[str, Any]]: 主要问题列表
        """
        issues = []
        
        # 检查高延迟会话
        high_latency_sessions = {
            sid: h for sid, h in sessions_health.items() 
            if h["latency"] >= self.latency_threshold_warning
        }
        if high_latency_sessions:
            issues.append({
                "type": "high_latency",
                "count": len(high_latency_sessions),
                "affected_sessions": list(high_latency_sessions.keys()),
                "severity": "warning" if len(high_latency_sessions) < 3 else "error"
            })
        
        # 检查频繁断开连接的会话
        for session_id, disconnects in self.disconnect_history.items():
            # 只计算最近一小时内的断开
            recent_disconnects = [d for d in disconnects if time.time() - d <= 3600]
            if len(recent_disconnects) >= self.disconnect_threshold:
                issues.append({
                    "type": "frequent_disconnect",
                    "session_id": session_id,
                    "disconnect_count": len(recent_disconnects),
                    "severity": "warning" if len(recent_disconnects) < 5 else "error"
                })
        
        # 检查失败的会话
        failed_sessions = {
            sid: h for sid, h in sessions_health.items() 
            if h["state"] == SessionState.FAILED.value
        }
        if failed_sessions:
            issues.append({
                "type": "failed_sessions",
                "count": len(failed_sessions),
                "affected_sessions": list(failed_sessions.keys()),
                "severity": "error"
            })
        
        return issues
    
    def _save_health_report(self, report: Dict[str, Any]) -> None:
        """
        保存健康报告
        
        参数:
            report: 健康报告
        """
        try:
            # 生成报告文件名
            timestamp = datetime.fromtimestamp(report["timestamp"]).strftime("%Y%m%d_%H%M%S")
            filename = f"health_report_{timestamp}.json"
            filepath = os.path.join(self.report_dir, filename)
            
            # 保存报告
            with open(filepath, "w") as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"健康报告已保存到: {filepath}")
            
            # 清理旧报告(保留最近50个)
            reports = sorted([
                os.path.join(self.report_dir, f) 
                for f in os.listdir(self.report_dir) 
                if f.startswith("health_report_") and f.endswith(".json")
            ], reverse=True)
            
            for old_report in reports[50:]:
                try:
                    os.remove(old_report)
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"保存健康报告失败: {str(e)}")
    
    def _calculate_health_score(self, session: Session) -> float:
        """
        计算会话健康分数
        
        参数:
            session: 会话对象
            
        返回:
            float: 0-100之间的健康分数
        """
        # 基础分数
        if session.state == SessionState.ACTIVE:
            base_score = 100
        elif session.state == SessionState.SUSPENDED:
            base_score = 70
        elif session.state == SessionState.RECONNECTING:
            base_score = 50
        elif session.state == SessionState.DISCONNECTED:
            base_score = 30
        else:
            base_score = 0
        
        # 延迟扣分
        latency_penalty = 0
        if session.metrics.avg_latency > 0:
            if session.metrics.avg_latency >= self.latency_threshold_critical:
                latency_penalty = 30
            elif session.metrics.avg_latency >= self.latency_threshold_warning:
                latency_penalty = 15
        
        # 断开连接扣分
        disconnect_penalty = 0
        disconnect_history = self.disconnect_history.get(session.session_id, [])
        recent_disconnects = [d for d in disconnect_history if time.time() - d <= 3600]
        if len(recent_disconnects) >= self.disconnect_threshold:
            disconnect_penalty = 5 * len(recent_disconnects)
            disconnect_penalty = min(disconnect_penalty, 30)  # 最多扣30分
        
        # 失败重连扣分
        reconnect_penalty = 0
        if session.metrics.failed_reconnects > 0:
            reconnect_penalty = 5 * session.metrics.failed_reconnects
            reconnect_penalty = min(reconnect_penalty, 25)  # 最多扣25分
        
        # 计算最终分数
        health_score = base_score - latency_penalty - disconnect_penalty - reconnect_penalty
        
        # 确保分数在0-100之间
        health_score = max(0, min(100, health_score))
        
        return health_score
    
    def _handle_session_event(self, session_id: str, event: SessionEvent, 
                             details: Dict[str, Any]) -> None:
        """
        处理会话事件
        
        参数:
            session_id: 会话ID
            event: 事件类型
            details: 事件详情
        """
        try:
            # 记录断开连接
            if event == SessionEvent.DISCONNECTED:
                if session_id not in self.disconnect_history:
                    self.disconnect_history[session_id] = []
                
                self.disconnect_history[session_id].append(time.time())
                
                # 只保留最近24小时的断开记录
                self.disconnect_history[session_id] = [
                    t for t in self.disconnect_history[session_id]
                    if time.time() - t <= 86400
                ]
                
                # 检查是否需要发送频繁断开的警报
                recent_disconnects = [
                    t for t in self.disconnect_history[session_id]
                    if time.time() - t <= 3600
                ]
                
                if len(recent_disconnects) >= self.disconnect_threshold:
                    self._trigger_alert(
                        AlertLevel.WARNING,
                        AlertType.FREQUENT_DISCONNECT,
                        {
                            "session_id": session_id,
                            "disconnect_count": len(recent_disconnects),
                            "time_window": "1 hour",
                            "details": details
                        }
                    )
            
            # 记录重连成功
            elif event == SessionEvent.RECONNECTED:
                self.successful_reconnects += 1
            
            # 记录重连失败
            elif event == SessionEvent.FAILED and details.get("reason") == "reconnect_timeout":
                self.failed_reconnects += 1
                
                # 发送重连失败警报
                self._trigger_alert(
                    AlertLevel.ERROR,
                    AlertType.RECONNECT_FAILED,
                    {
                        "session_id": session_id,
                        "details": details
                    }
                )
            
            # 详细日志
            if self.detailed_logging:
                logger.debug(f"会话事件: {session_id} {event.value} {details}")
                
        except Exception as e:
            logger.error(f"处理会话事件出错: {str(e)}")
    
    def _monitor_loop(self) -> None:
        """会话监控循环"""
        while self.running:
            try:
                self._check_sessions()
                
            except Exception as e:
                logger.error(f"会话监控循环出错: {str(e)}")
            
            # 等待下一次检查
            time.sleep(self.check_interval)
    
    def _stats_loop(self) -> None:
        """统计循环"""
        while self.running:
            try:
                # 更新统计信息
                self._update_stats()
                
                # 生成健康报告
                self.generate_health_report()
                
            except Exception as e:
                logger.error(f"统计循环出错: {str(e)}")
            
            # 等待下一次统计
            time.sleep(self.stats_interval)
    
    def _check_sessions(self) -> None:
        """检查所有会话"""
        try:
            sessions = self.session_manager.sessions
            current_states = {}
            
            for session_id, session in sessions.items():
                current_states[session_id] = session.state
                
                # 检查会话状态变化
                prev_state = self.previous_states.get(session_id)
                if prev_state and prev_state != session.state:
                    if self.detailed_logging:
                        logger.debug(f"会话状态变化: {session_id} {prev_state.value} -> {session.state.value}")
                
                # 检查延迟
                if session.state == SessionState.ACTIVE and session.metrics.avg_latency > 0:
                    if session.metrics.avg_latency >= self.latency_threshold_critical:
                        self._trigger_alert(
                            AlertLevel.ERROR,
                            AlertType.HIGH_LATENCY,
                            {
                                "session_id": session_id,
                                "latency": session.metrics.avg_latency,
                                "threshold": self.latency_threshold_critical,
                                "level": "critical"
                            }
                        )
                    elif session.metrics.avg_latency >= self.latency_threshold_warning:
                        self._trigger_alert(
                            AlertLevel.WARNING,
                            AlertType.HIGH_LATENCY,
                            {
                                "session_id": session_id,
                                "latency": session.metrics.avg_latency,
                                "threshold": self.latency_threshold_warning,
                                "level": "warning"
                            }
                        )
            
            # 更新之前的状态
            self.previous_states = current_states
            
        except Exception as e:
            logger.error(f"检查会话出错: {str(e)}")
    
    def _update_stats(self) -> None:
        """更新统计信息"""
        try:
            sessions = self.session_manager.sessions
            
            # 重置计数器
            self.active_sessions = 0
            self.suspended_sessions = 0
            self.reconnecting_sessions = 0
            self.failed_sessions = 0
            self.total_sessions = len(sessions)
            
            # 重置性能指标
            total_latency = 0.0
            latency_count = 0
            
            # 统计各种会话状态
            for session in sessions.values():
                if session.state == SessionState.ACTIVE:
                    self.active_sessions += 1
                elif session.state == SessionState.SUSPENDED:
                    self.suspended_sessions += 1
                elif session.state == SessionState.RECONNECTING:
                    self.reconnecting_sessions += 1
                elif session.state == SessionState.FAILED:
                    self.failed_sessions += 1
                
                # 收集延迟数据
                if session.metrics.avg_latency > 0:
                    total_latency += session.metrics.avg_latency
                    latency_count += 1
                    
                    # 更新峰值延迟
                    if session.metrics.max_latency > self.peak_latency:
                        self.peak_latency = session.metrics.max_latency
            
            # 计算平均延迟
            if latency_count > 0:
                self.avg_latency = total_latency / latency_count
            
            if self.detailed_logging:
                logger.debug(f"会话统计: 活跃={self.active_sessions}, 挂起={self.suspended_sessions}, "
                           f"重连中={self.reconnecting_sessions}, 失败={self.failed_sessions}, "
                           f"总计={self.total_sessions}, 平均延迟={self.avg_latency:.2f}ms")
                
        except Exception as e:
            logger.error(f"更新统计信息出错: {str(e)}")
    
    def _trigger_alert(self, level: AlertLevel, alert_type: AlertType, 
                      details: Dict[str, Any]) -> None:
        """
        触发警报
        
        参数:
            level: 警报级别
            alert_type: 警报类型
            details: 警报详情
        """
        try:
            alert = {
                "level": level.value,
                "type": alert_type.value,
                "timestamp": time.time(),
                "details": details
            }
            
            # 记录警报
            log_func = {
                AlertLevel.INFO: logger.info,
                AlertLevel.WARNING: logger.warning,
                AlertLevel.ERROR: logger.error,
                AlertLevel.CRITICAL: logger.critical
            }.get(level, logger.warning)
            
            log_func(f"会话警报: [{level.value}] {alert_type.value} - {json.dumps(details)}")
            
            # 调用警报处理器
            handlers = self.alert_handlers.get(level, [])
            for handler in handlers:
                try:
                    handler(alert)
                except Exception as handler_error:
                    logger.error(f"警报处理器出错: {str(handler_error)}")
                    
        except Exception as e:
            logger.error(f"触发警报出错: {str(e)}")


def get_session_monitor(session_manager: Optional[SessionManager] = None, **kwargs) -> SessionMonitor:
    """
    获取会话监控器实例(单例模式)
    
    参数:
        session_manager: 会话管理器实例
        **kwargs: 其他初始化参数
        
    返回:
        SessionMonitor: 会话监控器实例
    """
    if not hasattr(get_session_monitor, "_instance") or get_session_monitor._instance is None:
        from .session_manager import get_session_manager
        session_manager = session_manager or get_session_manager()
        get_session_monitor._instance = SessionMonitor(session_manager, **kwargs)
    
    return get_session_monitor._instance 
