from PyQt5.QtCore import QSettings
from PyQt5.QtWidgets import QMessageBox

from desktop.client_app.services import api_service, virtualhere_service

class LoginController:
    """
    Controller to handle the logic for the LoginView.
    """
    def __init__(self, view: 'LoginView'):
        self.view = view
        # Initialize QSettings
        # Using standard locations for settings
        self.settings = QSettings("OmniLink", "OmniLinkClient")

        # Connect view signals to controller methods
        self.view.set_controller(self)
        
        # Load settings into view
        self._load_settings()

    def _load_settings(self):
        """Loads settings and populates the view."""
        server_address = self.settings.value("server_address", "http://127.0.0.1:8000")
        username = self.settings.value("username", "")
        remember_user = self.settings.value("remember_user", "false", type=str) == "true"
        remember_pass = self.settings.value("remember_pass", "false", type=str) == "true"

        self.view.server_address_input.setText(server_address)
        self.view.username_input.setText(username)
        self.view.remember_username_checkbox.setChecked(remember_user)

        # Only load password if both user and pass were set to be remembered
        if remember_user and remember_pass:
            # Note: Storing passwords in plaintext is insecure. This is a placeholder.
            password = self.settings.value("password", "")
            self.view.password_input.setText(password)
            self.view.remember_password_checkbox.setChecked(True)
        else:
            self.view.remember_password_checkbox.setChecked(False)
        
        # Enable/disable password checkbox based on username checkbox
        self.view.on_remember_username_toggled(remember_user)

    def _save_settings(self):
        """Saves settings from the view."""
        remember_user, remember_pass = self.view.get_remember_me_options()
        server_address, username, password = self.view.get_credentials()

        self.settings.setValue("server_address", server_address)

        if remember_user:
            self.settings.setValue("username", username)
            self.settings.setValue("remember_user", "true")
        else:
            self.settings.remove("username")
            self.settings.setValue("remember_user", "false")

        if remember_user and remember_pass:
            self.settings.setValue("password", password)
            self.settings.setValue("remember_pass", "true")
        else:
            self.settings.remove("password")
            self.settings.setValue("remember_pass", "false")
        
        self.settings.sync()


    def handle_login(self):
        """
        Gets credentials and server address from the view, configures the API service,
        logs in, gets slave servers, and configures VirtualHere.
        """
        server_address, username, password = self.view.get_credentials()
        
        if not server_address or not username or not password:
            self.view.show_error_message("服务器地址、用户名和密码均不能为空。")
            return

        api_service.set_base_url(server_address)

        login_success, error_message = api_service.login(username, password)
        if not login_success:
            self.view.show_error_message(error_message)
            return

        # On successful login, save settings
        self._save_settings()

        servers, error_message = api_service.get_slave_servers()
        if servers is None:
            self.view.show_error_message(f"登录成功，但无法获取服务器列表: {error_message}")
        
        if servers:
            print(f"获取到 {len(servers)} 个从服务器，正在配置VirtualHere...")
            for server in servers:
                address = f"{server.get('ip_address')}:{server.get('port')}"
                if not server.get('ip_address') or not server.get('port'):
                    print(f"Skipping server with incomplete data: {server}")
                    continue
                print(f"添加Hub: {address}")
                virtualhere_service.add_manual_hub(address)
        else:
            print("未获取到从服务器列表，跳过VirtualHere配置。")

        self.view.on_login_success()

    def handle_register(self):
        """Placeholder for register functionality."""
        self.view.show_info_message("注册", "注册功能正在紧张开发中，敬请期待！")

    def handle_change_password(self):
        """Placeholder for change password functionality."""
        self.view.show_info_message("修改密码", "修改密码功能正在紧张开发中，敬请期待！")
