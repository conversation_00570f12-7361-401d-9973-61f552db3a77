<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Plus, Edit, Delete, Refresh, Monitor, View } from '@element-plus/icons-vue';
import axios from 'axios';

const router = useRouter();

// --- State ---
const devices = ref([]);
const loading = ref(true);
const searchQuery = ref('');
const dialogVisible = ref(false);
const isEdit = ref(false);
const editedItem = ref({});
const saveLoading = ref(false);

// --- Computed ---
const filteredDevices = computed(() => {
  // 前端过滤暂时保留，但现在是基于从后端获取的数据
  const search = searchQuery.value.toLowerCase();
  if (!search) return devices.value;
  return devices.value.filter(device => 
    device.name.toLowerCase().includes(search) ||
    device.type.toLowerCase().includes(search) ||
    device.ip.toLowerCase().includes(search)
  );
});

// --- Methods ---
const fetchDevices = async () => {
  loading.value = true;
  try {
    const response = await axios.get('/api/v1/devices');
    if (response.data && response.data.data) {
      devices.value = response.data.data || [];
    } else {
      ElMessage.error('获取设备列表失败: 响应格式不正确');
      devices.value = [];
    }
  } catch (error) {
    ElMessage.error('获取设备列表失败: ' + (error.response?.data?.message || '网络错误'));
    devices.value = [];
  } finally {
    loading.value = false;
  }
};

const refreshDevices = () => {
  fetchDevices();
};

const openAddDialog = () => {
  isEdit.value = false;
  editedItem.value = { name: '', type: '', ip: '', mac: '', location: '', description: '' };
  dialogVisible.value = true;
};

const editDevice = (item) => {
  isEdit.value = true;
  editedItem.value = { ...item };
  dialogVisible.value = true;
};

const saveDevice = async () => {
  saveLoading.value = true;
  try {
    if (isEdit.value) {
      // 更新设备
      await axios.put(`/api/v1/devices/${editedItem.value.id}`, editedItem.value);
      ElMessage.success('设备更新成功');
    } else {
      // 创建新设备
      await axios.post('/api/v1/devices', editedItem.value);
      ElMessage.success('设备创建成功');
    }
    dialogVisible.value = false;
    fetchDevices(); // 重新加载数据
  } catch (error) {
    ElMessage.error('保存设备失败: ' + (error.response?.data?.message || '网络错误'));
  } finally {
    saveLoading.value = false;
  }
};

const deleteDevice = (item) => {
  ElMessageBox.confirm(
    `您确定要删除设备 "${item.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/v1/devices/${item.id}`);
      ElMessage.success('设备删除成功');
      fetchDevices(); // 重新加载数据
    } catch (error) {
      ElMessage.error('删除设备失败: ' + (error.response?.data?.message || '网络错误'));
    }
  }).catch(() => {
    // 用户取消
  });
};

const viewDevice = (item) => {
  router.push(`/devices/${item.id}/detail`);
};

const getStatusType = (status) => {
  if (status === 'online') return 'success';
  if (status === 'offline') return 'info';
  if (status === 'in_use') return 'warning';
  if (status === 'error') return 'danger';
  return 'primary';
};

// --- Lifecycle Hooks ---
onMounted(() => {
  fetchDevices();
});
</script> 