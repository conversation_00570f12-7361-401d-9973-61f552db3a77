"""
自定义异常类

定义项目中使用的各种异常类型
"""

from typing import Optional, List, Dict, Any

class BaseError(Exception):
    """基础异常类"""
    def __init__(self, message: str = None, code: int = None):
        self.message = message or self.__class__.__name__
        self.code = code or 500
        super().__init__(self.message)

# 资源相关异常
class ResourceNotFoundError(BaseError):
    """资源未找到异常"""
    def __init__(self, message: str = "Resource not found", code: int = 404):
        super().__init__(message, code)

class ResourceExistsError(BaseError):
    """资源已存在异常"""
    def __init__(self, message: str = "Resource already exists", code: int = 409):
        super().__init__(message, code)

class ResourceExhaustedError(BaseError):
    """资源耗尽异常"""
    def __init__(self, message: str = "Resource exhausted", code: int = 429):
        super().__init__(message, code)

# 认证相关异常
class AuthenticationError(BaseError):
    """认证异常"""
    def __init__(self, message: str = "Authentication failed", code: int = 401):
        super().__init__(message, code)

class AuthorizationError(BaseError):
    """授权异常"""
    def __init__(self, message: str = "Not authorized", code: int = 403):
        super().__init__(message, code)

class TokenExpiredError(AuthenticationError):
    """令牌过期异常"""
    def __init__(self, message: str = "Token has expired", code: int = 401):
        super().__init__(message, code)

class InvalidTokenError(AuthenticationError):
    """无效令牌异常"""
    def __init__(self, message: str = "Invalid token", code: int = 401):
        super().__init__(message, code)

# 操作相关异常
class ValidationError(BaseError):
    """验证异常"""
    def __init__(self, message: str = "Validation failed", code: int = 400):
        super().__init__(message, code)

class InvalidOperationError(BaseError):
    """无效操作异常"""
    def __init__(self, message: str = "Invalid operation", code: int = 400):
        super().__init__(message, code)

class OperationFailedError(BaseError):
    """操作失败异常"""
    def __init__(self, message: str = "Operation failed", code: int = 500):
        super().__init__(message, code)

# 连接相关异常
class ConnectionError(BaseError):
    """连接异常"""
    def __init__(self, message: str = "Connection error", code: int = 503):
        super().__init__(message, code)

class TimeoutError(ConnectionError):
    """超时异常"""
    def __init__(self, message: str = "Connection timeout", code: int = 504):
        super().__init__(message, code)

# 数据相关异常
class DataError(BaseError):
    """数据异常"""
    def __init__(self, message: str = "Data error", code: int = 400):
        super().__init__(message, code)

class DatabaseError(BaseError):
    """数据库异常"""
    def __init__(self, message: str = "Database error", code: int = 500):
        super().__init__(message, code)

# 配置相关异常
class ConfigurationError(BaseError):
    """配置异常"""
    def __init__(self, message: str = "Configuration error", code: int = 500):
        super().__init__(message, code)

# 设备相关异常
class DeviceError(BaseError):
    """设备异常"""
    def __init__(self, message: str = "Device error", code: int = 500):
        super().__init__(message, code)

class DeviceNotFoundError(DeviceError):
    """设备不存在异常"""
    def __init__(self, message: str = "Device not found", code: int = 404):
        super().__init__(message, code)

class DeviceConnectionError(DeviceError):
    """设备连接异常"""
    def __init__(self, message: str = "Device connection error", code: int = 503):
        super().__init__(message, code)

class DeviceOfflineError(DeviceError):
    """设备离线异常"""
    def __init__(self, message: str = "Device is offline", code: int = 503):
        super().__init__(message, code)

class DeviceOperationError(DeviceError):
    """设备操作异常"""
    def __init__(self, message: str = "Device operation error", code: int = 500):
        super().__init__(message, code)

class DeviceStatusError(DeviceError):
    """设备状态异常"""
    def __init__(self, message: str = "Device status error", code: int = 500):
        super().__init__(message, code)

# 服务器相关异常
class ServerError(BaseError):
    """服务器异常"""
    def __init__(self, message: str = "Server error", code: int = 500):
        super().__init__(message, code)

class ServerOfflineError(ServerError):
    """服务器离线异常"""
    def __init__(self, message: str = "Server is offline", code: int = 503):
        super().__init__(message, code)

class OmniLinkError(BaseError):
    """OmniLink系统基础异常类"""
    def __init__(self, message: str = "OmniLink error", error_code: str = "UNKNOWN", code: int = 500):
        super().__init__(message, code)
        self.error_code = error_code
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'message': self.message,
            'error_code': self.error_code,
            'code': self.code
        }

class InvalidStateError(OmniLinkError):
    """当系统或对象处于无效状态以执行请求的操作时引发。"""
    def __init__(self, message="Invalid state for requested operation."):
        super().__init__(message, error_code="INVALID_STATE")

# 新增 SoD 冲突异常
class SoDConflictError(OmniLinkError):
    """当检测到职责分离 (SoD) 冲突时引发。"""
    def __init__(self, message="Segregation of Duties conflict detected.", conflicts: Optional[List[Dict[str, Any]]] = None):
        super().__init__(message, error_code="SOD_CONFLICT")
        self.conflicts = conflicts if conflicts is not None else []

    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data['details'] = {
            'message': self.message,
            'conflicts': self.conflicts
        }
        return data

class ExternalServiceError(OmniLinkError):
    """外部服务异常"""
    def __init__(self, message="External service error"):
        super().__init__(message, error_code="EXTERNAL_SERVICE_ERROR") 
