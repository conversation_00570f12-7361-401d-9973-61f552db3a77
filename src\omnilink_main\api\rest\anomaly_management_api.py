from fastapi import APIRouter, Depends, HTTPException, Body
from typing import List, Dict, Any, Optional

# Placeholder for actual AnomalyDetectionService and auth dependencies
# from ....auth.dependencies import get_current_superuser # Assuming auth dependency
# from ....soc.detection.anomaly_service import anomaly_service_instance # Assuming service instance

# Mocking service and auth for standalone example
class MockUser: # Placeholder for user model/dependency
    def __init__(self, username: str, roles: List[str]):
        self.username = username
        self.roles = roles

def get_current_superuser(): # Placeholder for actual dependency
    # In a real app, this would validate JWT and check user roles
    user = MockUser(username="superadmin", roles=["super_administrator"])
    if "super_administrator" not in user.roles:
        raise HTTPException(status_code=403, detail="Not a superuser")
    return user

class MockAnomalyDetectionService: # Placeholder for AnomalyDetectionService
    def get_all_rules_status(self) -> List[Dict[str, Any]]:
        return [{"rule_id": "IP_MISMATCH_001", "name": "IP Mismatch", "is_enabled": True, "params": {}, "severity": "MEDIUM"}]
    def enable_rule(self, rule_id: str, enable: bool):
        print(f"Rule {rule_id} toggled to {enable}")
        return True
    def update_rule_parameters(self, rule_id: str, params: Dict):
        print(f"Rule {rule_id} params updated to {params}")
        return True
    def set_global_mode(self, mode: str):
        print(f"Global mode set to {mode}")
        return True
    def get_anomaly_events(self, limit: int, offset: int, filters: Dict) -> List[Dict[str, Any]]:
        # Mock events
        return [
            {"event_id": "evt1", "rule_id": "IP_MISMATCH_001", "timestamp": "2023-01-01T10:00:00Z", "severity": "MEDIUM", "details": "..."}
        ]

anomaly_service_instance = MockAnomalyDetectionService()
# End of Mocks

router = APIRouter(
    prefix="/api/v1/soc/anomaly",
    tags=["Anomaly Detection Management"],
)

@router.get("/rules", response_model=List[Dict[str, Any]])
async def get_anomaly_rules(current_user: MockUser = Depends(get_current_superuser)):
    """获取所有异常检测规则及其状态。"""
    return anomaly_service_instance.get_all_rules_status()

@router.post("/rule/{rule_id}/toggle")
async def toggle_anomaly_rule(
    rule_id: str, 
    payload: Dict[str, bool] = Body(...), 
    current_user: MockUser = Depends(get_current_superuser)
):
    """启用/禁用特定规则。"""
    enable = payload.get("enable")
    if enable is None:
        raise HTTPException(status_code=400, detail="'enable' field is required.")
    if anomaly_service_instance.enable_rule(rule_id, enable):
        return {"message": f"Rule {rule_id} has been {'enabled' if enable else 'disabled'}."}
    raise HTTPException(status_code=404, detail=f"Rule {rule_id} not found or failed to update.")

@router.post("/rule/{rule_id}/params")
async def update_anomaly_rule_params(
    rule_id: str, 
    payload: Dict[str, Dict] = Body(...), 
    current_user: MockUser = Depends(get_current_superuser)
):
    """更新特定规则的参数。"""
    params = payload.get("params")
    if params is None:
        raise HTTPException(status_code=400, detail="'params' field is required.")
    if anomaly_service_instance.update_rule_parameters(rule_id, params):
        return {"message": f"Parameters for rule {rule_id} have been updated."}
    raise HTTPException(status_code=404, detail=f"Rule {rule_id} not found or failed to update parameters.")

@router.post("/mode")
async def set_global_anomaly_mode(
    payload: Dict[str, str] = Body(...), 
    current_user: MockUser = Depends(get_current_superuser)
):
    """设置全局异常检测模式。"""
    mode = payload.get("mode")
    if mode not in ["normal", "initial_deployment", "maintenance"]:
        raise HTTPException(status_code=400, detail="Invalid mode. Must be one of [normal, initial_deployment, maintenance].")
    if anomaly_service_instance.set_global_mode(mode):
        return {"message": f"Global anomaly detection mode set to {mode}."}
    raise HTTPException(status_code=500, detail="Failed to set global mode.")

@router.get("/events", response_model=List[Dict[str, Any]])
async def get_detected_anomaly_events(
    limit: int = 100, 
    offset: int = 0, 
    severity: Optional[str] = None,
    rule_id: Optional[str] = None,
    start_date: Optional[str] = None, # ISO format e.g. 2023-01-01T00:00:00Z
    end_date: Optional[str] = None,   # ISO format
    current_user: MockUser = Depends(get_current_superuser)
):
    """获取检测到的异常事件列表（可分页、筛选）。"""
    filters = {
        "severity": severity,
        "rule_id": rule_id,
        "start_date": start_date,
        "end_date": end_date
    }
    # Remove None filters
    active_filters = {k: v for k, v in filters.items() if v is not None}
    return anomaly_service_instance.get_anomaly_events(limit, offset, active_filters)

# To integrate this router into the main FastAPI application:
# In your main app.py or similar:
# from main_server.api.rest import anomaly_management_api
# app.include_router(anomaly_management_api.router) 
