"""
分布式任务管理和调度

提供分布式任务的管理和调度功能，支持任务创建、调度、执行和状态监控。
"""

import logging
import threading
import time
import heapq
import queue
import uuid
from typing import Dict, List, Set, Any, Optional, Callable, Tuple, Union, Type
from datetime import datetime, timedelta
import json
import random
import os
import pickle
from pathlib import Path
import socket
import requests
import sqlite3
import traceback
import functools
from threading import Timer
import importlib
import importlib.util
import inspect
import pkgutil
from types import ModuleType
import sys
import shutil

from .task_models import Task, TaskStatus, TaskPriority, TaskResult, TaskDependency

logger = logging.getLogger(__name__)

class TaskHistoryLogger:
    """
    任务历史记录器
    
    负责记录、存储和查询任务执行历史，包括状态变化、资源使用和执行结果。
    """
    
    def __init__(self, db_path: Optional[str] = None, max_history_days: int = 30):
        """
        初始化任务历史记录器
        
        参数:
            db_path: SQLite数据库路径，如果为None则使用默认路径
            max_history_days: 最大历史保留天数
        """
        # 设置数据库路径
        if db_path is None:
            db_dir = Path("data/tasks")
            os.makedirs(db_dir, exist_ok=True)
            db_path = str(db_dir / "task_history.db")
        
        self._db_path = db_path
        self._max_history_days = max_history_days
        self._conn = None
        self._lock = threading.RLock()
        self._running = False
        self._cleanup_thread = None
        self._cleanup_interval = 86400  # 清理间隔(秒)，默认每天一次
        
        logger.info(f"任务历史记录器初始化，数据库路径: {self._db_path}")
        
        # 初始化数据库
        self._init_database()
        
    def start(self):
        """启动历史记录器，包括周期性清理任务"""
        with self._lock:
            if self._running:
                return
                
            self._running = True
            
            # 启动清理线程
            self._cleanup_thread = threading.Thread(target=self._cleanup_loop)
            self._cleanup_thread.daemon = True
            self._cleanup_thread.start()
            
            logger.info("任务历史记录器已启动")
            
    def stop(self):
        """停止历史记录器"""
        with self._lock:
            if not self._running:
                return
                
            self._running = False
            
            # 等待清理线程结束
            if self._cleanup_thread:
                self._cleanup_thread.join(timeout=5)
                
            # 关闭数据库连接
            if self._conn:
                self._conn.close()
                self._conn = None
                
            logger.info("任务历史记录器已停止")
            
    def record_task_created(self, task: Task):
        """
        记录任务创建事件
        
        参数:
            task: 任务对象
        """
        self._record_task_event(task, "created")
        
    def record_task_scheduled(self, task: Task):
        """
        记录任务调度事件
        
        参数:
            task: 任务对象
        """
        self._record_task_event(task, "scheduled")
        
    def record_task_started(self, task: Task):
        """
        记录任务开始执行事件
        
        参数:
            task: 任务对象
        """
        self._record_task_event(task, "started")
        
    def record_task_completed(self, task: Task, resource_usage: Optional[Dict[str, Any]] = None):
        """
        记录任务完成事件
        
        参数:
            task: 任务对象
            resource_usage: 资源使用信息
        """
        self._record_task_event(task, "completed", resource_usage)
        
    def record_task_failed(self, task: Task, resource_usage: Optional[Dict[str, Any]] = None):
        """
        记录任务失败事件
        
        参数:
            task: 任务对象
            resource_usage: 资源使用信息
        """
        self._record_task_event(task, "failed", resource_usage)
        
    def record_task_cancelled(self, task: Task):
        """
        记录任务取消事件
        
        参数:
            task: 任务对象
        """
        self._record_task_event(task, "cancelled")
        
    def record_task_timeout(self, task: Task):
        """
        记录任务超时事件
        
        参数:
            task: 任务对象
        """
        self._record_task_event(task, "timeout")
        
    def get_task_history(self, task_id: str) -> List[Dict[str, Any]]:
        """
        获取任务的完整历史记录
        
        参数:
            task_id: 任务ID
            
        返回:
            历史记录列表，每项为一个事件字典
        """
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # 查询任务历史
            cursor.execute(
                "SELECT event_time, event_type, task_data, resource_usage FROM task_history "
                "WHERE task_id = ? ORDER BY event_time ASC",
                (task_id,)
            )
            
            history = []
            for row in cursor.fetchall():
                event_time, event_type, task_data, resource_usage = row
                
                # 解析JSON数据
                task_data = json.loads(task_data) if task_data else {}
                resource_usage = json.loads(resource_usage) if resource_usage else {}
                
                history.append({
                    "event_time": event_time,
                    "event_type": event_type,
                    "task_data": task_data,
                    "resource_usage": resource_usage
                })
                
            return history
            
    def search_tasks(self, 
                   task_type: Optional[str] = None,
                   status: Optional[str] = None,
                   worker_id: Optional[str] = None,
                   from_time: Optional[str] = None,
                   to_time: Optional[str] = None,
                   limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索符合条件的任务
        
        参数:
            task_type: 任务类型
            status: 任务状态
            worker_id: 工作者ID
            from_time: 开始时间(ISO格式)
            to_time: 结束时间(ISO格式)
            limit: 最大返回记录数
            
        返回:
            符合条件的任务列表
        """
        query = "SELECT DISTINCT task_id FROM task_history WHERE 1=1"
        params = []
        
        # 添加查询条件
        if task_type:
            query += " AND task_data LIKE ?"
            params.append(f"%\"task_type\":\"{task_type}\"%")
            
        if status:
            query += " AND task_data LIKE ?"
            params.append(f"%\"status\":\"{status}\"%")
            
        if worker_id:
            query += " AND task_data LIKE ?"
            params.append(f"%\"worker_id\":\"{worker_id}\"%")
            
        if from_time:
            query += " AND event_time >= ?"
            params.append(from_time)
            
        if to_time:
            query += " AND event_time <= ?"
            params.append(to_time)
            
        query += " ORDER BY event_time DESC LIMIT ?"
        params.append(limit)
        
        # 执行查询
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            
            # 获取任务ID列表
            task_ids = [row[0] for row in cursor.fetchall()]
            
            # 对于每个任务ID，获取最新状态
            results = []
            for task_id in task_ids:
                # 获取任务最新事件
                cursor.execute(
                    "SELECT event_time, event_type, task_data, resource_usage FROM task_history "
                    "WHERE task_id = ? ORDER BY event_time DESC LIMIT 1",
                    (task_id,)
                )
                
                row = cursor.fetchone()
                if row:
                    event_time, event_type, task_data, resource_usage = row
                    
                    # 解析JSON数据
                    task_data = json.loads(task_data) if task_data else {}
                    resource_usage = json.loads(resource_usage) if resource_usage else {}
                    
                    results.append({
                        "task_id": task_id,
                        "event_time": event_time,
                        "event_type": event_type,
                        "task_data": task_data,
                        "resource_usage": resource_usage
                    })
                    
            return results
            
    def get_task_stats(self, days: int = 7) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        参数:
            days: 统计天数
            
        返回:
            统计信息字典
        """
        # 计算截止时间
        cutoff_time = (datetime.now() - timedelta(days=days)).isoformat()
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # 总任务数
            cursor.execute(
                "SELECT COUNT(DISTINCT task_id) FROM task_history WHERE event_time >= ?",
                (cutoff_time,)
            )
            total_tasks = cursor.fetchone()[0]
            
            # 各类型任务数量
            cursor.execute(
                "SELECT task_data, COUNT(DISTINCT task_id) FROM task_history "
                "WHERE event_type = 'created' AND event_time >= ? "
                "GROUP BY SUBSTR(task_data, INSTR(task_data, '\"task_type\":\"') + 13, "
                "INSTR(SUBSTR(task_data, INSTR(task_data, '\"task_type\":\"') + 13), '\"'))",
                (cutoff_time,)
            )
            
            task_types = {}
            for row in cursor.fetchall():
                task_data, count = row
                if task_data:
                    task_data = json.loads(task_data)
                    task_type = task_data.get("task_type", "unknown")
                    task_types[task_type] = count
            
            # 各状态任务数量
            status_counts = {
                "completed": 0,
                "failed": 0,
                "cancelled": 0,
                "timeout": 0
            }
            
            for status in status_counts.keys():
                cursor.execute(
                    "SELECT COUNT(DISTINCT task_id) FROM task_history "
                    "WHERE event_type = ? AND event_time >= ?",
                    (status, cutoff_time)
                )
                status_counts[status] = cursor.fetchone()[0]
                
            # 计算平均执行时间
            cursor.execute(
                """SELECT 
                    th1.task_id, 
                    th1.event_time AS start_time, 
                    th2.event_time AS end_time
                FROM task_history th1
                JOIN task_history th2 ON th1.task_id = th2.task_id
                WHERE th1.event_type = 'started' 
                AND th2.event_type IN ('completed', 'failed', 'timeout')
                AND th1.event_time >= ?
                AND th2.event_time > th1.event_time
                ORDER BY th1.task_id, th1.event_time ASC, th2.event_time ASC""",
                (cutoff_time,)
            )
            
            execution_times = []
            for row in cursor.fetchall():
                task_id, start_time, end_time = row
                try:
                    start_dt = datetime.fromisoformat(start_time)
                    end_dt = datetime.fromisoformat(end_time)
                    execution_seconds = (end_dt - start_dt).total_seconds()
                    execution_times.append(execution_seconds)
                except (ValueError, TypeError):
                    continue
                    
            avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
            
            # 返回统计信息
            return {
                "period_days": days,
                "total_tasks": total_tasks,
                "task_types": task_types,
                "status_counts": status_counts,
                "avg_execution_time": avg_execution_time,
                "execution_time_percentiles": self._calculate_percentiles(execution_times)
            }
            
    def _calculate_percentiles(self, values: List[float]) -> Dict[str, float]:
        """
        计算百分位数
        
        参数:
            values: 数值列表
            
        返回:
            百分位统计字典
        """
        if not values:
            return {
                "min": 0,
                "p25": 0,
                "p50": 0,
                "p75": 0,
                "p90": 0,
                "p95": 0,
                "p99": 0,
                "max": 0
            }
            
        sorted_values = sorted(values)
        n = len(sorted_values)
        
        return {
            "min": sorted_values[0],
            "p25": sorted_values[int(n * 0.25)],
            "p50": sorted_values[int(n * 0.5)],
            "p75": sorted_values[int(n * 0.75)],
            "p90": sorted_values[int(n * 0.9)],
            "p95": sorted_values[int(n * 0.95)],
            "p99": sorted_values[int(n * 0.99)] if n >= 100 else sorted_values[-1],
            "max": sorted_values[-1]
        }
        
    def _init_database(self):
        """初始化数据库结构"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建任务历史表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS task_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id TEXT NOT NULL,
                event_time TEXT NOT NULL,
                event_type TEXT NOT NULL,
                task_data TEXT,
                resource_usage TEXT
            )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_task_id ON task_history(task_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_event_time ON task_history(event_time)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_event_type ON task_history(event_type)")
            
            conn.commit()
            
    def _record_task_event(self, task: Task, event_type: str, resource_usage: Optional[Dict[str, Any]] = None):
        """
        记录任务事件
        
        参数:
            task: 任务对象
            event_type: 事件类型
            resource_usage: 资源使用信息
        """
        try:
            now = datetime.now().isoformat()
            
            # 转换任务为字典
            task_dict = task.to_dict()
            task_data = json.dumps(task_dict)
            
            # 转换资源使用为JSON
            resource_data = json.dumps(resource_usage) if resource_usage else None
            
            # 写入数据库
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT INTO task_history (task_id, event_time, event_type, task_data, resource_usage) "
                    "VALUES (?, ?, ?, ?, ?)",
                    (task.task_id, now, event_type, task_data, resource_data)
                )
                conn.commit()
                
        except Exception as e:
            logger.error(f"记录任务事件出错: {e}")
            logger.debug(traceback.format_exc())
            
    def _cleanup_loop(self):
        """清理循环，定期删除过期历史记录"""
        while self._running:
            try:
                # 执行清理
                self._cleanup_old_records()
                
                # 等待下次清理
                for _ in range(self._cleanup_interval):
                    if not self._running:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                logger.error(f"历史记录清理出错: {e}")
                time.sleep(3600)  # 出错后等待较长时间
                
    def _cleanup_old_records(self):
        """清理过期的历史记录"""
        try:
            # 计算截止时间
            cutoff_time = (datetime.now() - timedelta(days=self._max_history_days)).isoformat()
            
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取要删除的记录数
                cursor.execute(
                    "SELECT COUNT(*) FROM task_history WHERE event_time < ?",
                    (cutoff_time,)
                )
                count = cursor.fetchone()[0]
                
                if count > 0:
                    # 删除过期记录
                    cursor.execute(
                        "DELETE FROM task_history WHERE event_time < ?",
                        (cutoff_time,)
                    )
                    conn.commit()
                    
                    logger.info(f"清理了 {count} 条过期任务历史记录(超过 {self._max_history_days} 天)")
                    
        except Exception as e:
            logger.error(f"清理过期历史记录出错: {e}")
            
    def _get_connection(self):
        """获取数据库连接，如果不存在则创建"""
        if self._conn is None:
            self._conn = sqlite3.connect(self._db_path, check_same_thread=False)
            
        return self._conn

class PluginManager:
    """
    插件管理器
    
    负责动态加载、注册和管理任务处理插件，支持系统扩展。
    """
    
    def __init__(self, plugin_dirs: Optional[List[str]] = None):
        """
        初始化插件管理器
        
        参数:
            plugin_dirs: 插件目录列表，如果为None则使用默认目录
        """
        # 设置插件目录
        self._plugin_dirs = plugin_dirs or ["plugins", "ky/plugins"]
        self._plugins: Dict[str, Dict[str, Any]] = {}  # 插件信息 {插件ID: 插件信息}
        self._task_handlers: Dict[str, Callable] = {}  # 任务处理器 {任务类型: 处理函数}
        self._running = False
        self._lock = threading.RLock()
        
        # 插件版本兼容性
        self._api_version = "1.0.0"  # 当前API版本
        self._min_supported_version = "1.0.0"  # 最低支持的版本
        
        logger.info(f"插件管理器初始化，插件目录: {self._plugin_dirs}")
        
    def start(self):
        """启动插件管理器，加载所有插件"""
        with self._lock:
            if self._running:
                return
                
            self._running = True
            
            # 确保插件目录存在
            for dir_path in self._plugin_dirs:
                os.makedirs(dir_path, exist_ok=True)
                
            # 加载所有插件
            self._discover_plugins()
            
            logger.info(f"插件管理器已启动，发现 {len(self._plugins)} 个插件")
            
    def stop(self):
        """停止插件管理器"""
        with self._lock:
            if not self._running:
                return
                
            self._running = False
            
            # 关闭所有插件
            for plugin_id, plugin_info in self._plugins.items():
                try:
                    if callable(plugin_info.get("cleanup_func")):
                        plugin_info["cleanup_func"]()
                        logger.debug(f"插件 {plugin_id} 已清理资源")
                except Exception as e:
                    logger.error(f"关闭插件 {plugin_id} 出错: {e}")
                    
            # 清空插件和处理器
            self._plugins.clear()
            self._task_handlers.clear()
            
            logger.info("插件管理器已停止")
            
    def register_plugin(self, module: ModuleType) -> bool:
        """
        注册插件模块
        
        参数:
            module: 插件模块
            
        返回:
            是否成功注册
        """
        try:
            if not hasattr(module, "__plugin_info__"):
                logger.warning(f"模块 {module.__name__} 不是有效的插件，缺少 __plugin_info__")
                return False
                
            plugin_info = module.__plugin_info__
            
            # 检查必要属性
            required_fields = ["id", "name", "version", "description", "author"]
            for field in required_fields:
                if field not in plugin_info:
                    logger.warning(f"插件 {module.__name__} 缺少必要的信息: {field}")
                    return False
                    
            plugin_id = plugin_info["id"]
            plugin_version = plugin_info["version"]
            
            # 检查API版本兼容性
            api_version = plugin_info.get("api_version", "1.0.0")
            if not self._check_version_compatibility(api_version):
                logger.warning(f"插件 {plugin_id} (API版本 {api_version}) 与系统API版本 {self._api_version} 不兼容")
                return False
                
            # 检查插件是否已存在
            if plugin_id in self._plugins:
                logger.warning(f"插件 {plugin_id} 已注册，将被覆盖")
                
            # 查找任务处理函数
            task_handlers = {}
            for name, obj in inspect.getmembers(module):
                if callable(obj) and hasattr(obj, "__task_handler__"):
                    task_type = getattr(obj, "__task_handler__")
                    task_handlers[task_type] = obj
                    
            # 查找初始化和清理函数
            init_func = getattr(module, "initialize", None)
            cleanup_func = getattr(module, "cleanup", None)
            
            # 保存插件信息
            self._plugins[plugin_id] = {
                "id": plugin_id,
                "name": plugin_info["name"],
                "version": plugin_version,
                "description": plugin_info["description"],
                "author": plugin_info["author"],
                "module": module,
                "task_handlers": task_handlers,
                "init_func": init_func,
                "cleanup_func": cleanup_func,
                "loaded_at": datetime.now()
            }
            
            # 注册任务处理器
            for task_type, handler in task_handlers.items():
                if task_type in self._task_handlers:
                    logger.warning(f"任务类型 {task_type} 的处理器已存在，将被覆盖")
                    
                self._task_handlers[task_type] = handler
                logger.debug(f"注册任务类型 {task_type} 的处理器: {handler.__name__}")
                
            # 调用初始化函数
            if callable(init_func):
                init_func()
                
            logger.info(f"成功注册插件: {plugin_info['name']} (ID: {plugin_id}, 版本: {plugin_version})")
            return True
            
        except Exception as e:
            logger.error(f"注册插件 {getattr(module, '__name__', '未知')} 出错: {e}")
            logger.debug(traceback.format_exc())
            return False
            
    def unregister_plugin(self, plugin_id: str) -> bool:
        """
        取消注册插件
        
        参数:
            plugin_id: 插件ID
            
        返回:
            是否成功取消注册
        """
        with self._lock:
            if plugin_id not in self._plugins:
                logger.warning(f"插件 {plugin_id} 不存在，无法取消注册")
                return False
                
            plugin_info = self._plugins[plugin_id]
            
            # 调用清理函数
            try:
                if callable(plugin_info.get("cleanup_func")):
                    plugin_info["cleanup_func"]()
            except Exception as e:
                logger.error(f"调用插件 {plugin_id} 的清理函数出错: {e}")
                
            # 取消注册任务处理器
            task_handlers = plugin_info.get("task_handlers", {})
            for task_type in task_handlers:
                if task_type in self._task_handlers:
                    del self._task_handlers[task_type]
                    
            # 删除插件信息
            del self._plugins[plugin_id]
            
            logger.info(f"成功取消注册插件: {plugin_id}")
            return True
            
    def get_task_handler(self, task_type: str) -> Optional[Callable]:
        """
        获取任务处理器
        
        参数:
            task_type: 任务类型
            
        返回:
            任务处理器函数，如果不存在则返回None
        """
        with self._lock:
            return self._task_handlers.get(task_type)
            
    def get_all_plugins(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有已注册的插件信息
        
        返回:
            插件信息字典 {插件ID: 插件信息}
        """
        with self._lock:
            # 创建副本，避免修改原始数据
            result = {}
            for plugin_id, plugin_info in self._plugins.items():
                # 排除模块对象以避免序列化问题
                info_copy = plugin_info.copy()
                info_copy.pop("module", None)
                info_copy.pop("init_func", None)
                info_copy.pop("cleanup_func", None)
                info_copy.pop("task_handlers", None)
                
                # 添加任务类型列表
                info_copy["supported_task_types"] = list(plugin_info.get("task_handlers", {}).keys())
                
                result[plugin_id] = info_copy
                
            return result
            
    def load_plugin_from_path(self, plugin_path: str) -> Optional[str]:
        """
        从指定路径加载插件
        
        参数:
            plugin_path: 插件路径(文件或目录)
            
        返回:
            插件ID，如果加载失败则返回None
        """
        try:
            path = Path(plugin_path)
            
            if path.is_file() and path.suffix == ".py":
                # 加载单个Python文件
                spec = importlib.util.spec_from_file_location(
                    f"dynamic_plugin_{path.stem}", 
                    plugin_path
                )
                if not spec or not spec.loader:
                    logger.error(f"无法加载插件文件: {plugin_path}")
                    return None
                    
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                if self.register_plugin(module):
                    return getattr(module, "__plugin_info__", {}).get("id")
                    
            elif path.is_dir():
                # 加载Python包
                for sub_path in path.iterdir():
                    if sub_path.is_file() and sub_path.suffix == ".py" and sub_path.stem != "__init__":
                        self.load_plugin_from_path(str(sub_path))
            
            return None
        
        except Exception as e:
            logger.error(f"加载插件 {plugin_path} 出错: {e}")
            logger.debug(traceback.format_exc())
            return None
            
    def _discover_plugins(self):
        """发现并加载所有插件"""
        # 确保插件目录存在于Python搜索路径中
        for plugin_dir in self._plugin_dirs:
            abs_path = os.path.abspath(plugin_dir)
            if abs_path not in sys.path:
                sys.path.append(abs_path)
                
        # 搜索所有插件目录
        for plugin_dir in self._plugin_dirs:
            if not os.path.exists(plugin_dir):
                continue
                
            # 加载目录中的所有Python文件
            for item in os.listdir(plugin_dir):
                item_path = os.path.join(plugin_dir, item)
                
                if item.endswith('.py') and not item.startswith('__'):
                    self.load_plugin_from_path(item_path)
                elif os.path.isdir(item_path) and os.path.exists(os.path.join(item_path, '__init__.py')):
                    # 加载子包
                    package_name = item
                    try:
                        package = importlib.import_module(package_name)
                        
                        # 检查包本身是否是插件
                        if hasattr(package, "__plugin_info__"):
                            self.register_plugin(package)
                            
                        # 加载包中的所有模块
                        for _, name, is_pkg in pkgutil.iter_modules([item_path]):
                            if not is_pkg and not name.startswith('__'):
                                try:
                                    module = importlib.import_module(f"{package_name}.{name}")
                                    self.register_plugin(module)
                                except Exception as e:
                                    logger.error(f"加载模块 {package_name}.{name} 出错: {e}")
                    except Exception as e:
                        logger.error(f"加载包 {package_name} 出错: {e}")
                        
    def _check_version_compatibility(self, plugin_api_version: str) -> bool:
        """
        检查插件的API版本是否兼容
        
        参数:
            plugin_api_version: 插件API版本
            
        返回:
            是否兼容
        """
        # 简单的基于字符串比较的版本检查
        try:
            plugin_parts = list(map(int, plugin_api_version.split('.')))
            min_supported_parts = list(map(int, self._min_supported_version.split('.')))
            
            # 主版本必须相同
            if plugin_parts[0] != min_supported_parts[0]:
                return False
                
            # 插件版本必须大于等于最低支持版本
            for i in range(min(len(plugin_parts), len(min_supported_parts))):
                if plugin_parts[i] < min_supported_parts[i]:
                    return False
                elif plugin_parts[i] > min_supported_parts[i]:
                    return True
                    
            return True
            
        except (ValueError, IndexError):
            logger.warning(f"版本格式无效: {plugin_api_version}")
            return False

# 添加插件任务处理器装饰器
def task_handler(task_type: str) -> Callable:
    """
    任务处理器装饰器
    
    使用此装饰器将函数注册为特定任务类型的处理器。
    
    参数:
        task_type: 任务类型
    
    返回:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        setattr(func, "__task_handler__", task_type)
        return func
    return decorator

class TaskScheduler:
    """
    任务调度器
    
    负责调度和执行分布式任务，支持基于优先级和依赖关系的任务调度。
    """
    
    def __init__(self, worker_id: Optional[str] = None):
        """
        初始化任务调度器
        
        参数:
            worker_id: 工作者ID，如果为None则生成一个随机ID
        """
        self.worker_id = worker_id or f"worker-{uuid.uuid4()}"
        self._running = False
        self._lock = threading.RLock()
        
        # 调度队列 - 按优先级排序的待调度任务
        self._schedule_queue: List[Tuple[int, str]] = []  # (-priority, task_id)
        # 执行队列 - 已调度待执行的任务
        self._execution_queue = queue.Queue()
        
        # 任务存储
        self._tasks: Dict[str, Task] = {}
        # 任务处理器映射
        self._task_handlers: Dict[str, Callable[[Task], TaskResult]] = {}
        # 正在执行的任务集合
        self._running_tasks: Set[str] = set()
        
        # 工作线程
        self._scheduler_thread: Optional[threading.Thread] = None
        self._executor_threads: List[threading.Thread] = []
        self._max_executor_threads = 5
        
        # 任务超时检查间隔(秒)
        self._timeout_check_interval = 30
        # 完成任务保留时间(小时)
        self._completed_task_ttl = 24
        
        # 状态回调
        self._status_callbacks: Dict[str, List[Callable[[Task], None]]] = {}
        
        # 资源限制相关属性
        self._resource_limits = {
            "max_cpu_percent": 80,      # 最大CPU使用率(%)
            "max_memory_percent": 80,   # 最大内存使用率(%)
            "max_concurrent_tasks": 10, # 最大并发任务数
            "task_priority_boost": 10,  # 任务优先级提升值(每次延迟)
        }
        
        # 重试策略相关属性
        self._retry_strategies = {
            "default": {
                "max_retries": 3,           # 最大重试次数
                "base_delay": 60,           # 基础延迟时间(秒)
                "backoff_factor": 2.0,      # 退避因子
                "max_delay": 3600,          # 最大延迟时间(秒)
                "jitter": True              # 是否添加随机抖动
            }
        }
        
        # 任务执行指标
        self._task_resource_usage: Dict[str, Dict[str, float]] = {}  # 任务ID -> {cpu_percent, memory_mb, ...}
        
        logger.info(f"任务调度器初始化, worker_id={self.worker_id}")
        
    def start(self):
        """启动任务调度器"""
        with self._lock:
            if self._running:
                return
                
            self._running = True
            
            # 启动调度线程
            self._scheduler_thread = threading.Thread(target=self._scheduler_loop)
            self._scheduler_thread.daemon = True
            self._scheduler_thread.start()
            
            # 启动执行线程
            for i in range(self._max_executor_threads):
                thread = threading.Thread(target=self._executor_loop)
                thread.daemon = True
                thread.start()
                self._executor_threads.append(thread)
                
            logger.info("任务调度器已启动")
                
    def stop(self):
        """停止任务调度器"""
        with self._lock:
            if not self._running:
                return
                
            self._running = False
            
            # 停止所有线程
            if self._scheduler_thread:
                self._scheduler_thread.join(timeout=5)
                
            for thread in self._executor_threads:
                thread.join(timeout=5)
            
            # 清空状态
            self._executor_threads = []
            self._scheduler_thread = None
            
            # 清空队列
            self._schedule_queue = []
            while not self._execution_queue.empty():
                try:
                    self._execution_queue.get_nowait()
                except queue.Empty:
                    break
                    
            logger.info("任务调度器已停止")
            
    def register_task_handler(self, task_type: str, handler: Callable[[Task], TaskResult]) -> bool:
        """
        注册任务处理器
        
        参数:
            task_type: 任务类型
            handler: 任务处理器函数，接收Task对象，返回TaskResult对象
            
        返回:
            是否成功注册
        """
        with self._lock:
            if task_type in self._task_handlers:
                logger.warning(f"任务类型 {task_type} 的处理器已存在，将被替换")
                
            self._task_handlers[task_type] = handler
            logger.info(f"已注册任务类型 {task_type} 的处理器")
            return True
            
    def unregister_task_handler(self, task_type: str) -> bool:
        """
        取消注册任务处理器
        
        参数:
            task_type: 任务类型
            
        返回:
            是否成功取消注册
        """
        with self._lock:
            if task_type not in self._task_handlers:
                logger.warning(f"任务类型 {task_type} 的处理器不存在")
                return False
                
            del self._task_handlers[task_type]
            logger.info(f"已取消注册任务类型 {task_type} 的处理器")
            return True
            
    def submit_task(self, task: Task) -> str:
        """
        提交任务
        
        参数:
            task: 任务对象
            
        返回:
            任务ID
        """
        with self._lock:
            # 检查任务类型是否已注册处理器
            if task.task_type not in self._task_handlers:
                logger.warning(f"任务类型 {task.task_type} 没有注册处理器，任务将无法执行")
                
            # 存储任务
            self._tasks[task.task_id] = task
            
            # 添加到调度队列
            negative_priority = -task.priority.value  # 使用负优先级，这样优先级高的任务会排在堆顶
            heapq.heappush(self._schedule_queue, (negative_priority, task.task_id))
            
            logger.info(f"提交任务: id={task.task_id}, name={task.name}, 类型={task.task_type}, 优先级={task.priority.name}")
            return task.task_id
            
    def get_task(self, task_id: str) -> Optional[Task]:
        """
        获取任务
        
        参数:
            task_id: 任务ID
            
        返回:
            任务对象，如果不存在则返回None
        """
        with self._lock:
            return self._tasks.get(task_id)
            
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        参数:
            task_id: 任务ID
            
        返回:
            是否成功取消
        """
        with self._lock:
            task = self._tasks.get(task_id)
            if not task:
                logger.warning(f"任务 {task_id} 不存在，无法取消")
                return False
                
            # 只能取消未执行的任务
            if task.status in [TaskStatus.PENDING, TaskStatus.SCHEDULED]:
                task.status = TaskStatus.CANCELLED
                task.completed_at = datetime.now()
                
                # 触发状态回调
                self._trigger_status_callback(task)
                
                logger.info(f"取消任务: id={task.task_id}, name={task.name}")
                return True
            else:
                logger.warning(f"任务 {task_id} 状态为 {task.status.name}，无法取消")
                return False
                
    def register_status_callback(self, status: TaskStatus, callback: Callable[[Task], None]) -> bool:
        """
        注册任务状态回调
        
        参数:
            status: 任务状态
            callback: 回调函数，接收Task对象
            
        返回:
            是否成功注册
        """
        with self._lock:
            status_name = status.value
            if status_name not in self._status_callbacks:
                self._status_callbacks[status_name] = []
                
            self._status_callbacks[status_name].append(callback)
            return True
            
    def unregister_status_callback(self, status: TaskStatus, callback: Callable[[Task], None]) -> bool:
        """
        取消注册任务状态回调
        
        参数:
            status: 任务状态
            callback: 回调函数
            
        返回:
            是否成功取消注册
        """
        with self._lock:
            status_name = status.value
            if status_name not in self._status_callbacks:
                return False
                
            try:
                self._status_callbacks[status_name].remove(callback)
                return True
            except ValueError:
                return False
                
    def _trigger_status_callback(self, task: Task):
        """
        触发任务状态回调
        
        参数:
            task: 任务对象
        """
        status_name = task.status.value
        callbacks = []
        
        with self._lock:
            if status_name in self._status_callbacks:
                callbacks = self._status_callbacks[status_name].copy()
                
        for callback in callbacks:
            try:
                callback(task)
            except Exception as e:
                logger.error(f"执行任务状态回调出错: {e}")
                
    def _scheduler_loop(self):
        """调度线程主循环"""
        last_timeout_check = datetime.now()
        last_cleanup = datetime.now()
        
        while self._running:
            try:
                # 调度任务
                self._schedule_tasks()
                
                # 检查超时任务
                now = datetime.now()
                if (now - last_timeout_check).total_seconds() >= self._timeout_check_interval:
                    self._check_timeouts()
                    last_timeout_check = now
                    
                # 清理已完成任务
                if (now - last_cleanup).total_seconds() >= 3600:  # 每小时清理一次
                    self._cleanup_completed_tasks()
                    last_cleanup = now
                    
                time.sleep(1)  # 避免CPU占用过高
                
            except Exception as e:
                logger.error(f"任务调度循环出错: {e}")
                time.sleep(5)  # 出错后等待较长时间
                
    def _executor_loop(self):
        """执行线程主循环"""
        while self._running:
            try:
                # 从执行队列获取任务ID
                try:
                    task_id = self._execution_queue.get(timeout=1)
                except queue.Empty:
                    continue
                    
                # 获取任务对象
                task = None
                with self._lock:
                    task = self._tasks.get(task_id)
                    if task and task.status == TaskStatus.SCHEDULED:
                        self._running_tasks.add(task_id)
                        task.status = TaskStatus.RUNNING
                        task.started_at = datetime.now()
                        task.worker_id = self.worker_id
                        
                # 如果任务不存在或状态不对，跳过执行
                if not task or task.status != TaskStatus.RUNNING:
                    if task_id in self._running_tasks:
                        self._running_tasks.remove(task_id)
                    self._execution_queue.task_done()
                    continue
                    
                # 触发状态回调
                self._trigger_status_callback(task)
                
                # 执行任务
                logger.info(f"开始执行任务: id={task.task_id}, name={task.name}")
                result = self._execute_task(task)
                
                # 更新任务状态
                with self._lock:
                    if task_id in self._running_tasks:
                        self._running_tasks.remove(task_id)
                        
                    task.completed_at = datetime.now()
                    task.result = result
                    
                    if result.success:
                        task.status = TaskStatus.COMPLETED
                        logger.info(f"任务执行成功: id={task.task_id}, name={task.name}, 耗时={result.execution_time:.2f}秒")
                    else:
                        task.status = TaskStatus.FAILED
                        logger.warning(f"任务执行失败: id={task.task_id}, name={task.name}, 错误={result.error}")
                        
                        # 检查是否需要重试
                        if task.should_retry():
                            # 获取任务类型的重试策略
                            strategy = self._retry_strategies.get(task.task_type, self._retry_strategies["default"])
                            
                            # 检查最大重试次数
                            if task.retry_count < strategy["max_retries"]:
                                task.retry_count += 1
                                task.status = TaskStatus.PENDING
                                task.started_at = None
                                task.completed_at = None
                                task.result = None
                                
                                # 计算重试延迟
                                retry_delay = self._calculate_retry_delay(task)
                                
                                # 调整优先级
                                retry_priority = task.priority.value
                                retry_priority = max(1, retry_priority - self._resource_limits["task_priority_boost"])
                                negative_priority = -retry_priority
                                
                                # 如果重试延迟很短，直接添加到调度队列
                                if retry_delay <= 5:
                                    heapq.heappush(self._schedule_queue, (negative_priority, task.task_id))
                                    logger.info(f"任务将立即重试: id={task.task_id}, name={task.name}, 重试次数={task.retry_count}")
                                else:
                                    # 否则启动延迟重试线程
                                    self._schedule_delayed_retry(task, retry_delay, negative_priority)
                                    logger.info(f"任务将在 {retry_delay}秒 后重试: id={task.task_id}, name={task.name}, 重试次数={task.retry_count}")
                    
                # 触发状态回调
                self._trigger_status_callback(task)
                
                # 标记任务为完成
                self._execution_queue.task_done()
                
            except Exception as e:
                logger.error(f"任务执行循环出错: {e}")
                time.sleep(1)  # 出错后短暂等待
                
    def _execute_task(self, task: Task) -> TaskResult:
        """
        执行任务
        
        参数:
            task: 任务对象
            
        返回:
            任务执行结果
        """
        start_time = time.time()
        result = TaskResult()
        
        # 初始化资源使用记录
        self._task_resource_usage[task.task_id] = {
            "start_time": start_time,
            "cpu_percent": 0.0,
            "memory_mb": 0.0,
            "execution_time": 0.0
        }
        
        try:
            # 尝试监控资源使用
            process_info = self._start_resource_monitoring(task.task_id)
            
            # 获取任务处理器
            handler = self._task_handlers.get(task.task_type)
            if not handler:
                raise ValueError(f"任务类型 {task.task_type} 没有注册处理器")
                
            # 执行任务处理器
            result = handler(task)
            if not isinstance(result, TaskResult):
                result = TaskResult(success=True, data=result)
                
            # 停止资源监控
            self._stop_resource_monitoring(task.task_id, process_info)
                
        except Exception as e:
            # 处理异常
            logger.error(f"执行任务 {task.task_id} 出错: {e}")
            result = TaskResult(success=False, error=str(e))
            
        finally:
            # 计算执行时间
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            
            # 更新资源使用统计
            if task.task_id in self._task_resource_usage:
                self._task_resource_usage[task.task_id]["execution_time"] = execution_time
            
        return result
        
    def _schedule_tasks(self):
        """调度任务"""
        with self._lock:
            # 检查系统资源使用情况
            if not self._check_resource_availability():
                logger.warning("系统资源不足，暂停调度新任务")
                return
                
            # 检查当前运行任务数是否达到上限
            if len(self._running_tasks) >= self._resource_limits["max_concurrent_tasks"]:
                logger.debug(f"当前已达到最大并发任务数限制({self._resource_limits['max_concurrent_tasks']})")
                return
                
            # 临时存储无法调度的任务
            delayed_tasks = []
            
            # 获取所有任务的状态映射，用于检查依赖
            task_statuses = {task_id: task.status for task_id, task in self._tasks.items()}
            
            # 从调度队列中取出任务并处理
            while self._schedule_queue and len(self._running_tasks) < self._resource_limits["max_concurrent_tasks"]:
                # 查看堆顶元素但不移除
                if not self._schedule_queue:
                    break
                    
                _, task_id = self._schedule_queue[0]
                task = self._tasks.get(task_id)
                
                # 如果任务不存在，移除并继续
                if not task:
                    heapq.heappop(self._schedule_queue)
                    continue
                    
                # 如果任务不是PENDING状态，移除并继续
                if task.status != TaskStatus.PENDING:
                    heapq.heappop(self._schedule_queue)
                    continue
                    
                # 检查依赖是否满足
                dependencies_met = True
                for dep in task.dependencies:
                    dep_status = task_statuses.get(dep.task_id)
                    if not dep_status or dep_status != dep.required_status:
                        dependencies_met = False
                        break
                        
                # 如果依赖未满足，将任务放入延迟队列
                if not dependencies_met:
                    heapq.heappop(self._schedule_queue)
                    delayed_tasks.append((task.priority.value, task_id))
                    continue
                    
                # 依赖满足，调度任务
                heapq.heappop(self._schedule_queue)
                task.status = TaskStatus.SCHEDULED
                task.scheduled_at = datetime.now()
                
                # 触发状态回调
                self._trigger_status_callback(task)
                
                # 添加到执行队列
                self._execution_queue.put(task_id)
                logger.debug(f"调度任务: id={task.task_id}, name={task.name}")
                
            # 将延迟的任务重新加入调度队列
            for priority, task_id in delayed_tasks:
                heapq.heappush(self._schedule_queue, (-priority, task_id))
                
    def _check_timeouts(self):
        """检查超时任务"""
        with self._lock:
            now = datetime.now()
            
            for task_id in list(self._running_tasks):
                task = self._tasks.get(task_id)
                if not task or task.status != TaskStatus.RUNNING:
                    self._running_tasks.discard(task_id)
                    continue
                    
                # 如果任务设置了超时且已超时
                if task.timeout and task.started_at:
                    elapsed = (now - task.started_at).total_seconds()
                    if elapsed > task.timeout:
                        logger.warning(f"任务超时: id={task.task_id}, name={task.name}, 已运行{elapsed:.1f}秒")
                        task.status = TaskStatus.TIMEOUT
                        task.completed_at = now
                        task.result = TaskResult(
                            success=False, 
                            error=f"任务执行超时(限制{task.timeout}秒)",
                            execution_time=elapsed
                        )
                        
                        # 从运行集合中移除
                        self._running_tasks.discard(task_id)
                        
                        # 触发状态回调
                        self._trigger_status_callback(task)
                
    def _cleanup_completed_tasks(self):
        """清理已完成的任务"""
        with self._lock:
            now = datetime.now()
            cutoff = now - timedelta(hours=self._completed_task_ttl)
            
            expired_tasks = []
            for task_id, task in self._tasks.items():
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED, TaskStatus.TIMEOUT]:
                    if task.completed_at and task.completed_at < cutoff:
                        expired_tasks.append(task_id)
                        
            # 删除过期任务
            for task_id in expired_tasks:
                del self._tasks[task_id]
                
            if expired_tasks:
                logger.info(f"清理了 {len(expired_tasks)} 个已完成任务")

    def set_resource_limits(self, max_cpu_percent: Optional[int] = None,
                          max_memory_percent: Optional[int] = None,
                          max_concurrent_tasks: Optional[int] = None) -> None:
        """
        设置资源限制
        
        参数:
            max_cpu_percent: 最大CPU使用率(%)
            max_memory_percent: 最大内存使用率(%)
            max_concurrent_tasks: 最大并发任务数
        """
        with self._lock:
            if max_cpu_percent is not None:
                self._resource_limits["max_cpu_percent"] = max(0, min(100, max_cpu_percent))
                
            if max_memory_percent is not None:
                self._resource_limits["max_memory_percent"] = max(0, min(100, max_memory_percent))
                
            if max_concurrent_tasks is not None:
                self._resource_limits["max_concurrent_tasks"] = max(1, max_concurrent_tasks)
                
            logger.info(f"设置资源限制: CPU={self._resource_limits['max_cpu_percent']}%, "
                     f"内存={self._resource_limits['max_memory_percent']}%, "
                     f"并发任务={self._resource_limits['max_concurrent_tasks']}")

    def set_retry_strategy(self, task_type: str, max_retries: int = 3,
                        base_delay: int = 60, backoff_factor: float = 2.0,
                        max_delay: int = 3600, jitter: bool = True) -> None:
        """
        设置任务重试策略
        
        参数:
            task_type: 任务类型，'default'表示默认策略
            max_retries: 最大重试次数
            base_delay: 基础延迟时间(秒)
            backoff_factor: 退避因子
            max_delay: 最大延迟时间(秒)
            jitter: 是否添加随机抖动
        """
        with self._lock:
            self._retry_strategies[task_type] = {
                "max_retries": max(0, max_retries),
                "base_delay": max(1, base_delay),
                "backoff_factor": max(1.0, backoff_factor),
                "max_delay": max(1, max_delay),
                "jitter": jitter
            }
            
            logger.info(f"设置任务类型 {task_type} 的重试策略: "
                     f"最大重试次数={max_retries}, "
                     f"基础延迟={base_delay}秒, "
                     f"退避因子={backoff_factor}")

    def _check_resource_availability(self) -> bool:
        """
        检查系统资源是否满足要求
        
        返回:
            资源是否充足
        """
        try:
            # 尝试导入psutil库监控系统资源
            import psutil
            
            # 获取当前CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            if cpu_percent > self._resource_limits["max_cpu_percent"]:
                logger.warning(f"当前CPU使用率({cpu_percent}%)超过限制({self._resource_limits['max_cpu_percent']}%)")
                return False
                
            # 获取当前内存使用率
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > self._resource_limits["max_memory_percent"]:
                logger.warning(f"当前内存使用率({memory_percent}%)超过限制({self._resource_limits['max_memory_percent']}%)")
                return False
                
            return True
            
        except ImportError:
            # 如果没有psutil库，默认允许调度
            logger.debug("未安装psutil库，无法监控系统资源使用情况")
            return True
        except Exception as e:
            logger.warning(f"检查系统资源时出错: {e}")
            return True  # 出错时默认允许调度

    def _start_resource_monitoring(self, task_id: str) -> Optional[Any]:
        """
        开始监控任务资源使用
        
        参数:
            task_id: 任务ID
            
        返回:
            资源监控上下文，如进程对象
        """
        try:
            import psutil
            current_process = psutil.Process()
            return current_process
        except ImportError:
            return None
        except Exception as e:
            logger.debug(f"启动资源监控出错: {e}")
            return None
            
    def _stop_resource_monitoring(self, task_id: str, process_info: Optional[Any]) -> None:
        """
        停止监控任务资源使用并记录统计数据
        
        参数:
            task_id: 任务ID
            process_info: 资源监控上下文
        """
        if not process_info:
            return
            
        try:
            import psutil
            if isinstance(process_info, psutil.Process):
                # 获取CPU和内存使用情况
                cpu_percent = process_info.cpu_percent()
                memory_info = process_info.memory_info()
                memory_mb = memory_info.rss / (1024 * 1024)  # 转换为MB
                
                # 记录资源使用
                if task_id in self._task_resource_usage:
                    self._task_resource_usage[task_id]["cpu_percent"] = cpu_percent
                    self._task_resource_usage[task_id]["memory_mb"] = memory_mb
                    
                    logger.debug(f"任务 {task_id} 资源使用: CPU={cpu_percent}%, 内存={memory_mb:.2f}MB")
        except Exception as e:
            logger.debug(f"记录资源使用出错: {e}")

    def _calculate_retry_delay(self, task: Task) -> int:
        """
        计算任务重试延迟时间
        
        参数:
            task: 任务对象
            
        返回:
            重试延迟时间(秒)
        """
        # 获取任务类型的重试策略，如果不存在则使用默认策略
        strategy = self._retry_strategies.get(task.task_type, self._retry_strategies["default"])
        
        # 计算基础延迟时间
        base_delay = strategy["base_delay"]
        
        # 指数退避计算
        backoff = base_delay * (strategy["backoff_factor"] ** task.retry_count)
        delay = min(backoff, strategy["max_delay"])
        
        # 添加随机抖动，±20%
        if strategy["jitter"]:
            jitter_factor = 1.0 + (random.random() * 0.4 - 0.2)  # 0.8-1.2之间的随机数
            delay = delay * jitter_factor
            
        return int(delay)

    def _schedule_delayed_retry(self, task: Task, delay: int, priority: int) -> None:
        """
        安排延迟重试
        
        参数:
            task: 任务对象
            delay: 延迟时间(秒)
            priority: 优先级(负值)
        """
        def _delayed_retry():
            try:
                time.sleep(delay)
                with self._lock:
                    # 确保任务仍处于pending状态(可能在等待期间被取消)
                    current_task = self._tasks.get(task.task_id)
                    if current_task and current_task.status == TaskStatus.PENDING:
                        heapq.heappush(self._schedule_queue, (priority, task.task_id))
                        logger.debug(f"延迟重试任务已添加到调度队列: id={task.task_id}")
            except Exception as e:
                logger.error(f"延迟重试出错: {e}")
                
        # 启动延迟重试线程
        retry_thread = threading.Thread(target=_delayed_retry)
        retry_thread.daemon = True
        retry_thread.start()

    def get_task_resource_usage(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务资源使用情况
        
        参数:
            task_id: 任务ID
            
        返回:
            资源使用统计字典
        """
        with self._lock:
            return self._task_resource_usage.get(task_id, {})

    # 在TaskScheduler类中添加获取已注册任务类型的方法
    def get_registered_task_types(self) -> List[str]:
        """
        获取所有已注册的任务类型
        
        返回:
            任务类型列表
        """
        with self._lock:
            return list(self._task_handlers.keys())

class DistributedTaskManager:
    """
    分布式任务管理器
    
    协调多个节点间的任务分发和执行，实现任务的负载均衡和高可用性。
    """
    
    def __init__(self, local_scheduler: Optional[TaskScheduler] = None, health_aware_group_manager: Optional[Any] = None,
                task_history_logger: Optional[TaskHistoryLogger] = None, plugin_manager: Optional[PluginManager] = None):
        """
        初始化分布式任务管理器
        
        参数:
            local_scheduler: 本地任务调度器，如果为None则创建一个新的调度器
            health_aware_group_manager: 健康感知分组管理器，用于获取系统健康信息
            task_history_logger: 任务历史记录器，如果为None则创建一个新的记录器
            plugin_manager: 插件管理器，如果为None则创建一个新的管理器
        """
        # 本地调度器
        self.local_scheduler = local_scheduler or TaskScheduler()
        
        # 健康感知分组管理器
        self._health_aware_group_manager: Optional[Any] = health_aware_group_manager
        
        # 任务历史记录器
        self._task_history_logger = task_history_logger or TaskHistoryLogger()
        
        # 插件管理器
        self._plugin_manager = plugin_manager or PluginManager()
        
        # 远程节点信息
        self.nodes: Dict[str, Dict[str, Any]] = {}
        self._node_lock = threading.RLock()
        
        # 节点健康检查相关
        self._running = False
        self._health_check_thread: Optional[threading.Thread] = None
        self._health_check_interval = 30  # 健康检查间隔(秒)
        
        # 任务分发策略
        self.distribution_strategy = "health_aware"  # health_aware, load_based, round_robin, random
        self._task_counter = 0  # 用于round_robin策略
        
        # 任务同步相关
        self._sync_thread: Optional[threading.Thread] = None
        self._sync_interval = 60  # 同步间隔(秒)
        self._pending_sync_tasks: Set[str] = set()
        
        # 任务持久化
        self._state_dir = Path("data/tasks")
        self._state_file = self._state_dir / "task_state.pickle"
        self._state_backup_file = self._state_dir / "task_state.backup.pickle"
        self._persistence_enabled = True
        self._persistence_thread: Optional[threading.Thread] = None
        self._persistence_interval = 300  # 持久化间隔(秒)
        self._last_persisted = datetime.now()
        self._dirty = False  # 标记状态是否有变化
        
        # 事件通知回调
        self._event_handlers: Dict[str, List[Callable[[Dict[str, Any]], None]]] = {}
        
        # 任务执行指标
        self._task_metrics: Dict[str, Dict[str, Any]] = {}
        
        logger.info("分布式任务管理器初始化")
        
    def start(self):
        """启动分布式任务管理器"""
        if self._running:
            return
            
        logger.info("启动分布式任务管理器...")
        
        # 确保目录存在
        if self._persistence_enabled:
            os.makedirs(self._state_dir, exist_ok=True)
            
            # 尝试加载持久化状态
            self._load_state()
            
        # 启动本地调度器
        self.local_scheduler.start()
        
        # 启动任务历史记录器
        self._task_history_logger.start()
        
        # 启动插件管理器
        self._plugin_manager.start()
        
        # 从插件加载任务处理器
        self._load_task_handlers_from_plugins()
        
        # 启动健康检查线程
        self._running = True
        self._health_check_thread = threading.Thread(target=self._health_check_loop)
        self._health_check_thread.daemon = True
        self._health_check_thread.start()
        
        # 启动任务同步线程
        self._sync_thread = threading.Thread(target=self._sync_loop)
        self._sync_thread.daemon = True
        self._sync_thread.start()
        
        # 启动持久化线程
        if self._persistence_enabled:
            self._persistence_thread = threading.Thread(target=self._persistence_loop)
            self._persistence_thread.daemon = True
            self._persistence_thread.start()
            
        # 注册任务状态回调
        for status in TaskStatus:
            self.local_scheduler.register_status_callback(
                status, 
                lambda task: self._handle_task_status_change(task)
            )
            
        logger.info("分布式任务管理器已启动")
        
    def stop(self):
        """停止分布式任务管理器"""
        if not self._running:
            return
            
        logger.info("停止分布式任务管理器...")
        
        self._running = False
        
        # 停止本地调度器
        self.local_scheduler.stop()
        
        # 停止任务历史记录器
        self._task_history_logger.stop()
        
        # 停止插件管理器
        self._plugin_manager.stop()
        
        # 等待线程结束
        if self._health_check_thread:
            self._health_check_thread.join(timeout=5)
            
        if self._sync_thread:
            self._sync_thread.join(timeout=5)
            
        if self._persistence_thread:
            self._persistence_thread.join(timeout=5)
            
        # 持久化最终状态
        if self._persistence_enabled and self._dirty:
            self._save_state()
            
        logger.info("分布式任务管理器已停止")
        
    def register_node(self, node_id: str, host: str, port: int, 
                    capacity: int = 100, capabilities: Optional[List[str]] = None) -> bool:
        """
        注册远程节点
        
        参数:
            node_id: 节点ID
            host: 主机地址
            port: API端口
            capacity: 节点处理能力(1-100)
            capabilities: 节点支持的任务类型列表
            
        返回:
            是否成功注册
        """
        with self._node_lock:
            if node_id in self.nodes:
                logger.warning(f"节点 {node_id} 已注册，将更新信息")
                
            self.nodes[node_id] = {
                "id": node_id,
                "host": host,
                "port": port,
                "capacity": max(1, min(100, capacity)),
                "capabilities": capabilities or [],
                "status": "online",
                "load": 0.0,
                "task_count": 0,
                "last_seen": datetime.now(),
                "health_check_failures": 0
            }
            
            logger.info(f"注册节点: {node_id}, {host}:{port}")
            self._dirty = True
            return True
            
    def unregister_node(self, node_id: str) -> bool:
        """
        取消注册远程节点
        
        参数:
            node_id: 节点ID
            
        返回:
            是否成功取消注册
        """
        with self._node_lock:
            if node_id not in self.nodes:
                logger.warning(f"节点 {node_id} 不存在，无法取消注册")
                return False
                
            del self.nodes[node_id]
            logger.info(f"取消注册节点: {node_id}")
            self._dirty = True
            return True
            
    def submit_task(self, task: Task, node_id: Optional[str] = None) -> str:
        """
        提交任务
        
        参数:
            task: 任务对象
            node_id: 指定的节点ID，如果为None则根据分发策略选择节点
            
        返回:
            任务ID
        """
        # 如果指定了节点，尝试提交到该节点
        if node_id:
            if node_id == self.local_scheduler.worker_id:
                # 提交到本地调度器
                return self.local_scheduler.submit_task(task)
            else:
                # 提交到远程节点
                with self._node_lock:
                    if node_id not in self.nodes:
                        logger.warning(f"节点 {node_id} 不存在，将使用分发策略选择节点")
                    else:
                        success = self._submit_to_remote(task, node_id)
                        if success:
                            return task.task_id
                
        # 使用分发策略选择节点
        target_node = self._select_node_for_task(task)
        if target_node == self.local_scheduler.worker_id:
            # 提交到本地调度器
            return self.local_scheduler.submit_task(task)
        else:
            # 提交到远程节点
            success = self._submit_to_remote(task, target_node)
            if success:
                return task.task_id
                
        # 如果远程提交失败，回退到本地执行
        logger.warning(f"无法提交任务到远程节点，回退到本地执行: {task.task_id}")
        return self.local_scheduler.submit_task(task)
        
    def get_task(self, task_id: str) -> Optional[Task]:
        """
        获取任务
        
        参数:
            task_id: 任务ID
            
        返回:
            任务对象，如果不存在则返回None
        """
        # 首先尝试从本地获取
        local_task = self.local_scheduler.get_task(task_id)
        if local_task:
            return local_task
            
        # 尝试从远程节点获取
        with self._node_lock:
            for node_id, node_info in self.nodes.items():
                if node_info["status"] != "online":
                    continue
                    
                try:
                    url = f"http://{node_info['host']}:{node_info['port']}/api/tasks/{task_id}"
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        task_data = response.json()
                        return Task.from_dict(task_data)
                except Exception as e:
                    logger.warning(f"从节点 {node_id} 获取任务 {task_id} 失败: {e}")
                    
        return None
        
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        参数:
            task_id: 任务ID
            
        返回:
            是否成功取消
        """
        # 首先尝试从本地取消
        local_task = self.local_scheduler.get_task(task_id)
        if local_task:
            return self.local_scheduler.cancel_task(task_id)
            
        # 尝试从远程节点取消
        with self._node_lock:
            for node_id, node_info in self.nodes.items():
                if node_info["status"] != "online":
                    continue
                    
                try:
                    url = f"http://{node_info['host']}:{node_info['port']}/api/tasks/{task_id}/cancel"
                    response = requests.post(url, timeout=5)
                    if response.status_code == 200:
                        return True
                except Exception as e:
                    logger.warning(f"从节点 {node_id} 取消任务 {task_id} 失败: {e}")
                    
        return False
        
    def register_task_handler(self, task_type: str, handler: Callable[[Task], TaskResult]) -> bool:
        """
        注册任务处理器
        
        参数:
            task_type: 任务类型
            handler: 任务处理器函数
            
        返回:
            是否成功注册
        """
        return self.local_scheduler.register_task_handler(task_type, handler)
        
    def register_event_handler(self, event_type: str, handler: Callable[[Dict[str, Any]], None]) -> bool:
        """
        注册事件处理器
        
        参数:
            event_type: 事件类型
            handler: 事件处理器函数
            
        返回:
            是否成功注册
        """
        with self._node_lock:
            if event_type not in self._event_handlers:
                self._event_handlers[event_type] = []
                
            if handler not in self._event_handlers[event_type]:
                self._event_handlers[event_type].append(handler)
                return True
                
            return False
            
    def unregister_event_handler(self, event_type: str, handler: Callable[[Dict[str, Any]], None]) -> bool:
        """
        取消注册事件处理器
        
        参数:
            event_type: 事件类型
            handler: 事件处理器函数
            
        返回:
            是否成功取消注册
        """
        with self._node_lock:
            if event_type not in self._event_handlers:
                return False
                
            try:
                self._event_handlers[event_type].remove(handler)
                return True
            except ValueError:
                return False
                
    def _trigger_event(self, event_type: str, data: Dict[str, Any]):
        """
        触发事件
        
        参数:
            event_type: 事件类型
            data: 事件数据
        """
        handlers = []
        with self._node_lock:
            if event_type in self._event_handlers:
                handlers = self._event_handlers[event_type].copy()
                
        for handler in handlers:
            try:
                handler(data)
            except Exception as e:
                logger.error(f"执行事件处理器出错: {e}")
                
    def _health_check_loop(self):
        """健康检查循环"""
        while self._running:
            try:
                self._check_nodes_health()
                time.sleep(self._health_check_interval)
            except Exception as e:
                logger.error(f"节点健康检查出错: {e}")
                time.sleep(60)  # 出错后等待较长时间
                
    def _check_nodes_health(self):
        """检查所有节点的健康状态"""
        with self._node_lock:
            for node_id, node_info in self.nodes.items():
                try:
                    # 如果节点已经offline，尝试恢复
                    if node_info["status"] == "offline":
                        # 如果失败次数过多，则较少检查
                        if node_info["health_check_failures"] > 5 and random.random() > 0.2:
                            continue
                    
                    # 发送健康检查请求
                    url = f"http://{node_info['host']}:{node_info['port']}/api/health"
                    response = requests.get(url, timeout=5)
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        # 更新节点信息
                        node_info["status"] = "online"
                        node_info["last_seen"] = datetime.now()
                        node_info["load"] = data.get("load", 0.0)
                        node_info["task_count"] = data.get("task_count", 0)
                        node_info["health_check_failures"] = 0
                        
                        # 如果节点状态从offline变为online，触发事件
                        if node_info["status"] == "offline":
                            self._trigger_event("node_recovered", {"node_id": node_id})
                    else:
                        self._handle_node_failure(node_id, node_info, f"HTTP错误: {response.status_code}")
                        
                except Exception as e:
                    self._handle_node_failure(node_id, node_info, str(e))
                    
    def _handle_node_failure(self, node_id: str, node_info: Dict[str, Any], reason: str):
        """
        处理节点故障
        
        参数:
            node_id: 节点ID
            node_info: 节点信息
            reason: 故障原因
        """
        node_info["health_check_failures"] += 1
        
        # 如果连续失败超过阈值，标记为离线
        if node_info["health_check_failures"] >= 3:
            # 如果之前是在线状态，触发事件
            if node_info["status"] == "online":
                node_info["status"] = "offline"
                self._trigger_event("node_failure", {
                    "node_id": node_id,
                    "reason": reason,
                    "failures": node_info["health_check_failures"]
                })
                
                # 重新分配该节点的任务
                self._reassign_tasks_from_node(node_id)
                
        logger.warning(f"节点 {node_id} 健康检查失败: {reason}, 连续失败次数: {node_info['health_check_failures']}")
        
    def _reassign_tasks_from_node(self, node_id: str):
        """
        重新分配节点的任务
        
        参数:
            node_id: 节点ID
        """
        # 此函数需要通过API获取节点的任务列表，然后重新分配
        # 由于没有实际的API实现，此处只记录日志
        logger.info(f"开始重新分配节点 {node_id} 的任务")
        self._trigger_event("tasks_reassigned", {"node_id": node_id})
        
    def _select_node_for_task(self, task: Task) -> str:
        """
        为任务选择合适的节点
        
        参数:
            task: 任务对象
            
        返回:
            节点ID
        """
        with self._node_lock:
            # 准备候选节点列表
            candidates = []
            
            # 首先将本地节点加入候选
            candidates.append((self.local_scheduler.worker_id, 0.5, 100))  # (node_id, load, capacity)
            
            # 添加远程节点
            for node_id, node_info in self.nodes.items():
                if node_info["status"] != "online":
                    continue
                    
                # 检查节点是否支持此任务类型
                capabilities = node_info.get("capabilities", [])
                if capabilities and task.task_type not in capabilities:
                    continue
                    
                # 添加到候选列表
                candidates.append((
                    node_id, 
                    node_info["load"], 
                    node_info["capacity"]
                ))
                
            # 如果没有候选节点，返回本地节点
            if not candidates:
                return self.local_scheduler.worker_id
                
            # 根据分发策略选择节点
            if self.distribution_strategy == "health_aware" and self._health_aware_group_manager:
                # 健康感知策略：考虑节点健康状况和负载
                return self._select_node_by_health(task, candidates)
                
            elif self.distribution_strategy == "load_based":
                # 负载均衡策略：选择负载最低的节点
                selected = min(candidates, key=lambda x: x[1] / x[2])
                return selected[0]
                
            elif self.distribution_strategy == "round_robin":
                # 轮询策略
                self._task_counter = (self._task_counter + 1) % len(candidates)
                return candidates[self._task_counter][0]
                
            elif self.distribution_strategy == "random":
                # 随机策略，但考虑容量权重
                weights = [c[2] for c in candidates]
                return random.choices(
                    [c[0] for c in candidates], 
                    weights=weights, 
                    k=1
                )[0]
                
            # 默认返回本地节点
            return self.local_scheduler.worker_id
            
    def _select_node_by_health(self, task: Task, candidates: List[Tuple[str, float, int]]) -> str:
        """
        基于健康状况为任务选择最合适的节点
        
        参数:
            task: 任务对象
            candidates: 候选节点列表，每项为(node_id, load, capacity)
            
        返回:
            选择的节点ID
        """
        # 为每个候选节点计算综合得分
        node_scores = {}
        
        for node_id, load, capacity in candidates:
            # 基础分数
            base_score = 50.0
            
            # 通过分组管理器获取健康分数
            health_score = 1.0  # 默认健康分数
            if self._health_aware_group_manager is not None:
                if node_id == self.local_scheduler.worker_id:
                    # 本地节点，获取整体健康状况
                    if hasattr(self._health_aware_group_manager, 'get_all_health_statuses'):
                        health_statuses = self._health_aware_group_manager.get_all_health_statuses()
                        health_scores = [info.get("score", 1.0) for info in health_statuses.values()]
                        if health_scores:
                            health_score = sum(health_scores) / len(health_scores)
                else:
                    # 远程节点，通过分组管理器获取健康分数
                    if hasattr(self._health_aware_group_manager, 'get_server_health_score'):
                        health_score = self._health_aware_group_manager.get_server_health_score(node_id)
            
            # 健康分数调整（最多±30分）
            health_adjustment = (health_score - 0.5) * 60
            
            # 负载调整（满负载时最多-20分）
            load_adjustment = -20 * (load / max(1.0, capacity))
            
            # 任务类型匹配调整
            type_match_adjustment = 0
            if node_id != self.local_scheduler.worker_id:
                node_info = self.nodes.get(node_id, {})
                capabilities = node_info.get("capabilities", [])
                if task.task_type in capabilities:
                    type_match_adjustment = 10
            
            # 计算最终得分
            final_score = base_score + health_adjustment + load_adjustment + type_match_adjustment
            node_scores[node_id] = final_score
            
            logger.debug(f"节点 {node_id} 的任务分配得分: {final_score:.1f} (健康: {health_score:.2f}, 负载: {load:.2f}/{capacity})")
        
        # 选择得分最高的节点
        if not node_scores:
            return self.local_scheduler.worker_id
            
        best_node = max(node_scores.items(), key=lambda x: x[1])[0]
        logger.info(f"基于健康状况为任务 {task.task_id} 选择节点 {best_node}")
        return best_node
        
    def _submit_to_remote(self, task: Task, node_id: str) -> bool:
        """
        提交任务到远程节点
        
        参数:
            task: 任务对象
            node_id: 节点ID
            
        返回:
            是否成功提交
        """
        with self._node_lock:
            if node_id not in self.nodes:
                logger.warning(f"节点 {node_id} 不存在")
                return False
                
            node_info = self.nodes[node_id]
            if node_info["status"] != "online":
                logger.warning(f"节点 {node_id} 当前不在线")
                return False
                
            try:
                # 发送任务到远程节点
                url = f"http://{node_info['host']}:{node_info['port']}/api/tasks"
                response = requests.post(
                    url, 
                    json=task.to_dict(), 
                    timeout=5
                )
                
                if response.status_code == 200 or response.status_code == 201:
                    # 更新节点信息
                    node_info["task_count"] += 1
                    logger.info(f"成功提交任务 {task.task_id} 到节点 {node_id}")
                    
                    # 将任务添加到同步队列
                    self._pending_sync_tasks.add(task.task_id)
                    
                    return True
                else:
                    logger.warning(f"提交任务到节点 {node_id} 失败: HTTP {response.status_code}")
                    return False
                    
            except Exception as e:
                logger.warning(f"提交任务到节点 {node_id} 出错: {e}")
                return False
                
    def _sync_loop(self):
        """任务同步循环"""
        while self._running:
            try:
                self._sync_tasks()
                time.sleep(self._sync_interval)
            except Exception as e:
                logger.error(f"任务同步出错: {e}")
                time.sleep(60)  # 出错后等待较长时间
                
    def _sync_tasks(self):
        """同步任务状态"""
        # 同步需要跟踪的任务
        tasks_to_sync = list(self._pending_sync_tasks)
        self._pending_sync_tasks.clear()
        
        for task_id in tasks_to_sync:
            task = self.get_task(task_id)
            if task:
                logger.debug(f"同步任务状态: {task_id}, 状态: {task.status.name}")
                
                # 如果任务已完成，可以从同步列表中移除
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED, TaskStatus.TIMEOUT]:
                    continue
                    
                # 否则，继续跟踪此任务
                self._pending_sync_tasks.add(task_id)
                
    def _persistence_loop(self):
        """持久化循环"""
        while self._running:
            try:
                now = datetime.now()
                
                # 如果状态有变化且距上次持久化超过时间间隔，则进行持久化
                if self._dirty and (now - self._last_persisted).total_seconds() >= self._persistence_interval:
                    self._save_state()
                    self._last_persisted = now
                    self._dirty = False
                    
                time.sleep(10)  # 检查频率不需要太高
                
            except Exception as e:
                logger.error(f"持久化状态出错: {e}")
                time.sleep(60)  # 出错后等待较长时间
                
    def _save_state(self):
        """将当前状态保存到磁盘"""
        if not self._state_file:
            return
            
        try:
            # 准备要保存的状态字典
            state_to_save = {
                "nodes": self.nodes,
                "distribution_strategy": self.distribution_strategy,
                "task_counter": self._task_counter,
                # 将 set 转换为 list 以便 JSON 序列化
                "pending_sync_tasks": list(self._pending_sync_tasks),
                "task_metrics": self._task_metrics, # 假设 task_metrics 本身是 JSON 兼容的
                "timestamp": datetime.utcnow().isoformat() 
            }
            
            # 创建临时文件
            temp_file = self._state_file.with_suffix('.tmp')
            
            # 保存到临时文件 (使用 JSON)
            with open(temp_file, "w", encoding='utf-8') as f:
                json.dump(state_to_save, f, ensure_ascii=False, indent=2)
            
            # 替换主文件 (原子操作)
            os.replace(temp_file, self._state_file)
            
            # 尝试创建备份
            try:
                shutil.copy2(self._state_file, self._state_backup_file)
            except Exception as e_backup:
                logger.warning(f"创建状态备份文件失败: {e_backup}")
                
            self._dirty = False # 重置脏标记
            logger.info(f"状态已保存到 {self._state_file}")
            
        except Exception as e:
            logger.error(f"保存状态失败: {e}")
            
    def _load_state(self):
        """从磁盘加载状态 (已更新为JSON格式)"""
        try:
            file_to_load = None
            if self._state_file.exists():
                file_to_load = self._state_file
            elif self._state_backup_file.exists():
                file_to_load = self._state_backup_file
                logger.warning(f"使用备份状态文件: {self._state_backup_file}")
            else:
                logger.info("没有找到状态文件，使用默认状态")
                return
                
            # 加载状态 (使用 JSON)
            with open(file_to_load, "r", encoding='utf-8') as f:
                state = json.load(f)
                
            # 恢复状态
            self.nodes = state.get("nodes", {})
            self.distribution_strategy = state.get("distribution_strategy", "health_aware")
            self._task_counter = state.get("task_counter", 0)
            # 从 list 转换回 set
            loaded_pending_tasks = state.get("pending_sync_tasks", [])
            self._pending_sync_tasks = set(loaded_pending_tasks)
            self._task_metrics = state.get("task_metrics", {}) # 假设 task_metrics 是 JSON 兼容的
            
            timestamp_str = state.get("timestamp")
            loaded_timestamp = None
            if timestamp_str:
                try:
                    loaded_timestamp = datetime.fromisoformat(timestamp_str)
                except ValueError:
                    logger.warning(f"无法解析状态文件中的时间戳: {timestamp_str}")
            
            logger.info(f"从 {file_to_load} 加载状态, 时间戳: {loaded_timestamp or '未知'}")
            
        except json.JSONDecodeError as je:
            logger.error(f"加载状态文件 {file_to_load} 失败 (JSON解码错误): {je}")
        except IOError as ioe:
            logger.error(f"加载状态文件 {file_to_load} 失败 (IO错误): {ioe}")
        except Exception as e:
            logger.error(f"加载状态失败: {e}")
            # 在未知错误时，可以考虑重置为默认状态或采取其他恢复措施
            self.nodes = {}
            self.distribution_strategy = "health_aware"
            self._task_counter = 0
            self._pending_sync_tasks = set()
            self._task_metrics = {}
            logger.warning("由于加载错误，任务管理器状态已重置为默认值。")
            
    def _handle_task_status_change(self, task: Task):
        """
        处理任务状态变更
        
        参数:
            task: 任务对象
        """
        # 记录任务指标
        self.record_task_metrics(task)
        
        # 记录任务历史
        if task.status == TaskStatus.PENDING:
            self._task_history_logger.record_task_created(task)
        elif task.status == TaskStatus.SCHEDULED:
            self._task_history_logger.record_task_scheduled(task)
        elif task.status == TaskStatus.RUNNING:
            self._task_history_logger.record_task_started(task)
        elif task.status == TaskStatus.COMPLETED:
            # 获取资源使用信息
            resource_usage = None
            if hasattr(self.local_scheduler, '_task_resource_usage'):
                resource_usage = self.local_scheduler._task_resource_usage.get(task.task_id)
            
            self._task_history_logger.record_task_completed(task, resource_usage)
        elif task.status == TaskStatus.FAILED:
            # 获取资源使用信息
            resource_usage = None
            if hasattr(self.local_scheduler, '_task_resource_usage'):
                resource_usage = self.local_scheduler._task_resource_usage.get(task.task_id)
                
            self._task_history_logger.record_task_failed(task, resource_usage)
        elif task.status == TaskStatus.CANCELLED:
            self._task_history_logger.record_task_cancelled(task)
        elif task.status == TaskStatus.TIMEOUT:
            self._task_history_logger.record_task_timeout(task)
        
        # 触发任务状态变更事件
        self._trigger_event("task_status_changed", {
            "task_id": task.task_id,
            "status": task.status.value,
            "name": task.name,
            "type": task.task_type
        })
        
        # 如果任务完成或失败，可能需要触发其他事件
        if task.status == TaskStatus.COMPLETED:
            self._trigger_event("task_completed", {
                "task_id": task.task_id,
                "name": task.name,
                "type": task.task_type,
                "result": task.result.to_dict() if task.result else None
            })
        elif task.status == TaskStatus.FAILED:
            self._trigger_event("task_failed", {
                "task_id": task.task_id,
                "name": task.name,
                "type": task.task_type,
                "error": task.result.error if task.result else "Unknown error"
            })
            
        # 标记状态为脏，需要进行持久化
        self._dirty = True
        
    def record_task_metrics(self, task: Task) -> None:
        """
        记录任务执行指标
        
        参数:
            task: 任务对象
        """
        if not task or not task.task_id:
            return
            
        # 如果任务已完成，记录执行指标
        if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED, TaskStatus.TIMEOUT]:
            metrics = {
                "task_id": task.task_id,
                "name": task.name,
                "type": task.task_type,
                "status": task.status.value,
                "priority": task.priority.value,
                "worker_id": task.worker_id,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "scheduled_at": task.scheduled_at.isoformat() if task.scheduled_at else None,
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            }
            
            # 添加执行时间指标
            if task.started_at and task.completed_at:
                execution_time = (task.completed_at - task.started_at).total_seconds()
                metrics["execution_time"] = execution_time
                
            # 添加等待时间指标
            if task.created_at and task.started_at:
                wait_time = (task.started_at - task.created_at).total_seconds()
                metrics["wait_time"] = wait_time
                
            # 添加结果指标
            if task.result:
                metrics["success"] = task.result.success
                metrics["error"] = task.result.error
                metrics["result_execution_time"] = task.result.execution_time
                
            # 保存指标
            self._task_metrics[task.task_id] = metrics
            self._dirty = True  # 标记需要持久化
            
            # 如果指标数量过多，清理最旧的指标
            if len(self._task_metrics) > 10000:
                # 按完成时间排序，删除最旧的20%
                sorted_metrics = sorted(
                    self._task_metrics.items(),
                    key=lambda x: x[1].get("completed_at", ""),
                    reverse=False
                )
                to_delete = sorted_metrics[:int(len(sorted_metrics) * 0.2)]
                
                for task_id, _ in to_delete:
                    if task_id in self._task_metrics:
                        del self._task_metrics[task_id]

    def set_health_aware_group_manager(self, manager) -> None:
        """
        设置健康感知分组管理器
        
        参数:
            manager: 健康感知分组管理器实例
        """
        self._health_aware_group_manager = manager
        
        # 如果分组管理器存在，将自己注册为任务分配管理器
        if self._health_aware_group_manager and hasattr(self._health_aware_group_manager, 'set_task_distribution_manager'):
            self._health_aware_group_manager.set_task_distribution_manager(self)
            logger.info("已向健康感知分组管理器注册任务分配管理器")
        
        # 更新分发策略
        if self._health_aware_group_manager:
            self.distribution_strategy = "health_aware"
            logger.info("已启用健康感知的任务分发策略")

    def get_pending_tasks(self) -> List[Task]:
        """
        获取待处理的任务列表
        
        返回:
            待处理的任务对象列表
        """
        pending_tasks = []
        
        if self.local_scheduler:
            with self.local_scheduler._lock:
                for task_id, task in self.local_scheduler._tasks.items():
                    if task.status == TaskStatus.PENDING:
                        pending_tasks.append(task)
                    
        return pending_tasks

    def get_task_history(self, task_id: str) -> List[Dict[str, Any]]:
        """
        获取任务的完整历史记录
        
        参数:
            task_id: 任务ID
            
        返回:
            历史记录列表
        """
        return self._task_history_logger.get_task_history(task_id)
        
    def search_tasks(self, **kwargs) -> List[Dict[str, Any]]:
        """
        搜索符合条件的任务
        
        参数:
            task_type: 任务类型
            status: 任务状态
            worker_id: 工作者ID
            from_time: 开始时间(ISO格式)
            to_time: 结束时间(ISO格式)
            limit: 最大返回记录数
            
        返回:
            符合条件的任务列表
        """
        return self._task_history_logger.search_tasks(**kwargs)
        
    def get_task_stats(self, days: int = 7) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        参数:
            days: 统计天数
            
        返回:
            统计信息字典
        """
        return self._task_history_logger.get_task_stats(days) 

    def _load_task_handlers_from_plugins(self):
        """从插件加载任务处理器"""
        # 获取所有插件
        all_plugins = self._plugin_manager.get_all_plugins()
        
        for plugin_id, plugin_info in all_plugins.items():
            # 获取插件支持的任务类型
            task_types = plugin_info.get("supported_task_types", [])
            
            # 注册每个任务类型的处理器
            for task_type in task_types:
                handler = self._plugin_manager.get_task_handler(task_type)
                if handler:
                    # 包装处理器以返回标准TaskResult
                    wrapped_handler = self._wrap_plugin_handler(handler)
                    
                    # 注册到本地调度器
                    self.local_scheduler.register_task_handler(task_type, wrapped_handler)
                    logger.info(f"从插件 {plugin_id} 注册任务类型 {task_type} 的处理器")
                    
    def _wrap_plugin_handler(self, handler: Callable) -> Callable:
        """
        包装插件处理器以确保返回标准TaskResult
        
        参数:
            handler: 原始处理器函数
            
        返回:
            包装后的处理器函数
        """
        def wrapped_handler(task: Task) -> TaskResult:
            try:
                # 调用原始处理器
                result = handler(task)
                
                # 如果结果已经是TaskResult，直接返回
                if isinstance(result, TaskResult):
                    return result
                    
                # 否则，包装结果
                return TaskResult(success=True, data=result)
                
            except Exception as e:
                logger.error(f"执行插件任务处理器出错: {e}")
                return TaskResult(success=False, error=str(e))
                
        return wrapped_handler

    def load_plugin(self, plugin_path: str) -> Optional[str]:
        """
        加载插件
        
        参数:
            plugin_path: 插件路径
            
        返回:
            插件ID，如果加载失败则返回None
        """
        plugin_id = self._plugin_manager.load_plugin_from_path(plugin_path)
        
        if plugin_id:
            # 从新插件中加载任务处理器
            handler = self._plugin_manager.get_task_handler(plugin_id)
            if handler:
                wrapped_handler = self._wrap_plugin_handler(handler)
                self.local_scheduler.register_task_handler(plugin_id, wrapped_handler)
                
        return plugin_id
        
    def unload_plugin(self, plugin_id: str) -> bool:
        """
        卸载插件
        
        参数:
            plugin_id: 插件ID
            
        返回:
            是否成功卸载
        """
        return self._plugin_manager.unregister_plugin(plugin_id)
        
    def get_loaded_plugins(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有已加载的插件
        
        返回:
            插件信息字典
        """
        return self._plugin_manager.get_all_plugins()
