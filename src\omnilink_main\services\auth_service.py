from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from typing import Set, Optional, Tuple, Dict, Any
from datetime import datetime, timedelta
from fastapi import HTTPException, status

from common.models.user import User
from common.schemas.token_schema import TokenPayload
from src.omnilink_main.core import security
from src.omnilink_main.core.security import verify_password, decode_token
from src.omnilink_main.core.redis import token_blacklist_service

class AuthService:
    """
    Service layer for handling all authentication-related business logic.
    """

    async def authenticate_user(self, db: AsyncSession, *, username: str, password: str) -> Optional[User]:
        """
        验证用户凭据并返回用户对象（如果认证成功）
        """
        user = await self.get_user_by_username(db, username=username)
        if not user or not self._authenticate(user=user, password=password):
            return None
        return user

    async def login(self, db: AsyncSession, *, username: str, password: str) -> Dict[str, Any]:
        """
        Handles the complete login process for a user.
        1. Fetches the user by username.
        2. Authenticates the user's password.
        3. Updates login metadata (timestamps, IP).
        4. Creates access and refresh tokens.
        
        Returns a dictionary containing token information.
        """
        user = await self.authenticate_user(db, username=username, password=password)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Update login metadata
        user.last_login_at = datetime.utcnow()
        await db.commit()
        
        access_token = self.create_access_token(user)
        refresh_token = self.create_refresh_token(user)
        
        return {
            "access_token": access_token, 
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }

    def _authenticate(self, *, user: Optional[User], password: str) -> bool:
        """
        Private method to authenticate a user.
        """
        if not user or not user.is_active:
            return False
        if not verify_password(password, user.hashed_password):
            return False
        return True

    def create_access_token(self, user: User) -> str:
        return security.create_access_token(
            subject=user.id
        )

    def create_refresh_token(self, user: User) -> str:
        return security.create_refresh_token(
            subject=user.id
        )

    async def logout(self, *, token: str) -> None:
        """
        Logs out a user by blacklisting their access token.
        Raises HTTPException if the token is invalid.
        """
        payload = decode_token(token)
        if not payload:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="无效的令牌")
        
        jti = payload.get("jti")
        exp = payload.get("exp")
        if not jti or not exp:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="令牌缺少必要的声明")
        
        expires_in = int(exp - datetime.utcnow().timestamp())
        if expires_in > 0:
            await token_blacklist_service.add_to_blacklist(jti, expires_in)
        
    async def refresh_access_token(self, *, refresh_token: str) -> str:
        """
        Refreshes an access token using a valid refresh token.
        Raises HTTPException if the refresh token is invalid or expired.
        """
        payload = decode_token(refresh_token)
        if not payload:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的刷新令牌")

        if payload.get("type") != "refresh" or datetime.utcnow().timestamp() > payload.get("exp", 0):
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="刷新令牌已过期或无效")
        
        jti = payload.get("jti")
        if not jti or await token_blacklist_service.is_blacklisted(jti):
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="刷新令牌已被撤销")

        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="刷新令牌中的主体无效")
        
        # Create a temporary user object for token creation
        from common.models.user import User
        temp_user = User(id=int(user_id))
        new_access_token = self.create_access_token(temp_user)
        return new_access_token

    async def get_user_by_username(self, db: AsyncSession, *, username: str) -> Optional[User]:
        """
        Retrieves a user by their username, eagerly loading roles.
        """
        statement = select(User).options(selectinload(User.roles)).where(User.username == username)
        result = await db.execute(statement)
        return result.scalars().first()

    async def get_user_by_id(self, db: AsyncSession, *, user_id: int) -> Optional[User]:
        """
        根据用户ID获取用户，包含角色信息
        """
        statement = select(User).options(selectinload(User.roles)).where(User.id == user_id)
        result = await db.execute(statement)
        return result.scalars().first()

    def get_user_permissions(self, user: User) -> Set[str]:
        """
        获取用户的所有权限
        """
        permissions = set()
        for role in user.roles:
            if role.permissions:
                permissions.update(role.permissions)
        return permissions

# Create a single, reusable instance of the service
auth_service_instance = AuthService()

