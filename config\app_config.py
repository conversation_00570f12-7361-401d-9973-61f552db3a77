"""
应用主配置模块

包含应用范围的配置，例如调试模式、密钥等。
"""

import os
import logging

logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# 核心应用配置
# -----------------------------------------------------------------------------

# 调试模式 (True 表示开启调试，False 表示关闭)
# 生产环境中应始终为 False
DEBUG = os.getenv("APP_DEBUG", "False").lower() == "true"

# 应用密钥，用于会话管理、CSRF保护等加密操作
# 生产环境中必须使用一个强大且随机生成的密钥，并从环境变量加载
SECRET_KEY = os.getenv("APP_SECRET_KEY", "a-very-secret-and-complex-key-for-dev-only")

# -----------------------------------------------------------------------------
# JWT 相关配置 (主用户认证)
# 注意：这些是用于普通用户/管理员的JWT，与从服务器的JWT分开
# -----------------------------------------------------------------------------
JWT_SECRET = os.getenv("JWT_SECRET", "your-default-main-jwt-secret-key") # 主JWT密钥
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
JWT_EXPIRE_SECONDS = int(os.getenv("JWT_EXPIRE_SECONDS", 3600)) # 1 hour
JWT_REFRESH_EXPIRE_SECONDS = int(os.getenv("JWT_REFRESH_EXPIRE_SECONDS", 86400 * 7)) # 7 days

# -----------------------------------------------------------------------------
# 从服务器JWT 相关配置 (由主服务器签发给从服务器的令牌的配置)
# 这些配置用于主服务器生成和验证发给从服务器的令牌
# 注意: ky/config/jwt_slave_config.py 是从服务器自己使用的配置
# AuthService._get_jwt_config_for_slave 需要这些值
# -----------------------------------------------------------------------------
JWT_SLAVE_SECRET = os.getenv("JWT_MASTER_FOR_SLAVE_SECRET", "master-secret-for-slave-tokens")
JWT_SLAVE_ALGORITHM = os.getenv("JWT_MASTER_FOR_SLAVE_ALGORITHM", "HS256")
JWT_SLAVE_EXPIRE_SECONDS = int(os.getenv("JWT_MASTER_FOR_SLAVE_EXPIRE_SECONDS", 86400)) # 24 hours


# -----------------------------------------------------------------------------
# 全局配置字典 (推荐)
# 将所有配置项组织到一个字典中，方便管理和访问
# -----------------------------------------------------------------------------
_CONFIG = {
    "DEBUG": DEBUG,
    "SECRET_KEY": SECRET_KEY,
    # 主用户JWT
    "JWT_SECRET": JWT_SECRET,
    "JWT_ALGORITHM": JWT_ALGORITHM,
    "JWT_EXPIRE_SECONDS": JWT_EXPIRE_SECONDS,
    "JWT_REFRESH_EXPIRE_SECONDS": JWT_REFRESH_EXPIRE_SECONDS,
    # 主服务器签发给从服务器的JWT
    "JWT_SLAVE_SECRET": JWT_SLAVE_SECRET, # AuthService._get_jwt_config_for_slave 使用
    "JWT_SLAVE_ALGORITHM": JWT_SLAVE_ALGORITHM, # AuthService._get_jwt_config_for_slave 使用
    "JWT_SLAVE_EXPIRE_SECONDS": JWT_SLAVE_EXPIRE_SECONDS, # AuthService._get_jwt_config_for_slave 使用
    # 可以添加更多配置项，例如数据库URL, Redis配置等
    # "DATABASE_URL": os.getenv("DATABASE_URL", "sqlite:///./test.db"),
    # "REDIS_HOST": os.getenv("REDIS_HOST", "localhost"),
    # "REDIS_PORT": int(os.getenv("REDIS_PORT", 6379)),
}


def get_app_config(key: str, default: any = None) -> any:
    """
    从应用配置中获取指定键的值。

    参数:
        key (str): 配置项的键。
        default (any, optional): 如果键不存在时返回的默认值。默认为 None。

    返回:
        any: 配置项的值，如果不存在则返回默认值。
    """
    value = _CONFIG.get(key, default)
    # logger.debug(f"获取应用配置: key='{key}', value='{value}', default='{default}'")
    if value is default and key not in _CONFIG:
        logger.warning(f"尝试访问的应用配置项 '{key}' 未找到，已返回默认值 '{default}'。")
    return value

# 可以在启动时打印一些关键配置项 (如果不是敏感信息)
if __name__ == "__main__" or DEBUG:
    logger.info("应用配置加载完成:")
    logger.info(f"  DEBUG Mode: {get_app_config('DEBUG')}")
    # 不要直接打印密钥等敏感信息
    # logger.info(f"  SECRET_KEY: {get_app_config('SECRET_KEY')[:5]}...") 
    logger.info(f"  JWT_SECRET (Main): Present")
    logger.info(f"  JWT_SLAVE_SECRET (Master for Slave): Present") 