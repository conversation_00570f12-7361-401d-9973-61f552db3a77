import asyncio
import logging
import aiohttp
from typing import Optional

from slave_server.core.config import settings
from slave_server.services.vh_service import VirtualHereService

logger = logging.getLogger(__name__)

class CommandHandlerService:
    def __init__(self, vh_service: VirtualHereService):
        self.vh_service = vh_service
        ws_protocol_url = settings.MAIN_SERVER_URL.replace("http", "ws", 1)
        self.ws_url = f"{ws_protocol_url}/api/v1/ws/slave/{settings.MAIN_SERVER_API_KEY}"
        self.reconnect_delay = 10  # seconds
        self._task: Optional[asyncio.Task] = None

    async def start(self):
        if self._task is None:
            self._task = asyncio.create_task(self._connection_loop())
            logger.info("Command handler service started.")

    async def stop(self):
        if self._task:
            self._task.cancel()
            self._task = None
            logger.info("Command handler service stopped.")

    async def _connection_loop(self):
        while True:
            try:
                logger.info(f"Connecting to WebSocket at {self.ws_url}...")
                async with aiohttp.ClientSession() as session:
                    async with session.ws_connect(self.ws_url) as ws:
                        logger.info("WebSocket connection established.")
                        await self._listen_for_commands(ws)
            except asyncio.CancelledError:
                logger.info("Connection loop cancelled.")
                break
            except Exception as e:
                logger.error(f"WebSocket connection failed: {e}. Retrying in 10 seconds...")
                await asyncio.sleep(self.reconnect_delay)

    async def _listen_for_commands(self, ws: aiohttp.ClientWebSocketResponse):
        async for msg in ws:
            if msg.type == aiohttp.WSMsgType.TEXT:
                try:
                    command_data = msg.json()
                    logger.info(f"Received command: {command_data}")
                    await self._dispatch_command(command_data)
                except Exception as e:
                    logger.error(f"Failed to process command: {msg.data}, Error: {e}")
            elif msg.type == aiohttp.WSMsgType.ERROR:
                break

    async def _dispatch_command(self, command: dict):
        action = command.get("action")
        device_address = command.get("device_address") # Expecting a universal device address

        if not device_address:
            logger.error(f"Command '{action}' is missing 'device_address'.")
            return

        if action == "SHARE_DEVICE":
            await self.vh_service.share_device(device_address=device_address)
        elif action == "UNSHARE_DEVICE":
            await self.vh_service.unshare_device(device_address=device_address)
        else:
            logger.warning(f"Unknown command action received: {action}")
