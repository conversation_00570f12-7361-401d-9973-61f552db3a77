"""
Session Management Module - Device session persistence and reconnection.

This module provides functionality for managing device connection sessions,
including automatic reconnection during network disruptions, session persistence,
and monitoring of device connectivity.
"""

from .session_manager import (
    SessionManager,
    Session,
    SessionState,
    SessionEvent,
    SessionMetrics,
    SessionHistoryEntry,
    session_manager
)

__all__ = [
    'SessionManager',
    'Session',
    'SessionState',
    'SessionEvent',
    'SessionMetrics',
    'SessionHistoryEntry',
    'session_manager'
] 
