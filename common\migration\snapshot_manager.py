"""
快照管理器

提供系统状态快照创建、恢复和管理功能。
"""

import logging
import os
import json
import shutil
import threading
import time
import hashlib
import tarfile
import datetime
import sys
import platform
import psutil
from enum import Enum
from typing import Dict, List, Optional, Set, Tuple, Any, Union, Callable
from dataclasses import dataclass, field
from pathlib import Path

# 数据库相关导入
import sqlite3
try:
    import psycopg2
    from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
    HAS_POSTGRES = True
except ImportError:
    HAS_POSTGRES = False

# 缓存相关导入
try:
    import redis
    HAS_REDIS = True
except ImportError:
    HAS_REDIS = False

logger = logging.getLogger("migration.snapshot")

class SnapshotStatus(Enum):
    """快照状态枚举"""
    IDLE = "idle"               # 空闲
    CREATING = "creating"       # 创建中
    RESTORING = "restoring"     # 恢复中
    VALIDATING = "validating"   # 验证中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"           # 失败
    DELETED = "deleted"         # 已删除

@dataclass
class SnapshotConfig:
    """快照配置数据类"""
    include_system: bool = True         # 是否包含系统状态
    include_processes: bool = True      # 是否包含进程信息
    include_network: bool = True        # 是否包含网络配置
    include_files: bool = True          # 是否包含文件系统
    include_database: bool = True       # 是否包含数据库
    include_cache: bool = True          # 是否包含缓存
    compress: bool = True               # 是否压缩
    encrypt: bool = True                # 是否加密
    validate_before: bool = True        # 创建前验证
    validate_after: bool = True         # 创建后验证
    auto_cleanup: bool = True           # 自动清理旧快照
    max_snapshots: int = 10             # 最大保留快照数
    retention_days: int = 30            # 保留天数

@dataclass
class SnapshotInfo:
    """快照信息数据类"""
    id: str                             # 快照ID
    name: str                           # 快照名称
    description: str = ""               # 快照描述
    config: SnapshotConfig = field(default_factory=SnapshotConfig)  # 快照配置
    status: SnapshotStatus = SnapshotStatus.IDLE  # 快照状态
    created_at: datetime.datetime = field(default_factory=datetime.datetime.now)  # 创建时间
    completed_at: Optional[datetime.datetime] = None  # 完成时间
    system_info: Dict[str, Any] = field(default_factory=dict)  # 系统信息
    process_info: Dict[str, Any] = field(default_factory=dict)  # 进程信息
    network_info: Dict[str, Any] = field(default_factory=dict)  # 网络信息
    files_info: Dict[str, Any] = field(default_factory=dict)  # 文件信息
    database_info: Dict[str, Any] = field(default_factory=dict)  # 数据库信息
    cache_info: Dict[str, Any] = field(default_factory=dict)  # 缓存信息
    progress: float = 0.0                # 进度（0-1）
    current_step: str = ""               # 当前步骤
    total_steps: int = 0                 # 总步骤数
    current_step_progress: float = 0.0   # 当前步骤进度
    logs: List[str] = field(default_factory=list)  # 日志
    errors: List[str] = field(default_factory=list)  # 错误
    warnings: List[str] = field(default_factory=list)  # 警告
    snapshot_path: Optional[str] = None   # 快照路径
    snapshot_size: int = 0                # 快照大小（字节）
    checksum: Optional[str] = None       # 校验和

class SnapshotManager:
    """快照管理器"""
    
    def __init__(self, data_dir: str = None):
        """
        初始化快照管理器
        
        Args:
            data_dir: 数据目录，默认为当前目录下的snapshot_data
        """
        self.data_dir = data_dir or os.path.join(os.getcwd(), "snapshot_data")
        self._lock = threading.RLock()
        self._snapshots: Dict[str, SnapshotInfo] = {}  # 快照ID -> 快照信息
        self._current_snapshot: Optional[str] = None  # 当前快照ID
        self._snapshot_thread: Optional[threading.Thread] = None  # 快照线程
        self._stop_snapshot = False  # 停止快照标志
        
        # 创建数据目录
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(os.path.join(self.data_dir, "snapshots"), exist_ok=True)
        os.makedirs(os.path.join(self.data_dir, "temp"), exist_ok=True)
        
        # 尝试加载已有快照信息
        self._load_snapshots()
        
        logger.info(f"快照管理器已初始化，数据目录：{self.data_dir}")
        
    def _load_snapshots(self):
        """加载已有快照信息"""
        snapshots_file = os.path.join(self.data_dir, "snapshots.json")
        if os.path.exists(snapshots_file):
            try:
                with open(snapshots_file, "r", encoding="utf-8") as f:
                    snapshots_data = json.load(f)
                    
                for snapshot_id, snapshot_data in snapshots_data.items():
                    # 转换状态枚举
                    status_str = snapshot_data.pop("status", "idle")
                    try:
                        status = SnapshotStatus(status_str)
                    except ValueError:
                        status = SnapshotStatus.IDLE
                        
                    # 转换日期时间
                    for dt_field in ["created_at", "completed_at"]:
                        if dt_field in snapshot_data and snapshot_data[dt_field]:
                            try:
                                snapshot_data[dt_field] = datetime.datetime.fromisoformat(snapshot_data[dt_field])
                            except (ValueError, TypeError):
                                snapshot_data[dt_field] = None
                                
                    # 转换配置
                    config_data = snapshot_data.pop("config", {})
                    config = SnapshotConfig(**config_data)
                    
                    # 创建快照信息
                    snapshot_info = SnapshotInfo(
                        id=snapshot_id,
                        status=status,
                        config=config,
                        **snapshot_data
                    )
                    
                    self._snapshots[snapshot_id] = snapshot_info
                    
                logger.info(f"已加载 {len(self._snapshots)} 个快照记录")
                
            except Exception as e:
                logger.error(f"加载快照信息出错: {e}")
                
    def _save_snapshots(self):
        """保存快照信息"""
        with self._lock:
            snapshots_file = os.path.join(self.data_dir, "snapshots.json")
            try:
                snapshots_data = {}
                for snapshot_id, snapshot_info in self._snapshots.items():
                    # 转换为可序列化的数据
                    snapshot_dict = {}
                    for k, v in snapshot_info.__dict__.items():
                        if isinstance(v, datetime.datetime):
                            snapshot_dict[k] = v.isoformat()
                        elif isinstance(v, Enum):
                            snapshot_dict[k] = v.value
                        elif isinstance(v, SnapshotConfig):
                            snapshot_dict[k] = v.__dict__
                        else:
                            snapshot_dict[k] = v
                    
                    snapshots_data[snapshot_id] = snapshot_dict
                    
                with open(snapshots_file, "w", encoding="utf-8") as f:
                    json.dump(snapshots_data, f, ensure_ascii=False, indent=2)
                    
                logger.debug(f"已保存 {len(self._snapshots)} 个快照记录")
                
            except Exception as e:
                logger.error(f"保存快照信息出错: {e}")
                
    def create_snapshot(self, name: str, description: str = "", config: Optional[SnapshotConfig] = None) -> Optional[str]:
        """
        创建快照
        
        Args:
            name: 快照名称
            description: 快照描述
            config: 快照配置
            
        Returns:
            快照ID，如果创建失败则返回None
        """
        with self._lock:
            # 检查是否有正在进行的快照
            if self._current_snapshot is not None:
                active_snapshot = self._snapshots.get(self._current_snapshot)
                if active_snapshot and active_snapshot.status not in [
                    SnapshotStatus.COMPLETED, SnapshotStatus.FAILED, SnapshotStatus.DELETED
                ]:
                    logger.warning(f"已有正在进行的快照: {self._current_snapshot}")
                    return None
                    
            # 生成快照ID
            snapshot_id = f"snapshot_{int(time.time())}_{hashlib.md5(name.encode()).hexdigest()[:8]}"
            
            # 创建快照信息
            snapshot_info = SnapshotInfo(
                id=snapshot_id,
                name=name,
                description=description,
                config=config or SnapshotConfig(),
                status=SnapshotStatus.IDLE,
                created_at=datetime.datetime.now()
            )
            
            # 保存快照信息
            self._snapshots[snapshot_id] = snapshot_info
            self._save_snapshots()
            
            logger.info(f"已创建快照: {name} (ID: {snapshot_id})")
            return snapshot_id
            
    def get_snapshot(self, snapshot_id: str) -> Optional[SnapshotInfo]:
        """
        获取快照信息
        
        Args:
            snapshot_id: 快照ID
            
        Returns:
            快照信息，如果不存在则返回None
        """
        with self._lock:
            return self._snapshots.get(snapshot_id)
            
    def list_snapshots(self) -> List[SnapshotInfo]:
        """
        获取所有快照信息
        
        Returns:
            快照信息列表
        """
        with self._lock:
            return list(self._snapshots.values())
            
    def start_snapshot(self, snapshot_id: str) -> bool:
        """
        启动快照创建
        
        Args:
            snapshot_id: 快照ID
            
        Returns:
            是否成功启动
        """
        with self._lock:
            # 检查快照是否存在
            snapshot_info = self._snapshots.get(snapshot_id)
            if not snapshot_info:
                logger.warning(f"快照不存在: {snapshot_id}")
                return False
                
            # 检查快照状态
            if snapshot_info.status not in [SnapshotStatus.IDLE, SnapshotStatus.FAILED]:
                logger.warning(f"快照状态不允许启动: {snapshot_info.status.value}")
                return False
                
            # 检查是否有正在进行的快照
            if self._current_snapshot is not None:
                active_snapshot = self._snapshots.get(self._current_snapshot)
                if active_snapshot and active_snapshot.status not in [
                    SnapshotStatus.COMPLETED, SnapshotStatus.FAILED, SnapshotStatus.DELETED
                ]:
                    logger.warning(f"已有正在进行的快照: {self._current_snapshot}")
                    return False
                    
            # 更新快照状态
            snapshot_info.status = SnapshotStatus.CREATING
            snapshot_info.progress = 0.0
            snapshot_info.logs = []
            snapshot_info.errors = []
            snapshot_info.warnings = []
            snapshot_info.current_step = "准备快照"
            
            # 启动快照线程
            self._current_snapshot = snapshot_id
            self._stop_snapshot = False
            self._snapshot_thread = threading.Thread(target=self._snapshot_process, args=(snapshot_id,))
            self._snapshot_thread.daemon = True
            self._snapshot_thread.start()
            
            self._save_snapshots()
            
            logger.info(f"已启动快照: {snapshot_id}")
            return True
            
    def stop_snapshot(self, snapshot_id: str) -> bool:
        """
        停止快照创建
        
        Args:
            snapshot_id: 快照ID
            
        Returns:
            是否成功停止
        """
        with self._lock:
            # 检查快照是否存在
            snapshot_info = self._snapshots.get(snapshot_id)
            if not snapshot_info:
                logger.warning(f"快照不存在: {snapshot_id}")
                return False
                
            # 检查是否是当前快照
            if self._current_snapshot != snapshot_id:
                logger.warning(f"不是当前快照: {snapshot_id}")
                return False
                
            # 设置停止标志
            self._stop_snapshot = True
            
            # 等待快照线程结束
            if self._snapshot_thread and self._snapshot_thread.is_alive():
                # 不阻塞当前线程，仅设置停止标志
                logger.info(f"已设置快照停止标志: {snapshot_id}")
                
                # 更新快照状态
                snapshot_info.status = SnapshotStatus.FAILED
                snapshot_info.errors.append("快照被手动停止")
                self._save_snapshots()
                
            return True
            
    def _snapshot_process(self, snapshot_id: str):
        """
        快照处理过程
        
        Args:
            snapshot_id: 快照ID
        """
        snapshot_info = self._snapshots.get(snapshot_id)
        if not snapshot_info:
            return
            
        try:
            # 记录日志
            self._add_log(snapshot_info, "开始快照处理")
            
            # 设置总步骤数
            total_steps = 0
            if snapshot_info.config.include_system:
                total_steps += 1
            if snapshot_info.config.include_processes:
                total_steps += 1
            if snapshot_info.config.include_network:
                total_steps += 1
            if snapshot_info.config.include_files:
                total_steps += 1
            if snapshot_info.config.include_database:
                total_steps += 1
            if snapshot_info.config.include_cache:
                total_steps += 1
            if snapshot_info.config.validate_before:
                total_steps += 1
            if snapshot_info.config.validate_after:
                total_steps += 1
                
            snapshot_info.total_steps = total_steps
            
            # 创建临时目录
            temp_dir = os.path.join(self.data_dir, "temp", snapshot_id)
            os.makedirs(temp_dir, exist_ok=True)
            
            # 步骤1: 快照前验证（可选）
            if snapshot_info.config.validate_before:
                snapshot_info.current_step = "快照前验证"
                snapshot_info.status = SnapshotStatus.VALIDATING
                snapshot_info.current_step_progress = 0.0
                self._save_snapshots()
                
                self._add_log(snapshot_info, "执行快照前验证")
                
                # 执行快照前验证
                success, errors = self._validate_before_snapshot(snapshot_info)
                if not success:
                    self._add_log(snapshot_info, f"快照前验证失败: {', '.join(errors)}")
                    snapshot_info.status = SnapshotStatus.FAILED
                    snapshot_info.errors.extend(errors)
                    self._save_snapshots()
                    return
                else:
                    self._add_log(snapshot_info, "快照前验证通过")
                
                # 更新进度
                snapshot_info.progress = 1 / total_steps
                snapshot_info.current_step_progress = 1.0
                self._save_snapshots()
                
                # 检查是否需要停止
                if self._stop_snapshot:
                    self._add_log(snapshot_info, "快照被停止")
                    snapshot_info.status = SnapshotStatus.FAILED
                    self._save_snapshots()
                    return
                    
            # 步骤2: 收集系统信息
            if snapshot_info.config.include_system:
                snapshot_info.current_step = "收集系统信息"
                snapshot_info.current_step_progress = 0.0
                self._save_snapshots()
                
                self._add_log(snapshot_info, "收集系统信息")
                
                # 收集系统信息
                snapshot_info.system_info = self._collect_system_info()
                
                # 更新进度
                current_step = 1 if snapshot_info.config.validate_before else 0
                snapshot_info.progress = (current_step + 1) / total_steps
                snapshot_info.current_step_progress = 1.0
                self._save_snapshots()
                
                # 检查是否需要停止
                if self._stop_snapshot:
                    self._add_log(snapshot_info, "快照被停止")
                    snapshot_info.status = SnapshotStatus.FAILED
                    self._save_snapshots()
                    return
                    
            # 步骤3: 收集进程信息
            if snapshot_info.config.include_processes:
                snapshot_info.current_step = "收集进程信息"
                snapshot_info.current_step_progress = 0.0
                self._save_snapshots()
                
                self._add_log(snapshot_info, "收集进程信息")
                
                # 收集进程信息
                snapshot_info.process_info = self._collect_process_info()
                
                # 更新进度
                current_step = 2 if snapshot_info.config.validate_before else 1
                snapshot_info.progress = (current_step + 1) / total_steps
                snapshot_info.current_step_progress = 1.0
                self._save_snapshots()
                
                # 检查是否需要停止
                if self._stop_snapshot:
                    self._add_log(snapshot_info, "快照被停止")
                    snapshot_info.status = SnapshotStatus.FAILED
                    self._save_snapshots()
                    return
                    
            # 步骤4: 收集网络信息
            if snapshot_info.config.include_network:
                snapshot_info.current_step = "收集网络信息"
                snapshot_info.current_step_progress = 0.0
                self._save_snapshots()
                
                self._add_log(snapshot_info, "收集网络信息")
                
                # 收集网络信息
                snapshot_info.network_info = self._collect_network_info()
                
                # 更新进度
                current_step = 3 if snapshot_info.config.validate_before else 2
                snapshot_info.progress = (current_step + 1) / total_steps
                snapshot_info.current_step_progress = 1.0
                self._save_snapshots()
                
                # 检查是否需要停止
                if self._stop_snapshot:
                    self._add_log(snapshot_info, "快照被停止")
                    snapshot_info.status = SnapshotStatus.FAILED
                    self._save_snapshots()
                    return
                    
            # 步骤5: 收集文件系统信息
            if snapshot_info.config.include_files:
                snapshot_info.current_step = "收集文件系统信息"
                snapshot_info.current_step_progress = 0.0
                self._save_snapshots()
                
                self._add_log(snapshot_info, "收集文件系统信息")
                
                # 收集文件系统信息
                snapshot_info.files_info = self._collect_files_info()
                
                # 更新进度
                current_step = 4 if snapshot_info.config.validate_before else 3
                snapshot_info.progress = (current_step + 1) / total_steps
                snapshot_info.current_step_progress = 1.0
                self._save_snapshots()
                
                # 检查是否需要停止
                if self._stop_snapshot:
                    self._add_log(snapshot_info, "快照被停止")
                    snapshot_info.status = SnapshotStatus.FAILED
                    self._save_snapshots()
                    return
                    
            # 步骤6: 收集数据库信息
            if snapshot_info.config.include_database:
                snapshot_info.current_step = "收集数据库信息"
                snapshot_info.current_step_progress = 0.0
                self._save_snapshots()
                
                self._add_log(snapshot_info, "收集数据库信息")
                
                # 收集数据库信息
                snapshot_info.database_info = self._collect_database_info()
                
                # 更新进度
                current_step = 5 if snapshot_info.config.validate_before else 4
                snapshot_info.progress = (current_step + 1) / total_steps
                snapshot_info.current_step_progress = 1.0
                self._save_snapshots()
                
                # 检查是否需要停止
                if self._stop_snapshot:
                    self._add_log(snapshot_info, "快照被停止")
                    snapshot_info.status = SnapshotStatus.FAILED
                    self._save_snapshots()
                    return
                    
            # 步骤7: 收集缓存信息
            if snapshot_info.config.include_cache:
                snapshot_info.current_step = "收集缓存信息"
                snapshot_info.current_step_progress = 0.0
                self._save_snapshots()
                
                self._add_log(snapshot_info, "收集缓存信息")
                
                # 收集缓存信息
                snapshot_info.cache_info = self._collect_cache_info()
                
                # 更新进度
                current_step = 6 if snapshot_info.config.validate_before else 5
                snapshot_info.progress = (current_step + 1) / total_steps
                snapshot_info.current_step_progress = 1.0
                self._save_snapshots()
                
                # 检查是否需要停止
                if self._stop_snapshot:
                    self._add_log(snapshot_info, "快照被停止")
                    snapshot_info.status = SnapshotStatus.FAILED
                    self._save_snapshots()
                    return
                    
            # 步骤8: 快照后验证（可选）
            if snapshot_info.config.validate_after:
                snapshot_info.current_step = "快照后验证"
                snapshot_info.status = SnapshotStatus.VALIDATING
                snapshot_info.current_step_progress = 0.0
                self._save_snapshots()
                
                self._add_log(snapshot_info, "执行快照后验证")
                
                # 创建临时快照文件用于验证
                temp_snapshot_path = os.path.join(temp_dir, "temp_snapshot.tar.gz")
                self._create_snapshot_file(snapshot_info, temp_dir, temp_snapshot_path)
                
                # 执行快照后验证
                success, errors = self._validate_after_snapshot(snapshot_info, temp_snapshot_path)
                if not success:
                    self._add_log(snapshot_info, f"快照后验证失败: {', '.join(errors)}")
                    snapshot_info.status = SnapshotStatus.FAILED
                    snapshot_info.errors.extend(errors)
                    self._save_snapshots()
                    return
                else:
                    self._add_log(snapshot_info, "快照后验证通过")
                    # 删除临时验证文件
                    try:
                        os.remove(temp_snapshot_path)
                    except Exception:
                        pass
                
                # 更新进度
                current_step = 7 if snapshot_info.config.validate_before else 6
                snapshot_info.progress = (current_step + 1) / total_steps
                snapshot_info.current_step_progress = 1.0
                self._save_snapshots()
                
                # 检查是否需要停止
                if self._stop_snapshot:
                    self._add_log(snapshot_info, "快照被停止")
                    snapshot_info.status = SnapshotStatus.FAILED
                    self._save_snapshots()
                    return
                    
            # 步骤9: 创建快照文件
            snapshot_info.current_step = "创建快照文件"
            snapshot_info.current_step_progress = 0.0
            self._save_snapshots()
            
            self._add_log(snapshot_info, "创建快照文件")
            
            # 创建快照文件
            snapshot_path = self._create_snapshot_file(snapshot_info, temp_dir)
            if not snapshot_path:
                self._add_log(snapshot_info, "创建快照文件失败")
                snapshot_info.status = SnapshotStatus.FAILED
                self._save_snapshots()
                return
                
            snapshot_info.snapshot_path = snapshot_path
            
            # 计算快照大小和校验和
            snapshot_info.snapshot_size = os.path.getsize(snapshot_path)
            with open(snapshot_path, "rb") as f:
                snapshot_info.checksum = hashlib.sha256(f.read()).hexdigest()
                
            # 更新状态和进度
            snapshot_info.status = SnapshotStatus.COMPLETED
            snapshot_info.progress = 1.0
            snapshot_info.current_step_progress = 1.0
            snapshot_info.completed_at = datetime.datetime.now()
            self._save_snapshots()
            
            self._add_log(snapshot_info, f"快照成功创建: {snapshot_path}")
            
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
            
            # 自动清理旧快照
            if snapshot_info.config.auto_cleanup:
                self._cleanup_old_snapshots()
            
        except Exception as e:
            logger.error(f"快照处理出错: {e}", exc_info=True)
            
            self._add_log(snapshot_info, f"快照处理出错: {e}")
            snapshot_info.errors.append(str(e))
            snapshot_info.status = SnapshotStatus.FAILED
            self._save_snapshots()
            
        finally:
            # 清理
            self._current_snapshot = None
            
    def _collect_system_info(self) -> Dict[str, Any]:
        """
        收集系统信息
        
        Returns:
            系统信息
        """
        try:
            return {
                "timestamp": datetime.datetime.now().isoformat(),
                "system": {
                    "platform": sys.platform,
                    "version": sys.version,
                    "machine": platform.machine(),
                    "processor": platform.processor(),
                    "hostname": platform.node()
                },
                "cpu": {
                    "physical_cores": psutil.cpu_count(logical=False),
                    "total_cores": psutil.cpu_count(logical=True),
                    "cpu_freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
                    "cpu_percent": psutil.cpu_percent(interval=1)
                },
                "memory": {
                    "total": psutil.virtual_memory().total,
                    "available": psutil.virtual_memory().available,
                    "percent": psutil.virtual_memory().percent,
                    "used": psutil.virtual_memory().used,
                    "free": psutil.virtual_memory().free
                },
                "disk": {
                    "partitions": [
                        {
                            "device": partition.device,
                            "mountpoint": partition.mountpoint,
                            "fstype": partition.fstype,
                            "opts": partition.opts
                        }
                        for partition in psutil.disk_partitions()
                    ],
                    "usage": {
                        "total": psutil.disk_usage("/").total,
                        "used": psutil.disk_usage("/").used,
                        "free": psutil.disk_usage("/").free,
                        "percent": psutil.disk_usage("/").percent
                    }
                }
            }
        except Exception as e:
            logger.error(f"收集系统信息出错: {e}")
            return {}
            
    def _collect_process_info(self) -> Dict[str, Any]:
        """
        收集进程信息
        
        Returns:
            进程信息
        """
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
            return {
                "timestamp": datetime.datetime.now().isoformat(),
                "processes": processes
            }
        except Exception as e:
            logger.error(f"收集进程信息出错: {e}")
            return {}
            
    def _collect_network_info(self) -> Dict[str, Any]:
        """
        收集网络信息
        
        Returns:
            网络信息
        """
        try:
            return {
                "timestamp": datetime.datetime.now().isoformat(),
                "interfaces": psutil.net_if_stats(),
                "addresses": psutil.net_if_addrs(),
                "connections": [
                    {
                        "fd": conn.fd,
                        "family": conn.family,
                        "type": conn.type,
                        "local_addr": conn.laddr._asdict() if conn.laddr else None,
                        "remote_addr": conn.raddr._asdict() if conn.raddr else None,
                        "status": conn.status,
                        "pid": conn.pid
                    }
                    for conn in psutil.net_connections()
                ]
            }
        except Exception as e:
            logger.error(f"收集网络信息出错: {e}")
            return {}
            
    def _collect_files_info(self) -> Dict[str, Any]:
        """
        收集文件系统信息
        
        Returns:
            文件系统信息
        """
        try:
            return {
                "timestamp": datetime.datetime.now().isoformat(),
                "files": {
                    "config": {
                        "path": os.path.join(self.data_dir, "config"),
                        "files": self._list_directory(os.path.join(self.data_dir, "config"))
                    },
                    "data": {
                        "path": os.path.join(self.data_dir, "data"),
                        "files": self._list_directory(os.path.join(self.data_dir, "data"))
                    },
                    "logs": {
                        "path": os.path.join(self.data_dir, "logs"),
                        "files": self._list_directory(os.path.join(self.data_dir, "logs"))
                    }
                }
            }
        except Exception as e:
            logger.error(f"收集文件系统信息出错: {e}")
            return {}
            
    def _collect_database_info(self) -> Dict[str, Any]:
        """
        收集数据库信息
        
        Returns:
            数据库信息
        """
        try:
            # 获取配置
            db_info = self._get_database_config()
            if not db_info:
                logger.warning("无法获取数据库配置信息")
                return {
                    "timestamp": datetime.datetime.now().isoformat(),
                    "database": {
                        "type": "unknown",
                        "tables": [],
                        "size": 0,
                        "backup": None
                    }
                }
            
            db_type = db_info.get("type", "unknown")
            tables = []
            total_size = 0
            backup_path = None
            
            # 根据数据库类型收集信息
            if db_type == "postgresql":
                tables, total_size = self._collect_postgres_info(db_info)
                backup_path = self._backup_postgres_db(db_info)
            elif db_type == "sqlite":
                tables, total_size = self._collect_sqlite_info(db_info)
                backup_path = self._backup_sqlite_db(db_info)
            else:
                logger.warning(f"不支持的数据库类型: {db_type}")
            
            # 构建数据库统计信息
            stats = {
                "total_tables": len(tables),
                "total_size": total_size,
                "total_size_human": self._format_size(total_size),
                "total_rows": sum(table.get("rows", 0) for table in tables),
                "avg_table_size": total_size // max(len(tables), 1),
                "avg_table_size_human": self._format_size(total_size // max(len(tables), 1)),
                "largest_tables": sorted(tables, key=lambda t: t.get("size", 0), reverse=True)[:5] if tables else [],
                "most_rows_tables": sorted(tables, key=lambda t: t.get("rows", 0), reverse=True)[:5] if tables else []
            }
            
            return {
                "timestamp": datetime.datetime.now().isoformat(),
                "database": {
                    "type": db_type,
                    "tables": tables,
                    "size": total_size,
                    "stats": stats,
                    "backup": backup_path
                }
            }
        except Exception as e:
            logger.error(f"收集数据库信息出错: {e}", exc_info=True)
            return {
                "timestamp": datetime.datetime.now().isoformat(),
                "database": {
                    "type": "unknown",
                    "tables": [],
                    "size": 0,
                    "backup": None,
                    "error": str(e)
                }
            }
            
    def _get_database_config(self) -> Dict[str, Any]:
        """
        获取数据库配置信息
        
        Returns:
            数据库配置信息
        """
        # 尝试从环境配置中获取数据库信息
        try:
            # 优先尝试从配置文件获取
            config_path = os.path.join("E:\\key\\config\\keys\\", "sqlpsd.json")
            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    return json.load(f)
                
            # 尝试从环境变量获取
            db_type = os.environ.get("DB_TYPE", "")
            if db_type:
                return {
                    "type": db_type,
                    "host": os.environ.get("DB_HOST", "localhost"),
                    "port": os.environ.get("DB_PORT", "5432"),
                    "name": os.environ.get("DB_NAME", ""),
                    "user": os.environ.get("DB_USER", ""),
                    "password": os.environ.get("DB_PASSWORD", "")
                }
            
            # 尝试检测常见的数据库文件
            for db_path in [
                os.path.join(self.data_dir, "data.db"),
                os.path.join(self.data_dir, "..", "data.db"),
                os.path.join(self.data_dir, "..", "..", "data.db"),
            ]:
                if os.path.exists(db_path):
                    return {
                        "type": "sqlite",
                        "path": db_path
                    }
                
            return {}
        except Exception as e:
            logger.error(f"获取数据库配置出错: {e}")
            return {}
            
    def _collect_postgres_info(self, db_info: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], int]:
        """
        收集PostgreSQL数据库信息
        
        Args:
            db_info: 数据库配置信息
            
        Returns:
            表信息列表, 总大小(字节)
        """
        if not HAS_POSTGRES:
            logger.warning("未安装psycopg2模块，无法收集PostgreSQL信息")
            return [], 0
        
        try:
            tables = []
            total_size = 0
            
            # 连接数据库
            conn = psycopg2.connect(
                host=db_info.get("host", "localhost"),
                port=db_info.get("port", 5432),
                dbname=db_info.get("name", "postgres"),
                user=db_info.get("user", "postgres"),
                password=db_info.get("password", "")
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            
            with conn.cursor() as cursor:
                # 获取数据库大小
                cursor.execute("SELECT pg_database_size(%s)", (db_info.get("name", "postgres"),))
                total_size = cursor.fetchone()[0]
                
                # 获取表信息
                cursor.execute("""
                    SELECT 
                        table_name,
                        pg_relation_size(quote_ident(table_name)) as table_size
                    FROM 
                        information_schema.tables
                    WHERE 
                        table_schema = 'public'
                        AND table_type = 'BASE TABLE'
                    ORDER BY 
                        table_size DESC
                """)
                
                for table_name, table_size in cursor.fetchall():
                    # 获取行数
                    cursor.execute(f"SELECT COUNT(*) FROM \"{table_name}\"")
                    row_count = cursor.fetchone()[0]
                    
                    # 获取列信息
                    cursor.execute("""
                        SELECT 
                            column_name, 
                            data_type,
                            character_maximum_length
                        FROM 
                            information_schema.columns 
                        WHERE 
                            table_schema = 'public' 
                            AND table_name = %s
                    """, (table_name,))
                    
                    columns = [
                        {
                            "name": col[0],
                            "type": col[1],
                            "max_length": col[2]
                        }
                        for col in cursor.fetchall()
                    ]
                    
                    # 获取索引信息
                    cursor.execute("""
                        SELECT
                            indexname,
                            indexdef
                        FROM
                            pg_indexes
                        WHERE
                            tablename = %s
                            AND schemaname = 'public'
                    """, (table_name,))
                    
                    indexes = [
                        {
                            "name": idx[0],
                            "definition": idx[1]
                        }
                        for idx in cursor.fetchall()
                    ]
                    
                    # 获取表的最后更新时间
                    try:
                        cursor.execute(f"""
                            SELECT
                                greatest(last_vacuum, last_autovacuum, '1970-01-01'::timestamp) as last_maintenance,
                                greatest(last_analyze, last_autoanalyze, '1970-01-01'::timestamp) as last_analyze
                            FROM
                                pg_stat_user_tables
                            WHERE
                                relname = %s
                        """, (table_name,))
                        
                        maintenance_info = cursor.fetchone()
                        last_maintenance = maintenance_info[0].isoformat() if maintenance_info and maintenance_info[0] else None
                        last_analyze = maintenance_info[1].isoformat() if maintenance_info and maintenance_info[1] else None
                    except Exception:
                        last_maintenance = None
                        last_analyze = None
                    
                    tables.append({
                        "name": table_name,
                        "size": table_size,
                        "size_human": self._format_size(table_size),
                        "rows": row_count,
                        "columns": len(columns),
                        "column_info": columns,
                        "indexes": indexes,
                        "last_maintenance": last_maintenance,
                        "last_analyze": last_analyze
                    })
            
            return tables, total_size
        except Exception as e:
            logger.error(f"收集PostgreSQL信息出错: {e}", exc_info=True)
            return [], 0
        
    def _collect_sqlite_info(self, db_info: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], int]:
        """
        收集SQLite数据库信息
        
        Args:
            db_info: 数据库配置信息
            
        Returns:
            表信息列表, 总大小(字节)
        """
        try:
            db_path = db_info.get("path", "")
            if not db_path or not os.path.exists(db_path):
                logger.warning(f"SQLite数据库文件不存在: {db_path}")
                return [], 0
            
            tables = []
            
            # 获取数据库文件大小
            total_size = os.path.getsize(db_path)
            
            # 连接数据库
            conn = sqlite3.connect(db_path)
            
            with conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                # 获取表列表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                table_names = cursor.fetchall()
                
                # 获取SQLite版本信息
                cursor.execute("SELECT sqlite_version()")
                sqlite_version = cursor.fetchone()[0]
                
                # 获取数据库页面大小和页面计数
                cursor.execute("PRAGMA page_size")
                page_size = cursor.fetchone()[0]
                
                cursor.execute("PRAGMA page_count")
                page_count = cursor.fetchone()[0]
                
                # 计算理论数据库大小
                calculated_size = page_size * page_count
                
                # 获取数据库完整性信息
                cursor.execute("PRAGMA integrity_check")
                integrity_check = cursor.fetchone()[0]
                
                # 获取数据库统计信息
                db_stats = {
                    "sqlite_version": sqlite_version,
                    "page_size": page_size,
                    "page_count": page_count,
                    "calculated_size": calculated_size,
                    "calculated_size_human": self._format_size(calculated_size),
                    "actual_size": total_size,
                    "actual_size_human": self._format_size(total_size),
                    "integrity_check": integrity_check
                }
                
                # 计算每个表的预计行数
                total_rows = 0
                
                for (table_name,) in table_names:
                    if table_name.startswith('sqlite_'):
                        continue  # 跳过系统表
                    
                    # 获取表行数
                    cursor.execute(f"SELECT COUNT(*) FROM \"{table_name}\"")
                    row_count = cursor.fetchone()[0]
                    total_rows += row_count
                    
                    # 获取表结构信息
                    cursor.execute(f"PRAGMA table_info(\"{table_name}\")")
                    columns_info = cursor.fetchall()
                    
                    columns = [
                        {
                            "name": col["name"],
                            "type": col["type"],
                            "notnull": bool(col["notnull"]),
                            "default": col["dflt_value"],
                            "pk": bool(col["pk"])
                        }
                        for col in columns_info
                    ]
                    
                    # 获取表索引信息
                    cursor.execute(f"PRAGMA index_list(\"{table_name}\")")
                    indexes_info = cursor.fetchall()
                    
                    indexes = []
                    for index_info in indexes_info:
                        index_name = index_info["name"]
                        is_unique = bool(index_info["unique"])
                        
                        # 获取索引列信息
                        cursor.execute(f"PRAGMA index_info(\"{index_name}\")")
                        index_columns_info = cursor.fetchall()
                        index_columns = [info["name"] for info in index_columns_info]
                        
                        indexes.append({
                            "name": index_name,
                            "unique": is_unique,
                            "columns": index_columns
                        })
                    
                    # 获取样本数据
                    try:
                        cursor.execute(f"SELECT * FROM \"{table_name}\" LIMIT 1")
                        sample_row = cursor.fetchone()
                        if sample_row:
                            sample_data = dict(sample_row)
                        else:
                            sample_data = None
                    except Exception:
                        sample_data = None
                    
                    # 估算表大小
                    # 如果表行数为0，分配最小值
                    # 否则基于行数在总行数中的占比分配空间
                    if total_rows == 0:
                        table_size = 0
                    else:
                        table_size = int(total_size * (row_count / total_rows))
                    
                    tables.append({
                        "name": table_name,
                        "size": table_size,
                        "size_human": self._format_size(table_size),
                        "rows": row_count,
                        "columns": len(columns),
                        "column_info": columns,
                        "indexes": indexes,
                        "sample_data": sample_data
                    })
                
                # 更新db_stats添加总行数
                db_stats["total_rows"] = total_rows
                
                # 为每个表添加db_stats引用
                for table in tables:
                    table["db_stats"] = db_stats
            
            return tables, total_size
        except Exception as e:
            logger.error(f"收集SQLite信息出错: {e}", exc_info=True)
            return [], 0

    def _backup_postgres_db(self, db_info: Dict[str, Any]) -> Optional[str]:
        """
        备份PostgreSQL数据库
        
        Args:
            db_info: 数据库配置信息
            
        Returns:
            备份文件路径，备份失败则返回None
        """
        try:
            # 检查pg_dump是否可用
            pg_dump_path = shutil.which("pg_dump")
            if not pg_dump_path:
                logger.warning("未找到pg_dump工具，无法备份PostgreSQL数据库")
                return None
            
            # 创建备份目录
            backup_dir = os.path.join(self.data_dir, "db_backups")
            os.makedirs(backup_dir, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"pg_backup_{timestamp}.sql"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            # 构建pg_dump命令
            env = os.environ.copy()
            env["PGPASSWORD"] = db_info.get("password", "")
            
            cmd = [
                pg_dump_path,
                "-h", db_info.get("host", "localhost"),
                "-p", str(db_info.get("port", 5432)),
                "-U", db_info.get("user", "postgres"),
                "-d", db_info.get("name", "postgres"),
                "-f", backup_path,
                "-w",  # 不提示输入密码
                "-v"   # 显示详细信息
            ]
            
            # 执行备份
            logger.info(f"开始备份PostgreSQL数据库到: {backup_path}")
            result = subprocess.run(
                cmd, 
                env=env, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                text=True,
                check=False
            )
            
            if result.returncode != 0:
                logger.error(f"PostgreSQL备份失败: {result.stderr}")
                return None
            
            logger.info(f"PostgreSQL数据库备份完成: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"备份PostgreSQL数据库出错: {e}", exc_info=True)
            return None

    def _backup_sqlite_db(self, db_info: Dict[str, Any]) -> Optional[str]:
        """
        备份SQLite数据库
        
        Args:
            db_info: 数据库配置信息
            
        Returns:
            备份文件路径，备份失败则返回None
        """
        try:
            db_path = db_info.get("path", "")
            if not db_path or not os.path.exists(db_path):
                logger.warning(f"SQLite数据库文件不存在: {db_path}")
                return None
            
            # 创建备份目录
            backup_dir = os.path.join(self.data_dir, "db_backups")
            os.makedirs(backup_dir, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.basename(db_path)
            backup_filename = f"{os.path.splitext(filename)[0]}_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            # 直接复制文件作为备份
            logger.info(f"开始备份SQLite数据库到: {backup_path}")
            shutil.copy2(db_path, backup_path)
            logger.info(f"SQLite数据库备份完成: {backup_path}")
            
            return backup_path
        except Exception as e:
            logger.error(f"备份SQLite数据库出错: {e}", exc_info=True)
            return None
            
    def _collect_cache_info(self) -> Dict[str, Any]:
        """
        收集缓存信息
        
        Returns:
            缓存信息
        """
        try:
            # 尝试获取Redis缓存信息
            redis_info = self._collect_redis_info()
            
            # 尝试获取内存缓存信息
            memory_cache_info = self._collect_memory_cache_info()
            
            # 合并缓存统计信息
            total_memory_bytes = 0
            total_keys = 0
            total_hits = 0
            total_misses = 0
            
            # 计算Redis缓存统计数据
            if redis_info.get("connected", False):
                redis_memory = redis_info.get("memory", {}).get("used_memory", 0)
                total_memory_bytes += redis_memory
                
                redis_keys = redis_info.get("total_keys", 0)
                total_keys += redis_keys
                
                redis_hits = redis_info.get("performance", {}).get("hits", 0)
                redis_misses = redis_info.get("performance", {}).get("misses", 0)
                total_hits += redis_hits
                total_misses += redis_misses
            
            # 计算内存缓存统计数据
            memory_cache_memory = memory_cache_info.get("estimated_memory", 0)
            total_memory_bytes += memory_cache_memory
            
            memory_cache_keys = memory_cache_info.get("total_keys", 0)
            total_keys += memory_cache_keys
            
            memory_cache_hits = memory_cache_info.get("hit_count", 0)
            memory_cache_misses = memory_cache_info.get("miss_count", 0)
            total_hits += memory_cache_hits
            total_misses += memory_cache_misses
            
            # 计算总体命中率和未命中率
            total_operations = total_hits + total_misses
            hit_rate = (total_hits / total_operations) * 100 if total_operations > 0 else 0
            miss_rate = (total_misses / total_operations) * 100 if total_operations > 0 else 0
            
            # 构建总体缓存统计信息
            cache_stats = {
                "total_memory_bytes": total_memory_bytes,
                "total_memory_human": self._format_size(total_memory_bytes),
                "total_keys": total_keys,
                "total_hits": total_hits,
                "total_misses": total_misses,
                "total_hit_rate": hit_rate,
                "total_miss_rate": miss_rate,
                "performance_summary": {
                    "hit_rate": f"{hit_rate:.2f}%",
                    "miss_rate": f"{miss_rate:.2f}%",
                    "efficiency": "高" if hit_rate > 80 else ("中" if hit_rate > 60 else "低"),
                }
            }
            
            # 合并缓存信息
            cache_info = {
                "timestamp": datetime.datetime.now().isoformat(),
                "estimated_memory": total_memory_bytes,
                "estimated_memory_human": self._format_size(total_memory_bytes),
                "hit_count": total_hits,
                "miss_count": total_misses,
                "hit_rate": hit_rate,
                "miss_rate": miss_rate,
                "stats": cache_stats,
                "redis": redis_info,
                "memory_cache": memory_cache_info
            }
            
            return cache_info
            
        except Exception as e:
            logger.warning(f"收集缓存信息出错: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.datetime.now().isoformat()
            }

    def _get_memory_caches(self) -> Dict[str, Any]:
        """
        获取系统中的内存缓存对象
        
        Returns:
            缓存对象字典 {缓存名称: 缓存对象}
        """
        memory_caches = {}
        
        try:
            # 尝试导入和检查常见的缓存模块
            import importlib
            import sys
            
            # 检查项目中可能使用的缓存管理模块
            for module_name in [
                "ky.src.cache.memory_cache", 
                "cache.memory_cache", 
                "ky.common.cache.memory_cache"
            ]:
                try:
                    module = importlib.import_module(module_name)
                    if hasattr(module, "CacheManager"):
                        manager = getattr(module, "CacheManager")
                        if hasattr(manager, "get_instance"):
                            instance = manager.get_instance()
                            if hasattr(instance, "get_caches"):
                                memory_caches.update(instance.get_caches())
                            elif hasattr(instance, "_caches"):
                                memory_caches.update(instance._caches)
                except (ImportError, AttributeError) as e:
                    pass
                
            # 检查全局变量中的缓存对象
            for module_name, module in sys.modules.items():
                if "cache" in module_name.lower():
                    for attr_name in dir(module):
                        if "cache" in attr_name.lower():
                            attr = getattr(module, attr_name)
                            # 检查是否是 cachetools 的缓存实例
                            if hasattr(attr, "currsize") and hasattr(attr, "maxsize"):
                                memory_caches[f"{module_name}.{attr_name}"] = attr
        
        except Exception as e:
            logger.warning(f"获取内存缓存对象出错: {e}")
        
        return memory_caches

    def _estimate_object_size(self, obj: Any) -> int:
        """
        估算对象内存占用大小
        
        Args:
            obj: 要估算大小的对象
            
        Returns:
            估算的内存占用大小（字节）
        """
        try:
            import sys
            
            # 对于简单对象，直接使用sys.getsizeof
            size = sys.getsizeof(obj)
            
            # 对于字典，还需要考虑键和值的大小
            if isinstance(obj, dict):
                for key, value in obj.items():
                    size += sys.getsizeof(key)
                    size += sys.getsizeof(value)
            # 对于列表/元组/集合，还需要考虑元素的大小
            elif isinstance(obj, (list, tuple, set)):
                for item in obj:
                    size += sys.getsizeof(item)
                
            return size
        except Exception:
            # 如果无法准确估算，返回一个默认值
            return 4096  # 假设平均每个缓存对象占用4KB

    def _format_size(self, size_bytes: int) -> str:
        """
        格式化字节大小为可读形式
        
        Args:
            size_bytes: 字节大小
            
        Returns:
            格式化后的大小字符串，如 "1.23 MB"
        """
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.2f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.2f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"
            
    def _list_directory(self, directory: str) -> List[Dict[str, Any]]:
        """
        列出目录内容
        
        Args:
            directory: 目录路径
            
        Returns:
            目录内容列表
        """
        try:
            files = []
            for root, dirs, filenames in os.walk(directory):
                for filename in filenames:
                    file_path = os.path.join(root, filename)
                    try:
                        stat = os.stat(file_path)
                        files.append({
                            "path": file_path,
                            "size": stat.st_size,
                            "created": datetime.datetime.fromtimestamp(stat.st_ctime).isoformat(),
                            "modified": datetime.datetime.fromtimestamp(stat.st_mtime).isoformat(),
                            "accessed": datetime.datetime.fromtimestamp(stat.st_atime).isoformat()
                        })
                    except (OSError, IOError):
                        continue
            return files
        except Exception as e:
            logger.error(f"列出目录内容出错: {e}")
            return []
            
    def _create_snapshot_file(self, snapshot_info: SnapshotInfo, temp_dir: str, temp_snapshot_path: Optional[str] = None) -> Optional[str]:
        """
        创建快照文件
        
        Args:
            snapshot_info: 快照信息
            temp_dir: 临时目录
            temp_snapshot_path: 临时快照文件路径
            
        Returns:
            快照文件路径，如果创建失败则返回None
        """
        try:
            # 创建快照数据
            snapshot_data = {
                "version": "1.0",
                "created_at": snapshot_info.created_at.isoformat(),
                "created_by": "system",
                "system_info": snapshot_info.system_info,
                "process_info": snapshot_info.process_info,
                "network_info": snapshot_info.network_info,
                "files_info": snapshot_info.files_info,
                "database_info": snapshot_info.database_info,
                "cache_info": snapshot_info.cache_info
            }
            
            # 保存快照数据
            snapshot_file = os.path.join(temp_dir, "snapshot.json")
            with open(snapshot_file, "w", encoding="utf-8") as f:
                json.dump(snapshot_data, f, ensure_ascii=False, indent=2)
                
            # 创建压缩包
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            snapshot_name = f"snapshot_{timestamp}.tar.gz"
            snapshot_path = os.path.join(self.data_dir, "snapshots", snapshot_name)
            
            with tarfile.open(snapshot_path, "w:gz") as tar:
                tar.add(snapshot_file, arcname="snapshot.json")
                
                # 添加其他必要文件
                for info_type, info_data in snapshot_data.items():
                    if info_type.endswith("_info") and isinstance(info_data, dict):
                        for key, value in info_data.items():
                            if isinstance(value, list) and value and isinstance(value[0], dict) and "path" in value[0]:
                                # 添加文件列表
                                files_file = os.path.join(temp_dir, f"{key}_files.json")
                                with open(files_file, "w", encoding="utf-8") as f:
                                    json.dump(value, f, ensure_ascii=False, indent=2)
                                tar.add(files_file, arcname=f"{key}_files.json")
                                
            if temp_snapshot_path:
                with tarfile.open(temp_snapshot_path, "w:gz") as tar:
                    tar.add(snapshot_file, arcname="snapshot.json")
            
            return snapshot_path
            
        except Exception as e:
            logger.error(f"创建快照文件出错: {e}")
            return None
            
    def _cleanup_old_snapshots(self):
        """清理旧快照"""
        try:
            # 按创建时间排序
            snapshots = sorted(
                self._snapshots.values(),
                key=lambda x: x.created_at,
                reverse=True
            )
            
            # 获取当前快照的配置
            current_snapshot_id = self._current_snapshot
            if not current_snapshot_id:
                return
                
            current_snapshot = self._snapshots.get(current_snapshot_id)
            if not current_snapshot:
                return
                
            # 删除超过保留数量的快照
            for snapshot in snapshots[current_snapshot.config.max_snapshots:]:
                self.delete_snapshot(snapshot.id)
                
            # 删除超过保留天数的快照
            cutoff_date = datetime.datetime.now() - datetime.timedelta(
                days=current_snapshot.config.retention_days
            )
            for snapshot in snapshots:
                if snapshot.created_at < cutoff_date:
                    self.delete_snapshot(snapshot.id)
                    
        except Exception as e:
            logger.error(f"清理旧快照出错: {e}")
            
    def _add_log(self, snapshot_info: SnapshotInfo, message: str):
        """
        添加快照日志
        
        Args:
            snapshot_info: 快照信息
            message: 日志消息
        """
        log_entry = f"[{datetime.datetime.now().isoformat()}] {message}"
        logger.info(f"快照 {snapshot_info.id}: {message}")
        snapshot_info.logs.append(log_entry)
        self._save_snapshots()
        
    def delete_snapshot(self, snapshot_id: str) -> bool:
        """
        删除快照
        
        Args:
            snapshot_id: 快照ID
            
        Returns:
            是否成功删除
        """
        with self._lock:
            # 检查快照是否存在
            snapshot_info = self._snapshots.get(snapshot_id)
            if not snapshot_info:
                logger.warning(f"快照不存在: {snapshot_id}")
                return False
                
            # 检查快照状态
            if snapshot_info.status == SnapshotStatus.CREATING:
                logger.warning(f"快照正在创建中，无法删除: {snapshot_id}")
                return False
                
            try:
                # 删除快照文件
                if snapshot_info.snapshot_path and os.path.exists(snapshot_info.snapshot_path):
                    os.remove(snapshot_info.snapshot_path)
                    
                # 更新快照状态
                snapshot_info.status = SnapshotStatus.DELETED
                self._save_snapshots()
                
                logger.info(f"已删除快照: {snapshot_id}")
                return True
                
            except Exception as e:
                logger.error(f"删除快照出错: {e}")
                return False
                
    def restore_snapshot(self, snapshot_id: str) -> bool:
        """
        恢复快照
        
        Args:
            snapshot_id: 快照ID
            
        Returns:
            是否成功恢复
        """
        with self._lock:
            # 检查快照是否存在
            snapshot_info = self._snapshots.get(snapshot_id)
            if not snapshot_info:
                logger.warning(f"快照不存在: {snapshot_id}")
                return False
                
            # 检查快照状态
            if snapshot_info.status != SnapshotStatus.COMPLETED:
                logger.warning(f"快照状态不允许恢复: {snapshot_info.status.value}")
                return False
                
            # 检查是否有正在进行的快照
            if self._current_snapshot is not None:
                active_snapshot = self._snapshots.get(self._current_snapshot)
                if active_snapshot and active_snapshot.status not in [
                    SnapshotStatus.COMPLETED, SnapshotStatus.FAILED, SnapshotStatus.DELETED
                ]:
                    logger.warning(f"已有正在进行的快照: {self._current_snapshot}")
                    return False
                    
            # 检查快照文件是否存在
            snapshot_path = snapshot_info.snapshot_path
            if not snapshot_path:
                logger.warning("快照文件路径为空")
                return False
                
            if not os.path.exists(snapshot_path):
                logger.warning(f"快照文件不存在: {snapshot_path}")
                return False
                
            # 更新快照状态
            snapshot_info.status = SnapshotStatus.RESTORING
            snapshot_info.progress = 0.0
            snapshot_info.logs = []
            snapshot_info.errors = []
            snapshot_info.warnings = []
            snapshot_info.current_step = "准备恢复"
            
            # 启动恢复线程
            self._current_snapshot = snapshot_id
            self._stop_snapshot = False
            self._snapshot_thread = threading.Thread(target=self._restore_process, args=(snapshot_id,))
            self._snapshot_thread.daemon = True
            self._snapshot_thread.start()
            
            self._save_snapshots()
            
            logger.info(f"已启动快照恢复: {snapshot_id}")
            return True
            
    def _restore_process(self, snapshot_id: str):
        """
        快照恢复过程
        
        Args:
            snapshot_id: 快照ID
        """
        snapshot_info = self._snapshots.get(snapshot_id)
        if not snapshot_info:
            return
            
        try:
            # 记录日志
            self._add_log(snapshot_info, "开始快照恢复")
            
            # 设置总步骤数
            total_steps = 4  # 准备、验证、恢复、完成
            
            # 步骤1: 准备
            snapshot_info.current_step = "准备恢复"
            snapshot_info.current_step_progress = 0.0
            self._save_snapshots()
            
            self._add_log(snapshot_info, "准备恢复环境")
            
            # 创建临时目录
            temp_dir = os.path.join(self.data_dir, "temp", f"restore_{snapshot_id}")
            os.makedirs(temp_dir, exist_ok=True)
            
            # 解压快照文件
            if not os.path.exists(snapshot_info.snapshot_path):
                self._add_log(snapshot_info, "快照文件不存在")
                snapshot_info.status = SnapshotStatus.FAILED
                self._save_snapshots()
                return
                
            with tarfile.open(snapshot_info.snapshot_path, "r:gz") as tar:
                tar.extractall(temp_dir)
                
            # 更新进度
            snapshot_info.progress = 1 / total_steps
            snapshot_info.current_step_progress = 1.0
            self._save_snapshots()
            
            # 检查是否需要停止
            if self._stop_snapshot:
                self._add_log(snapshot_info, "恢复被停止")
                snapshot_info.status = SnapshotStatus.FAILED
                self._save_snapshots()
                return
                
            # 步骤2: 验证快照
            snapshot_info.current_step = "验证快照"
            snapshot_info.status = SnapshotStatus.VALIDATING
            snapshot_info.current_step_progress = 0.0
            self._save_snapshots()
            
            self._add_log(snapshot_info, "验证快照")
            
            # 读取快照数据
            snapshot_file = os.path.join(temp_dir, "snapshot.json")
            if not os.path.exists(snapshot_file):
                self._add_log(snapshot_info, "快照数据文件不存在")
                snapshot_info.status = SnapshotStatus.FAILED
                self._save_snapshots()
                return
                
            with open(snapshot_file, "r", encoding="utf-8") as f:
                snapshot_data = json.load(f)
                
            # 执行快照验证
            is_valid, validation_errors = self._validate_snapshot(snapshot_info, snapshot_data, temp_dir)
            
            if not is_valid:
                self._add_log(snapshot_info, "快照验证失败，无法恢复")
                for error in validation_errors:
                    snapshot_info.errors.append(error)
                snapshot_info.status = SnapshotStatus.FAILED
                self._save_snapshots()
                return
                
            # 更新进度
            snapshot_info.progress = 2 / total_steps
            snapshot_info.current_step_progress = 1.0
            self._save_snapshots()
            
            # 步骤3: 恢复系统状态
            snapshot_info.current_step = "恢复系统状态"
            snapshot_info.current_step_progress = 0.0
            self._save_snapshots()
            
            self._add_log(snapshot_info, "恢复系统状态")
            
            # 实现系统状态恢复逻辑
            restore_steps = []
            restore_progress = 0
            
            # 构建恢复步骤列表
            if snapshot_info.config.include_system:
                restore_steps.append("system")
            if snapshot_info.config.include_network:
                restore_steps.append("network")
            if snapshot_info.config.include_database:
                restore_steps.append("database")
            if snapshot_info.config.include_cache:
                restore_steps.append("cache")
                
            total_restore_steps = len(restore_steps)
            if total_restore_steps == 0:
                self._add_log(snapshot_info, "没有需要恢复的系统状态")
            else:
                for step in restore_steps:
                    # 检查是否需要停止
                    if self._stop_snapshot:
                        self._add_log(snapshot_info, "恢复被停止")
                        snapshot_info.status = SnapshotStatus.FAILED
                        self._save_snapshots()
                        return
                        
                    if step == "system":
                        self._add_log(snapshot_info, "恢复系统配置")
                        try:
                            # 读取快照中的系统信息
                            system_info = snapshot_data.get("system_info", {})
                            if system_info:
                                # 记录系统信息
                                self._add_log(snapshot_info, f"系统类型: {system_info.get('system', 'unknown')}")
                                self._add_log(snapshot_info, f"系统版本: {system_info.get('release', 'unknown')}")
                                self._add_log(snapshot_info, f"主机名: {system_info.get('hostname', 'unknown')}")
                                
                                # 恢复相关系统配置文件
                                system_config_path = os.path.join(temp_dir, "system_config")
                                if os.path.exists(system_config_path):
                                    self._add_log(snapshot_info, "恢复系统配置文件")
                                    try:
                                        # 恢复到配置目录
                                        config_dir = os.path.join("E:\\key\\config")
                                        os.makedirs(config_dir, exist_ok=True)
                                        
                                        # 复制配置文件
                                        for item in os.listdir(system_config_path):
                                            src = os.path.join(system_config_path, item)
                                            dst = os.path.join(config_dir, item)
                                            if os.path.isfile(src):
                                                shutil.copy2(src, dst)
                                                self._add_log(snapshot_info, f"恢复配置文件: {item}")
                                        
                                        self._add_log(snapshot_info, "系统配置恢复完成")
                                    except Exception as e:
                                        warning_msg = f"恢复系统配置文件出错: {e}"
                                        snapshot_info.warnings.append(warning_msg)
                                        self._add_log(snapshot_info, f"警告: {warning_msg}")
                                else:
                                    self._add_log(snapshot_info, "未找到系统配置备份文件，跳过")
                            else:
                                self._add_log(snapshot_info, "快照中没有系统信息，跳过")
                        except Exception as e:
                            warning_msg = f"恢复系统配置时出错: {e}"
                            snapshot_info.warnings.append(warning_msg)
                            self._add_log(snapshot_info, f"警告: {warning_msg}")
                            logger.warning(warning_msg, exc_info=True)
                    
                    elif step == "network":
                        self._add_log(snapshot_info, "恢复网络配置")
                        try:
                            # 读取快照中的网络信息
                            network_info = snapshot_data.get("network_info", {})
                            if network_info:
                                # 记录网络信息
                                self._add_log(snapshot_info, f"恢复网络接口数量: {len(network_info.get('interfaces', []))}")
                                
                                # 恢复相关网络配置文件
                                network_config_path = os.path.join(temp_dir, "network_config")
                                if os.path.exists(network_config_path):
                                    self._add_log(snapshot_info, "恢复网络配置文件")
                                    try:
                                        # 恢复到网络配置目录
                                        network_dir = os.path.join("E:\\key\\config\\network")
                                        os.makedirs(network_dir, exist_ok=True)
                                        
                                        # 复制配置文件
                                        for item in os.listdir(network_config_path):
                                            src = os.path.join(network_config_path, item)
                                            dst = os.path.join(network_dir, item)
                                            if os.path.isfile(src):
                                                shutil.copy2(src, dst)
                                                self._add_log(snapshot_info, f"恢复网络配置文件: {item}")
                                        
                                        self._add_log(snapshot_info, "网络配置恢复完成")
                                    except Exception as e:
                                        warning_msg = f"恢复网络配置文件出错: {e}"
                                        snapshot_info.warnings.append(warning_msg)
                                        self._add_log(snapshot_info, f"警告: {warning_msg}")
                                else:
                                    self._add_log(snapshot_info, "未找到网络配置备份文件，跳过")
                            else:
                                self._add_log(snapshot_info, "快照中没有网络信息，跳过")
                        except Exception as e:
                            warning_msg = f"恢复网络配置时出错: {e}"
                            snapshot_info.warnings.append(warning_msg)
                            self._add_log(snapshot_info, f"警告: {warning_msg}")
                            logger.warning(warning_msg, exc_info=True)
                    
                    elif step == "database":
                        self._add_log(snapshot_info, "恢复数据库状态")
                        try:
                            # 读取快照中的数据库信息
                            database_info = snapshot_data.get("database_info", {})
                            if database_info:
                                db_config = database_info.get("config", {})
                                db_type = db_config.get("type", "unknown")
                                
                                self._add_log(snapshot_info, f"数据库类型: {db_type}")
                                
                                # 恢复数据库备份
                                db_backup_path = os.path.join(temp_dir, "database_backup")
                                if os.path.exists(db_backup_path):
                                    if db_type == "postgresql":
                                        self._add_log(snapshot_info, "恢复PostgreSQL数据库")
                                        try:
                                            # 执行 PostgreSQL 数据库恢复
                                            db_name = db_config.get("database", "omnilink")
                                            backup_file = os.path.join(db_backup_path, f"{db_name}.sql")
                                            
                                            if os.path.exists(backup_file):
                                                # 使用 psql 执行恢复
                                                cmd = [
                                                    "psql",
                                                    "-h", db_config.get("host", "localhost"),
                                                    "-p", str(db_config.get("port", 5432)),
                                                    "-U", db_config.get("username", "postgres"),
                                                    "-d", db_name,
                                                    "-f", backup_file
                                                ]
                                                
                                                env = os.environ.copy()
                                                if db_config.get("password"):
                                                    env["PGPASSWORD"] = db_config["password"]
                                                
                                                result = subprocess.run(
                                                    cmd, 
                                                    capture_output=True, 
                                                    text=True, 
                                                    env=env
                                                )
                                                
                                                if result.returncode == 0:
                                                    self._add_log(snapshot_info, "PostgreSQL数据库恢复完成")
                                                else:
                                                    warning_msg = f"PostgreSQL恢复失败: {result.stderr}"
                                                    snapshot_info.warnings.append(warning_msg)
                                                    self._add_log(snapshot_info, f"警告: {warning_msg}")
                                            else:
                                                warning_msg = f"PostgreSQL备份文件不存在: {backup_file}"
                                                snapshot_info.warnings.append(warning_msg)
                                                self._add_log(snapshot_info, f"警告: {warning_msg}")
                                        except Exception as e:
                                            warning_msg = f"恢复PostgreSQL数据库时出错: {e}"
                                            snapshot_info.warnings.append(warning_msg)
                                            self._add_log(snapshot_info, f"警告: {warning_msg}")
                                    
                                    elif db_type == "sqlite":
                                        self._add_log(snapshot_info, "恢复SQLite数据库")
                                        try:
                                            # 恢复 SQLite 数据库文件
                                            db_path = db_config.get("path", "")
                                            backup_file = os.path.join(db_backup_path, os.path.basename(db_path))
                                            
                                            if os.path.exists(backup_file):
                                                # 备份当前数据库
                                                if os.path.exists(db_path):
                                                    backup_current = f"{db_path}.backup_{int(time.time())}"
                                                    shutil.copy2(db_path, backup_current)
                                                    self._add_log(snapshot_info, f"当前数据库已备份至: {backup_current}")
                                                
                                                # 恢复数据库文件
                                                os.makedirs(os.path.dirname(db_path), exist_ok=True)
                                                shutil.copy2(backup_file, db_path)
                                                self._add_log(snapshot_info, "SQLite数据库恢复完成")
                                            else:
                                                warning_msg = f"SQLite备份文件不存在: {backup_file}"
                                                snapshot_info.warnings.append(warning_msg)
                                                self._add_log(snapshot_info, f"警告: {warning_msg}")
                                        except Exception as e:
                                            warning_msg = f"恢复SQLite数据库时出错: {e}"
                                            snapshot_info.warnings.append(warning_msg)
                                            self._add_log(snapshot_info, f"警告: {warning_msg}")
                                    
                                    else:
                                        warning_msg = f"不支持的数据库类型: {db_type}"
                                        snapshot_info.warnings.append(warning_msg)
                                        self._add_log(snapshot_info, f"警告: {warning_msg}")
                                else:
                                    warning_msg = "未找到数据库备份文件，跳过数据库恢复"
                                    snapshot_info.warnings.append(warning_msg)
                                    self._add_log(snapshot_info, f"警告: {warning_msg}")
                            else:
                                self._add_log(snapshot_info, "快照中没有数据库信息，跳过")
                        except Exception as e:
                            warning_msg = f"恢复数据库时出错: {e}"
                            snapshot_info.warnings.append(warning_msg)
                            self._add_log(snapshot_info, f"警告: {warning_msg}")
                            logger.warning(warning_msg, exc_info=True)
                    
                    elif step == "cache":
                        self._add_log(snapshot_info, "恢复缓存状态")
                        try:
                            # 读取快照中的缓存信息
                            cache_info = snapshot_data.get("cache_info", {})
                            if cache_info:
                                redis_info = cache_info.get("redis", {})
                                memory_cache_info = cache_info.get("memory_cache", {})
                                
                                # 恢复Redis缓存
                                if redis_info and redis_info.get("available"):
                                    self._add_log(snapshot_info, "尝试恢复Redis缓存")
                                    try:
                                        # 连接Redis并清除当前数据
                                        import redis
                                        redis_client = redis.Redis(
                                            host=redis_info.get("host", "localhost"),
                                            port=redis_info.get("port", 6379),
                                            db=redis_info.get("database", 0),
                                            decode_responses=True
                                        )
                                        
                                        # 测试连接
                                        redis_client.ping()
                                        
                                        # 清空当前数据库（可选）
                                        redis_client.flushdb()
                                        self._add_log(snapshot_info, "Redis当前数据已清空")
                                        
                                        # 恢复键值对
                                        restored_keys = 0
                                        for key, value in redis_info.get("keys", {}).items():
                                            try:
                                                redis_client.set(key, value)
                                                restored_keys += 1
                                            except Exception as e:
                                                warning_msg = f"恢复Redis键 '{key}' 失败: {e}"
                                                snapshot_info.warnings.append(warning_msg)
                                        
                                        self._add_log(snapshot_info, f"Redis缓存恢复完成，恢复了 {restored_keys} 个键")
                                    except ImportError:
                                        warning_msg = "Redis模块未安装，跳过Redis缓存恢复"
                                        snapshot_info.warnings.append(warning_msg)
                                        self._add_log(snapshot_info, f"警告: {warning_msg}")
                                    except Exception as e:
                                        warning_msg = f"恢复Redis缓存时出错: {e}"
                                        snapshot_info.warnings.append(warning_msg)
                                else:
                                    self._add_log(snapshot_info, "快照中没有Redis缓存信息或Redis不可用，跳过")
                                
                                # 记录内存缓存信息（通常不需要恢复）
                                if memory_cache_info:
                                    self._add_log(snapshot_info, f"内存缓存信息已记录，包含 {len(memory_cache_info)} 项缓存统计")
                                else:
                                    self._add_log(snapshot_info, "快照中没有内存缓存信息")
                            else:
                                self._add_log(snapshot_info, "快照中没有缓存信息，跳过")
                        except Exception as e:
                            warning_msg = f"恢复缓存时出错: {e}"
                            snapshot_info.warnings.append(warning_msg)
                            self._add_log(snapshot_info, f"警告: {warning_msg}")
                            logger.warning(warning_msg, exc_info=True)
                    
                    # 更新恢复进度
                    restore_progress += 1
                    step_progress = restore_progress / total_restore_steps
                    snapshot_info.current_step_progress = step_progress
                    self._save_snapshots()
                    
                    self._add_log(snapshot_info, f"恢复步骤 '{step}' 完成")
            
            # 更新进度
            snapshot_info.progress = 3 / total_steps
            snapshot_info.current_step_progress = 1.0
            self._save_snapshots()
            
            # 步骤4: 完成恢复
            snapshot_info.current_step = "完成恢复"
            snapshot_info.current_step_progress = 0.0
            self._save_snapshots()
            
            self._add_log(snapshot_info, "完成恢复过程")
            
            # 清理临时文件
            try:
                shutil.rmtree(temp_dir)
                self._add_log(snapshot_info, "临时文件清理完成")
            except Exception as e:
                warning_msg = f"清理临时文件失败: {e}"
                snapshot_info.warnings.append(warning_msg)
                self._add_log(snapshot_info, f"警告: {warning_msg}")
            
            # 更新状态和进度
            snapshot_info.status = SnapshotStatus.COMPLETED
            snapshot_info.progress = 1.0
            snapshot_info.current_step_progress = 1.0
            snapshot_info.completed_at = datetime.datetime.now()
            self._save_snapshots()
            
            # 记录恢复完成
            if snapshot_info.warnings:
                self._add_log(snapshot_info, f"快照恢复完成，但有 {len(snapshot_info.warnings)} 个警告")
            else:
                self._add_log(snapshot_info, "快照恢复成功完成")
            
            logger.info(f"快照恢复完成: {snapshot_id}")
            
        except Exception as e:
            error_msg = f"快照恢复过程中发生错误: {e}"
            logger.error(error_msg, exc_info=True)
            self._add_log(snapshot_info, error_msg)
            snapshot_info.errors.append(error_msg)
            snapshot_info.status = SnapshotStatus.FAILED
            self._save_snapshots()
        
        finally:
            # 清理临时文件
            temp_dir = os.path.join(self.data_dir, "temp", f"restore_{snapshot_id}")
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {e}")

    def _validate_snapshot(self, snapshot_info: SnapshotInfo, snapshot_data: Dict[str, Any], temp_dir: str) -> Tuple[bool, List[str]]:
        """
        在恢复前验证快照的有效性
        
        Args:
            snapshot_info: 快照信息
            snapshot_data: 从snapshot.json读取的数据
            temp_dir: 临时目录路径
            
        Returns:
            验证结果 (是否通过, 错误消息列表)
        """
        errors = []
        
        try:
            logger.info("验证快照数据")
            self._add_log(snapshot_info, "验证快照数据完整性")
            
            # 1. 检查快照状态
            if snapshot_info.status not in [SnapshotStatus.COMPLETED, SnapshotStatus.IDLE]:
                error_msg = f"快照状态不适合恢复: {snapshot_info.status.value}"
                errors.append(error_msg)
                self._add_log(snapshot_info, f"错误: {error_msg}")
                
            # 2. 检查快照文件是否存在
            if not os.path.exists(snapshot_info.snapshot_path):
                error_msg = f"快照文件不存在: {snapshot_info.snapshot_path}"
                errors.append(error_msg)
                self._add_log(snapshot_info, f"错误: {error_msg}")
                
            # 3. 检查快照文件大小
            if snapshot_info.snapshot_size == 0:
                error_msg = "快照文件大小为0，文件可能已损坏"
                errors.append(error_msg)
                self._add_log(snapshot_info, f"错误: {error_msg}")
                
            # 4. 检查校验和
            if snapshot_info.checksum:
                self._add_log(snapshot_info, "验证文件校验和")
                current_checksum = self._calculate_file_checksum(snapshot_info.snapshot_path)
                if current_checksum != snapshot_info.checksum:
                    error_msg = f"校验和不匹配: 预期 {snapshot_info.checksum}, 实际 {current_checksum}"
                    errors.append(error_msg)
                    self._add_log(snapshot_info, f"错误: {error_msg}")
                else:
                    self._add_log(snapshot_info, "校验和验证通过")
            else:
                warning_msg = "快照没有校验和，跳过校验和验证"
                snapshot_info.warnings.append(warning_msg)
                self._add_log(snapshot_info, f"警告: {warning_msg}")
                
            # 5. 检查必要的数据是否存在
            required_fields = ["id", "name", "created_at"]
            for field in required_fields:
                if field not in snapshot_data:
                    error_msg = f"快照数据中缺少必要字段: {field}"
                    errors.append(error_msg)
                    self._add_log(snapshot_info, f"错误: {error_msg}")
            
            # 6. 校验快照ID是否匹配
            if "id" in snapshot_data and snapshot_data["id"] != snapshot_info.id:
                error_msg = f"快照ID不匹配: 预期 {snapshot_info.id}, 实际 {snapshot_data['id']}"
                errors.append(error_msg)
                self._add_log(snapshot_info, f"错误: {error_msg}")
                
            # 7. 检查系统信息
            if "system_info" not in snapshot_data and snapshot_info.config.include_system:
                warning_msg = "快照中缺少系统信息"
                snapshot_info.warnings.append(warning_msg)
                self._add_log(snapshot_info, f"警告: {warning_msg}")
                
            # 8. 检查数据库信息
            if "database_info" not in snapshot_data and snapshot_info.config.include_database:
                warning_msg = "快照中缺少数据库信息"
                snapshot_info.warnings.append(warning_msg)
                self._add_log(snapshot_info, f"警告: {warning_msg}")
                
            # 9. 检查缓存信息
            if "cache_info" not in snapshot_data and snapshot_info.config.include_cache:
                warning_msg = "快照中缺少缓存信息"
                snapshot_info.warnings.append(warning_msg)
                self._add_log(snapshot_info, f"警告: {warning_msg}")
                
            # 10. 检查数据库备份文件
            if snapshot_info.config.include_database:
                db_backup_path = os.path.join(temp_dir, "database_backup")
                if not os.path.exists(db_backup_path):
                    warning_msg = "快照中缺少数据库备份文件"
                    snapshot_info.warnings.append(warning_msg)
                    self._add_log(snapshot_info, f"警告: {warning_msg}")
                    
            # 验证完成
            self._add_log(snapshot_info, "快照验证完成")
            if errors:
                self._add_log(snapshot_info, f"验证失败: 发现 {len(errors)} 个错误")
                return False, errors
            else:
                if snapshot_info.warnings:
                    self._add_log(snapshot_info, f"验证通过，但有 {len(snapshot_info.warnings)} 个警告")
                else:
                    self._add_log(snapshot_info, "验证通过")
                return True, []
                
        except Exception as e:
            error_msg = f"验证过程发生错误: {e}"
            errors.append(error_msg)
            logger.error(error_msg, exc_info=True)
            self._add_log(snapshot_info, f"验证失败: {error_msg}")
            return False, errors

    def _validate_before_snapshot(self, snapshot_info: SnapshotInfo) -> Tuple[bool, List[str]]:
        """
        执行快照前验证
        
        Args:
            snapshot_info: 快照信息
            
        Returns:
            Tuple[bool, List[str]]: (是否验证通过, 错误列表)
        """
        errors = []
        warnings = []
        
        try:
            # 检查磁盘空间
            estimated_size = self._estimate_required_space(snapshot_info)
            
            try:
                import shutil
                free_space = shutil.disk_usage(self.data_dir).free
                
                # 预留额外空间（估算大小的1.5倍）
                required_space = int(estimated_size * 1.5)
                
                if free_space < required_space:
                    errors.append(f"磁盘空间不足：需要 {self._format_size(required_space)}，可用 {self._format_size(free_space)}")
                elif free_space < estimated_size * 2:
                    warnings.append(f"磁盘空间紧张：需要 {self._format_size(required_space)}，可用 {self._format_size(free_space)}")
                    
            except Exception as e:
                warnings.append(f"无法检查磁盘空间: {str(e)}")
            
            # 检查数据库连接
            if snapshot_info.config.include_database:
                try:
                    db_config = self._get_database_config()
                    if db_config:
                        db_type = db_config.get('type', 'unknown')
                        
                        if db_type == 'postgresql':
                            import psycopg2
                            conn = psycopg2.connect(
                                host=db_config.get('host', 'localhost'),
                                port=db_config.get('port', 5432),
                                database=db_config.get('database'),
                                user=db_config.get('user'),
                                password=db_config.get('password')
                            )
                            conn.close()
                        elif db_type == 'sqlite':
                            import sqlite3
                            db_path = db_config.get('path')
                            if not os.path.exists(db_path):
                                errors.append(f"SQLite数据库文件不存在: {db_path}")
                            else:
                                conn = sqlite3.connect(db_path)
                                conn.close()
                        else:
                            warnings.append(f"未知的数据库类型: {db_type}")
                            
                except Exception as e:
                    errors.append(f"数据库连接测试失败: {str(e)}")
            
            # 检查系统资源
            try:
                import psutil
                
                # 检查CPU使用率
                cpu_usage = psutil.cpu_percent(interval=1)
                if cpu_usage > 90:
                    warnings.append(f"CPU使用率过高: {cpu_usage}%")
                
                # 检查内存使用率
                memory = psutil.virtual_memory()
                if memory.percent > 90:
                    warnings.append(f"内存使用率过高: {memory.percent}%")
                
                # 检查磁盘IO
                try:
                    disk_io = psutil.disk_io_counters()
                    if disk_io and disk_io.read_time > 1000:  # 读取时间过长
                        warnings.append("磁盘IO负载较高，可能影响快照性能")
                except:
                    pass
                    
            except Exception as e:
                warnings.append(f"无法检查系统资源: {str(e)}")
            
            # 检查Redis连接（如果包含缓存）
            if snapshot_info.config.include_cache:
                try:
                    import redis
                    
                    # 尝试连接默认Redis实例
                    redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
                    redis_client.ping()
                    redis_client.close()
                    
                except Exception as e:
                    warnings.append(f"Redis连接检查失败: {str(e)}")
            
            # 检查权限
            try:
                # 检查数据目录写权限
                if not os.access(self.data_dir, os.W_OK):
                    errors.append(f"没有数据目录写权限: {self.data_dir}")
                
                # 检查是否可以创建临时目录
                temp_dir = os.path.join(self.data_dir, "temp", "permission_test")
                try:
                    os.makedirs(temp_dir, exist_ok=True)
                    os.rmdir(temp_dir)
                except Exception as e:
                    errors.append(f"无法创建临时目录: {str(e)}")
                    
            except Exception as e:
                warnings.append(f"权限检查失败: {str(e)}")
            
            # 检查进程状态（如果包含进程信息）
            if snapshot_info.config.include_processes:
                try:
                    import psutil
                    
                    process_count = len(psutil.pids())
                    if process_count > 1000:
                        warnings.append(f"进程数量较多({process_count})，快照可能需要更长时间")
                        
                except Exception as e:
                    warnings.append(f"进程检查失败: {str(e)}")
            
            # 记录警告
            if warnings:
                snapshot_info.warnings.extend(warnings)
            
            # 返回验证结果
            is_valid = len(errors) == 0
            
            if is_valid:
                self._add_log(snapshot_info, "快照前验证通过")
                if warnings:
                    self._add_log(snapshot_info, f"警告: {'; '.join(warnings)}")
            else:
                self._add_log(snapshot_info, f"快照前验证失败: {'; '.join(errors)}")
            
            return is_valid, errors
            
        except Exception as e:
            error_msg = f"执行快照前验证时发生异常: {str(e)}"
            self._add_log(snapshot_info, error_msg)
            return False, [error_msg]

    def _validate_after_snapshot(self, snapshot_info: SnapshotInfo, snapshot_path: str) -> Tuple[bool, List[str]]:
        """
        执行快照创建后的验证
        
        Args:
            snapshot_info: 快照信息
            snapshot_path: 快照文件路径
            
        Returns:
            验证结果 (是否通过, 错误消息列表)
        """
        errors = []
        
        try:
            logger.info("执行快照后验证")
            self._add_log(snapshot_info, "验证快照文件")
            
            # 1. 验证快照文件是否存在
            if not os.path.exists(snapshot_path):
                error_msg = f"快照文件不存在: {snapshot_path}"
                errors.append(error_msg)
                self._add_log(snapshot_info, f"错误: {error_msg}")
                return False, errors
            
            # 2. 验证快照文件大小
            file_size = os.path.getsize(snapshot_path)
            if file_size == 0:
                error_msg = "快照文件大小为0，文件可能已损坏"
                errors.append(error_msg)
                self._add_log(snapshot_info, f"错误: {error_msg}")
            else:
                self._add_log(snapshot_info, f"快照文件大小: {self._format_size(file_size)}")
            
            # 3. 验证快照文件完整性
            self._add_log(snapshot_info, "验证文件完整性")
            try:
                # 尝试打开 tar.gz 文件
                with tarfile.open(snapshot_path, 'r:gz') as tar:
                    # 获取文件列表
                    members = tar.getnames()
                    
                    # 检查必要文件是否存在
                    required_files = ["snapshot_data.json"]
                    for required_file in required_files:
                        if required_file not in members:
                            error_msg = f"快照文件缺少必要文件: {required_file}"
                            errors.append(error_msg)
                            self._add_log(snapshot_info, f"错误: {error_msg}")
                    
                    # 检查 snapshot_data.json 文件
                    if "snapshot_data.json" in members:
                        try:
                            snapshot_data_file = tar.extractfile("snapshot_data.json")
                            if snapshot_data_file:
                                snapshot_data = json.load(snapshot_data_file)
                                
                                # 验证快照ID是否匹配
                                if snapshot_data.get("id") != snapshot_info.id:
                                    error_msg = "快照文件中的ID与当前快照ID不匹配"
                                    errors.append(error_msg)
                                    self._add_log(snapshot_info, f"错误: {error_msg}")
                            else:
                                error_msg = "无法读取快照数据文件"
                                errors.append(error_msg)
                                self._add_log(snapshot_info, f"错误: {error_msg}")
                        except json.JSONDecodeError as e:
                            error_msg = f"快照数据文件格式错误: {e}"
                            errors.append(error_msg)
                            self._add_log(snapshot_info, f"错误: {error_msg}")
                        except Exception as e:
                            error_msg = f"验证快照数据失败: {e}"
                            errors.append(error_msg)
                            self._add_log(snapshot_info, f"错误: {error_msg}")
            except Exception as e:
                error_msg = f"快照文件格式错误或已损坏: {e}"
                errors.append(error_msg)
                self._add_log(snapshot_info, f"错误: {error_msg}")
            
            # 4. 计算并验证文件校验和
            self._add_log(snapshot_info, "计算文件校验和")
            try:
                checksum = self._calculate_file_checksum(snapshot_path)
                if checksum:
                    self._add_log(snapshot_info, f"文件校验和: {checksum}")
                    snapshot_info.checksum = checksum
                else:
                    warning_msg = "无法计算文件校验和"
                    snapshot_info.warnings.append(warning_msg)
                    self._add_log(snapshot_info, f"警告: {warning_msg}")
            except Exception as e:
                warning_msg = f"计算文件校验和出错: {e}"
                snapshot_info.warnings.append(warning_msg)
                self._add_log(snapshot_info, f"警告: {warning_msg}")
            
            # 5. 更新快照文件信息
            snapshot_info.snapshot_path = snapshot_path
            snapshot_info.snapshot_size = file_size
            
            # 验证完成
            self._add_log(snapshot_info, "快照后验证完成")
            if errors:
                self._add_log(snapshot_info, f"验证失败: 发现 {len(errors)} 个错误")
                return False, errors
            else:
                self._add_log(snapshot_info, "验证通过")
                return True, []
            
        except Exception as e:
            error_msg = f"验证过程发生错误: {e}"
            errors.append(error_msg)
            logger.error(error_msg, exc_info=True)
            self._add_log(snapshot_info, f"验证失败: {error_msg}")
            return False, errors

    def _estimate_required_space(self, snapshot_info: SnapshotInfo) -> int:
        """
        估算创建快照需要的磁盘空间
        
        Args:
            snapshot_info: 快照信息
            
        Returns:
            估算的所需空间大小（字节）
        """
        # 基础空间需求
        required_space = 10 * 1024 * 1024  # 10MB 基础空间
        
        # 估算各部分数据大小
        if snapshot_info.config.include_system:
            required_space += 1 * 1024 * 1024  # 系统信息约 1MB
            
        if snapshot_info.config.include_processes:
            # 进程信息根据进程数量估算
            process_count = len(psutil.process_iter())
            required_space += process_count * 10 * 1024  # 每个进程约 10KB
            
        if snapshot_info.config.include_network:
            required_space += 2 * 1024 * 1024  # 网络信息约 2MB
            
        if snapshot_info.config.include_files:
            # 文件信息根据当前目录大小估算
            stats = os.statvfs(self.data_dir)
            files_size = stats.f_blocks * stats.f_frsize
            # 文件元数据大约是文件大小的 1%
            required_space += max(5 * 1024 * 1024, int(files_size * 0.01))
            
        if snapshot_info.config.include_database:
            # 数据库信息估算
            db_info = self._get_database_config()
            if db_info:
                if db_info.get("type") == "sqlite":
                    db_path = db_info.get("path", "")
                    if os.path.exists(db_path):
                        required_space += os.path.getsize(db_path)
                else:
                    # 对于其他数据库，假设需要 50MB 空间
                    required_space += 50 * 1024 * 1024
                
        if snapshot_info.config.include_cache:
            required_space += 2 * 1024 * 1024  # 缓存信息约 2MB
        
        # 如果启用压缩，实际空间需求可能较少
        if snapshot_info.config.compress:
            required_space = int(required_space * 0.5)  # 假设压缩比为 50%
        
        # 添加安全余量
        required_space = int(required_space * 1.5)
        
        return required_space

    def _calculate_file_checksum(self, file_path: str) -> str:
        """
        计算文件的MD5校验和
        
        Args:
            file_path: 文件路径
            
        Returns:
            MD5校验和字符串
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件校验和出错: {e}", exc_info=True)
            return "" 

    def _collect_redis_info(self) -> Dict[str, Any]:
        """
        收集Redis缓存信息
        
        Returns:
            Redis缓存信息
        """
        # 初始化默认返回数据结构
        redis_info = {
            "connected": False,
            "server": {},
            "memory": {},
            "db_stats": {},
            "performance": {},
            "timestamp": datetime.datetime.now().isoformat(),
            "total_keys": 0,
            "keyspace_stats": []
        }
        
        try:
            # 尝试导入redis模块
            import redis
            import importlib
            import sys
            
            # 在不同的位置查找Redis配置
            redis_configs = []
            
            # 1. 尝试从配置文件中获取
            try:
                for module_name in ["ky.config", "config", "ky.common.config"]:
                    try:
                        config_module = importlib.import_module(module_name)
                        if hasattr(config_module, "REDIS_CONFIG"):
                            redis_configs.append(getattr(config_module, "REDIS_CONFIG"))
                        elif hasattr(config_module, "RedisConfig"):
                            redis_configs.append(getattr(config_module, "RedisConfig"))
                        # 查找设置对象
                        elif hasattr(config_module, "Settings"):
                            settings = getattr(config_module, "Settings")
                            if hasattr(settings, "REDIS_HOST") and hasattr(settings, "REDIS_PORT"):
                                redis_configs.append({
                                    "host": getattr(settings, "REDIS_HOST"),
                                    "port": getattr(settings, "REDIS_PORT"),
                                    "password": getattr(settings, "REDIS_PASSWORD", None),
                                    "db": getattr(settings, "REDIS_DB", 0)
                                })
                    except ImportError:
                        continue
            except Exception as e:
                logger.warning(f"从配置模块获取Redis配置出错: {e}")
            
            # 2. 查看是否有环境变量
            import os
            if "REDIS_HOST" in os.environ and "REDIS_PORT" in os.environ:
                redis_configs.append({
                    "host": os.environ.get("REDIS_HOST"),
                    "port": int(os.environ.get("REDIS_PORT")),
                    "password": os.environ.get("REDIS_PASSWORD", None),
                    "db": int(os.environ.get("REDIS_DB", "0"))
                })
            
            # 3. 常见的默认配置
            redis_configs.extend([
                {"host": "localhost", "port": 6379, "password": None, "db": 0},
                {"host": "redis", "port": 6379, "password": None, "db": 0},
                {"host": "127.0.0.1", "port": 6379, "password": None, "db": 0}
            ])
            
            # 寻找已存在的Redis连接
            for module_name, module in sys.modules.items():
                if "redis" in module_name.lower():
                    for attr_name in dir(module):
                        if any(name in attr_name.lower() for name in ["client", "redis", "connection", "pool"]):
                            try:
                                attr = getattr(module, attr_name)
                                # 检测是否是Redis客户端或连接池
                                if hasattr(attr, "connection_pool") or hasattr(attr, "execute_command"):
                                    r = attr
                                    # 收集信息
                                    info = r.info()
                                    redis_info = self._process_redis_info(r, info)
                                    return redis_info
                            except Exception:
                                continue
            
            # 尝试使用配置连接Redis
            for config in redis_configs:
                try:
                    r = redis.Redis(**config)
                    # 测试连接
                    if not r.ping():
                        continue
                    
                    # 收集信息
                    info = r.info()
                    redis_info = self._process_redis_info(r, info)
                    return redis_info
                except Exception as e:
                    logger.debug(f"尝试连接Redis失败: {config}, 错误: {e}")
                    continue
            
            logger.info("未找到可用的Redis连接")
            return redis_info
            
        except ImportError:
            logger.info("未找到Redis库，无法收集Redis信息")
            return redis_info
        except Exception as e:
            logger.warning(f"收集Redis信息出错: {e}")
            return redis_info
            
    def _process_redis_info(self, redis_client, info: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理Redis信息
        
        Args:
            redis_client: Redis客户端
            info: Redis info命令返回的信息
            
        Returns:
            处理后的Redis信息
        """
        redis_info = {
            "connected": True,
            "server": {
                "version": info.get("redis_version", "unknown"),
                "os": info.get("os", "unknown"),
                "process_id": info.get("process_id", 0),
                "uptime_in_seconds": info.get("uptime_in_seconds", 0),
                "uptime_in_days": info.get("uptime_in_days", 0)
            },
            "memory": {
                "used_memory": info.get("used_memory", 0),
                "used_memory_human": info.get("used_memory_human", "0B"),
                "used_memory_peak": info.get("used_memory_peak", 0),
                "used_memory_peak_human": info.get("used_memory_peak_human", "0B"),
                "mem_fragmentation_ratio": info.get("mem_fragmentation_ratio", 0)
            },
            "performance": {
                "connected_clients": info.get("connected_clients", 0),
                "blocked_clients": info.get("blocked_clients", 0),
                "total_connections_received": info.get("total_connections_received", 0),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "instantaneous_ops_per_sec": info.get("instantaneous_ops_per_sec", 0),
                "hits": info.get("keyspace_hits", 0),
                "misses": info.get("keyspace_misses", 0),
                "hit_rate": 0.0  # 将在下面计算
            },
            "timestamp": datetime.datetime.now().isoformat(),
            "total_keys": 0,
            "keyspace_stats": []
        }
        
        # 计算命中率
        hits = info.get("keyspace_hits", 0)
        misses = info.get("keyspace_misses", 0)
        total = hits + misses
        redis_info["performance"]["hit_rate"] = (hits / total * 100) if total > 0 else 0
        
        # 收集数据库信息和键数量
        total_keys = 0
        keyspace_stats = []
        
        # 获取所有数据库的统计信息
        for db_name, db_info in info.items():
            if db_name.startswith("db"):
                db_number = int(db_name[2:])
                keys = db_info.get("keys", 0)
                total_keys += keys
                expires = db_info.get("expires", 0)
                keyspace_stats.append({
                    "db": db_number,
                    "keys": keys,
                    "expires": expires,
                    "avg_ttl": db_info.get("avg_ttl", 0)
                })
                
                # 尝试获取更多键的详细信息
                try:
                    redis_client.select(db_number)
                    # 示例键
                    sample_keys = []
                    cursor = 0
                    key_pattern = "*"
                    max_samples = 5
                    
                    # 使用SCAN命令安全地遍历键，避免阻塞Redis
                    while len(sample_keys) < max_samples:
                        cursor, keys = redis_client.scan(cursor, key_pattern, 10)
                        for key in keys[:max_samples - len(sample_keys)]:
                            key_str = key.decode('utf-8', errors='ignore') if isinstance(key, bytes) else str(key)
                            key_type = redis_client.type(key).decode('utf-8', errors='ignore')
                            ttl = redis_client.ttl(key)
                            
                            key_info = {
                                "name": key_str,
                                "type": key_type,
                                "ttl": ttl
                            }
                            
                            # 根据类型获取额外信息
                            if key_type == "string":
                                try:
                                    value = redis_client.get(key)
                                    value_str = value.decode('utf-8', errors='ignore') if isinstance(value, bytes) else str(value)
                                    size = len(value_str)
                                    key_info["size"] = size
                                    if len(value_str) < 100:  # 只保存小值
                                        key_info["sample"] = value_str
                                except Exception:
                                    pass
                            elif key_type == "list":
                                key_info["length"] = redis_client.llen(key)
                            elif key_type == "set":
                                key_info["cardinality"] = redis_client.scard(key)
                            elif key_type == "zset":
                                key_info["cardinality"] = redis_client.zcard(key)
                            elif key_type == "hash":
                                key_info["field_count"] = redis_client.hlen(key)
                            
                            sample_keys.append(key_info)
                        
                        if cursor == 0:
                            break
                    
                    keyspace_stats[-1]["sample_keys"] = sample_keys
                except Exception as e:
                    logger.debug(f"获取Redis DB {db_number}的键信息出错: {e}")
                    
        redis_info["total_keys"] = total_keys
        redis_info["keyspace_stats"] = keyspace_stats
        
        # 尝试获取慢日志信息
        try:
            slow_logs = redis_client.slowlog_get(10)
            if slow_logs:
                redis_info["slow_logs"] = [
                    {
                        "id": log.get("id", ""),
                        "timestamp": datetime.datetime.fromtimestamp(log.get("start_time", 0)).isoformat(),
                        "duration": log.get("duration", 0),
                        "command": " ".join([arg.decode('utf-8', errors='ignore') 
                                          if isinstance(arg, bytes) else str(arg) 
                                          for arg in log.get("command", [])])
                    }
                    for log in slow_logs
                ]
        except Exception as e:
            logger.debug(f"获取Redis慢日志出错: {e}")
        
        return redis_info

    def _collect_memory_cache_info(self) -> Dict[str, Any]:
        """
        收集内存缓存信息
        
        Returns:
            内存缓存信息
        """
        try:
            # 获取可能的内存缓存实例
            memory_caches = self._get_memory_caches()
            
            # 初始化结果
            total_keys = 0
            hit_count = 0
            miss_count = 0
            estimated_memory = 0
            cache_details = []
            
            # 遍历所有缓存对象
            for cache_name, cache_obj in memory_caches.items():
                # 基本信息
                cache_info = {
                    "name": cache_name,
                    "type": type(cache_obj).__name__
                }
                
                # 键数量
                keys_count = 0
                if hasattr(cache_obj, "currsize"):
                    keys_count = cache_obj.currsize
                elif hasattr(cache_obj, "__len__"):
                    keys_count = len(cache_obj)
                
                cache_info["keys_count"] = keys_count
                total_keys += keys_count
                
                # 容量
                if hasattr(cache_obj, "maxsize"):
                    cache_info["max_size"] = cache_obj.maxsize
                    cache_info["usage_percentage"] = (keys_count / cache_obj.maxsize * 100) if cache_obj.maxsize > 0 else 0
                
                # 命中统计
                if hasattr(cache_obj, "_hits"):
                    cache_info["hits"] = cache_obj._hits
                    hit_count += cache_obj._hits
                elif hasattr(cache_obj, "hits"):
                    cache_info["hits"] = cache_obj.hits
                    hit_count += cache_obj.hits
                
                if hasattr(cache_obj, "_misses"):
                    cache_info["misses"] = cache_obj._misses
                    miss_count += cache_obj._misses
                elif hasattr(cache_obj, "misses"):
                    cache_info["misses"] = cache_obj.misses
                    miss_count += cache_obj.misses
                
                # 计算命中率
                if "hits" in cache_info and "misses" in cache_info:
                    cache_hit_count = cache_info["hits"]
                    cache_miss_count = cache_info["misses"]
                    cache_total = cache_hit_count + cache_miss_count
                    if cache_total > 0:
                        cache_info["hit_rate"] = (cache_hit_count / cache_total) * 100
                    else:
                        cache_info["hit_rate"] = 0.0
                
                # 内存估算
                cache_memory = 0
                
                # 如果是字典类缓存，可以遍历所有键值对估算内存
                if hasattr(cache_obj, "items") and callable(cache_obj.items):
                    try:
                        for key, value in cache_obj.items():
                            cache_memory += self._estimate_object_size(key)
                            cache_memory += self._estimate_object_size(value)
                    except Exception as e:
                        logger.debug(f"估算缓存 {cache_name} 内存使用出错: {e}")
                
                # 如果上面的方法不可用或者返回了0，使用粗略估计
                if cache_memory == 0 and keys_count > 0:
                    # 假设每个缓存项平均4KB
                    cache_memory = keys_count * 4 * 1024
                
                cache_info["estimated_memory"] = cache_memory
                cache_info["estimated_memory_human"] = self._format_size(cache_memory)
                estimated_memory += cache_memory
                
                # 添加到详细列表
                cache_details.append(cache_info)
                
                # 尝试获取缓存的键样本
                samples = []
                try:
                    if hasattr(cache_obj, "keys") and callable(cache_obj.keys):
                        for idx, key in enumerate(list(cache_obj.keys())[:5]):  # 最多5个样本
                            value = cache_obj.get(key)
                            key_str = str(key)
                            value_str = str(value) if len(str(value)) < 100 else f"{str(value)[:97]}..."
                            samples.append({
                                "key": key_str,
                                "value": value_str,
                                "key_type": type(key).__name__,
                                "value_type": type(value).__name__
                            })
                except Exception as e:
                    logger.debug(f"获取缓存 {cache_name} 键样本出错: {e}")
                    
                if samples:
                    cache_info["samples"] = samples
            
            # 汇总统计信息
            memory_cache_info = {
                "timestamp": datetime.datetime.now().isoformat(),
                "total_keys": total_keys,
                "estimated_memory": estimated_memory,
                "estimated_memory_human": self._format_size(estimated_memory),
                "hit_count": hit_count,
                "miss_count": miss_count,
                "hit_rate": (hit_count / (hit_count + miss_count) * 100) if (hit_count + miss_count) > 0 else 0,
                "miss_rate": (miss_count / (hit_count + miss_count) * 100) if (hit_count + miss_count) > 0 else 0,
                "caches": cache_details
            }
            
            return memory_cache_info
            
        except Exception as e:
            logger.warning(f"收集内存缓存信息出错: {e}")
            return {
                "timestamp": datetime.datetime.now().isoformat(),
                "total_keys": 0,
                "estimated_memory": 0,
                "estimated_memory_human": "0 B",
                "hit_count": 0,
                "miss_count": 0,
                "hit_rate": 0,
                "miss_rate": 0,
                "caches": [],
                "error": str(e)
            }
