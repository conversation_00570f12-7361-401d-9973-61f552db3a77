"""
Session Manager - Device session persistence and management.

This module provides functionality for managing device connection sessions,
including session persistence, recovery during network disruptions, and
automatic reconnection.
"""

import json
import logging
import threading
import time
import uuid
import os
import pickle
import copy
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Callable, Any, Set, Tuple

logger = logging.getLogger(__name__)


class SessionState(Enum):
    """Possible states for a device session."""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    RECONNECTING = "reconnecting"
    DISCONNECTED = "disconnected"
    FAILED = "failed"
    EXPIRED = "expired"


class SessionEvent(Enum):
    """Events that can occur during a session."""
    CREATED = "created"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    SUSPENDED = "suspended"
    RESUMED = "resumed"
    RECONNECTING = "reconnecting"
    RECONNECTED = "reconnected"
    FAILED = "failed"
    EXPIRED = "expired"
    HEARTBEAT = "heartbeat"


@dataclass
class SessionMetrics:
    """Metrics and statistics for a session."""
    total_connected_time: int = 0  # Total time in seconds
    connection_count: int = 0  # Number of connections
    last_connected: Optional[float] = None  # Timestamp of last connection
    last_disconnected: Optional[float] = None  # Timestamp of last disconnection
    last_heartbeat: Optional[float] = None  # Timestamp of last heartbeat
    reconnect_attempts: int = 0  # Number of reconnection attempts
    successful_reconnects: int = 0  # Number of successful reconnections
    failed_reconnects: int = 0  # Number of failed reconnections
    network_interruptions: int = 0  # Number of network interruptions
    max_latency: float = 0.0  # Maximum latency in seconds
    min_latency: float = 0.0  # Minimum latency in seconds
    avg_latency: float = 0.0  # Average latency in seconds
    total_data_sent: int = 0  # Total data sent in bytes
    total_data_received: int = 0  # Total data received in bytes
    
    def update_connected_time(self) -> None:
        """Update total connected time if session is currently active."""
        if self.last_connected and not self.last_disconnected:
            self.total_connected_time += int(time.time() - self.last_connected)
            self.last_connected = time.time()
    
    def start_connection(self) -> None:
        """Record start of a connection."""
        self.last_connected = time.time()
        self.connection_count += 1
    
    def end_connection(self) -> None:
        """Record end of a connection."""
        if self.last_connected:
            self.total_connected_time += int(time.time() - self.last_connected)
            self.last_disconnected = time.time()
    
    def record_heartbeat(self, latency: Optional[float] = None) -> None:
        """Record a heartbeat."""
        self.last_heartbeat = time.time()
        
        if latency is not None:
            if self.min_latency == 0.0 or latency < self.min_latency:
                self.min_latency = latency
            
            if latency > self.max_latency:
                self.max_latency = latency
            
            # Update average latency
            if self.avg_latency == 0.0:
                self.avg_latency = latency
            else:
                self.avg_latency = (self.avg_latency * 0.9) + (latency * 0.1)  # Weighted average
    
    def record_network_interruption(self) -> None:
        """Record a network interruption."""
        self.network_interruptions += 1
    
    def record_reconnect_attempt(self) -> None:
        """Record a reconnection attempt."""
        self.reconnect_attempts += 1
    
    def record_successful_reconnect(self) -> None:
        """Record a successful reconnection."""
        self.successful_reconnects += 1
    
    def record_failed_reconnect(self) -> None:
        """Record a failed reconnection."""
        self.failed_reconnects += 1
    
    def record_data_sent(self, bytes_sent: int) -> None:
        """Record data sent."""
        self.total_data_sent += bytes_sent
    
    def record_data_received(self, bytes_received: int) -> None:
        """Record data received."""
        self.total_data_received += bytes_received
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to a dictionary."""
        return {
            "total_connected_time": self.total_connected_time,
            "connection_count": self.connection_count,
            "last_connected": self.last_connected,
            "last_disconnected": self.last_disconnected,
            "last_heartbeat": self.last_heartbeat,
            "reconnect_attempts": self.reconnect_attempts,
            "successful_reconnects": self.successful_reconnects,
            "failed_reconnects": self.failed_reconnects,
            "network_interruptions": self.network_interruptions,
            "max_latency": self.max_latency,
            "min_latency": self.min_latency,
            "avg_latency": self.avg_latency,
            "total_data_sent": self.total_data_sent,
            "total_data_received": self.total_data_received
        }


@dataclass
class SessionHistoryEntry:
    """An entry in the session history."""
    timestamp: float
    event: SessionEvent
    details: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert history entry to a dictionary."""
        return {
            "timestamp": self.timestamp,
            "event": self.event.value,
            "details": self.details
        }


@dataclass
class Session:
    """Represents a device connection session."""
    session_id: str
    device_uuid: str
    user_id: Optional[str]
    state: SessionState
    client_id: Optional[str] = None  # Client identifier (e.g., IP address)
    client_info: Dict[str, Any] = field(default_factory=dict)  # Additional client information
    created_at: float = field(default_factory=time.time)  # Creation timestamp
    expires_at: Optional[float] = None  # Expiration timestamp
    last_activity: float = field(default_factory=time.time)  # Last activity timestamp
    connection_params: Dict[str, Any] = field(default_factory=dict)  # Connection parameters
    metrics: SessionMetrics = field(default_factory=SessionMetrics)  # Session metrics
    history: List[SessionHistoryEntry] = field(default_factory=list)  # Session history
    reconnect_token: Optional[str] = None  # Token for reconnection
    max_reconnect_attempts: int = 3  # Maximum number of reconnection attempts
    reconnect_timeout: int = 30  # Reconnection timeout in seconds
    heartbeat_interval: int = 10  # Heartbeat interval in seconds
    
    def __post_init__(self) -> None:
        """Initialize session."""
        if not self.reconnect_token:
            self.reconnect_token = str(uuid.uuid4())
        
        # Add created event to history
        self.add_history_entry(SessionEvent.CREATED)
    
    def update_state(self, new_state: SessionState, details: Dict[str, Any] = None) -> None:
        """Update the session state and record in history."""
        old_state = self.state
        self.state = new_state
        self.last_activity = time.time()
        
        # Record metrics based on state transition
        if new_state == SessionState.ACTIVE and old_state != SessionState.ACTIVE:
            self.metrics.start_connection()
            self.add_history_entry(SessionEvent.CONNECTED, details)
        
        elif old_state == SessionState.ACTIVE and new_state != SessionState.ACTIVE:
            self.metrics.end_connection()
            
            if new_state == SessionState.DISCONNECTED:
                self.add_history_entry(SessionEvent.DISCONNECTED, details)
            elif new_state == SessionState.SUSPENDED:
                self.add_history_entry(SessionEvent.SUSPENDED, details)
            elif new_state == SessionState.RECONNECTING:
                self.metrics.record_network_interruption()
                self.add_history_entry(SessionEvent.RECONNECTING, details)
            elif new_state == SessionState.FAILED:
                self.add_history_entry(SessionEvent.FAILED, details)
            elif new_state == SessionState.EXPIRED:
                self.add_history_entry(SessionEvent.EXPIRED, details)
        
        elif new_state == SessionState.ACTIVE and old_state == SessionState.RECONNECTING:
            self.metrics.record_successful_reconnect()
            self.add_history_entry(SessionEvent.RECONNECTED, details)
        
        elif new_state == SessionState.ACTIVE and old_state == SessionState.SUSPENDED:
            self.add_history_entry(SessionEvent.RESUMED, details)
    
    def add_history_entry(self, event: SessionEvent, details: Dict[str, Any] = None) -> None:
        """Add an entry to the session history."""
        entry = SessionHistoryEntry(
            timestamp=time.time(),
            event=event,
            details=details or {}
        )
        self.history.append(entry)
        
        # Limit history size to prevent memory issues
        if len(self.history) > 100:
            self.history = self.history[-100:]
    
    def is_expired(self) -> bool:
        """Check if the session has expired."""
        if self.expires_at and time.time() > self.expires_at:
            return True
        return False
    
    def is_inactive(self, inactive_threshold: int = 300) -> bool:
        """Check if the session has been inactive for too long."""
        return (time.time() - self.last_activity) > inactive_threshold
    
    def can_reconnect(self) -> bool:
        """Check if the session can be reconnected."""
        if self.state != SessionState.RECONNECTING:
            return False
        
        # Check if we've exceeded max reconnect attempts
        if self.metrics.reconnect_attempts >= self.max_reconnect_attempts:
            return False
        
        # Check if we're within the reconnect timeout
        last_disconnected = self.metrics.last_disconnected
        if not last_disconnected:
            return False
        
        return (time.time() - last_disconnected) <= self.reconnect_timeout
    
    def record_heartbeat(self, latency: Optional[float] = None) -> None:
        """Record a heartbeat."""
        self.last_activity = time.time()
        self.metrics.record_heartbeat(latency)
        self.add_history_entry(SessionEvent.HEARTBEAT, {"latency": latency} if latency else {})
    
    def needs_heartbeat(self) -> bool:
        """Check if a heartbeat is needed."""
        if self.state != SessionState.ACTIVE:
            return False
        
        last_heartbeat = self.metrics.last_heartbeat
        if not last_heartbeat:
            return True
        
        return (time.time() - last_heartbeat) >= self.heartbeat_interval
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to a dictionary."""
        return {
            "session_id": self.session_id,
            "device_uuid": self.device_uuid,
            "user_id": self.user_id,
            "state": self.state.value,
            "client_id": self.client_id,
            "client_info": self.client_info,
            "created_at": self.created_at,
            "expires_at": self.expires_at,
            "last_activity": self.last_activity,
            "connection_params": self.connection_params,
            "metrics": self.metrics.to_dict(),
            "history": [entry.to_dict() for entry in self.history],
            "reconnect_token": self.reconnect_token,
            "max_reconnect_attempts": self.max_reconnect_attempts,
            "reconnect_timeout": self.reconnect_timeout,
            "heartbeat_interval": self.heartbeat_interval
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Session':
        """Create a session from a dictionary."""
        # Create metrics
        metrics_data = data.get("metrics", {})
        metrics = SessionMetrics(
            total_connected_time=metrics_data.get("total_connected_time", 0),
            connection_count=metrics_data.get("connection_count", 0),
            last_connected=metrics_data.get("last_connected"),
            last_disconnected=metrics_data.get("last_disconnected"),
            last_heartbeat=metrics_data.get("last_heartbeat"),
            reconnect_attempts=metrics_data.get("reconnect_attempts", 0),
            successful_reconnects=metrics_data.get("successful_reconnects", 0),
            failed_reconnects=metrics_data.get("failed_reconnects", 0),
            network_interruptions=metrics_data.get("network_interruptions", 0),
            max_latency=metrics_data.get("max_latency", 0.0),
            min_latency=metrics_data.get("min_latency", 0.0),
            avg_latency=metrics_data.get("avg_latency", 0.0),
            total_data_sent=metrics_data.get("total_data_sent", 0),
            total_data_received=metrics_data.get("total_data_received", 0)
        )
        
        # Create history entries
        history = []
        for entry_data in data.get("history", []):
            try:
                event = SessionEvent(entry_data.get("event", ""))
                history.append(SessionHistoryEntry(
                    timestamp=entry_data.get("timestamp", 0.0),
                    event=event,
                    details=entry_data.get("details", {})
                ))
            except (ValueError, KeyError):
                # Skip invalid entries
                pass
        
        # Create session
        session = cls(
            session_id=data.get("session_id", ""),
            device_uuid=data.get("device_uuid", ""),
            user_id=data.get("user_id"),
            state=SessionState(data.get("state", SessionState.DISCONNECTED.value)),
            client_id=data.get("client_id"),
            client_info=data.get("client_info", {}),
            created_at=data.get("created_at", time.time()),
            expires_at=data.get("expires_at"),
            last_activity=data.get("last_activity", time.time()),
            connection_params=data.get("connection_params", {}),
            reconnect_token=data.get("reconnect_token"),
            max_reconnect_attempts=data.get("max_reconnect_attempts", 3),
            reconnect_timeout=data.get("reconnect_timeout", 30),
            heartbeat_interval=data.get("heartbeat_interval", 10)
        )
        
        # Set metrics and history separately to avoid post_init overwriting
        session.metrics = metrics
        session.history = history
        
        return session


class SessionManager:
    """Manages device connection sessions, providing persistence and recovery."""
    
    def __init__(self, storage_dir: Optional[str] = None, 
                 backup_interval: int = 60,
                 cleanup_interval: int = 300,
                 session_inactivity_timeout: int = 1800,
                 session_max_lifetime: int = 86400,
                 default_reconnect_timeout: int = 180,
                 default_max_reconnect_attempts: int = 5):
        """Initialize the session manager."""
        # Sessions by ID
        self.sessions: Dict[str, Session] = {}
        
        # Sessions by device UUID
        self.device_sessions: Dict[str, Set[str]] = {}
        
        # Sessions by user ID
        self.user_sessions: Dict[str, Set[str]] = {}
        
        # Sessions by reconnect token
        self.reconnect_tokens: Dict[str, str] = {}
        
        # Session event listeners
        self.listeners: List[Callable[[str, SessionEvent, Dict[str, Any]], None]] = []
        
        # Storage directory
        self.storage_dir = storage_dir or os.path.expanduser("~/.ky/sessions")
        os.makedirs(self.storage_dir, exist_ok=True)
        
        # Session timeouts
        self.session_inactivity_timeout = session_inactivity_timeout
        self.session_max_lifetime = session_max_lifetime
        self.default_reconnect_timeout = default_reconnect_timeout
        self.default_max_reconnect_attempts = default_max_reconnect_attempts
        
        # Locks
        self.session_lock = threading.RLock()
        
        # Maintenance thread
        self.maintenance_thread = None
        self.running = False
        self.backup_interval = backup_interval
        self.cleanup_interval = cleanup_interval
        self.last_backup_time = 0
        self.last_cleanup_time = 0
        
        # Shadow copy for crash recovery
        self.shadow_sessions: Dict[str, Session] = {}
        self.shadow_lock = threading.RLock()
        
        # Start maintenance thread
        self.start_maintenance()
        
        # Load any persisted sessions
        self._load_sessions()
        
        logger.info(f"Session manager initialized with storage at {self.storage_dir}")
    
    def register_session_listener(self, listener: Callable[[str, SessionEvent, Dict[str, Any]], None]) -> None:
        """Register a listener for session events.
        
        Args:
            listener: Callback function that takes (session_id, event, details)
        """
        if listener not in self.listeners:
            self.listeners.append(listener)
    
    def unregister_session_listener(self, listener: Callable[[str, SessionEvent, Dict[str, Any]], None]) -> None:
        """Unregister a session event listener."""
        if listener in self.listeners:
            self.listeners.remove(listener)
    
    def _notify_listeners(self, session_id: str, event: SessionEvent, details: Dict[str, Any] = None) -> None:
        """Notify all listeners of a session event."""
        for listener in self.listeners:
            try:
                listener(session_id, event, details or {})
            except Exception as e:
                logger.error(f"Error in session listener: {e}")
    
    def create_session(self, device_uuid: str, user_id: Optional[str] = None,
                      client_id: Optional[str] = None, client_info: Dict[str, Any] = None,
                      connection_params: Dict[str, Any] = None,
                      expires_in: Optional[int] = None) -> Session:
        """Create a new session.
        
        Args:
            device_uuid: UUID of the device
            user_id: ID of the user
            client_id: ID of the client
            client_info: Additional client information
            connection_params: Connection parameters
            expires_in: Session expiration time in seconds
            
        Returns:
            The created session
        """
        session_id = str(uuid.uuid4())
        expires_at = time.time() + expires_in if expires_in else None
        
        session = Session(
            session_id=session_id,
            device_uuid=device_uuid,
            user_id=user_id,
            state=SessionState.INITIALIZING,
            client_id=client_id,
            client_info=client_info or {},
            expires_at=expires_at,
            connection_params=connection_params or {}
        )
        
        # Register session
        self.sessions[session_id] = session
        self.reconnect_tokens[session.reconnect_token] = session_id
        
        # Add to device sessions
        if device_uuid not in self.device_sessions:
            self.device_sessions[device_uuid] = set()
        self.device_sessions[device_uuid].add(session_id)
        
        # Add to user sessions
        if user_id:
            if user_id not in self.user_sessions:
                self.user_sessions[user_id] = set()
            self.user_sessions[user_id].add(session_id)
        
        logger.info(f"Created session {session_id} for device {device_uuid}")
        self._notify_listeners(session_id, SessionEvent.CREATED, {"device_uuid": device_uuid, "user_id": user_id})
        
        return session
    
    def get_session(self, session_id: str) -> Optional[Session]:
        """Get a session by ID."""
        return self.sessions.get(session_id)
    
    def get_session_by_reconnect_token(self, reconnect_token: str) -> Optional[Session]:
        """Get a session by reconnect token."""
        session_id = self.reconnect_tokens.get(reconnect_token)
        if session_id:
            return self.get_session(session_id)
        return None
    
    def get_device_sessions(self, device_uuid: str) -> List[Session]:
        """Get all sessions for a device."""
        session_ids = self.device_sessions.get(device_uuid, set())
        return [self.sessions[sid] for sid in session_ids if sid in self.sessions]
    
    def get_user_sessions(self, user_id: str) -> List[Session]:
        """Get all sessions for a user."""
        session_ids = self.user_sessions.get(user_id, set())
        return [self.sessions[sid] for sid in session_ids if sid in self.sessions]
    
    def get_active_device_session(self, device_uuid: str) -> Optional[Session]:
        """Get the active session for a device."""
        sessions = self.get_device_sessions(device_uuid)
        for session in sessions:
            if session.state == SessionState.ACTIVE:
                return session
        return None
    
    def activate_session(self, session_id: str) -> bool:
        """Activate a session.
        
        Args:
            session_id: ID of the session
            
        Returns:
            True if successful, False otherwise
        """
        session = self.get_session(session_id)
        if not session:
            logger.warning(f"Session {session_id} not found")
            return False
        
        # Check if session is expired
        if session.is_expired():
            session.update_state(SessionState.EXPIRED)
            logger.warning(f"Session {session_id} has expired")
            self._notify_listeners(session_id, SessionEvent.EXPIRED)
            return False
        
        # Update session state
        session.update_state(SessionState.ACTIVE)
        logger.info(f"Activated session {session_id}")
        
        return True
    
    def suspend_session(self, session_id: str, reason: Optional[str] = None) -> bool:
        """Suspend a session.
        
        Args:
            session_id: ID of the session
            reason: Reason for suspension
            
        Returns:
            True if successful, False otherwise
        """
        session = self.get_session(session_id)
        if not session:
            logger.warning(f"Session {session_id} not found")
            return False
        
        # Update session state
        session.update_state(SessionState.SUSPENDED, {"reason": reason} if reason else {})
        logger.info(f"Suspended session {session_id}")
        
        return True
    
    def resume_session(self, session_id: str) -> bool:
        """Resume a suspended session."""
        with self.session_lock:
            session = self.sessions.get(session_id)
            if not session:
                logger.warning(f"Cannot resume session {session_id}: not found")
                return False
            
            if session.state != SessionState.SUSPENDED:
                logger.warning(f"Cannot resume session {session_id}: not suspended (current state: {session.state.value})")
                return False
            
            # Check if device is still available
            # This would typically involve checking with the device service
            # For now, we'll just assume it's available
            
            # Update session state
            session.update_state(SessionState.ACTIVE, {"resumed_at": time.time()})
            
            # Notify listeners
            self._notify_listeners(session_id, SessionEvent.RESUMED)
            
            # Take a shadow copy for crash recovery
            self._update_shadow_copy(session)
            
            logger.info(f"Resumed session {session_id} for device {session.device_uuid}")
            return True
    
    def disconnect_session(self, session_id: str, reason: Optional[str] = None) -> bool:
        """Disconnect a session.
        
        Args:
            session_id: ID of the session
            reason: Reason for disconnection
            
        Returns:
            True if successful, False otherwise
        """
        session = self.get_session(session_id)
        if not session:
            logger.warning(f"Session {session_id} not found")
            return False
        
        # Update session state
        session.update_state(SessionState.DISCONNECTED, {"reason": reason} if reason else {})
        logger.info(f"Disconnected session {session_id}")
        
        return True
    
    def reconnect_session(self, reconnect_token: str, client_id: Optional[str] = None,
                         client_info: Optional[Dict[str, Any]] = None) -> Optional[Session]:
        """Reconnect to an existing session using a reconnect token."""
        with self.session_lock:
            session_id = self.reconnect_tokens.get(reconnect_token)
            if not session_id:
                logger.warning(f"Cannot reconnect: invalid reconnect token")
                return None
            
            session = self.sessions.get(session_id)
            if not session:
                logger.warning(f"Cannot reconnect: session {session_id} not found")
                # Clean up the dangling token
                self.reconnect_tokens.pop(reconnect_token, None)
                return None
            
            # Check if session can be reconnected
            if not session.can_reconnect():
                logger.warning(f"Cannot reconnect session {session_id}: max attempts exceeded or timeout")
                return None
            
            # Update client information if provided
            if client_id:
                session.client_id = client_id
            if client_info:
                session.client_info.update(client_info)
            
            # Record reconnect attempt
            session.metrics.record_reconnect_attempt()
            
            # Try to reconnect
            try:
                # In a real implementation, this would involve checking with the device service
                # For now, we'll just assume it's successful
                
                # Update session state
                session.update_state(SessionState.ACTIVE, {
                    "reconnected_at": time.time(),
                    "client_id": client_id,
                    "attempt": session.metrics.reconnect_attempts
                })
                
                # Notify listeners
                self._notify_listeners(session_id, SessionEvent.RECONNECTED, {
                    "reconnect_token": reconnect_token,
                    "client_id": client_id
                })
                
                # Take a shadow copy for crash recovery
                self._update_shadow_copy(session)
                
                logger.info(f"Successfully reconnected session {session_id} for device {session.device_uuid}")
                return session
                
            except Exception as e:
                logger.error(f"Failed to reconnect session {session_id}: {str(e)}")
                
                # Record failed reconnect
                session.metrics.record_failed_reconnect()
                
                # Check if we've reached the maximum number of reconnect attempts
                if session.metrics.reconnect_attempts >= session.max_reconnect_attempts:
                    # Mark session as failed
                    session.update_state(SessionState.FAILED, {
                        "error": f"Max reconnect attempts exceeded: {session.max_reconnect_attempts}",
                        "last_error": str(e)
                    })
                    
                    # Notify listeners
                    self._notify_listeners(session_id, SessionEvent.FAILED, {
                        "reason": "max_reconnect_attempts_exceeded",
                        "error": str(e)
                    })
                    
                    # Take a shadow copy for crash recovery
                    self._update_shadow_copy(session)
                    
                    logger.warning(f"Session {session_id} failed after {session.metrics.reconnect_attempts} reconnect attempts")
                
                return None
    
    def handle_connection_failure(self, session_id: str, reason: Optional[str] = None) -> bool:
        """Handle connection failure.
        
        Args:
            session_id: ID of the session
            reason: Reason for failure
            
        Returns:
            True if reconnection is possible, False otherwise
        """
        session = self.get_session(session_id)
        if not session:
            logger.warning(f"Session {session_id} not found")
            return False
        
        # If session is already in a terminal state, do nothing
        if session.state in [SessionState.DISCONNECTED, SessionState.EXPIRED, SessionState.FAILED]:
            return False
        
        # If session can be reconnected, set state to reconnecting
        if session.metrics.reconnect_attempts < session.max_reconnect_attempts:
            session.update_state(SessionState.RECONNECTING, {"reason": reason} if reason else {})
            logger.info(f"Session {session_id} is reconnecting")
            return True
        else:
            # Otherwise, set state to failed
            session.update_state(SessionState.FAILED, {"reason": reason} if reason else {})
            session.metrics.record_failed_reconnect()
            logger.info(f"Session {session_id} has failed")
            return False
    
    def record_heartbeat(self, session_id: str, latency: Optional[float] = None) -> bool:
        """Record a heartbeat for a session.
        
        Args:
            session_id: ID of the session
            latency: Latency in seconds
            
        Returns:
            True if successful, False otherwise
        """
        session = self.get_session(session_id)
        if not session:
            logger.warning(f"Session {session_id} not found")
            return False
        
        # Record heartbeat
        session.record_heartbeat(latency)
        return True
    
    def record_data_transfer(self, session_id: str, bytes_sent: int = 0, bytes_received: int = 0) -> bool:
        """Record data transfer for a session.
        
        Args:
            session_id: ID of the session
            bytes_sent: Bytes sent
            bytes_received: Bytes received
            
        Returns:
            True if successful, False otherwise
        """
        session = self.get_session(session_id)
        if not session:
            logger.warning(f"Session {session_id} not found")
            return False
        
        # Record data transfer
        if bytes_sent > 0:
            session.metrics.record_data_sent(bytes_sent)
        
        if bytes_received > 0:
            session.metrics.record_data_received(bytes_received)
        
        # Update last activity
        session.last_activity = time.time()
        
        return True
    
    def close_session(self, session_id: str, reason: Optional[str] = None) -> bool:
        """Close a session.
        
        Args:
            session_id: ID of the session
            reason: Reason for closure
            
        Returns:
            True if successful, False otherwise
        """
        session = self.get_session(session_id)
        if not session:
            logger.warning(f"Session {session_id} not found")
            return False
        
        # First disconnect the session
        session.update_state(SessionState.DISCONNECTED, {"reason": reason} if reason else {})
        
        # Remove reconnect token
        if session.reconnect_token in self.reconnect_tokens:
            del self.reconnect_tokens[session.reconnect_token]
        
        # Remove from device sessions
        device_uuid = session.device_uuid
        if device_uuid in self.device_sessions and session_id in self.device_sessions[device_uuid]:
            self.device_sessions[device_uuid].discard(session_id)
            if not self.device_sessions[device_uuid]:
                del self.device_sessions[device_uuid]
        
        # Remove from user sessions
        user_id = session.user_id
        if user_id and user_id in self.user_sessions and session_id in self.user_sessions[user_id]:
            self.user_sessions[user_id].discard(session_id)
            if not self.user_sessions[user_id]:
                del self.user_sessions[user_id]
        
        # Remove session
        del self.sessions[session_id]
        
        logger.info(f"Closed session {session_id}")
        
        return True
    
    def close_device_sessions(self, device_uuid: str, reason: Optional[str] = None) -> int:
        """Close all sessions for a device.
        
        Args:
            device_uuid: UUID of the device
            reason: Reason for closure
            
        Returns:
            Number of sessions closed
        """
        session_ids = list(self.device_sessions.get(device_uuid, set()))
        closed_count = 0
        
        for session_id in session_ids:
            if self.close_session(session_id, reason):
                closed_count += 1
        
        return closed_count
    
    def close_user_sessions(self, user_id: str, reason: Optional[str] = None) -> int:
        """Close all sessions for a user.
        
        Args:
            user_id: ID of the user
            reason: Reason for closure
            
        Returns:
            Number of sessions closed
        """
        session_ids = list(self.user_sessions.get(user_id, set()))
        closed_count = 0
        
        for session_id in session_ids:
            if self.close_session(session_id, reason):
                closed_count += 1
        
        return closed_count
    
    def _update_shadow_copy(self, session: Session) -> None:
        """Update the shadow copy of a session for crash recovery."""
        if not session or not session.session_id:
            return
        
        with self.shadow_lock:
            self.shadow_sessions[session.session_id] = copy.deepcopy(session)
    
    def _save_sessions(self) -> None:
        """Save all sessions to disk."""
        try:
            # Create a snapshot of the sessions
            sessions_snapshot = {}
            with self.session_lock:
                for session_id, session in self.sessions.items():
                    sessions_snapshot[session_id] = session.to_dict()
            
            # Save to main file
            main_file = os.path.join(self.storage_dir, "sessions.json")
            with open(main_file, "w") as f:
                json.dump(sessions_snapshot, f)
            
            # Save to backup file with timestamp
            backup_file = os.path.join(self.storage_dir, f"sessions_{int(time.time())}.json")
            with open(backup_file, "w") as f:
                json.dump(sessions_snapshot, f)
            
            # Clean up old backup files (keep last 5)
            backup_files = [f for f in os.listdir(self.storage_dir) 
                           if f.startswith("sessions_") and f.endswith(".json")]
            backup_files.sort(reverse=True)
            for old_file in backup_files[5:]:
                try:
                    os.remove(os.path.join(self.storage_dir, old_file))
                except:
                    pass
            
            # Save shadow copies for crash recovery
            shadow_file = os.path.join(self.storage_dir, "sessions_shadow.pickle")
            with self.shadow_lock:
                with open(shadow_file, "wb") as f:
                    pickle.dump(self.shadow_sessions, f)
            
            self.last_backup_time = time.time()
            logger.debug(f"Saved {len(sessions_snapshot)} sessions to disk")
            
        except Exception as e:
            logger.error(f"Failed to save sessions: {str(e)}")
    
    def _load_sessions(self) -> None:
        """Load sessions from disk."""
        try:
            # Try to load from main file first
            main_file = os.path.join(self.storage_dir, "sessions.json")
            if os.path.exists(main_file):
                with open(main_file, "r") as f:
                    sessions_data = json.load(f)
                
                with self.session_lock:
                    for session_id, session_data in sessions_data.items():
                        try:
                            session = Session.from_dict(session_data)
                            
                            # Don't restore sessions that are too old
                            if time.time() - session.created_at > self.session_max_lifetime:
                                continue
                            
                            # Don't restore failed or expired sessions
                            if session.state in (SessionState.FAILED, SessionState.EXPIRED):
                                continue
                            
                            # Add to sessions
                            self.sessions[session_id] = session
                            
                            # Update indices
                            if session.device_uuid:
                                if session.device_uuid not in self.device_sessions:
                                    self.device_sessions[session.device_uuid] = set()
                                self.device_sessions[session.device_uuid].add(session_id)
                            
                            if session.user_id:
                                if session.user_id not in self.user_sessions:
                                    self.user_sessions[session.user_id] = set()
                                self.user_sessions[session.user_id].add(session_id)
                            
                            if session.reconnect_token:
                                self.reconnect_tokens[session.reconnect_token] = session_id
                            
                        except Exception as e:
                            logger.error(f"Failed to load session {session_id}: {str(e)}")
                
                logger.info(f"Loaded {len(self.sessions)} sessions from disk")
                
            # Check for shadow copy in case of crash
            shadow_file = os.path.join(self.storage_dir, "sessions_shadow.pickle")
            if os.path.exists(shadow_file):
                try:
                    with open(shadow_file, "rb") as f:
                        shadow_sessions = pickle.load(f)
                    
                    # Restore any sessions from shadow copy that might be missing or newer
                    with self.session_lock, self.shadow_lock:
                        for session_id, shadow_session in shadow_sessions.items():
                            if session_id not in self.sessions:
                                # Session doesn't exist, restore it
                                self.sessions[session_id] = shadow_session
                                
                                # Update indices
                                if shadow_session.device_uuid:
                                    if shadow_session.device_uuid not in self.device_sessions:
                                        self.device_sessions[shadow_session.device_uuid] = set()
                                    self.device_sessions[shadow_session.device_uuid].add(session_id)
                                
                                if shadow_session.user_id:
                                    if shadow_session.user_id not in self.user_sessions:
                                        self.user_sessions[shadow_session.user_id] = set()
                                    self.user_sessions[shadow_session.user_id].add(session_id)
                                
                                if shadow_session.reconnect_token:
                                    self.reconnect_tokens[shadow_session.reconnect_token] = session_id
                    
                    logger.info(f"Recovered shadow sessions from disk")
                
                except Exception as e:
                    logger.error(f"Failed to load shadow sessions: {str(e)}")
            
        except Exception as e:
            logger.error(f"Failed to load sessions: {str(e)}")
    
    def _maintenance_loop(self) -> None:
        """Maintenance loop that periodically backs up sessions and cleans up expired ones."""
        while self.running:
            try:
                current_time = time.time()
                
                # Save sessions periodically
                if current_time - self.last_backup_time >= self.backup_interval:
                    self._save_sessions()
                
                # Clean up sessions periodically
                if current_time - self.last_cleanup_time >= self.cleanup_interval:
                    self._check_sessions()
                    self.last_cleanup_time = current_time
                
            except Exception as e:
                logger.error(f"Error in session maintenance loop: {str(e)}")
            
            # Sleep for a bit
            time.sleep(min(self.backup_interval, self.cleanup_interval) / 4)
    
    def _check_sessions(self) -> None:
        """Check sessions for expiration, inactivity, etc."""
        current_time = time.time()
        expired_count = 0
        inactive_count = 0
        
        with self.session_lock:
            for session_id, session in list(self.sessions.items()):
                try:
                    # Check if session has expired
                    if session.expires_at and current_time > session.expires_at:
                        session.update_state(SessionState.EXPIRED, {"reason": "lifetime_exceeded"})
                        self._notify_listeners(session_id, SessionEvent.EXPIRED, {"reason": "lifetime_exceeded"})
                        expired_count += 1
                        
                        # Don't remove it yet, let it be cleaned up next time
                        continue
                    
                    # Check for inactivity
                    if session.state == SessionState.ACTIVE and session.is_inactive(self.session_inactivity_timeout):
                        # Move to SUSPENDED state
                        session.update_state(SessionState.SUSPENDED, {"reason": "inactivity"})
                        self._notify_listeners(session_id, SessionEvent.SUSPENDED, {"reason": "inactivity"})
                        inactive_count += 1
                        continue
                    
                    # Check if reconnecting session has timed out
                    if session.state == SessionState.RECONNECTING:
                        reconnect_time = session.metrics.last_disconnected or session.last_activity
                        if current_time - reconnect_time > session.reconnect_timeout:
                            # Mark as failed
                            session.update_state(SessionState.FAILED, {"reason": "reconnect_timeout"})
                            self._notify_listeners(session_id, SessionEvent.FAILED, {"reason": "reconnect_timeout"})
                            continue
                    
                    # Clean up old failed/expired/disconnected sessions
                    if session.state in (SessionState.FAILED, SessionState.EXPIRED, SessionState.DISCONNECTED):
                        # If it's been in this state for a while, remove it
                        state_time = max(
                            session.metrics.last_disconnected or 0,
                            session.last_activity
                        )
                        if current_time - state_time > 3600:  # 1 hour
                            # Actually remove the session
                            self._remove_session(session_id)
                
                except Exception as e:
                    logger.error(f"Error checking session {session_id}: {str(e)}")
        
        if expired_count > 0 or inactive_count > 0:
            logger.info(f"Session check: {expired_count} expired, {inactive_count} inactive")
    
    def _remove_session(self, session_id: str) -> None:
        """Remove a session from all indices."""
        session = self.sessions.pop(session_id, None)
        if not session:
            return
        
        # Remove from device sessions
        if session.device_uuid and session.device_uuid in self.device_sessions:
            self.device_sessions[session.device_uuid].discard(session_id)
            if not self.device_sessions[session.device_uuid]:
                self.device_sessions.pop(session.device_uuid)
        
        # Remove from user sessions
        if session.user_id and session.user_id in self.user_sessions:
            self.user_sessions[session.user_id].discard(session_id)
            if not self.user_sessions[session.user_id]:
                self.user_sessions.pop(session.user_id)
        
        # Remove from reconnect tokens
        if session.reconnect_token and session.reconnect_token in self.reconnect_tokens:
            self.reconnect_tokens.pop(session.reconnect_token)
        
        # Remove from shadow copies
        with self.shadow_lock:
            self.shadow_sessions.pop(session_id, None)
    
    def start_maintenance(self) -> None:
        """Start the maintenance thread."""
        if self.maintenance_thread and self.maintenance_thread.is_alive():
            return
        
        self.running = True
        self.maintenance_thread = threading.Thread(target=self._maintenance_loop)
        self.maintenance_thread.daemon = True
        self.maintenance_thread.start()
        logger.info("Session maintenance thread started")
    
    def shutdown(self) -> None:
        """Shut down the session manager, saving all sessions."""
        logger.info("Shutting down session manager")
        self.running = False
        
        # Wait for maintenance thread to stop
        if self.maintenance_thread and self.maintenance_thread.is_alive():
            self.maintenance_thread.join(timeout=5.0)
        
        # Save all sessions
        self._save_sessions()
        
        logger.info("Session manager shut down")


# Create a singleton instance
session_manager = SessionManager() 
