import asyncio
import logging
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy import select  # <<< FIX: Import 'select'

from common.models import Base, Organization, User
from common.schemas.user_schema import UserCreate
from src.omnilink_main.core.config import settings
from src.omnilink_main.core.security import get_password_hash

# --- CONFIGURATION ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
DATABASE_URL = "postgresql+asyncpg://postgres:bro2fhz12@localhost:5432/ky_db"

# --- CORE LOGIC ---
async def setup_database():
    logger.info(f"Connecting to database: {DATABASE_URL}")
    engine = create_async_engine(DATABASE_URL, echo=False)
    AsyncSessionFactory = async_sessionmaker(engine, expire_on_commit=False, class_=AsyncSession)
    session: AsyncSession = AsyncSessionFactory()

    try:
        # 1. Drop and Create Tables
        async with engine.begin() as conn:
            logger.info("Dropping all existing tables...")
            await conn.run_sync(Base.metadata.drop_all)
            logger.info("Creating all new tables...")
            await conn.run_sync(Base.metadata.create_all)
        logger.info("Tables created successfully.")

        # 2. Create a default organization
        default_org = Organization(name="Default Organization")
        session.add(default_org)
        await session.commit()
        await session.refresh(default_org)
        logger.info(f"Created default organization with ID: {default_org.id}")

        # 3. Create Superuser
        superuser_email = settings.FIRST_SUPERUSER_EMAIL
        user_check = await session.execute(
            select(User).where(User.email == superuser_email)
        )
        if user_check.scalar_one_or_none():
            logger.info("Superuser already exists.")
        else:
            logger.info("Creating superuser...")
            hashed_password = get_password_hash(settings.FIRST_SUPERUSER_PASSWORD)
            superuser = User(
                username=superuser_email,
                email=superuser_email,
                hashed_password=hashed_password,
                is_active=True,
                is_superuser=True,
                organization_id=default_org.id  # Assign to default org
            )
            session.add(superuser)
            await session.commit()
            logger.info("Superuser created successfully.")

    except Exception as e:
        logger.error(f"An error occurred during database setup: {e}", exc_info=True)
    finally:
        await session.close()
        await engine.dispose()
        logger.info("Database connection closed.")


if __name__ == "__main__":
    asyncio.run(setup_database())
