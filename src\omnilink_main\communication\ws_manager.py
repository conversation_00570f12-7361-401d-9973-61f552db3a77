import logging
from typing import Dict, Optional
from fastapi import WebSocket

logger = logging.getLogger(__name__)

class WebSocketManager:
    """管理从服务器主动连接过来的WebSocket"""
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        logger.info("WebSocket Manager initialized.")

    async def connect(self, slave_id: str, websocket: WebSocket):
        """接受一个新连接并存储它"""
        await websocket.accept()
        self.active_connections[slave_id] = websocket
        logger.info(f"Slave server {slave_id} connected via WebSocket.")

    def disconnect(self, slave_id: str):
        """断开一个连接"""
        if slave_id in self.active_connections:
            del self.active_connections[slave_id]
            logger.info(f"Slave server {slave_id} disconnected.")

    async def send_command(self, slave_id: str, command: dict):
        """向指定的从服务器发送指令"""
        websocket = self.active_connections.get(slave_id)
        if websocket:
            try:
                await websocket.send_json(command)
                logger.info(f"Sent command to slave {slave_id}: {command}")
            except Exception as e:
                logger.error(f"Failed to send command to slave {slave_id}: {e}")
                # Potentially handle disconnection here
                self.disconnect(slave_id)
        else:
            logger.error(f"No active WebSocket connection for slave {slave_id}.")
            raise ValueError(f"Slave server {slave_id} is not connected.")

# 创建一个全局的管理器实例
ws_manager = WebSocketManager() 