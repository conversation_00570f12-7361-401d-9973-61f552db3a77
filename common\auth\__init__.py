"""
认证模块

提供用户认证、授权、会话管理等功能。
"""

import logging
from typing import Optional

logger = logging.getLogger(__name__)

# 尝试导入认证服务
try:
    from .auth_service import (
        AuthService, 
        UserRole, 
        Permission, 
        User, 
        Session,
        get_auth_service,
        set_auth_service,
        require_auth
    )
    logger.debug("认证服务模块导入成功")
except ImportError as e:
    logger.warning(f"认证服务模块导入失败: {e}")
    # 提供空的实现以避免导入错误
    class AuthService:
        def __init__(self, *args, **kwargs):
            pass
    
    def get_auth_service():
        return AuthService()

# 尝试导入配置管理
try:
    from ..config import get_config
    logger.debug("配置管理模块导入成功")
except ImportError as e:
    logger.warning(f"配置管理模块导入失败: {e}")
    def get_config(*args, **kwargs):
        return {}

def initialize_auth(config: Optional[dict] = None) -> AuthService:
    """
    初始化认证服务
    
    参数:
        config: 认证配置字典
        
    返回:
        AuthService: 认证服务实例
    """
    try:
        if config is None:
            # 尝试从配置文件加载
            config = get_config('auth', default={})
        
        # 创建认证服务实例
        auth_service = AuthService(
            secret_key=config.get('secret_key'),
            token_expiry=config.get('token_expiry', 3600)
        )
        
        # 设置为全局实例
        set_auth_service(auth_service)
        
        logger.info("认证服务初始化成功")
        return auth_service
        
    except Exception as e:
        logger.error(f"认证服务初始化失败: {e}")
        # 返回基础实例
        return AuthService()

# 导出主要功能
__all__ = [
    'AuthService',
    'UserRole',
    'Permission', 
    'User',
    'Session',
    'get_auth_service',
    'set_auth_service',
    'require_auth',
    'initialize_auth'
] 
