"""
规则引擎评估器
此模块负责评估一个数据对象是否满足一个给定的、基于Pydantic模型的规则。
"""
import re
from typing import Any, Dict, List

from .models import Filter, FilterCondition, FilterGroup, FilterLogic, FilterOperator


class RuleEvaluator:
    """
    根据Pydantic模型定义的规则来评估数据对象的评估器。
    """

    def evaluate(self, rule: Filter, data: Dict[str, Any]) -> bool:
        """
        评估一个数据对象是否满足完整的过滤器规则。

        Args:
            rule: 包含顶层过滤组的完整Filter规则对象。
            data: 要评估的数据字典。

        Returns:
            如果数据满足规则，则为True，否则为False。
        """
        if not rule.enabled:
            return False  # 如果规则被禁用，直接返回False
        return self._evaluate_group(rule.filter_group, data)

    def _evaluate_group(self, group: FilterGroup, data: Dict[str, Any]) -> bool:
        """
        递归评估一个过滤条件组。

        Args:
            group: 要评估的FilterGroup对象。
            data: 数据字典。

        Returns:
            如果数据满足该组的逻辑，则为True。
        """
        # 评估所有子组
        group_results = [self._evaluate_group(sub_group, data) for sub_group in group.groups]
        
        # 评估所有直接条件
        condition_results = [self._evaluate_condition(condition, data) for condition in group.conditions]
        
        all_results = group_results + condition_results

        if not all_results:
            return True  # 一个空的AND/OR组应为True

        if group.logic == FilterLogic.AND:
            return all(all_results)
        
        elif group.logic == FilterLogic.OR:
            return any(all_results)
        
        elif group.logic == FilterLogic.NOT:
            # NOT逻辑通常应用于单个组或条件，这里我们假设它作用于所有子项的AND结果
            return not all(all_results)
            
        return False

    def _evaluate_condition(self, condition: FilterCondition, data: Dict[str, Any]) -> bool:
        """
        评估单个过滤条件。

        Args:
            condition: 要评估的FilterCondition对象。
            data: 数据字典。

        Returns:
            如果数据满足条件，则为True。
        """
        field_value = self._get_nested_field_value(data, condition.field)
        
        op = condition.operator
        val = condition.value
        
        # 处理大小写
        compare_value = val
        if isinstance(field_value, str) and isinstance(val, str) and not condition.case_sensitive:
            field_value = field_value.lower()
            compare_value = val.lower()

        # 根据操作符进行比较
        if op == FilterOperator.IS_NULL:
            return field_value is None
        if op == FilterOperator.IS_NOT_NULL:
            return field_value is not None
        
        # 如果字段值为None，但操作符不是空值检查，大部分情况应为False
        if field_value is None:
            return False

        if op == FilterOperator.EQUALS:
            return field_value == compare_value
        if op == FilterOperator.NOT_EQUALS:
            return field_value != compare_value
        
        # 关系比较
        try:
            if op == FilterOperator.GREATER_THAN:
                return field_value > compare_value
            if op == FilterOperator.GREATER_EQUALS:
                return field_value >= compare_value
            if op == FilterOperator.LESS_THAN:
                return field_value < compare_value
            if op == FilterOperator.LESS_EQUALS:
                return field_value <= compare_value
        except (TypeError, ValueError):
            return False # 如果类型不匹配无法比较，则评估为False

        # 字符串/列表包含
        if op == FilterOperator.CONTAINS:
            return compare_value in field_value
        if op == FilterOperator.NOT_CONTAINS:
            return compare_value not in field_value
            
        if not isinstance(field_value, str):
             # 后续操作基本都是字符串相关
             return False

        if op == FilterOperator.STARTS_WITH:
            return field_value.startswith(compare_value)
        if op == FilterOperator.ENDS_WITH:
            return field_value.endswith(compare_value)

        # 正则表达式
        if op == FilterOperator.MATCHES:
            try:
                return bool(re.search(compare_value, field_value))
            except re.error:
                return False
        if op == FilterOperator.NOT_MATCHES:
            try:
                return not bool(re.search(compare_value, field_value))
            except re.error:
                return True

        # IN / NOT IN
        if op in [FilterOperator.IN, FilterOperator.NOT_IN]:
            if not isinstance(compare_value, list):
                 return False
            if op == FilterOperator.IN:
                return field_value in compare_value
            if op == FilterOperator.NOT_IN:
                return field_value not in compare_value
            
        return False

    def _get_nested_field_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """
        从数据字典中安全地获取嵌套字段的值。

        Args:
            data: 数据字典。
            field_path: 字段路径，使用点表示法 (e.g., "device.info.type")。

        Returns:
            字段的值，如果路径不存在则返回None。
        """
        keys = field_path.split('.')
        current = data
        for key in keys:
            if not isinstance(current, dict) or key not in current:
                return None
            current = current[key]
        return current
