from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from common.models.slave_server import SlaveServer
from common.models.device import Device
from common.schemas.dashboard_schema import DashboardStats

class DashboardService:
    async def get_stats(self, db: AsyncSession) -> DashboardStats:
        """
        Retrieves dashboard statistics from the database.
        """
        connected_slaves_count_result = await db.execute(
            select(func.count(SlaveServer.id)).where(SlaveServer.status == 'online')
        )
        connected_slaves_count = connected_slaves_count_result.scalar_one()

        devices_in_use_count_result = await db.execute(
            select(func.count(Device.id)).where(Device.status == 'in_use')
        )
        devices_in_use_count = devices_in_use_count_result.scalar_one()

        return DashboardStats(
            connected_slaves_count=connected_slaves_count,
            devices_in_use_count=devices_in_use_count
        )

dashboard_service = DashboardService()
