# PostgreSQL 数据库配置
POSTGRES_SERVER=postgres-db
POSTGRES_USER=omnilink
POSTGRES_PASSWORD=bro2fhz12
POSTGRES_DB=omnilink_db
POSTGRES_PORT=5432

# Redis 配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=bro2fhz12

# JWT 配置
SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 应用配置
PROJECT_NAME=OmniLink
DEBUG=false
LOG_LEVEL=INFO

# 安全配置
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# 从服务器配置
SLAVE_API_KEY=dev-api-key-12345
SLAVE_SERVER_TIMEOUT=30

# VirtualHere 配置
VIRTUALHERE_DOWNLOAD_URL=https://www.virtualhere.com/sites/default/files/usbserver/vhusbdx86_64

# 文件上传配置
MAX_UPLOAD_SIZE=10485760
UPLOAD_DIR=/app/uploads

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_USE_TLS=true

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090 