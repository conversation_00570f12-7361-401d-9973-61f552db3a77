import sys
import time
import datetime

def main():
    """
    A mock VirtualHere executable.
    It prints its arguments and then runs forever until terminated.
    This allows us to verify that the slave_server is calling it correctly.
    """
    now = datetime.datetime.now().isoformat()
    args = " ".join(sys.argv)
    
    print(f"[{now}] Mock VirtualHere Started.")
    print(f"[{now}] Arguments: {args}")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print(f"[{now}] Mock VirtualHere Exiting.")

if __name__ == "__main__":
    main()
