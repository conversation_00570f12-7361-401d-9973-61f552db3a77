<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理 - 从服务器</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .device-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
            justify-content: flex-end;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            border: none;
            cursor: pointer;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-warning {
            background-color: var(--tertiary-color);
            color: var(--dark-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .device-list {
            margin-top: 1.5rem;
        }
        
        .device-item {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            box-shadow: var(--shadow);
            overflow: hidden;
        }
        
        .device-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background-color: var(--light-color);
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .device-name {
            font-weight: bold;
            color: var(--primary-color);
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .device-icon {
            font-size: 1.5rem;
        }
        
        .device-status {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-online {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .status-offline {
            background-color: var(--danger-color);
            color: white;
        }
        
        .device-body {
            padding: 1rem;
        }
        
        .device-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .info-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .info-item {
            display: flex;
            gap: 0.5rem;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .info-label {
            font-weight: bold;
            color: var(--dark-color);
            width: 100px;
        }
        
        .info-value {
            color: var(--gray-color);
        }
        
        .device-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--gray-color);
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--secondary-color);
        }
        
        input:focus + .slider {
            box-shadow: 0 0 1px var(--secondary-color);
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .toggle-label {
            font-size: 0.9rem;
        }
        
        .search-filter {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .search-container {
            flex: 1;
            display: flex;
            gap: 0.5rem;
        }
        
        .search-input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid var(--gray-color);
            border-radius: var(--border-radius);
        }
        
        .filter-container {
            display: flex;
            gap: 1rem;
        }
        
        .filter-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-control {
            padding: 0.5rem;
            border: 1px solid var(--gray-color);
            border-radius: var(--border-radius);
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: var(--border-radius);
            font-size: 0.8rem;
            margin-left: 0.5rem;
            background-color: var(--tertiary-color);
            color: var(--dark-color);
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--light-color);
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--gray-color);
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .modal-footer {
            margin-top: 1.5rem;
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }
    </style>
</head>
<body>
    <header>
        <h1>设备管理</h1>
        <nav>
            <ul>
                <li><a href="{{ url_for('websevs.lnd.index') }}">首页</a></li>
                <li><a href="{{ url_for('websevs.lnd.dashboard') }}">仪表盘</a></li>
                <li><a href="{{ url_for('websevs.lnd.devices') }}">设备管理</a></li>
                <li><a href="{{ url_for('websevs.lnd.settings') }}">系统设置</a></li>
                <li><a href="{{ url_for('websevs.lnd.diagnostics') }}">系统诊断</a></li>
                <li><a href="{{ url_for('websevs.lnd.virtualhere') }}">VirtualHere</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h2>连接设备管理</h2>
                <button class="btn btn-primary" id="scanDevicesBtn">扫描新设备</button>
            </div>
            
            <div class="search-filter">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索设备...">
                    <button class="btn btn-primary">搜索</button>
                </div>
                <div class="filter-container">
                    <div class="filter-item">
                        <label>状态:</label>
                        <select class="form-control">
                            <option value="all">全部</option>
                            <option value="online">在线</option>
                            <option value="offline">离线</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <label>类型:</label>
                        <select class="form-control">
                            <option value="all">全部</option>
                            <option value="usb">USB设备</option>
                            <option value="printer">打印机</option>
                            <option value="camera">摄像头</option>
                            <option value="audio">音频设备</option>
                            <option value="storage">存储设备</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="device-list">
                <!-- 设备项 -->
                <div class="device-item">
                    <div class="device-header">
                        <span class="device-name">
                            <span class="device-icon">🖨️</span>
                            HP LaserJet Pro MFP M428fdw
                        </span>
                        <span class="device-status status-online">在线</span>
                    </div>
                    <div class="device-body">
                        <div class="device-info">
                            <div class="info-group">
                                <div class="info-item">
                                    <span class="info-label">设备ID:</span>
                                    <span class="info-value">USB-PRT-001</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">类型:</span>
                                    <span class="info-value">打印机</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">厂商:</span>
                                    <span class="info-value">HP</span>
                                </div>
                            </div>
                            <div class="info-group">
                                <div class="info-item">
                                    <span class="info-label">端口:</span>
                                    <span class="info-value">USB001</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">序列号:</span>
                                    <span class="info-value">SN12345678</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">连接时间:</span>
                                    <span class="info-value">2023-07-15 09:30</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="device-toggle">
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <span class="toggle-label">共享设备 <span class="badge">已共享</span></span>
                        </div>
                        
                        <div class="device-actions">
                            <button class="btn btn-warning">编辑信息</button>
                            <button class="btn btn-primary">VirtualHere配置</button>
                            <button class="btn btn-danger">断开连接</button>
                        </div>
                    </div>
                </div>
                
                <div class="device-item">
                    <div class="device-header">
                        <span class="device-name">
                            <span class="device-icon">📷</span>
                            罗技 C920 HD Pro
                        </span>
                        <span class="device-status status-online">在线</span>
                    </div>
                    <div class="device-body">
                        <div class="device-info">
                            <div class="info-group">
                                <div class="info-item">
                                    <span class="info-label">设备ID:</span>
                                    <span class="info-value">USB-CAM-002</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">类型:</span>
                                    <span class="info-value">摄像头</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">厂商:</span>
                                    <span class="info-value">Logitech</span>
                                </div>
                            </div>
                            <div class="info-group">
                                <div class="info-item">
                                    <span class="info-label">端口:</span>
                                    <span class="info-value">USB002</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">序列号:</span>
                                    <span class="info-value">SN87654321</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">连接时间:</span>
                                    <span class="info-value">2023-07-15 10:15</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="device-toggle">
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <span class="toggle-label">共享设备 <span class="badge">已共享</span></span>
                        </div>
                        
                        <div class="device-actions">
                            <button class="btn btn-warning">编辑信息</button>
                            <button class="btn btn-primary">VirtualHere配置</button>
                            <button class="btn btn-danger">断开连接</button>
                        </div>
                    </div>
                </div>
                
                <div class="device-item">
                    <div class="device-header">
                        <span class="device-name">
                            <span class="device-icon">🔊</span>
                            Focusrite Scarlett 2i2
                        </span>
                        <span class="device-status status-offline">离线</span>
                    </div>
                    <div class="device-body">
                        <div class="device-info">
                            <div class="info-group">
                                <div class="info-item">
                                    <span class="info-label">设备ID:</span>
                                    <span class="info-value">USB-AUD-003</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">类型:</span>
                                    <span class="info-value">音频设备</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">厂商:</span>
                                    <span class="info-value">Focusrite</span>
                                </div>
                            </div>
                            <div class="info-group">
                                <div class="info-item">
                                    <span class="info-label">端口:</span>
                                    <span class="info-value">USB003</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">序列号:</span>
                                    <span class="info-value">SN45678123</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">最后在线:</span>
                                    <span class="info-value">2023-07-14 16:45</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="device-toggle">
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="slider"></span>
                            </label>
                            <span class="toggle-label">共享设备</span>
                        </div>
                        
                        <div class="device-actions">
                            <button class="btn btn-warning">编辑信息</button>
                            <button class="btn btn-primary">重新连接</button>
                            <button class="btn btn-danger">移除设备</button>
                        </div>
                    </div>
                </div>
                
                <div class="device-item">
                    <div class="device-header">
                        <span class="device-name">
                            <span class="device-icon">💽</span>
                            西部数据 4TB 移动硬盘
                        </span>
                        <span class="device-status status-online">在线</span>
                    </div>
                    <div class="device-body">
                        <div class="device-info">
                            <div class="info-group">
                                <div class="info-item">
                                    <span class="info-label">设备ID:</span>
                                    <span class="info-value">USB-STR-004</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">类型:</span>
                                    <span class="info-value">存储设备</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">厂商:</span>
                                    <span class="info-value">Western Digital</span>
                                </div>
                            </div>
                            <div class="info-group">
                                <div class="info-item">
                                    <span class="info-label">端口:</span>
                                    <span class="info-value">USB004</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">序列号:</span>
                                    <span class="info-value">SN98765432</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">连接时间:</span>
                                    <span class="info-value">2023-07-15 11:30</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="device-toggle">
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <span class="toggle-label">共享设备 <span class="badge">已共享</span></span>
                        </div>
                        
                        <div class="device-actions">
                            <button class="btn btn-warning">编辑信息</button>
                            <button class="btn btn-primary">VirtualHere配置</button>
                            <button class="btn btn-danger">断开连接</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 扫描设备模态框 -->
    <div class="modal" id="scanModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>扫描新设备</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div style="text-align: center; padding: 2rem 0;">
                <div class="spinner" style="border: 4px solid rgba(0, 0, 0, 0.1); width: 36px; height: 36px; border-radius: 50%; border-left-color: var(--primary-color); animation: spin 1s linear infinite; margin: 0 auto;"></div>
                <p style="margin-top: 1rem;">正在扫描连接的设备，请稍候...</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger modal-close-btn">取消</button>
            </div>
        </div>
    </div>

    <!-- 编辑设备模态框 -->
    <div class="modal" id="editDeviceModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑设备信息</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form id="deviceForm">
                <div class="form-group">
                    <label for="deviceName">设备名称</label>
                    <input type="text" id="deviceName" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="deviceType">设备类型</label>
                    <select id="deviceType" class="form-control" required>
                        <option value="printer">打印机</option>
                        <option value="scanner">扫描仪</option>
                        <option value="camera">摄像头</option>
                        <option value="audio">音频设备</option>
                        <option value="storage">存储设备</option>
                        <option value="input">输入设备</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="deviceVendor">设备厂商</label>
                    <input type="text" id="deviceVendor" class="form-control">
                </div>
                <div class="form-group">
                    <label for="deviceDesc">设备描述</label>
                    <textarea id="deviceDesc" class="form-control" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="deviceShare"> 共享此设备
                    </label>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger modal-close-btn">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <footer>
        <p>&copy; 2023 Web管理服务系统 - 版本 1.0.0</p>
    </footer>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 模态框相关元素
            const scanModal = document.getElementById('scanModal');
            const editDeviceModal = document.getElementById('editDeviceModal');
            const scanDevicesBtn = document.getElementById('scanDevicesBtn');
            const modalCloseBtns = document.querySelectorAll('.modal-close, .modal-close-btn');
            const deviceForm = document.getElementById('deviceForm');
            const editDeviceBtns = document.querySelectorAll('.btn-warning');
            
            // 共享设备切换
            const toggleSwitches = document.querySelectorAll('.toggle-switch input');
            
            // 扫描设备按钮点击
            scanDevicesBtn.addEventListener('click', function() {
                scanModal.style.display = 'flex';
                
                // 模拟扫描过程
                setTimeout(function() {
                    scanModal.style.display = 'none';
                    alert('设备扫描完成，发现3个新设备');
                }, 3000);
            });
            
            // 编辑设备按钮点击
            editDeviceBtns.forEach(function(btn) {
                btn.addEventListener('click', function() {
                    const deviceItem = this.closest('.device-item');
                    const deviceName = deviceItem.querySelector('.device-name').textContent.trim().replace(/^\S+\s+/, '');
                    const deviceType = deviceItem.querySelector('.info-item:nth-child(2) .info-value').textContent.trim();
                    const deviceVendor = deviceItem.querySelector('.info-item:nth-child(3) .info-value').textContent.trim();
                    const deviceShare = deviceItem.querySelector('.toggle-switch input').checked;
                    
                    // 填充表单
                    document.getElementById('deviceName').value = deviceName;
                    document.getElementById('deviceVendor').value = deviceVendor;
                    document.getElementById('deviceShare').checked = deviceShare;
                    
                    // 设置设备类型
                    let typeValue = 'other';
                    if (deviceType.includes('打印机')) typeValue = 'printer';
                    else if (deviceType.includes('扫描仪')) typeValue = 'scanner';
                    else if (deviceType.includes('摄像头')) typeValue = 'camera';
                    else if (deviceType.includes('音频')) typeValue = 'audio';
                    else if (deviceType.includes('存储')) typeValue = 'storage';
                    document.getElementById('deviceType').value = typeValue;
                    
                    // 打开模态框
                    editDeviceModal.style.display = 'flex';
                });
            });
            
            // 共享设备切换
            toggleSwitches.forEach(function(toggle) {
                toggle.addEventListener('change', function() {
                    const toggleLabel = this.closest('.device-toggle').querySelector('.toggle-label');
                    if (this.checked) {
                        if (!toggleLabel.querySelector('.badge')) {
                            toggleLabel.innerHTML = '共享设备 <span class="badge">已共享</span>';
                        }
                    } else {
                        toggleLabel.innerHTML = '共享设备';
                    }
                });
            });
            
            // 关闭模态框
            modalCloseBtns.forEach(function(btn) {
                btn.addEventListener('click', function() {
                    scanModal.style.display = 'none';
                    editDeviceModal.style.display = 'none';
                });
            });
            
            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === scanModal) {
                    scanModal.style.display = 'none';
                }
                if (event.target === editDeviceModal) {
                    editDeviceModal.style.display = 'none';
                }
            });
            
            // 设备表单提交
            deviceForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // 收集表单数据
                const deviceData = {
                    name: document.getElementById('deviceName').value,
                    type: document.getElementById('deviceType').value,
                    vendor: document.getElementById('deviceVendor').value,
                    description: document.getElementById('deviceDesc').value,
                    share: document.getElementById('deviceShare').checked
                };
                
                console.log('设备数据:', deviceData);
                // 这里应该发送AJAX请求保存数据
                
                alert('设备信息已保存');
                editDeviceModal.style.display = 'none';
            });
        });
    </script>
</body>
</html> 