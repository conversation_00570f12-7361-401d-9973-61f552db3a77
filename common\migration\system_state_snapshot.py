"""
系统状态快照

提供系统状态快照的创建、保存和恢复功能，支持系统状态迁移和重建场景。
"""

import os
import time
import json
import logging
import datetime
import hashlib
import pickle
import shutil
import threading
import traceback
import zipfile
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum

# 设置日志
logger = logging.getLogger('common.migration.snapshot')

class SnapshotType(Enum):
    """快照类型枚举"""
    FULL = "full"              # 完整系统状态快照
    INCREMENTAL = "incremental"  # 增量快照（基于上一个完整快照）
    DIFFERENTIAL = "differential"  # 差异快照（基于基准快照）
    CONFIGURATION = "configuration"  # 仅配置快照
    GROUPS = "groups"          # 组信息快照
    SERVICE = "service"        # 服务信息快照
    MIGRATION = "migration"    # 迁移快照（专用于系统迁移）
    BACKUP = "backup"          # 备份快照
    RECOVERY = "recovery"      # 恢复点快照

@dataclass
class SnapshotInfo:
    """快照信息数据类"""
    snapshot_id: str
    type: SnapshotType
    created_at: datetime.datetime
    description: str = ""
    size: int = 0
    checksum: str = ""
    creator: str = ""
    source_system: str = ""
    target_system: str = ""
    components: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "snapshot_id": self.snapshot_id,
            "type": self.type.value,
            "created_at": self.created_at.isoformat(),
            "description": self.description,
            "size": self.size,
            "checksum": self.checksum,
            "creator": self.creator,
            "source_system": self.source_system,
            "target_system": self.target_system,
            "components": self.components,
            "tags": self.tags,
            "metadata": self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SnapshotInfo':
        """从字典创建快照信息"""
        created_at = datetime.datetime.fromisoformat(data["created_at"])
        
        return cls(
            snapshot_id=data["snapshot_id"],
            type=SnapshotType(data["type"]),
            created_at=created_at,
            description=data.get("description", ""),
            size=data.get("size", 0),
            checksum=data.get("checksum", ""),
            creator=data.get("creator", ""),
            source_system=data.get("source_system", ""),
            target_system=data.get("target_system", ""),
            components=data.get("components", []),
            tags=data.get("tags", []),
            metadata=data.get("metadata", {})
        )

class SystemStateSnapshot:
    """系统状态快照类"""
    
    def __init__(self, base_dir: Optional[str] = None):
        """
        初始化系统状态快照
        
        参数:
            base_dir: 快照存储的基础目录，如果为None则使用默认目录
        """
        # 初始化存储目录
        if base_dir:
            self.base_dir = Path(base_dir)
        else:
            self.base_dir = Path.home() / '.ky' / 'snapshots'
            
        self.base_dir.mkdir(parents=True, exist_ok=True)
        self.snapshots_dir = self.base_dir / 'snapshots'
        self.snapshots_dir.mkdir(exist_ok=True)
        self.index_file = self.base_dir / 'snapshot_index.json'
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 快照索引
        self.snapshots: Dict[str, SnapshotInfo] = {}
        self._load_index()
        
        logger.info(f"系统状态快照初始化完成，存储目录: {self.base_dir}")
        
    def _load_index(self):
        """加载快照索引"""
        if not self.index_file.exists():
            return
            
        try:
            with open(self.index_file, 'r', encoding='utf-8') as f:
                index_data = json.load(f)
                
            self.snapshots = {
                snapshot_id: SnapshotInfo.from_dict(info)
                for snapshot_id, info in index_data.items()
            }
            
            logger.info(f"已加载 {len(self.snapshots)} 个快照记录")
            
        except Exception as e:
            logger.error(f"加载快照索引失败: {str(e)}")
            
    def _save_index(self):
        """保存快照索引"""
        try:
            # 准备索引数据
            index_data = {
                snapshot_id: info.to_dict()
                for snapshot_id, info in self.snapshots.items()
            }
            
            # 创建临时文件
            temp_file = self.index_file.with_suffix('.tmp')
            
            # 写入临时文件
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, indent=2)
                
            # 替换原文件
            if self.index_file.exists():
                backup_file = self.index_file.with_suffix('.bak')
                shutil.copy2(self.index_file, backup_file)
                
            temp_file.replace(self.index_file)
            
            logger.debug("快照索引已保存")
            
        except Exception as e:
            logger.error(f"保存快照索引失败: {str(e)}")
            
    def create_snapshot(self, 
                      system_state: Dict[str, Any],
                      snapshot_type: SnapshotType = SnapshotType.FULL,
                      description: str = "",
                      tags: Optional[List[str]] = None,
                      metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        创建系统状态快照
        
        参数:
            system_state: 系统状态数据
            snapshot_type: 快照类型
            description: 快照描述
            tags: 标签列表
            metadata: 元数据
            
        返回:
            快照ID，失败则返回None
        """
        with self._lock:
            try:
                # 生成快照ID和路径
                timestamp = datetime.datetime.now()
                snapshot_id = f"{timestamp.strftime('%Y%m%d%H%M%S')}_{snapshot_type.value}_{os.urandom(4).hex()}"
                snapshot_path = self.snapshots_dir / f"{snapshot_id}.zip"
                
                # 定义快照元数据
                if metadata is None:
                    metadata = {}
                    
                if tags is None:
                    tags = []
                    
                # 添加系统识别信息
                if "system_id" in system_state:
                    source_system = system_state["system_id"]
                else:
                    source_system = system_state.get("hostname", "unknown")
                    
                # 确定包含的组件
                components = list(system_state.keys())
                
                # 创建临时目录用于打包文件
                with zipfile.ZipFile(snapshot_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    # 保存状态数据
                    state_json = json.dumps(system_state, indent=2)
                    zipf.writestr('system_state.json', state_json)
                    
                    # 保存元数据
                    meta_json = json.dumps({
                        "type": snapshot_type.value,
                        "created_at": timestamp.isoformat(),
                        "description": description,
                        "components": components,
                        "tags": tags,
                        "metadata": metadata,
                        "source_system": source_system
                    }, indent=2)
                    zipf.writestr('metadata.json', meta_json)
                    
                # 计算文件大小和校验和
                size = snapshot_path.stat().st_size
                checksum = self._calculate_file_checksum(snapshot_path)
                
                # 创建快照信息
                snapshot_info = SnapshotInfo(
                    snapshot_id=snapshot_id,
                    type=snapshot_type,
                    created_at=timestamp,
                    description=description,
                    size=size,
                    checksum=checksum,
                    creator=metadata.get("creator", "system"),
                    source_system=source_system,
                    target_system=metadata.get("target_system", ""),
                    components=components,
                    tags=tags,
                    metadata=metadata
                )
                
                # 添加到索引
                self.snapshots[snapshot_id] = snapshot_info
                self._save_index()
                
                logger.info(f"已创建快照 {snapshot_id}，类型: {snapshot_type.value}，大小: {size/1024/1024:.2f} MB")
                
                return snapshot_id
                
            except Exception as e:
                logger.error(f"创建快照失败: {str(e)}")
                logger.debug(traceback.format_exc())
                return None
                
    def _calculate_file_checksum(self, file_path: Path) -> str:
        """
        计算文件的SHA-256校验和
        
        参数:
            file_path: 文件路径
            
        返回:
            校验和字符串
        """
        hash_obj = hashlib.sha256()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b''):
                hash_obj.update(chunk)
        return hash_obj.hexdigest()
        
    def get_snapshot_info(self, snapshot_id: str) -> Optional[SnapshotInfo]:
        """
        获取快照信息
        
        参数:
            snapshot_id: 快照ID
            
        返回:
            快照信息，不存在则返回None
        """
        with self._lock:
            return self.snapshots.get(snapshot_id)
            
    def list_snapshots(self, 
                     filter_type: Optional[SnapshotType] = None,
                     filter_tags: Optional[List[str]] = None,
                     limit: int = 0,
                     sort_by_time: bool = True) -> List[SnapshotInfo]:
        """
        列出快照
        
        参数:
            filter_type: 按类型过滤
            filter_tags: 按标签过滤
            limit: 结果数量限制，0表示不限制
            sort_by_time: 是否按时间排序
            
        返回:
            快照信息列表
        """
        with self._lock:
            # 过滤快照
            result = list(self.snapshots.values())
            
            if filter_type:
                result = [snapshot for snapshot in result if snapshot.type == filter_type]
                
            if filter_tags:
                result = [
                    snapshot for snapshot in result 
                    if all(tag in snapshot.tags for tag in filter_tags)
                ]
                
            # 排序
            if sort_by_time:
                result.sort(key=lambda x: x.created_at, reverse=True)
                
            # 限制数量
            if limit > 0:
                result = result[:limit]
                
            return result
            
    def delete_snapshot(self, snapshot_id: str) -> bool:
        """
        删除快照
        
        参数:
            snapshot_id: 快照ID
            
        返回:
            是否成功
        """
        with self._lock:
            if snapshot_id not in self.snapshots:
                logger.warning(f"快照 {snapshot_id} 不存在")
                return False
                
            try:
                # 获取快照文件路径
                snapshot_path = self.snapshots_dir / f"{snapshot_id}.zip"
                
                # 删除文件
                if snapshot_path.exists():
                    snapshot_path.unlink()
                    
                # 从索引中删除
                del self.snapshots[snapshot_id]
                self._save_index()
                
                logger.info(f"已删除快照 {snapshot_id}")
                return True
                
            except Exception as e:
                logger.error(f"删除快照 {snapshot_id} 失败: {str(e)}")
                return False
                
    def load_snapshot(self, snapshot_id: str) -> Optional[Dict[str, Any]]:
        """
        加载快照数据
        
        参数:
            snapshot_id: 快照ID
            
        返回:
            快照数据，失败则返回None
        """
        with self._lock:
            if snapshot_id not in self.snapshots:
                logger.warning(f"快照 {snapshot_id} 不存在")
                return None
                
            try:
                # 获取快照文件路径
                snapshot_path = self.snapshots_dir / f"{snapshot_id}.zip"
                
                if not snapshot_path.exists():
                    logger.error(f"快照文件 {snapshot_path} 不存在")
                    return None
                    
                # 加载快照数据
                with zipfile.ZipFile(snapshot_path, 'r') as zipf:
                    with zipf.open('system_state.json') as f:
                        system_state = json.load(f)
                        
                return system_state
                
            except Exception as e:
                logger.error(f"加载快照 {snapshot_id} 失败: {str(e)}")
                return None
                
    def restore_snapshot(self, snapshot_id: str,
                       target_system_id: Optional[str] = None,
                       components: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        准备恢复快照
        
        参数:
            snapshot_id: 快照ID
            target_system_id: 目标系统ID，None表示原系统
            components: 要恢复的组件列表，None表示全部
            
        返回:
            恢复准备结果，包含恢复数据和元数据
        """
        with self._lock:
            # 加载快照数据
            system_state = self.load_snapshot(snapshot_id)
            if not system_state:
                return {"success": False, "error": f"无法加载快照 {snapshot_id}"}
                
            # 获取快照信息
            snapshot_info = self.snapshots[snapshot_id]
            
            # 筛选组件
            if components:
                filtered_state = {}
                for component in components:
                    if component in system_state:
                        filtered_state[component] = system_state[component]
                system_state = filtered_state
                
            # 准备恢复元数据
            restore_meta = {
                "snapshot_id": snapshot_id,
                "snapshot_type": snapshot_info.type.value,
                "snapshot_created_at": snapshot_info.created_at.isoformat(),
                "restore_prepared_at": datetime.datetime.now().isoformat(),
                "source_system": snapshot_info.source_system,
                "target_system": target_system_id,
                "components": list(system_state.keys())
            }
            
            return {
                "success": True,
                "restore_meta": restore_meta,
                "system_state": system_state
            }
            
    def create_migration_snapshot(self, system_state: Dict[str, Any],
                                source_system_id: str,
                                target_system_id: str,
                                description: str = "系统迁移快照",
                                migration_plan: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        创建用于系统迁移的快照
        
        参数:
            system_state: 系统状态数据
            source_system_id: 源系统ID
            target_system_id: 目标系统ID
            description: 快照描述
            migration_plan: 迁移计划
            
        返回:
            快照ID，失败则返回None
        """
        metadata = {
            "creator": "migration_service",
            "source_system": source_system_id,
            "target_system": target_system_id,
            "migration_plan": migration_plan or {}
        }
        
        # 创建迁移快照
        return self.create_snapshot(
            system_state=system_state,
            snapshot_type=SnapshotType.MIGRATION,
            description=description,
            tags=["migration", f"source_{source_system_id}", f"target_{target_system_id}"],
            metadata=metadata
        )
        
    def create_scheduled_backup(self, system_state: Dict[str, Any],
                              backup_name: str = "",
                              retention_days: int = 30) -> Optional[str]:
        """
        创建计划备份快照
        
        参数:
            system_state: 系统状态数据
            backup_name: 备份名称
            retention_days: 保留天数
            
        返回:
            快照ID，失败则返回None
        """
        timestamp = datetime.datetime.now()
        expiration_date = timestamp + datetime.timedelta(days=retention_days)
        
        if not backup_name:
            backup_name = f"scheduled_backup_{timestamp.strftime('%Y%m%d')}"
            
        metadata = {
            "creator": "backup_service",
            "backup_name": backup_name,
            "retention_days": retention_days,
            "expiration_date": expiration_date.isoformat()
        }
        
        # 创建备份快照
        return self.create_snapshot(
            system_state=system_state,
            snapshot_type=SnapshotType.BACKUP,
            description=f"计划备份: {backup_name}",
            tags=["backup", "scheduled", f"expires_{expiration_date.strftime('%Y%m%d')}"],
            metadata=metadata
        )
        
    def clean_expired_backups(self) -> int:
        """
        清理过期的备份快照
        
        返回:
            清理的快照数量
        """
        with self._lock:
            now = datetime.datetime.now()
            to_delete = []
            
            # 查找过期的备份
            for snapshot_id, info in self.snapshots.items():
                if info.type != SnapshotType.BACKUP:
                    continue
                    
                expiration_str = info.metadata.get("expiration_date")
                if not expiration_str:
                    continue
                    
                try:
                    expiration_date = datetime.datetime.fromisoformat(expiration_str)
                    if now > expiration_date:
                        to_delete.append(snapshot_id)
                except (ValueError, TypeError):
                    continue
                    
            # 删除过期快照
            count = 0
            for snapshot_id in to_delete:
                if self.delete_snapshot(snapshot_id):
                    count += 1
                    
            if count > 0:
                logger.info(f"已清理 {count} 个过期备份快照")
                
            return count
            
    def export_snapshot(self, snapshot_id: str, 
                      export_path: str,
                      include_metadata: bool = True) -> bool:
        """
        导出快照到外部文件
        
        参数:
            snapshot_id: 快照ID
            export_path: 导出路径
            include_metadata: 是否包含元数据
            
        返回:
            是否成功
        """
        with self._lock:
            if snapshot_id not in self.snapshots:
                logger.warning(f"快照 {snapshot_id} 不存在")
                return False
                
            try:
                # 获取快照文件路径
                snapshot_path = self.snapshots_dir / f"{snapshot_id}.zip"
                
                if not snapshot_path.exists():
                    logger.error(f"快照文件 {snapshot_path} 不存在")
                    return False
                    
                # 复制文件
                export_file = Path(export_path)
                shutil.copy2(snapshot_path, export_file)
                
                # 导出元数据
                if include_metadata:
                    snapshot_info = self.snapshots[snapshot_id]
                    meta_file = export_file.with_suffix('.meta.json')
                    
                    with open(meta_file, 'w', encoding='utf-8') as f:
                        json.dump(snapshot_info.to_dict(), f, indent=2)
                        
                logger.info(f"快照 {snapshot_id} 已导出到 {export_path}")
                return True
                
            except Exception as e:
                logger.error(f"导出快照 {snapshot_id} 失败: {str(e)}")
                return False
                
    def import_snapshot(self, import_path: str, 
                      new_snapshot_id: Optional[str] = None) -> Optional[str]:
        """
        从外部文件导入快照
        
        参数:
            import_path: 导入文件路径
            new_snapshot_id: 新的快照ID，None表示自动生成
            
        返回:
            新的快照ID，失败则返回None
        """
        with self._lock:
            try:
                import_file = Path(import_path)
                
                if not import_file.exists():
                    logger.error(f"导入文件 {import_path} 不存在")
                    return None
                    
                # 检查元数据文件
                meta_file = import_file.with_suffix('.meta.json')
                if meta_file.exists():
                    with open(meta_file, 'r', encoding='utf-8') as f:
                        meta_data = json.load(f)
                else:
                    # 尝试从压缩包中读取元数据
                    try:
                        with zipfile.ZipFile(import_file, 'r') as zipf:
                            if 'metadata.json' in zipf.namelist():
                                with zipf.open('metadata.json') as f:
                                    meta_data = json.load(f)
                            else:
                                meta_data = {
                                    "type": "unknown",
                                    "created_at": datetime.datetime.now().isoformat(),
                                    "description": "导入的快照",
                                    "source_system": "unknown",
                                    "components": [],
                                    "tags": [],
                                    "metadata": {}
                                }
                    except:
                        meta_data = {
                            "type": "unknown",
                            "created_at": datetime.datetime.now().isoformat(),
                            "description": "导入的快照",
                            "source_system": "unknown",
                            "components": [],
                            "tags": [],
                            "metadata": {}
                        }
                        
                # 确保组件是列表类型
                if not isinstance(meta_data.get("components", []), list):
                    meta_data["components"] = []
                    
                # 确保标签是列表类型
                if not isinstance(meta_data.get("tags", []), list):
                    meta_data["tags"] = []
                    
                # 确保元数据是字典类型
                if not isinstance(meta_data.get("metadata", {}), dict):
                    meta_data["metadata"] = {}
                        
                # 生成新的快照ID
                if not new_snapshot_id:
                    timestamp = datetime.datetime.now()
                    snapshot_type = meta_data.get("type", "unknown")
                    new_snapshot_id = f"{timestamp.strftime('%Y%m%d%H%M%S')}_import_{snapshot_type}_{os.urandom(4).hex()}"
                    
                # 复制文件到快照目录
                snapshot_path = self.snapshots_dir / f"{new_snapshot_id}.zip"
                shutil.copy2(import_file, snapshot_path)
                
                # 计算文件大小和校验和
                size = snapshot_path.stat().st_size
                checksum = self._calculate_file_checksum(snapshot_path)
                
                # 创建快照信息
                created_at = datetime.datetime.fromisoformat(meta_data.get("created_at", datetime.datetime.now().isoformat()))
                
                # 合并导入标签和"imported"标签
                import_tags = meta_data.get("tags", [])
                import_tags.append("imported")
                
                snapshot_info = SnapshotInfo(
                    snapshot_id=new_snapshot_id,
                    type=SnapshotType(meta_data.get("type", "unknown")),
                    created_at=created_at,
                    description=meta_data.get("description", "导入的快照"),
                    size=size,
                    checksum=checksum,
                    creator=meta_data.get("creator", "import"),
                    source_system=meta_data.get("source_system", "unknown"),
                    target_system=meta_data.get("target_system", ""),
                    components=meta_data.get("components", []),
                    tags=import_tags,
                    metadata=meta_data.get("metadata", {})
                )
                
                # 添加到索引
                self.snapshots[new_snapshot_id] = snapshot_info
                self._save_index()
                
                logger.info(f"已导入快照为 {new_snapshot_id}，大小: {size/1024/1024:.2f} MB")
                
                return new_snapshot_id
                
            except Exception as e:
                logger.error(f"导入快照失败: {str(e)}")
                logger.debug(traceback.format_exc())
                return None
                
    def get_snapshot_statistics(self) -> Dict[str, Any]:
        """
        获取快照统计信息
        
        返回:
            统计信息
        """
        with self._lock:
            # 按类型统计
            type_stats = {}
            for snapshot_type in SnapshotType:
                type_count = sum(1 for info in self.snapshots.values() if info.type == snapshot_type)
                type_stats[snapshot_type.value] = type_count
                
            # 总大小
            total_size = sum(info.size for info in self.snapshots.values())
            
            # 按时间段统计
            now = datetime.datetime.now()
            time_stats = {
                "last_24h": sum(1 for info in self.snapshots.values() if (now - info.created_at).total_seconds() < 86400),
                "last_7d": sum(1 for info in self.snapshots.values() if (now - info.created_at).total_seconds() < 86400 * 7),
                "last_30d": sum(1 for info in self.snapshots.values() if (now - info.created_at).total_seconds() < 86400 * 30)
            }
            
            return {
                "total_count": len(self.snapshots),
                "total_size_bytes": total_size,
                "total_size_mb": total_size / 1024 / 1024,
                "by_type": type_stats,
                "by_time": time_stats,
                "storage_path": str(self.base_dir)
            } 
