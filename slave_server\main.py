#!/usr/bin/env python3
"""
OmniLink从服务器主程序
负责USB设备管理、VirtualHere集成和与主服务器通信
"""

import asyncio
import logging
import signal
import platform
import json
import sys
import psutil
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import uvicorn
import websockets

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from slave_server.core.config import settings
from slave_server.services.vh_service import VirtualHereService
from slave_server.services.device_monitor import DeviceMonitorService
from slave_server.services.reporter_service import ReporterService
from slave_server.services.command_handler import CommandHandlerService
from slave_server.schemas import SlaveServerRegister, DeviceInfo, HeartbeatData

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="OmniLink Slave Server",
    description="USB设备远程共享从服务器",
    version="1.0.0"
)

# HTTP Bearer认证
security = HTTPBearer()

class SlaveServerAgent:
    def __init__(self):
        self.logger = logging.getLogger(settings.PROJECT_NAME)
        self.logger.info("Initializing Slave Server Agent...")
        
        # 核心服务
        self.vh_service = VirtualHereService()
        self.device_monitor = DeviceMonitorService()
        self.reporter_service = ReporterService(
            device_monitor=self.device_monitor,
            vh_service=self.vh_service
        )
        self.command_handler = CommandHandlerService(vh_service=self.vh_service)
        
        # 状态管理
        self._shutdown_event = asyncio.Event()
        self._main_server_ws: Optional[websockets.WebSocketServerProtocol] = None
        self._heartbeat_task: Optional[asyncio.Task] = None
        self._registered = False
        
        # 设备状态缓存
        self._device_cache: Dict[str, Dict[str, Any]] = {}
        
        # 设置回调
        self.device_monitor.on_device_added = self.on_device_added_callback
        self.device_monitor.on_device_removed = self.on_device_removed_callback

    async def verify_api_key(self, credentials: HTTPAuthorizationCredentials = Depends(security)) -> bool:
        """验证API密钥"""
        if credentials.credentials != settings.MAIN_SERVER_API_KEY:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid API key"
            )
        return True

    def _get_system_load(self) -> float:
        """获取系统负载"""
        try:
            if platform.system() == "Linux":
                # Linux系统使用load average
                load_avg = psutil.getloadavg()
                return load_avg[0]  # 1分钟平均负载
            else:
                # Windows或其他系统使用CPU使用率
                return psutil.cpu_percent(interval=1)
        except Exception as e:
            self.logger.warning(f"Failed to get system load: {e}")
            return 0.0
    
    def _get_memory_usage(self) -> float:
        """获取内存使用率"""
        try:
            memory = psutil.virtual_memory()
            return memory.percent
        except Exception as e:
            self.logger.warning(f"Failed to get memory usage: {e}")
            return 0.0

    def on_device_added_callback(self, device_info: dict):
        """设备添加回调"""
        device_path = device_info.get('device_path')
        friendly_name = device_info.get('model', 'Unknown Device')
        
        self.logger.info(f"Device added: {friendly_name} (Path: {device_path})")
        
        # 更新设备缓存
        self._device_cache[device_path] = {
            **device_info,
            'added_at': datetime.utcnow().isoformat(),
            'status': 'available'
        }
        
        # 通知主服务器
        asyncio.create_task(self._notify_main_server_device_change('added', device_info))

    def on_device_removed_callback(self, device_info: dict):
        """设备移除回调"""
        device_path = device_info.get('device_path')
        friendly_name = device_info.get('model', 'Unknown Device')
        
        self.logger.info(f"Device removed: {friendly_name} (Path: {device_path})")
        
        # 从缓存中移除
        self._device_cache.pop(device_path, None)
        
        # 通知主服务器
        asyncio.create_task(self._notify_main_server_device_change('removed', device_info))

    async def _notify_main_server_device_change(self, action: str, device_info: dict):
        """通知主服务器设备状态变化"""
        if not self._main_server_ws:
            return
            
        try:
            message = {
                'type': 'device_change',
                'action': action,
                'device': device_info,
                'slave_id': settings.SLAVE_ID,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            await self._main_server_ws.send(json.dumps(message))
            self.logger.debug(f"Notified main server of device {action}: {device_info.get('model')}")
            
        except Exception as e:
            self.logger.error(f"Failed to notify main server of device change: {e}")

    async def register_with_main_server(self) -> bool:
        """向主服务器注册"""
        try:
            import aiohttp
            
            registration_data = SlaveServerRegister(
                server_id=settings.SLAVE_ID,
                name=f"Slave-{settings.SLAVE_ID}",
                description=f"从服务器运行在 {platform.node()}",
                ip_address="auto-detect",  # 主服务器会检测实际IP
                port=settings.SLAVE_SERVER_PORT,
                status="online",
                version="1.0.0"
            )
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{settings.MAIN_SERVER_URL}/api/v1/slaves/register",
                    json=registration_data.model_dump(),
                    headers={"Authorization": f"Bearer {settings.MAIN_SERVER_API_KEY}"}
                ) as response:
                    if response.status == 200:
                        self.logger.info("Successfully registered with main server")
                        self._registered = True
                        return True
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Failed to register with main server: {response.status} - {error_text}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"Exception during registration: {e}")
            return False

    async def connect_to_main_server_ws(self):
        """连接到主服务器的WebSocket"""
        try:
            ws_url = settings.MAIN_SERVER_URL.replace('http', 'ws') + f"/api/v1/slaves/{settings.SLAVE_ID}/ws"
            
            self._main_server_ws = await websockets.connect(
                ws_url,
                extra_headers={"Authorization": f"Bearer {settings.MAIN_SERVER_API_KEY}"}
            )
            
            self.logger.info("Connected to main server WebSocket")
            
            # 启动心跳任务
            self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            
            # 启动消息处理循环
            asyncio.create_task(self._handle_main_server_messages())
            
        except Exception as e:
            self.logger.error(f"Failed to connect to main server WebSocket: {e}")

    async def _heartbeat_loop(self):
        """心跳循环"""
        while not self._shutdown_event.is_set():
            try:
                if self._main_server_ws:
                    heartbeat_data = HeartbeatData(
                        slave_id=settings.SLAVE_ID,
                        timestamp=datetime.utcnow().isoformat(),
                        status="online",
                        device_count=len(self._device_cache),
                        load_average=self._get_system_load(),
                        memory_usage=self._get_memory_usage()
                    )
                    
                    message = {
                        'type': 'heartbeat',
                        'data': heartbeat_data.model_dump()
                    }
                    
                    await self._main_server_ws.send(json.dumps(message))
                    self.logger.debug("Sent heartbeat to main server")
                    
            except Exception as e:
                self.logger.error(f"Heartbeat failed: {e}")
                
            await asyncio.sleep(30)  # 每30秒发送一次心跳

    async def _handle_main_server_messages(self):
        """处理来自主服务器的消息"""
        try:
            async for message in self._main_server_ws:
                try:
                    data = json.loads(message)
                    await self._process_main_server_command(data)
                except json.JSONDecodeError:
                    self.logger.error(f"Invalid JSON received from main server: {message}")
                except Exception as e:
                    self.logger.error(f"Error processing main server message: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("WebSocket connection to main server closed")
        except Exception as e:
            self.logger.error(f"Error in main server message handler: {e}")

    async def _process_main_server_command(self, command: dict):
        """处理主服务器命令"""
        action = command.get('action')
        
        if action == 'share_device':
            device_path = command.get('device_path')
            if device_path:
                success = await self.vh_service.share_device(device_path)
                await self._send_command_response(command.get('command_id'), success)
                
        elif action == 'unshare_device':
            device_path = command.get('device_path')
            if device_path:
                success = await self.vh_service.unshare_device(device_path)
                await self._send_command_response(command.get('command_id'), success)
                
        elif action == 'list_devices':
            devices = list(self._device_cache.values())
            await self._send_command_response(command.get('command_id'), True, {'devices': devices})
            
        else:
            self.logger.warning(f"Unknown command action: {action}")

    async def _send_command_response(self, command_id: str, success: bool, data: dict = None):
        """发送命令响应"""
        if not self._main_server_ws or not command_id:
            return
            
        try:
            response = {
                'type': 'command_response',
                'command_id': command_id,
                'success': success,
                'data': data or {},
                'timestamp': datetime.utcnow().isoformat()
            }
            
            await self._main_server_ws.send(json.dumps(response))
            
        except Exception as e:
            self.logger.error(f"Failed to send command response: {e}")

    async def start_services(self):
        """启动所有服务"""
        self.logger.info("Starting slave server services...")
        
        # 启动VirtualHere服务
        await self.vh_service.start()
        
        # 启动设备监控
        self.device_monitor.start()
        
        # 启动报告服务
        await self.reporter_service.start()
        
        # 启动命令处理器
        await self.command_handler.start()
        
        # 向主服务器注册
        if await self.register_with_main_server():
            # 连接WebSocket
            await self.connect_to_main_server_ws()
        
        self.logger.info("All slave server services started successfully")

    async def stop_services(self):
        """停止所有服务"""
        self.logger.info("Stopping slave server services...")
        
        # 停止心跳
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
            
        # 关闭WebSocket连接
        if self._main_server_ws:
            await self._main_server_ws.close()
            
        # 停止其他服务
        self.device_monitor.stop()
        await self.vh_service.stop()
        await self.reporter_service.stop()
        await self.command_handler.stop()
        
        self.logger.info("All slave server services stopped")

    async def run(self):
        """运行从服务器"""
        await self.start_services()
        self.logger.info("Slave Server Agent is running. Press Ctrl+C to stop.")
        
        try:
            await self._shutdown_event.wait()
        finally:
            self.logger.info("Shutdown signal received")
            await self.stop_services()

# 全局代理实例
agent = SlaveServerAgent()

# FastAPI路由
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "slave_id": settings.SLAVE_ID,
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "virtualhere": agent.vh_service._is_connected,
            "device_monitor": agent.device_monitor.initialized,
            "registered": agent._registered
        }
    }

@app.get("/devices")
async def get_devices(authenticated: bool = Depends(agent.verify_api_key)):
    """获取设备列表"""
    return {
        "devices": list(agent._device_cache.values()),
        "count": len(agent._device_cache)
    }

@app.post("/devices/{device_path}/share")
async def share_device(device_path: str, authenticated: bool = Depends(agent.verify_api_key)):
    """共享设备"""
    if device_path not in agent._device_cache:
        raise HTTPException(status_code=404, detail="Device not found")
        
    success = await agent.vh_service.share_device(device_path)
    if success:
        agent._device_cache[device_path]['status'] = 'shared'
        return {"message": "Device shared successfully"}
    else:
        raise HTTPException(status_code=500, detail="Failed to share device")

@app.post("/devices/{device_path}/unshare")
async def unshare_device(device_path: str, authenticated: bool = Depends(agent.verify_api_key)):
    """取消共享设备"""
    if device_path not in agent._device_cache:
        raise HTTPException(status_code=404, detail="Device not found")
        
    success = await agent.vh_service.unshare_device(device_path)
    if success:
        agent._device_cache[device_path]['status'] = 'available'
        return {"message": "Device unshared successfully"}
    else:
        raise HTTPException(status_code=500, detail="Failed to unshare device")

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    asyncio.create_task(agent.run())

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    agent._shutdown_event.set()

def handle_shutdown_signal(agent_instance: SlaveServerAgent):
    """处理关闭信号"""
    logging.getLogger(settings.PROJECT_NAME).info("Graceful shutdown requested")
    agent_instance._shutdown_event.set()

async def main():
    """主函数"""
    try:
        # 启动FastAPI服务器
        config = uvicorn.Config(
            app,
            host="0.0.0.0",
            port=getattr(settings, 'SLAVE_SERVER_PORT', 8001),
            log_level=settings.LOG_LEVEL.lower()
        )
        server = uvicorn.Server(config)
        await server.serve()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Unexpected error in main: {e}")
    finally:
        if not agent._shutdown_event.is_set():
            agent._shutdown_event.set()

if __name__ == "__main__":
    asyncio.run(main())
