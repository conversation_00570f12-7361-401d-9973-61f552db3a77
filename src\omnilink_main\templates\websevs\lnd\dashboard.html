<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>从服务器仪表盘 - Web管理服务</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .dashboard-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .dashboard-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1.5rem;
        }
        
        .dashboard-card h3 {
            margin-bottom: 1rem;
            color: var(--primary-color);
            border-bottom: 1px solid var(--gray-color);
            padding-bottom: 0.5rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-online {
            background-color: var(--secondary-color);
        }
        
        .status-offline {
            background-color: var(--danger-color);
        }
        
        .status-warning {
            background-color: var(--tertiary-color);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        
        .stat-box {
            text-align: center;
            padding: 1rem;
            background-color: var(--light-color);
            border-radius: var(--border-radius);
        }
        
        .stat-box .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-box .stat-label {
            font-size: 0.9rem;
            color: var(--gray-color);
        }
        
        .progress-container {
            width: 100%;
            background-color: var(--light-color);
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 20px;
            border-radius: 10px;
            transition: width 0.3s ease-in-out;
        }
        
        .progress-cpu {
            background-color: var(--primary-color);
            width: 65%;
        }
        
        .progress-memory {
            background-color: var(--tertiary-color);
            width: 45%;
        }
        
        .progress-disk {
            background-color: var(--secondary-color);
            width: 30%;
        }
        
        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 1rem;
        }
        
        .device-card {
            background-color: var(--light-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            text-align: center;
            border-top: 3px solid var(--primary-color);
            transition: transform 0.2s;
        }
        
        .device-card:hover {
            transform: translateY(-5px);
        }
        
        .device-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .chart-container {
            height: 250px;
            position: relative;
        }
    </style>
</head>
<body>
    <header>
        <h1>从服务器仪表盘</h1>
        <nav>
            <ul>
                <li><a href="{{ url_for('websevs.lnd.index') }}">首页</a></li>
                <li><a href="{{ url_for('websevs.lnd.dashboard') }}">仪表盘</a></li>
                <li><a href="{{ url_for('websevs.lnd.devices') }}">设备管理</a></li>
                <li><a href="{{ url_for('websevs.lnd.settings') }}">系统设置</a></li>
                <li><a href="{{ url_for('websevs.lnd.diagnostics') }}">系统诊断</a></li>
                <li><a href="{{ url_for('websevs.lnd.virtualhere') }}">VirtualHere</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section>
            <h2>系统状态</h2>
            <div class="dashboard-container">
                <div class="dashboard-card">
                    <h3>服务器状态</h3>
                    <div style="margin-bottom: 1rem;">
                        <span class="status-indicator status-online"></span>
                        <strong>当前状态: </strong> 在线运行中
                    </div>
                    <div>
                        <strong>主机名: </strong> lnd-server-1
                    </div>
                    <div>
                        <strong>IP地址: </strong> *************
                    </div>
                    <div>
                        <strong>运行时间: </strong> 3.7 天
                    </div>
                    <div>
                        <strong>上次连接: </strong> 2023-07-15 14:32:18
                    </div>
                </div>

                <div class="dashboard-card">
                    <h3>资源使用</h3>
                    <div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>CPU 使用率</span>
                            <span>65%</span>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar progress-cpu"></div>
                        </div>
                    </div>
                    <div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>内存使用</span>
                            <span>45%</span>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar progress-memory"></div>
                        </div>
                    </div>
                    <div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>磁盘使用</span>
                            <span>30%</span>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar progress-disk"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section>
            <h2>设备概览</h2>
            <div class="dashboard-card">
                <h3>设备状态</h3>
                <div class="stats-grid">
                    <div class="stat-box">
                        <div class="stat-value">18</div>
                        <div class="stat-label">已连接设备</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-value">14</div>
                        <div class="stat-label">在线设备</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-value">12</div>
                        <div class="stat-label">已共享设备</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-value">2</div>
                        <div class="stat-label">故障设备</div>
                    </div>
                </div>
                
                <h3 style="margin-top: 1.5rem;">设备类型</h3>
                <div class="chart-container">
                    <canvas id="deviceTypeChart"></canvas>
                </div>
            </div>
        </section>

        <section>
            <h2>已连接设备</h2>
            <div class="dashboard-card">
                <div class="device-grid">
                    <div class="device-card">
                        <div class="device-icon">🖨️</div>
                        <div><strong>HP打印机</strong></div>
                        <div>类型: 打印设备</div>
                        <div style="color: var(--secondary-color);">状态: 在线</div>
                    </div>
                    <div class="device-card">
                        <div class="device-icon">📷</div>
                        <div><strong>罗技摄像头</strong></div>
                        <div>类型: 图像设备</div>
                        <div style="color: var(--secondary-color);">状态: 在线</div>
                    </div>
                    <div class="device-card">
                        <div class="device-icon">🎮</div>
                        <div><strong>游戏手柄</strong></div>
                        <div>类型: 输入设备</div>
                        <div style="color: var(--secondary-color);">状态: 在线</div>
                    </div>
                    <div class="device-card">
                        <div class="device-icon">🔊</div>
                        <div><strong>音频接口</strong></div>
                        <div>类型: 音频设备</div>
                        <div style="color: var(--danger-color);">状态: 离线</div>
                    </div>
                    <div class="device-card">
                        <div class="device-icon">💽</div>
                        <div><strong>外接硬盘</strong></div>
                        <div>类型: 存储设备</div>
                        <div style="color: var(--secondary-color);">状态: 在线</div>
                    </div>
                    <div class="device-card">
                        <div class="device-icon">📱</div>
                        <div><strong>手机</strong></div>
                        <div>类型: 通信设备</div>
                        <div style="color: var(--secondary-color);">状态: 在线</div>
                    </div>
                </div>
            </div>
        </section>

        <section>
            <h2>系统日志</h2>
            <div class="dashboard-card">
                <table>
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>类型</th>
                            <th>信息</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2023-07-15 14:23</td>
                            <td>设备</td>
                            <td>新设备 "HP打印机-P4580" 已连接</td>
                        </tr>
                        <tr>
                            <td>2023-07-15 13:45</td>
                            <td>系统</td>
                            <td>VirtualHere服务已重启</td>
                        </tr>
                        <tr>
                            <td>2023-07-15 12:32</td>
                            <td>网络</td>
                            <td>与主服务器的连接已重新建立</td>
                        </tr>
                        <tr>
                            <td>2023-07-15 12:30</td>
                            <td>错误</td>
                            <td>与主服务器的连接已断开</td>
                        </tr>
                        <tr>
                            <td>2023-07-15 10:05</td>
                            <td>设备</td>
                            <td>设备 "音频接口-A240" 已断开连接</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
    </main>

    <footer>
        <p>&copy; 2023 Web管理服务系统 - 版本 1.0.0</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 设备类型分布图表
            const ctx = document.getElementById('deviceTypeChart').getContext('2d');
            const deviceTypeChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['打印设备', '图像设备', '输入设备', '音频设备', '存储设备', '通信设备'],
                    datasets: [{
                        label: '设备数量',
                        data: [3, 4, 2, 3, 5, 1],
                        backgroundColor: [
                            '#4285f4',
                            '#34a853',
                            '#fbbc05',
                            '#ea4335',
                            '#9e9e9e',
                            '#db4437'
                        ],
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
            
            // 添加自动刷新功能
            setInterval(function() {
                // 这里可以添加AJAX调用来获取实时数据
                console.log("刷新数据...");
            }, 30000); // 每30秒刷新一次
        });
    </script>
</body>
</html> 