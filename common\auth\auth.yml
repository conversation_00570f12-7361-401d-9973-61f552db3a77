# 认证服务默认配置

server:
  host: "0.0.0.0"
  port: 65520
  workers: 4
  backlog: 100
  keepalive: 60
  timeout: 30

tls:
  enabled: true
  cert_file: "server.crt"
  key_file: "server.key"
  allow_http: true
  min_version: "TLS1.2"
  ciphers: "ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20"

jwt:
  algorithm: "HS256"
  expire: 3600
  refresh_expire: 86400

session:
  timeout: 3600
  max_sessions: 10
  cleanup_interval: 300

security:
  max_retries: 3
  retry_interval: 5
  lockout_time: 300
  min_password_length: 8
  password_complexity:
    uppercase: 1     # 至少1个大写字母
    lowercase: 1     # 至少1个小写字母
    numbers: 1       # 至少1个数字
    special: 1       # 至少1个特殊字符 