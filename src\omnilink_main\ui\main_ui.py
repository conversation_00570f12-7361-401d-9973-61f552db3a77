from fastapi import FastAP<PERSON>
from nicegui import ui
from .pages import login_page, home_page
from .layout import main_layout
from . import auth_client
from ..core.config import settings

def init(fastapi_app: FastAPI) -> None:
    """
    Initializes and mounts the NiceGUI UI on the FastAPI app.
    """

    @ui.page('/')
    def show_login_page():
        login_page.create()

    @ui.page('/home')
    @auth_client.protected_page
    async def show_home_page():
        # This page is now protected by the decorator
        with main_layout():
            home_page.create()
            
    # This mounts the GUI to the existing FastAPI app.
    ui.run_with(
        fastapi_app,
        storage_secret=settings.SECRET_KEY
    ) 
