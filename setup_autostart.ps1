# Playwright MCP 自动启动设置脚本
# 提供多种自动启动方案

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("task", "startup", "service", "remove")]
    [string]$Method = "task"
)

$ScriptPath = $PSScriptRoot
$PlaywrightScript = Join-Path $ScriptPath "start_playwright_mcp.ps1"
$TaskName = "PlaywrightMCP_AutoStart"

function Set-TaskScheduler {
    Write-Host "设置Windows任务计划程序自动启动..." -ForegroundColor Green
    
    try {
        # 检查是否已存在任务
        $existingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
        if ($existingTask) {
            Write-Host "删除现有任务..." -ForegroundColor Yellow
            Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
        }
        
        # 创建任务动作
        $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-ExecutionPolicy Bypass -WindowStyle Hidden -File `"$PlaywrightScript`""
        
        # 创建触发器 (系统启动时)
        $trigger = New-ScheduledTaskTrigger -AtStartup
        
        # 创建任务设置
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
        
        # 创建任务主体 (以当前用户身份运行)
        $principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive
        
        # 注册任务
        Register-ScheduledTask -TaskName $TaskName -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Description "自动启动Playwright MCP服务器"
        
        Write-Host "✅ 任务计划程序设置成功!" -ForegroundColor Green
        Write-Host "任务名称: $TaskName" -ForegroundColor Cyan
        Write-Host "触发条件: 系统启动时" -ForegroundColor Cyan
        
        # 测试任务
        Write-Host "正在测试任务..." -ForegroundColor Yellow
        Start-ScheduledTask -TaskName $TaskName
        Start-Sleep -Seconds 3
        
        $taskInfo = Get-ScheduledTask -TaskName $TaskName
        Write-Host "任务状态: $($taskInfo.State)" -ForegroundColor Cyan
        
    } catch {
        Write-Host "❌ 设置任务计划程序失败: $_" -ForegroundColor Red
    }
}

function Set-StartupFolder {
    Write-Host "设置启动文件夹自动启动..." -ForegroundColor Green
    
    try {
        $startupFolder = [Environment]::GetFolderPath("Startup")
        $shortcutPath = Join-Path $startupFolder "PlaywrightMCP.lnk"
        
        # 创建快捷方式
        $WshShell = New-Object -comObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut($shortcutPath)
        $Shortcut.TargetPath = "powershell.exe"
        $Shortcut.Arguments = "-ExecutionPolicy Bypass -WindowStyle Hidden -File `"$PlaywrightScript`""
        $Shortcut.WorkingDirectory = $ScriptPath
        $Shortcut.Description = "Playwright MCP 自动启动"
        $Shortcut.Save()
        
        Write-Host "✅ 启动文件夹设置成功!" -ForegroundColor Green
        Write-Host "快捷方式位置: $shortcutPath" -ForegroundColor Cyan
        
    } catch {
        Write-Host "❌ 设置启动文件夹失败: $_" -ForegroundColor Red
    }
}

function Set-WindowsService {
    Write-Host "设置Windows服务自动启动..." -ForegroundColor Green
    Write-Host "注意: 此方法需要管理员权限" -ForegroundColor Yellow
    
    # 检查管理员权限
    if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
        Write-Host "❌ 需要管理员权限才能创建Windows服务" -ForegroundColor Red
        Write-Host "请以管理员身份运行PowerShell，然后重新执行此脚本" -ForegroundColor Yellow
        return
    }
    
    try {
        $serviceName = "PlaywrightMCP"
        $serviceDisplayName = "Playwright MCP Server"
        $serviceDescription = "Playwright Model Context Protocol Server"
        
        # 检查服务是否已存在
        $existingService = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($existingService) {
            Write-Host "删除现有服务..." -ForegroundColor Yellow
            Stop-Service -Name $serviceName -Force -ErrorAction SilentlyContinue
            sc.exe delete $serviceName
            Start-Sleep -Seconds 2
        }
        
        # 创建服务包装脚本
        $serviceWrapperPath = Join-Path $ScriptPath "playwright_mcp_service.ps1"
        $serviceWrapperContent = @"
# Playwright MCP Windows服务包装脚本
while (`$true) {
    try {
        Write-EventLog -LogName Application -Source "PlaywrightMCP" -EventId 1001 -Message "启动Playwright MCP服务器"
        & npx @playwright/mcp@0.0.26 --port 8931 --headless
    } catch {
        Write-EventLog -LogName Application -Source "PlaywrightMCP" -EventId 1002 -Message "Playwright MCP服务器错误: `$_"
        Start-Sleep -Seconds 10
    }
}
"@
        Set-Content -Path $serviceWrapperPath -Value $serviceWrapperContent -Encoding UTF8
        
        # 创建事件日志源
        try {
            New-EventLog -LogName Application -Source "PlaywrightMCP" -ErrorAction SilentlyContinue
        } catch {
            # 忽略如果已存在
        }
        
        # 使用NSSM创建服务 (如果可用)
        $nssmPath = Get-Command nssm -ErrorAction SilentlyContinue
        if ($nssmPath) {
            & nssm install $serviceName powershell.exe
            & nssm set $serviceName Arguments "-ExecutionPolicy Bypass -File `"$serviceWrapperPath`""
            & nssm set $serviceName DisplayName $serviceDisplayName
            & nssm set $serviceName Description $serviceDescription
            & nssm set $serviceName Start SERVICE_AUTO_START
            
            Start-Service -Name $serviceName
            Write-Host "✅ Windows服务设置成功!" -ForegroundColor Green
        } else {
            Write-Host "❌ 需要安装NSSM (Non-Sucking Service Manager)" -ForegroundColor Red
            Write-Host "请访问: https://nssm.cc/download 下载并安装NSSM" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "❌ 设置Windows服务失败: $_" -ForegroundColor Red
    }
}

function Remove-AutoStart {
    Write-Host "移除所有自动启动设置..." -ForegroundColor Yellow
    
    # 移除任务计划程序
    try {
        $existingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
        if ($existingTask) {
            Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
            Write-Host "✅ 已移除任务计划程序" -ForegroundColor Green
        }
    } catch {
        Write-Host "移除任务计划程序时出错: $_" -ForegroundColor Red
    }
    
    # 移除启动文件夹快捷方式
    try {
        $startupFolder = [Environment]::GetFolderPath("Startup")
        $shortcutPath = Join-Path $startupFolder "PlaywrightMCP.lnk"
        if (Test-Path $shortcutPath) {
            Remove-Item $shortcutPath -Force
            Write-Host "✅ 已移除启动文件夹快捷方式" -ForegroundColor Green
        }
    } catch {
        Write-Host "移除启动文件夹快捷方式时出错: $_" -ForegroundColor Red
    }
    
    # 移除Windows服务
    try {
        $serviceName = "PlaywrightMCP"
        $existingService = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($existingService) {
            Stop-Service -Name $serviceName -Force
            sc.exe delete $serviceName
            Write-Host "✅ 已移除Windows服务" -ForegroundColor Green
        }
    } catch {
        Write-Host "移除Windows服务时出错: $_" -ForegroundColor Red
    }
}

function Show-Menu {
    Write-Host ""
    Write-Host "=== Playwright MCP 自动启动设置 ===" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "请选择自动启动方法:" -ForegroundColor White
    Write-Host "1. 任务计划程序 (推荐)" -ForegroundColor Green
    Write-Host "2. 启动文件夹" -ForegroundColor Yellow
    Write-Host "3. Windows服务 (需要管理员权限)" -ForegroundColor Red
    Write-Host "4. 移除所有自动启动设置" -ForegroundColor Magenta
    Write-Host "5. 退出" -ForegroundColor Gray
    Write-Host ""
}

# 主逻辑
if ($Method -eq "task") {
    if ($args.Count -eq 0) {
        # 交互模式
        do {
            Show-Menu
            $choice = Read-Host "请输入选择 (1-5)"
            
            switch ($choice) {
                "1" { Set-TaskScheduler; break }
                "2" { Set-StartupFolder; break }
                "3" { Set-WindowsService; break }
                "4" { Remove-AutoStart; break }
                "5" { Write-Host "退出设置"; exit }
                default { Write-Host "无效选择，请重新输入" -ForegroundColor Red }
            }
            
            if ($choice -in @("1", "2", "3", "4")) {
                Write-Host ""
                Read-Host "按任意键继续"
            }
        } while ($choice -ne "5")
    } else {
        Set-TaskScheduler
    }
} elseif ($Method -eq "startup") {
    Set-StartupFolder
} elseif ($Method -eq "service") {
    Set-WindowsService
} elseif ($Method -eq "remove") {
    Remove-AutoStart
}

Write-Host ""
Write-Host "设置完成!" -ForegroundColor Green 