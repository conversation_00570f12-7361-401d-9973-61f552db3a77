import os
import importlib
import sys
import traceback
from pathlib import Path

def check_imports_in_dir(directory: Path, project_root: Path):
    """Checks all Python files in a given directory."""
    errors = []
    # Define a set of modules to exclude from the import check
    EXCLUDED_MODULES = {
        'common.rules_engine.filter_template', # This module is a phantom, causing errors but cannot be read/deleted.
    }
    print(f"\n--- Scanning directory: {directory} ---")
    for file_path in directory.rglob('*.py'):
        # Skip pycache and virtual environment directories - rglob should handle this implicitly
        
        # Construct the module name in dot notation e.g., src.omnilink_main.api.v1.api
        try:
            relative_path = file_path.relative_to(project_root)
            module_name = str(relative_path).replace(os.sep, '.')[:-3] # remove .py
        except ValueError:
            # This can happen if file_path is not under project_root, which shouldn't occur
            print(f"  [WARNING] Skipping file outside project root: {file_path}")
            continue

        # Exclude specified modules
        if module_name in EXCLUDED_MODULES:
            print(f"  [INFO] Skipping explicitly excluded module: {module_name}")
            continue

        # Skip entry points or non-package files if necessary
        if "__init__" in file_path.name:
             print(f"  [INFO] Skipping __init__ file: {module_name}")
             continue
        
        print(f"--- Checking: {module_name} ---")
        try:
            importlib.import_module(module_name)
            print(f"  [SUCCESS] Successfully imported {module_name}")
        except Exception as e:
            error_info = {
                "module": module_name,
                "path": str(file_path),
                "error_type": type(e).__name__,
                "error": str(e),
                "traceback": traceback.format_exc()
            }
            errors.append(error_info)
            print(f"  [FAILURE] Failed to import {module_name}: {e}")
    return errors

def run_diagnostic():
    project_root = Path(__file__).parent.resolve()
    
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
        print(f"Added '{project_root}' to sys.path")

    # Directories to check
    target_directories = ['src', 'common']
    all_errors = []

    for dir_name in target_directories:
        target_path = project_root / dir_name
        if not target_path.is_dir():
            print(f"\n[ERROR] Directory '{target_path}' not found. Skipping.")
            continue
        all_errors.extend(check_imports_in_dir(target_path, project_root))

    print("\n--- Diagnostic Check Complete ---")
    if all_errors:
        print(f"\nFound {len(all_errors)} module(s) with import errors:\n")
        for error in all_errors:
            print("=========================================================")
            print(f"Module: {error['module']}")
            print(f"Path: {error['path']}")
            print(f"Error Type: {error['error_type']}")
            print(f"Error: {error['error']}")
            print("--- Traceback ---")
            print(error['traceback'].strip())
            print("=========================================================\n")
    else:
        print("\nAll modules imported successfully! No errors found.")

if __name__ == "__main__":
    run_diagnostic() 