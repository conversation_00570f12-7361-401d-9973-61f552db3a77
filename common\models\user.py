from sqlalchemy import (
    Column, Integer, String, TIMESTAMP, Boolean, Foreign<PERSON>ey, Table
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from common.database.base_class import Base

user_role_association = Table(
    'user_role_association',
    Base.metadata,
    Column('user_id', Integer, ForeignKey('users.id', ondelete='CASCADE'), primary_key=True),
    Column('role_id', Integer, ForeignKey('roles.id', ondelete='CASCADE'), primary_key=True)
)

class User(Base):
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), nullable=False, unique=True)
    email = Column(String(100), nullable=False, unique=True)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    organization_id = Column(Integer, Foreign<PERSON>ey('organizations.id'))
    last_login_at = Column(TIMESTAMP)
    current_login_ip = Column(String(50))
    last_login_ip = Column(String(50))
    created_at = Column(TIMESTAMP, server_default=func.now())
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now())
    
    organization = relationship('Organization', back_populates='users')
    roles = relationship('Role', secondary=user_role_association, back_populates='users')
    devices = relationship('Device', back_populates='current_user')
    audit_logs = relationship('AuditLog', back_populates='user')
