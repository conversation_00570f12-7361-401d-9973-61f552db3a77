from typing import Optional
from pydantic import BaseModel
import datetime

# Shared properties
class DeviceBase(BaseModel):
    device_id: Optional[str] = None
    vendor_id: Optional[str] = None
    product_id: Optional[str] = None
    serial_number: Optional[str] = None
    description: Optional[str] = None
    device_name: Optional[str] = None
    device_notes: Optional[str] = None
    device_group: Optional[str] = None
    device_type: Optional[str] = None
    status: Optional[str] = None
    is_shared: Optional[bool] = False

# Properties to receive on item creation
class DeviceCreate(DeviceBase):
    device_id: str
    vendor_id: str
    product_id: str
    slave_server_id: int
    status: str

# Properties to receive on item update
class DeviceUpdate(DeviceBase):
    pass

# Properties shared by models stored in DB
class DeviceInDBBase(DeviceBase):
    id: int
    device_id: str
    status: str
    slave_server_id: int
    current_user_id: Optional[int] = None
    current_user_contact: Optional[str] = None
    last_user_id: Optional[int] = None
    last_user_contact: Optional[str] = None
    connected_at: Optional[datetime.datetime] = None
    last_used_at: Optional[datetime.datetime] = None
    created_at: datetime.datetime
    updated_at: datetime.datetime

    class Config:
        from_attributes = True

# Properties to return to client
class Device(DeviceInDBBase):
    pass

# Properties stored in DB
class DeviceInDB(DeviceInDBBase):
    pass

# 设备连接请求模型
class DeviceConnectionRequest(BaseModel):
    """设备连接请求"""
    device_id: int
    user_contact: Optional[str] = ""

# 设备连接响应模型
class DeviceConnectionResponse(BaseModel):
    """设备连接响应"""
    device_id: int
    device_name: str
    virtualhere_host: str
    virtualhere_port: int
    connection_token: Optional[str] = None
    expires_at: Optional[str] = None

# 设备状态响应模型
class DeviceStatusResponse(BaseModel):
    """设备状态响应"""
    device_id: int
    status: str
    current_user_id: Optional[int] = None
    current_user_contact: Optional[str] = None
    connected_at: Optional[str] = None
    last_user_id: Optional[int] = None
    last_user_contact: Optional[str] = None
    last_used_at: Optional[str] = None
    is_connected: bool = False 