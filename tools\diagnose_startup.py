import asyncio
import logging
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - [%(name)s] - %(message)s')
logger = logging.getLogger("STARTUP_DIAGNOSIS")

async def check_imports():
    """Checks if all critical modules can be imported."""
    logger.info("--- Checking critical module imports ---")
    try:
        from src.omnilink_main.core import config, database, redis, security
        logger.info("[SUCCESS] Core modules imported successfully.")
        from src.omnilink_main.services import (
            auth_service, user_service, role_service, 
            policy_service, device_service, slave_server_service, status_service
        )
        logger.info("[SUCCESS] Service modules imported successfully.")
        from common import models, schemas
        logger.info("[SUCCESS] Common modules (models, schemas) imported successfully.")
        return True
    except ImportError as e:
        logger.critical(f"[FAILURE] Failed to import a critical module: {e}", exc_info=True)
        return False

async def check_database_connection():
    """Checks the connection to the PostgreSQL database."""
    logger.info("--- Checking database connection ---")
    try:
        from sqlalchemy import text
        from src.omnilink_main.core.database import engine
        async with engine.connect() as conn:
            await conn.run_sync(lambda sync_conn: sync_conn.execute(text("SELECT 1")))
        logger.info("[SUCCESS] Database connection successful.")
        return True
    except Exception as e:
        logger.critical(f"[FAILURE] Database connection failed: {e}", exc_info=True)
        return False

async def check_redis_connection():
    """Checks the connection to the Redis server."""
    logger.info("--- Checking Redis connection ---")
    try:
        from src.omnilink_main.core.redis import test_redis_connection
        if await test_redis_connection():
            return True
        else:
            logger.critical("[FAILURE] Redis connection test function returned False.")
            return False
    except Exception as e:
        logger.critical(f"[FAILURE] Redis connection failed: {e}", exc_info=True)
        logger.warning("Hint: Is the Redis server running on the configured host and port? Check your firewall settings.")
        return False

async def run_diagnostics():
    """Runs all diagnostic checks."""
    logger.info("=============================================")
    logger.info("  RUNNING OMNILINK SERVER STARTUP DIAGNOSTICS  ")
    logger.info("=============================================")
    
    # Run checks in sequence
    imports_ok = await check_imports()
    db_ok = await check_database_connection()
    redis_ok = await check_redis_connection()

    all_ok = imports_ok and db_ok and redis_ok
    
    logger.info("=============================================")
    if all_ok:
        logger.info("  DIAGNOSTICS PASSED: System is ready for startup.")
        logger.info("=============================================")
        sys.exit(0)
    else:
        logger.error("  DIAGNOSTICS FAILED: Please review the errors above.")
        logger.info("=============================================")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(run_diagnostics())
