import asyncio
import logging
from typing import Dict, Any, Optional, Callable, Awaitable

from .virtualhere_api_client import VirtualHereApiClient

logger = logging.getLogger(__name__)

# In our Docker setup, VirtualHere runs as a single service,
# so we interact with it as a client on its default port.
DEFAULT_VH_HOST = '127.0.0.1'
DEFAULT_VH_PORT = 7575

class VirtualHereService:
    """
    A simplified service to interact with a single, pre-existing VirtualHere instance
    running within the same container.
    """
    def __init__(self):
        self._api_client = VirtualHereApiClient(DEFAULT_VH_HOST, DEFAULT_VH_PORT)
        self._event_callback: Optional[Callable[[Dict], Awaitable[None]]] = None
        self._is_connected = False
        self._listener_task: Optional[asyncio.Task] = None

    def register_event_callback(self, callback: Callable[[Dict], Awaitable[None]]):
        """Registers a callback function to be called on VH events."""
        logger.info("Registering event callback for VirtualHereService.")
        self._event_callback = callback

    async def _handle_vh_event(self, event: str):
        """
        Callback to handle events from the VirtualHere instance.
        """
        logger.info(f"Received VirtualHere event: {event}")
        if self._event_callback:
            try:
                # The callback itself should be a coroutine function
                # We can parse the event string for more structured data if needed
                event_data = {"raw_event": event}
                await self._event_callback(event_data)
            except Exception as e:
                logger.error(f"Error executing event callback for event '{event}': {e}", exc_info=True)

    async def start(self):
        """
        Connects to the VirtualHere server and starts listening for events.
        """
        if self._is_connected:
            logger.warning("VirtualHereService is already connected.")
            return

        logger.info(f"Connecting to VirtualHere server at {DEFAULT_VH_HOST}:{DEFAULT_VH_PORT}...")
        if not await self._api_client.connect():
            logger.error("Failed to connect to the local VirtualHere server. Is it running?")
            return

        self._is_connected = True
        logger.info("Successfully connected to the VirtualHere server.")

        # Start the background task to listen for events
        self._listener_task = asyncio.create_task(
            self._api_client.listen_for_events(self._handle_vh_event)
        )
        logger.info("Started listening for VirtualHere events.")

    async def stop(self):
        """
        Stops the event listener and disconnects from the VirtualHere server.
        """
        if not self._is_connected:
            return

        logger.info("Stopping VirtualHereService...")
        if self._listener_task:
            self._listener_task.cancel()
            try:
                await self._listener_task
            except asyncio.CancelledError:
                pass  # Expected on cancellation
            self._listener_task = None
            logger.info("Stopped VirtualHere event listener.")

        await self._api_client.disconnect()
        self._is_connected = False
        logger.info("Disconnected from the VirtualHere server.")

    async def share_device(self, device_address: str) -> bool:
        """
        Commands the VirtualHere server to share a specific device.
        Example device_address: "vhub.local:7575:1"
        """
        if not self._is_connected:
            logger.error("Cannot share device, not connected to VirtualHere server.")
            return False
        
        logger.info(f"Requesting to share device: {device_address}")
        # The actual command might be different, this is based on common API patterns.
        # "DEVICE_USE,<address>" or "SHARE,<address>"
        response = await self._api_client.send_command(f"DEVICE_USE,{device_address}")
        if response and "OK" in response:
            logger.info(f"Successfully commanded VirtualHere to share device {device_address}.")
            return True
        else:
            logger.error(f"Failed to share device {device_address}. Response: {response}")
            return False
            
    async def unshare_device(self, device_address: str) -> bool:
        """
        Commands the VirtualHere server to stop sharing a specific device.
        """
        if not self._is_connected:
            logger.error("Cannot unshare device, not connected to VirtualHere server.")
            return False
            
        logger.info(f"Requesting to unshare device: {device_address}")
        response = await self._api_client.send_command(f"DEVICE_UNUSE,{device_address}")
        if response and "OK" in response:
            logger.info(f"Successfully commanded VirtualHere to unshare device {device_address}.")
            return True
        else:
            logger.error(f"Failed to unshare device {device_address}. Response: {response}")
            return False

    async def list_devices(self) -> Optional[str]:
        """
        Requests a list of devices from the VirtualHere server.
        """
        if not self._is_connected:
            logger.error("Cannot list devices, not connected to VirtualHere server.")
            return None
        
        logger.info("Requesting device list from VirtualHere server...")
        # The command "DEVICE_LIST" is a guess and might need to be adjusted
        # based on the actual VirtualHere scripting API.
        response = await self._api_client.send_command("DEVICE_LIST")
        return response
