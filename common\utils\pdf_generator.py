#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF生成适配器
支持多种PDF生成库，优雅处理依赖缺失
"""

import logging
from typing import Optional, Dict, Any, Union
from pathlib import Path
import io

logger = logging.getLogger(__name__)

class PDFGeneratorError(Exception):
    """PDF生成错误"""
    pass

class PDFGenerator:
    """PDF生成器适配器"""
    
    def __init__(self):
        self.available_engines = []
        self._check_available_engines()
    
    def _check_available_engines(self):
        """检查可用的PDF生成引擎"""
        # 检查WeasyPrint
        try:
            import weasyprint
            self.available_engines.append('weasyprint')
            logger.info("WeasyPrint PDF引擎可用")
        except ImportError as e:
            logger.warning(f"WeasyPrint不可用: {e}")
        
        # 检查ReportLab
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter, A4
            self.available_engines.append('reportlab')
            logger.info("ReportLab PDF引擎可用")
        except ImportError as e:
            logger.warning(f"ReportLab不可用: {e}")
        
        # 检查FPDF2
        try:
            from fpdf import FPDF
            self.available_engines.append('fpdf2')
            logger.info("FPDF2 PDF引擎可用")
        except ImportError as e:
            logger.warning(f"FPDF2不可用: {e}")
        
        if not self.available_engines:
            logger.error("没有可用的PDF生成引擎")
    
    def generate_from_html(self, html_content: str, output_path: Optional[Union[str, Path]] = None) -> bytes:
        """从HTML生成PDF"""
        if 'weasyprint' in self.available_engines:
            return self._generate_with_weasyprint(html_content, output_path)
        elif 'reportlab' in self.available_engines:
            return self._generate_with_reportlab_from_html(html_content, output_path)
        elif 'fpdf2' in self.available_engines:
            return self._generate_with_fpdf2_from_html(html_content, output_path)
        else:
            raise PDFGeneratorError("没有可用的PDF生成引擎")
    
    def generate_simple_report(self, title: str, content: str, output_path: Optional[Union[str, Path]] = None) -> bytes:
        """生成简单的文本报告PDF"""
        if 'reportlab' in self.available_engines:
            return self._generate_simple_with_reportlab(title, content, output_path)
        elif 'fpdf2' in self.available_engines:
            return self._generate_simple_with_fpdf2(title, content, output_path)
        elif 'weasyprint' in self.available_engines:
            # 将文本转换为HTML再生成
            html_content = f"""
            <html>
            <head>
                <meta charset="utf-8">
                <title>{title}</title>
                <style>
                    body {{ font-family: 'SimSun', serif; margin: 2cm; }}
                    h1 {{ color: #333; }}
                    pre {{ white-space: pre-wrap; }}
                </style>
            </head>
            <body>
                <h1>{title}</h1>
                <pre>{content}</pre>
            </body>
            </html>
            """
            return self._generate_with_weasyprint(html_content, output_path)
        else:
            raise PDFGeneratorError("没有可用的PDF生成引擎")
    
    def _generate_with_weasyprint(self, html_content: str, output_path: Optional[Union[str, Path]] = None) -> bytes:
        """使用WeasyPrint生成PDF"""
        try:
            import weasyprint
            
            # 创建PDF
            html_doc = weasyprint.HTML(string=html_content)
            pdf_bytes = html_doc.write_pdf()
            
            # 保存到文件（如果指定了路径）
            if output_path:
                with open(output_path, 'wb') as f:
                    f.write(pdf_bytes)
            
            return pdf_bytes
            
        except Exception as e:
            logger.error(f"WeasyPrint生成PDF失败: {e}")
            raise PDFGeneratorError(f"WeasyPrint生成失败: {e}")
    
    def _generate_with_reportlab(self, title: str, content: str, output_path: Optional[Union[str, Path]] = None) -> bytes:
        """使用ReportLab生成PDF"""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            
            # 创建内存缓冲区
            buffer = io.BytesIO()
            
            # 创建PDF画布
            c = canvas.Canvas(buffer, pagesize=A4)
            width, height = A4
            
            # 尝试注册中文字体
            try:
                # 注册系统中文字体
                pdfmetrics.registerFont(TTFont('SimSun', 'C:/Windows/Fonts/simsun.ttc'))
                font_name = 'SimSun'
            except:
                font_name = 'Helvetica'  # 回退到默认字体
            
            # 设置标题
            c.setFont(font_name, 16)
            c.drawString(50, height - 50, title)
            
            # 设置内容
            c.setFont(font_name, 12)
            y_position = height - 100
            
            # 分行处理内容
            lines = content.split('\n')
            for line in lines:
                if y_position < 50:  # 换页
                    c.showPage()
                    y_position = height - 50
                    c.setFont(font_name, 12)
                
                c.drawString(50, y_position, line[:80])  # 限制行长度
                y_position -= 20
            
            # 完成PDF
            c.save()
            
            # 获取PDF字节
            pdf_bytes = buffer.getvalue()
            buffer.close()
            
            # 保存到文件（如果指定了路径）
            if output_path:
                with open(output_path, 'wb') as f:
                    f.write(pdf_bytes)
            
            return pdf_bytes
            
        except Exception as e:
            logger.error(f"ReportLab生成PDF失败: {e}")
            raise PDFGeneratorError(f"ReportLab生成失败: {e}")
    
    def _generate_simple_with_reportlab(self, title: str, content: str, output_path: Optional[Union[str, Path]] = None) -> bytes:
        """使用ReportLab生成简单PDF"""
        return self._generate_with_reportlab(title, content, output_path)
    
    def _generate_simple_with_fpdf2(self, title: str, content: str, output_path: Optional[Union[str, Path]] = None) -> bytes:
        """使用FPDF2生成简单PDF"""
        try:
            from fpdf import FPDF
            
            # 创建PDF对象
            pdf = FPDF()
            pdf.add_page()
            
            # 尝试添加中文字体支持
            try:
                pdf.add_font('SimSun', '', 'C:/Windows/Fonts/simsun.ttc', uni=True)
                pdf.set_font('SimSun', size=12)
            except:
                pdf.set_font('Arial', size=12)  # 回退到默认字体
            
            # 添加标题
            pdf.set_font_size(16)
            pdf.cell(0, 10, title, ln=True, align='C')
            pdf.ln(10)
            
            # 添加内容
            pdf.set_font_size(12)
            lines = content.split('\n')
            for line in lines:
                # 处理长行
                if len(line) > 80:
                    words = line.split(' ')
                    current_line = ''
                    for word in words:
                        if len(current_line + word) < 80:
                            current_line += word + ' '
                        else:
                            pdf.cell(0, 8, current_line.strip(), ln=True)
                            current_line = word + ' '
                    if current_line:
                        pdf.cell(0, 8, current_line.strip(), ln=True)
                else:
                    pdf.cell(0, 8, line, ln=True)
            
            # 获取PDF字节
            pdf_bytes = pdf.output(dest='S').encode('latin1')
            
            # 保存到文件（如果指定了路径）
            if output_path:
                with open(output_path, 'wb') as f:
                    f.write(pdf_bytes)
            
            return pdf_bytes
            
        except Exception as e:
            logger.error(f"FPDF2生成PDF失败: {e}")
            raise PDFGeneratorError(f"FPDF2生成失败: {e}")
    
    def _generate_with_reportlab_from_html(self, html_content: str, output_path: Optional[Union[str, Path]] = None) -> bytes:
        """使用ReportLab从HTML生成PDF（简化版）"""
        # 简单的HTML解析，提取文本内容
        import re
        
        # 提取标题
        title_match = re.search(r'<title>(.*?)</title>', html_content, re.IGNORECASE | re.DOTALL)
        title = title_match.group(1) if title_match else "PDF报告"
        
        # 提取body内容
        body_match = re.search(r'<body[^>]*>(.*?)</body>', html_content, re.IGNORECASE | re.DOTALL)
        if body_match:
            body_content = body_match.group(1)
            # 移除HTML标签
            content = re.sub(r'<[^>]+>', '', body_content)
            # 清理多余的空白
            content = re.sub(r'\s+', ' ', content).strip()
        else:
            content = "无法解析HTML内容"
        
        return self._generate_with_reportlab(title, content, output_path)
    
    def _generate_with_fpdf2_from_html(self, html_content: str, output_path: Optional[Union[str, Path]] = None) -> bytes:
        """使用FPDF2从HTML生成PDF（简化版）"""
        # 简单的HTML解析，提取文本内容
        import re
        
        # 提取标题
        title_match = re.search(r'<title>(.*?)</title>', html_content, re.IGNORECASE | re.DOTALL)
        title = title_match.group(1) if title_match else "PDF报告"
        
        # 提取body内容
        body_match = re.search(r'<body[^>]*>(.*?)</body>', html_content, re.IGNORECASE | re.DOTALL)
        if body_match:
            body_content = body_match.group(1)
            # 移除HTML标签
            content = re.sub(r'<[^>]+>', '', body_content)
            # 清理多余的空白
            content = re.sub(r'\s+', ' ', content).strip()
        else:
            content = "无法解析HTML内容"
        
        return self._generate_simple_with_fpdf2(title, content, output_path)
    
    def get_available_engines(self) -> list:
        """获取可用的PDF生成引擎列表"""
        return self.available_engines.copy()
    
    def is_available(self) -> bool:
        """检查是否有可用的PDF生成引擎"""
        return len(self.available_engines) > 0

# 全局PDF生成器实例
pdf_generator = PDFGenerator()

def generate_pdf_report(title: str, content: str, output_path: Optional[Union[str, Path]] = None) -> bytes:
    """生成PDF报告的便捷函数"""
    return pdf_generator.generate_simple_report(title, content, output_path)

def generate_pdf_from_html(html_content: str, output_path: Optional[Union[str, Path]] = None) -> bytes:
    """从HTML生成PDF的便捷函数"""
    return pdf_generator.generate_from_html(html_content, output_path) 
