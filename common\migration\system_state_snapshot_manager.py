"""
系统状态快照管理器

管理系统状态快照的创建、恢复、迁移和备份等操作。
"""

import os
import time
import json
import logging
import datetime
import threading
from typing import Dict, List, Any, Optional, Union, Tuple
from pathlib import Path

from .system_state_snapshot import SystemStateSnapshot, SnapshotType, SnapshotInfo

# 设置日志
logger = logging.getLogger('common.migration.snapshot_manager')

class SystemStateSnapshotManager:
    """
    系统状态快照管理器
    
    提供系统状态快照的高级管理功能，包括定时备份、自动清理、状态迁移等。
    """
    
    def __init__(self, 
               system_id: str, 
               snapshot_dir: Optional[str] = None,
               max_backups: int = 10,
               backup_interval_hours: int = 24):
        """
        初始化系统状态快照管理器
        
        参数:
            system_id: 系统ID
            snapshot_dir: 快照存储目录，不提供则使用默认目录
            max_backups: 最大保留备份数量
            backup_interval_hours: 备份间隔（小时）
        """
        self.system_id = system_id
        self.max_backups = max_backups
        self.backup_interval_hours = backup_interval_hours
        
        # 创建快照系统
        self.snapshot = SystemStateSnapshot(base_dir=snapshot_dir)
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 上次备份时间
        self._last_backup_time = None
        
        # 备份线程
        self._backup_thread = None
        self._running = False
        
        # 状态收集器
        self._state_collectors = {}
        
        logger.info(f"系统状态快照管理器初始化完成，系统ID: {system_id}")
        
    def register_state_collector(self, name: str, collector_func):
        """
        注册状态收集器
        
        参数:
            name: 收集器名称
            collector_func: 状态收集函数，调用时不带参数，返回字典
        """
        with self._lock:
            self._state_collectors[name] = collector_func
            logger.debug(f"注册状态收集器: {name}")
    
    def unregister_state_collector(self, name: str):
        """
        注销状态收集器
        
        参数:
            name: 收集器名称
        """
        with self._lock:
            if name in self._state_collectors:
                del self._state_collectors[name]
                logger.debug(f"注销状态收集器: {name}")
    
    def collect_system_state(self) -> Dict[str, Any]:
        """
        收集系统状态
        
        返回:
            系统状态数据
        """
        with self._lock:
            system_state = {
                "system_id": self.system_id,
                "collected_at": datetime.datetime.now().isoformat(),
            }
            
            # 调用所有收集器
            for name, collector in self._state_collectors.items():
                try:
                    state = collector()
                    if state and isinstance(state, dict):
                        system_state[name] = state
                except Exception as e:
                    logger.error(f"调用状态收集器 {name} 失败: {str(e)}")
            
            return system_state
    
    def create_backup(self, description: str = "", tags: Optional[List[str]] = None) -> Optional[str]:
        """
        创建系统备份
        
        参数:
            description: 备份描述
            tags: 标签列表
            
        返回:
            快照ID，失败则返回None
        """
        try:
            # 收集系统状态
            system_state = self.collect_system_state()
            
            # 创建备份快照
            if not tags:
                tags = []
            tags.extend(["backup", "auto"])
            
            # 设置描述
            if not description:
                description = f"系统备份 {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                
            # 创建快照
            snapshot_id = self.snapshot.create_snapshot(
                system_state=system_state,
                snapshot_type=SnapshotType.BACKUP,
                description=description,
                tags=tags
            )
            
            if snapshot_id:
                self._last_backup_time = time.time()
                logger.info(f"创建系统备份成功: {snapshot_id}")
                
                # 清理旧备份
                self._cleanup_old_backups()
                
            return snapshot_id
            
        except Exception as e:
            logger.error(f"创建系统备份失败: {str(e)}")
            return None
    
    def _cleanup_old_backups(self):
        """清理旧备份"""
        try:
            # 获取所有备份
            backups = self.snapshot.list_snapshots(
                filter_type=SnapshotType.BACKUP,
                sort_by_time=True
            )
            
            if len(backups) > self.max_backups:
                # 超出限制，删除最旧的备份
                to_delete = backups[self.max_backups:]
                for backup in to_delete:
                    self.snapshot.delete_snapshot(backup.snapshot_id)
                    logger.info(f"清理旧备份: {backup.snapshot_id}")
                    
            # 清理过期备份
            self.snapshot.clean_expired_backups()
            
        except Exception as e:
            logger.error(f"清理旧备份失败: {str(e)}")
    
    def start_auto_backup(self):
        """启动自动备份"""
        with self._lock:
            if self._running:
                return
                
            self._running = True
            self._backup_thread = threading.Thread(target=self._auto_backup_loop, daemon=True)
            self._backup_thread.start()
            
            logger.info(f"启动自动备份，间隔: {self.backup_interval_hours}小时")
    
    def stop_auto_backup(self):
        """停止自动备份"""
        with self._lock:
            self._running = False
            if self._backup_thread:
                self._backup_thread.join(timeout=1.0)
                self._backup_thread = None
                
            logger.info("停止自动备份")
    
    def _auto_backup_loop(self):
        """自动备份循环"""
        while self._running:
            try:
                current_time = time.time()
                
                # 检查是否需要备份
                if (self._last_backup_time is None or 
                    current_time - self._last_backup_time > self.backup_interval_hours * 3600):
                    
                    self.create_backup(description="自动定时备份")
                
                # 睡眠一段时间
                time.sleep(3600)  # 每小时检查一次
                
            except Exception as e:
                logger.error(f"自动备份异常: {str(e)}")
                time.sleep(3600)  # 出错后等待一段时间
    
    def create_migration_snapshot(self, target_system_id: str, description: str = "") -> Optional[str]:
        """
        创建迁移快照
        
        参数:
            target_system_id: 目标系统ID
            description: 快照描述
            
        返回:
            快照ID，失败则返回None
        """
        try:
            # 收集系统状态
            system_state = self.collect_system_state()
            
            # 设置描述
            if not description:
                description = f"系统迁移至 {target_system_id}"
                
            # 创建迁移快照
            return self.snapshot.create_migration_snapshot(
                system_state=system_state,
                source_system_id=self.system_id,
                target_system_id=target_system_id,
                description=description
            )
            
        except Exception as e:
            logger.error(f"创建迁移快照失败: {str(e)}")
            return None
    
    def apply_snapshot(self, snapshot_id: str, 
                     components: Optional[List[str]] = None,
                     handlers: Optional[Dict[str, callable]] = None) -> Dict[str, Any]:
        """
        应用快照
        
        参数:
            snapshot_id: 快照ID
            components: 要恢复的组件列表，None表示全部
            handlers: 组件恢复处理器，字典格式 {组件名: 处理函数}
            
        返回:
            应用结果
        """
        try:
            # 准备恢复
            restore_result = self.snapshot.restore_snapshot(
                snapshot_id=snapshot_id,
                target_system_id=self.system_id,
                components=components
            )
            
            if not restore_result.get("success", False):
                return restore_result
                
            # 获取系统状态
            system_state = restore_result.get("system_state", {})
            
            # 应用各组件
            results = {}
            
            if handlers:
                for component, state in system_state.items():
                    if component in handlers:
                        try:
                            handler = handlers[component]
                            component_result = handler(state)
                            results[component] = {
                                "success": True,
                                "result": component_result
                            }
                        except Exception as e:
                            results[component] = {
                                "success": False,
                                "error": str(e)
                            }
                            logger.error(f"应用组件 {component} 失败: {str(e)}")
            
            # 返回结果
            return {
                "success": True,
                "restore_meta": restore_result.get("restore_meta", {}),
                "results": results
            }
            
        except Exception as e:
            logger.error(f"应用快照 {snapshot_id} 失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def export_snapshot_package(self, snapshot_id: str, export_dir: str,
                              include_metadata: bool = True) -> Optional[str]:
        """
        导出快照包
        
        参数:
            snapshot_id: 快照ID
            export_dir: 导出目录
            include_metadata: 是否包含元数据
            
        返回:
            导出文件路径，失败则返回None
        """
        try:
            export_dir_path = Path(export_dir)
            export_dir_path.mkdir(parents=True, exist_ok=True)
            
            # 获取快照信息
            snapshot_info = self.snapshot.get_snapshot_info(snapshot_id)
            if not snapshot_info:
                logger.error(f"导出失败，快照 {snapshot_id} 不存在")
                return None
                
            # 创建导出文件名
            timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            export_file = export_dir_path / f"{snapshot_info.type.value}_{timestamp}_{snapshot_id}.zip"
            
            # 导出快照
            if self.snapshot.export_snapshot(
                snapshot_id=snapshot_id,
                export_path=str(export_file),
                include_metadata=include_metadata
            ):
                logger.info(f"导出快照包 {snapshot_id} 至 {export_file}")
                return str(export_file)
            else:
                return None
                
        except Exception as e:
            logger.error(f"导出快照包 {snapshot_id} 失败: {str(e)}")
            return None
    
    def import_snapshot_package(self, package_path: str) -> Optional[str]:
        """
        导入快照包
        
        参数:
            package_path: 快照包文件路径
            
        返回:
            导入的快照ID，失败则返回None
        """
        try:
            # 导入快照
            return self.snapshot.import_snapshot(package_path)
            
        except Exception as e:
            logger.error(f"导入快照包 {package_path} 失败: {str(e)}")
            return None
    
    def get_backups(self, limit: int = 10) -> List[SnapshotInfo]:
        """
        获取备份列表
        
        参数:
            limit: 最大返回数量
            
        返回:
            备份快照信息列表
        """
        return self.snapshot.list_snapshots(
            filter_type=SnapshotType.BACKUP,
            limit=limit,
            sort_by_time=True
        )
    
    def get_migrations(self, as_source: bool = True, as_target: bool = True,
                     limit: int = 10) -> List[SnapshotInfo]:
        """
        获取迁移快照列表
        
        参数:
            as_source: 是否包含作为源系统的迁移
            as_target: 是否包含作为目标系统的迁移
            limit: 最大返回数量
            
        返回:
            迁移快照信息列表
        """
        migrations = self.snapshot.list_snapshots(
            filter_type=SnapshotType.MIGRATION,
            sort_by_time=True
        )
        
        # 过滤
        results = []
        for migration in migrations:
            if (as_source and migration.source_system == self.system_id) or \
               (as_target and migration.target_system == self.system_id):
                results.append(migration)
                
        # 限制数量
        if limit > 0 and len(results) > limit:
            results = results[:limit]
            
        return results
    
    def get_snapshot_statistics(self) -> Dict[str, Any]:
        """
        获取快照统计信息
        
        返回:
            统计信息
        """
        return self.snapshot.get_snapshot_statistics() 
