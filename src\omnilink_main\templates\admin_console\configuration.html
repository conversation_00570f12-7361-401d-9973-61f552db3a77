<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主从服务器系统 - 配置管理</title>
    <!-- Material Design Web Components -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Material+Icons&display=block" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet" />
    <!-- Material Design 3 Styles -->
    <link href="{{ url_for('static', filename='css/material-components-web.min.css') }}" rel="stylesheet">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin_material.css') }}">
    <!-- Monaco Editor (用于配置编辑) -->
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.33.0/min/vs/loader.js"></script>
</head>
<body class="mdc-typography">
    <div class="app-container">
        <!-- 顶部应用栏 -->
        <header class="mdc-top-app-bar mdc-top-app-bar--fixed">
            <div class="mdc-top-app-bar__row">
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-start">
                    <button class="material-icons mdc-top-app-bar__navigation-icon mdc-icon-button" id="menu-button">menu</button>
                    <span class="mdc-top-app-bar__title">主从服务器管理系统 - 配置管理</span>
                </section>
                <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-end" role="toolbar">
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="refresh-button" aria-label="刷新">refresh</button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="theme-toggle" aria-label="切换主题">dark_mode</button>
                    <button class="material-icons mdc-top-app-bar__action-item mdc-icon-button" id="user-menu-button" aria-label="用户菜单">account_circle</button>
                    
                    <!-- 用户菜单 -->
                    <div class="mdc-menu mdc-menu-surface" id="user-menu">
                        <ul class="mdc-list" role="menu" aria-hidden="true" aria-orientation="vertical" tabindex="-1">
                            <li class="mdc-list-item" role="menuitem">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="mdc-list-item__text">个人设置</span>
                            </li>
                            <li class="mdc-list-divider" role="separator"></li>
                            <li class="mdc-list-item" role="menuitem" id="logout-button" data-logout-url="/auth/logout">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="mdc-list-item__text">退出登录</span>
                            </li>
                        </ul>
                    </div>
                </section>
            </div>
        </header>

        <!-- 主内容区域 -->
        <div class="app-content mdc-top-app-bar--fixed-adjust">
            <div class="mdc-drawer-app-content">
                <!-- 侧边导航抽屉 -->
                <aside class="mdc-drawer mdc-drawer--dismissible">
                    <div class="mdc-drawer__content">
                        <nav class="mdc-list">
                            <a class="mdc-list-item" href="{{ url_for('admin_dashboard') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">dashboard</span>
                                <span class="mdc-list-item__text">仪表盘</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_devices') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">usb</span>
                                <span class="mdc-list-item__text">设备管理</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_users') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">people</span>
                                <span class="mdc-list-item__text">用户权限</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_group_management') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">dns</span>
                                <span class="mdc-list-item__text">分组管理</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_migration_wizard') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">swap_horiz</span>
                                <span class="mdc-list-item__text">迁移向导</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_alerts') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">notifications</span>
                                <span class="mdc-list-item__text">告警设置</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_health_monitor') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">favorite</span>
                                <span class="mdc-list-item__text">健康监控</span>
                            </a>
                            <a class="mdc-list-item mdc-list-item--activated" href="{{ url_for('admin_configuration') }}" aria-current="page">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">settings</span>
                                <span class="mdc-list-item__text">配置管理</span>
                            </a>
                            <a class="mdc-list-item" href="{{ url_for('admin_logs') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">description</span>
                                <span class="mdc-list-item__text">日志查看</span>
                            </a>
                            
                            <hr class="mdc-list-divider">
                            <h6 class="mdc-list-group__subheader">系统</h6>
                            
                            <a class="mdc-list-item" href="{{ url_for('admin_system_diagnostics') }}">
                                <span class="mdc-list-item__ripple"></span>
                                <span class="material-icons mdc-list-item__graphic" aria-hidden="true">build</span>
                                <span class="mdc-list-item__text">系统诊断</span>
                            </a>
                        </nav>
                    </div>
                </aside>

                <!-- 主内容 -->
                <main class="main-content">
                    <div class="page-content">
                        <h1 class="mdc-typography--headline4">配置管理</h1>
                        
                        <div class="configuration-layout">
                            <!-- 配置类别侧边栏 -->
                            <div class="config-sidebar mdc-card">
                                <div class="config-sidebar-header">
                                    <h2 class="mdc-typography--headline6">配置类别</h2>
                                    <div class="config-sidebar-actions">
                                        <button class="mdc-icon-button material-icons" id="add-category-button" title="添加类别">add</button>
                                    </div>
                                </div>
                                
                                <div class="mdc-list" id="config-category-list">
                                    <!-- 配置类别将通过JavaScript动态加载 -->
                                    <a class="mdc-list-item mdc-list-item--activated" data-category="system" href="#" aria-current="page">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="material-icons mdc-list-item__graphic" aria-hidden="true">settings</span>
                                        <span class="mdc-list-item__text">系统设置</span>
                                    </a>
                                    <a class="mdc-list-item" data-category="network" href="#">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="material-icons mdc-list-item__graphic" aria-hidden="true">wifi</span>
                                        <span class="mdc-list-item__text">网络配置</span>
                                    </a>
                                    <a class="mdc-list-item" data-category="security" href="#">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="material-icons mdc-list-item__graphic" aria-hidden="true">security</span>
                                        <span class="mdc-list-item__text">安全设置</span>
                                    </a>
                                    <a class="mdc-list-item" data-category="device" href="#">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="material-icons mdc-list-item__graphic" aria-hidden="true">usb</span>
                                        <span class="mdc-list-item__text">设备配置</span>
                                    </a>
                                    <a class="mdc-list-item" data-category="service" href="#">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="material-icons mdc-list-item__graphic" aria-hidden="true">miscellaneous_services</span>
                                        <span class="mdc-list-item__text">服务配置</span>
                                    </a>
                                    <a class="mdc-list-item" data-category="notification" href="#">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="material-icons mdc-list-item__graphic" aria-hidden="true">notifications</span>
                                        <span class="mdc-list-item__text">通知设置</span>
                                    </a>
                                </div>
                            </div>
                            
                            <!-- 配置内容区域 -->
                            <div class="config-content-container">
                                <div class="config-content-header mdc-card">
                                    <div class="config-title-area">
                                        <h2 class="mdc-typography--headline5" id="config-category-title">系统设置</h2>
                                        <span class="mdc-typography--caption" id="config-last-modified">最后修改: 2023-04-10 14:30</span>
                                    </div>
                                    
                                    <div class="config-actions">
                                        <div class="mdc-select mdc-select--outlined config-version-select">
                                            <div class="mdc-select__anchor">
                                                <span class="mdc-select__selected-text">当前版本</span>
                                                <span class="mdc-select__dropdown-icon">
                                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                                    </svg>
                                                </span>
                                                <span class="mdc-notched-outline">
                                                    <span class="mdc-notched-outline__leading"></span>
                                                    <span class="mdc-notched-outline__notch">
                                                        <span class="mdc-floating-label">版本</span>
                                                    </span>
                                                    <span class="mdc-notched-outline__trailing"></span>
                                                </span>
                                            </div>
                                            <div class="mdc-select__menu mdc-menu mdc-menu-surface">
                                                <ul class="mdc-list" id="config-version-list">
                                                    <li class="mdc-list-item mdc-list-item--selected" data-value="current" aria-selected="true">
                                                        <span class="mdc-list-item__text">当前版本</span>
                                                    </li>
                                                    <li class="mdc-list-item" data-value="2023-04-10-14-30">
                                                        <span class="mdc-list-item__text">2023-04-10 14:30</span>
                                                    </li>
                                                    <li class="mdc-list-item" data-value="2023-04-05-10-15">
                                                        <span class="mdc-list-item__text">2023-04-05 10:15</span>
                                                    </li>
                                                    <li class="mdc-list-item" data-value="2023-03-28-09-45">
                                                        <span class="mdc-list-item__text">2023-03-28 09:45</span>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        
                                        <button class="mdc-button" id="compare-button">
                                            <span class="mdc-button__ripple"></span>
                                            <span class="material-icons mdc-button__icon">compare</span>
                                            <span class="mdc-button__label">对比版本</span>
                                        </button>
                                        
                                        <button class="mdc-button" id="rollback-button">
                                            <span class="mdc-button__ripple"></span>
                                            <span class="material-icons mdc-button__icon">restore</span>
                                            <span class="mdc-button__label">回滚</span>
                                        </button>
                                        
                                        <button class="mdc-button mdc-button--raised" id="save-config-button">
                                            <span class="mdc-button__ripple"></span>
                                            <span class="material-icons mdc-button__icon">save</span>
                                            <span class="mdc-button__label">保存</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="config-content mdc-card">
                                    <div class="config-tabs">
                                        <button class="config-tab config-tab--active" data-tab="form">表单视图</button>
                                        <button class="config-tab" data-tab="json">JSON视图</button>
                                        <button class="config-tab" data-tab="yaml">YAML视图</button>
                                    </div>
                                    
                                    <div class="config-tab-content">
                                        <!-- 表单视图 -->
                                        <div class="config-tab-panel config-tab-panel--active" id="form-view">
                                            <div class="mdc-layout-grid config-form">
                                                <div class="mdc-layout-grid__inner">
                                                    <!-- 配置表单将通过JavaScript动态加载 -->
                                                    <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-12">
                                                        <h3 class="mdc-typography--subtitle1">基本设置</h3>
                                                        <div class="mdc-form-field-group">
                                                            <div class="mdc-text-field mdc-text-field--outlined">
                                                                <input type="text" class="mdc-text-field__input" id="system_name" name="system_name" value="主从服务器管理系统">
                                                                <div class="mdc-notched-outline">
                                                                    <div class="mdc-notched-outline__leading"></div>
                                                                    <div class="mdc-notched-outline__notch">
                                                                        <label for="system_name" class="mdc-floating-label">系统名称</label>
                                                                    </div>
                                                                    <div class="mdc-notched-outline__trailing"></div>
                                                                </div>
                                                            </div>
                                                            <p class="mdc-typography--caption config-field-help">系统显示名称，将显示在页面标题和顶部应用栏</p>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-6">
                                                        <div class="mdc-form-field-group">
                                                            <div class="mdc-select mdc-select--outlined">
                                                                <div class="mdc-select__anchor">
                                                                    <span class="mdc-select__selected-text">中文</span>
                                                                    <span class="mdc-select__dropdown-icon">
                                                                        <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                                                            <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                                                            <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                                                        </svg>
                                                                    </span>
                                                                    <span class="mdc-notched-outline">
                                                                        <span class="mdc-notched-outline__leading"></span>
                                                                        <span class="mdc-notched-outline__notch">
                                                                            <span class="mdc-floating-label">语言</span>
                                                                        </span>
                                                                        <span class="mdc-notched-outline__trailing"></span>
                                                                    </span>
                                                                </div>
                                                                <div class="mdc-select__menu mdc-menu mdc-menu-surface">
                                                                    <ul class="mdc-list">
                                                                        <li class="mdc-list-item mdc-list-item--selected" data-value="zh_CN" aria-selected="true">
                                                                            <span class="mdc-list-item__text">中文</span>
                                                                        </li>
                                                                        <li class="mdc-list-item" data-value="en_US">
                                                                            <span class="mdc-list-item__text">English</span>
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                            <p class="mdc-typography--caption config-field-help">系统界面语言</p>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-6">
                                                        <div class="mdc-form-field-group">
                                                            <div class="mdc-select mdc-select--outlined">
                                                                <div class="mdc-select__anchor">
                                                                    <span class="mdc-select__selected-text">跟随系统</span>
                                                                    <span class="mdc-select__dropdown-icon">
                                                                        <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                                                            <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                                                            <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                                                        </svg>
                                                                    </span>
                                                                    <span class="mdc-notched-outline">
                                                                        <span class="mdc-notched-outline__leading"></span>
                                                                        <span class="mdc-notched-outline__notch">
                                                                            <span class="mdc-floating-label">主题</span>
                                                                        </span>
                                                                        <span class="mdc-notched-outline__trailing"></span>
                                                                    </span>
                                                                </div>
                                                                <div class="mdc-select__menu mdc-menu mdc-menu-surface">
                                                                    <ul class="mdc-list">
                                                                        <li class="mdc-list-item mdc-list-item--selected" data-value="auto" aria-selected="true">
                                                                            <span class="mdc-list-item__text">跟随系统</span>
                                                                        </li>
                                                                        <li class="mdc-list-item" data-value="light">
                                                                            <span class="mdc-list-item__text">浅色</span>
                                                                        </li>
                                                                        <li class="mdc-list-item" data-value="dark">
                                                                            <span class="mdc-list-item__text">深色</span>
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                            <p class="mdc-typography--caption config-field-help">系统默认主题</p>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-12">
                                                        <h3 class="mdc-typography--subtitle1">会话设置</h3>
                                                        <div class="mdc-form-field-group">
                                                            <div class="mdc-text-field mdc-text-field--outlined">
                                                                <input type="number" class="mdc-text-field__input" id="session_timeout" name="session_timeout" value="30">
                                                                <div class="mdc-notched-outline">
                                                                    <div class="mdc-notched-outline__leading"></div>
                                                                    <div class="mdc-notched-outline__notch">
                                                                        <label for="session_timeout" class="mdc-floating-label">会话超时(分钟)</label>
                                                                    </div>
                                                                    <div class="mdc-notched-outline__trailing"></div>
                                                                </div>
                                                            </div>
                                                            <p class="mdc-typography--caption config-field-help">用户会话超时时间，超过该时间无操作将自动退出</p>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-12">
                                                        <div class="mdc-form-field">
                                                            <div class="mdc-checkbox">
                                                                <input type="checkbox" class="mdc-checkbox__native-control" id="allow_concurrent_login" checked/>
                                                                <div class="mdc-checkbox__background">
                                                                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                                        <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                                    </svg>
                                                                    <div class="mdc-checkbox__mixedmark"></div>
                                                                </div>
                                                            </div>
                                                            <label for="allow_concurrent_login">允许同一用户多设备同时登录</label>
                                                        </div>
                                                        <p class="mdc-typography--caption config-field-help">启用后，同一用户可以在多个设备上同时登录</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- JSON视图 -->
                                        <div class="config-tab-panel" id="json-view">
                                            <div id="json-editor" class="config-editor"></div>
                                        </div>
                                        
                                        <!-- YAML视图 -->
                                        <div class="config-tab-panel" id="yaml-view">
                                            <div id="yaml-editor" class="config-editor"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>
    
    <!-- 对比配置对话框 -->
    <div class="mdc-dialog" id="compare-dialog">
        <div class="mdc-dialog__container">
            <div class="mdc-dialog__surface" role="dialog" aria-modal="true" aria-labelledby="compare-dialog-title" aria-describedby="compare-dialog-content">
                <h2 class="mdc-dialog__title" id="compare-dialog-title">对比配置版本</h2>
                <div class="mdc-dialog__content" id="compare-dialog-content">
                    <div class="compare-versions-container">
                        <div class="compare-version-selector">
                            <div class="mdc-select mdc-select--outlined">
                                <div class="mdc-select__anchor">
                                    <span class="mdc-select__selected-text">当前版本</span>
                                    <span class="mdc-select__dropdown-icon">
                                        <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                            <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                            <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                        </svg>
                                    </span>
                                    <span class="mdc-notched-outline">
                                        <span class="mdc-notched-outline__leading"></span>
                                        <span class="mdc-notched-outline__notch">
                                            <span class="mdc-floating-label">从版本</span>
                                        </span>
                                        <span class="mdc-notched-outline__trailing"></span>
                                    </span>
                                </div>
                                <div class="mdc-select__menu mdc-menu mdc-menu-surface">
                                    <ul class="mdc-list" id="compare-from-version-list">
                                        <!-- 版本选项将通过JavaScript动态加载 -->
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="compare-version-separator">
                            <span class="material-icons">compare_arrows</span>
                        </div>
                        
                        <div class="compare-version-selector">
                            <div class="mdc-select mdc-select--outlined">
                                <div class="mdc-select__anchor">
                                    <span class="mdc-select__selected-text">当前版本</span>
                                    <span class="mdc-select__dropdown-icon">
                                        <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                            <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                            <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                        </svg>
                                    </span>
                                    <span class="mdc-notched-outline">
                                        <span class="mdc-notched-outline__leading"></span>
                                        <span class="mdc-notched-outline__notch">
                                            <span class="mdc-floating-label">到版本</span>
                                        </span>
                                        <span class="mdc-notched-outline__trailing"></span>
                                    </span>
                                </div>
                                <div class="mdc-select__menu mdc-menu mdc-menu-surface">
                                    <ul class="mdc-list" id="compare-to-version-list">
                                        <!-- 版本选项将通过JavaScript动态加载 -->
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="compare-result-container">
                        <div id="diff-viewer" class="config-editor"></div>
                    </div>
                </div>
                <div class="mdc-dialog__actions">
                    <button type="button" class="mdc-button mdc-dialog__button" data-mdc-dialog-action="close">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">关闭</span>
                    </button>
                    <button type="button" class="mdc-button mdc-button--raised mdc-dialog__button" id="apply-old-version-button">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">应用旧版本</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="mdc-dialog__scrim"></div>
    </div>
    
    <!-- Material Design 3 脚本 -->
    <script src="{{ url_for('static', filename='js/material-components-web.min.js') }}"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/admin_material.js') }}"></script>
    <script src="{{ url_for('static', filename='js/websocket_client.js') }}"></script>
    <script src="{{ url_for('static', filename='js/configuration_manager.js') }}"></script>
</body>
</html> 