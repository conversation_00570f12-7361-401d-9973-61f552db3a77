#!/usr/bin/env python3
"""
OmniLink项目状态检查脚本
验证项目所有组件的完整性和配置正确性
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Any

class ProjectStatusChecker:
    """项目状态检查器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.issues = []
        self.warnings = []
        
    def log_issue(self, message: str):
        """记录问题"""
        self.issues.append(message)
        print(f"❌ {message}")
    
    def log_warning(self, message: str):
        """记录警告"""
        self.warnings.append(message)
        print(f"⚠️  {message}")
    
    def log_success(self, message: str):
        """记录成功"""
        print(f"✅ {message}")
    
    def check_file_exists(self, file_path: Path, description: str) -> bool:
        """检查文件是否存在"""
        if file_path.exists():
            self.log_success(f"{description}: {file_path}")
            return True
        else:
            self.log_issue(f"{description}缺失: {file_path}")
            return False
    
    def check_docker_files(self):
        """检查Docker相关文件"""
        print("\n🔍 检查Docker配置文件...")
        
        docker_files = [
            (self.project_root / "docker-compose.yaml", "Docker Compose配置"),
            (self.project_root / "deployment/dockerfiles/Dockerfile.main", "主服务器Dockerfile"),
            (self.project_root / "deployment/dockerfiles/Dockerfile.slave", "从服务器Dockerfile"),
            (self.project_root / "app.env", "环境变量文件")
        ]
        
        for file_path, description in docker_files:
            self.check_file_exists(file_path, description)
    
    def check_main_server_files(self):
        """检查主服务器文件"""
        print("\n🔍 检查主服务器文件...")
        
        main_files = [
            (self.project_root / "src/omnilink_main/main.py", "主程序入口"),
            (self.project_root / "src/omnilink_main/core/config.py", "配置模块"),
            (self.project_root / "src/omnilink_main/core/database.py", "数据库模块"),
            (self.project_root / "src/omnilink_main/core/security.py", "安全模块"),
            (self.project_root / "src/omnilink_main/core/redis.py", "Redis模块"),
            (self.project_root / "src/omnilink_main/initial_data.py", "初始数据模块"),
            (self.project_root / "main_server/requirements.txt", "Python依赖")
        ]
        
        for file_path, description in main_files:
            self.check_file_exists(file_path, description)
    
    def check_slave_server_files(self):
        """检查从服务器文件"""
        print("\n🔍 检查从服务器文件...")
        
        slave_files = [
            (self.project_root / "slave_server/main.py", "从服务器主程序"),
            (self.project_root / "slave_server/core/config.py", "从服务器配置"),
            (self.project_root / "slave_server/services/vh_service.py", "VirtualHere服务"),
            (self.project_root / "slave_server/services/device_monitor.py", "设备监控服务"),
            (self.project_root / "slave_server/requirements.txt", "Python依赖")
        ]
        
        for file_path, description in slave_files:
            self.check_file_exists(file_path, description)
    
    def check_database_files(self):
        """检查数据库相关文件"""
        print("\n🔍 检查数据库文件...")
        
        db_files = [
            (self.project_root / "alembic.ini", "Alembic配置"),
            (self.project_root / "alembic/env.py", "Alembic环境配置"),
            (self.project_root / "common/models", "数据模型目录"),
            (self.project_root / "common/schemas", "数据模式目录")
        ]
        
        for file_path, description in db_files:
            self.check_file_exists(file_path, description)
    
    def check_api_files(self):
        """检查API文件"""
        print("\n🔍 检查API文件...")
        
        api_files = [
            (self.project_root / "src/omnilink_main/api/v1/api.py", "API路由"),
            (self.project_root / "src/omnilink_main/api/v1/endpoints/auth.py", "认证端点"),
            (self.project_root / "src/omnilink_main/services/auth_service.py", "认证服务"),
            (self.project_root / "src/omnilink_main/services/user_service.py", "用户服务"),
            (self.project_root / "src/omnilink_main/dependencies/auth.py", "认证依赖")
        ]
        
        for file_path, description in api_files:
            self.check_file_exists(file_path, description)
    
    def check_ui_files(self):
        """检查UI文件"""
        print("\n🔍 检查Web UI文件...")
        
        ui_files = [
            (self.project_root / "src/omnilink_main/ui/main_ui.py", "UI主模块"),
            (self.project_root / "src/omnilink_main/ui/layout.py", "UI布局"),
            (self.project_root / "src/omnilink_main/ui/pages/home_page.py", "主页面"),
            (self.project_root / "src/omnilink_main/ui/pages/login_page.py", "登录页面"),
            (self.project_root / "src/omnilink_main/ui/auth_client.py", "认证客户端")
        ]
        
        for file_path, description in ui_files:
            self.check_file_exists(file_path, description)
    
    def check_environment_config(self):
        """检查环境配置"""
        print("\n🔍 检查环境配置...")
        
        env_file = self.project_root / "app.env"
        if env_file.exists():
            try:
                with open(env_file, 'r', encoding='utf-8') as f:
                    env_content = f.read()
                
                required_vars = [
                    'POSTGRES_SERVER',
                    'POSTGRES_USER', 
                    'POSTGRES_PASSWORD',
                    'POSTGRES_DB',
                    'SECRET_KEY',
                    'REDIS_HOST'
                ]
                
                for var in required_vars:
                    if var in env_content:
                        self.log_success(f"环境变量 {var} 已配置")
                    else:
                        self.log_issue(f"环境变量 {var} 缺失")
                        
            except Exception as e:
                self.log_issue(f"读取环境配置文件失败: {e}")
        else:
            self.log_issue("环境配置文件 app.env 不存在")
    
    def check_deployment_scripts(self):
        """检查部署脚本"""
        print("\n🔍 检查部署脚本...")
        
        scripts = [
            (self.project_root / "deploy.py", "Python部署脚本"),
            (self.project_root / "deployment/scripts/start_main_server.sh", "主服务器启动脚本"),
            (self.project_root / "deployment/scripts/start_slave_server.sh", "从服务器启动脚本")
        ]
        
        for file_path, description in scripts:
            self.check_file_exists(file_path, description)
    
    def generate_report(self):
        """生成检查报告"""
        print("\n" + "="*60)
        print("📊 项目状态检查报告")
        print("="*60)
        
        total_checks = len(self.issues) + len(self.warnings)
        if total_checks == 0:
            print("🎉 所有检查通过！项目状态良好。")
        else:
            if self.issues:
                print(f"\n❌ 发现 {len(self.issues)} 个问题:")
                for i, issue in enumerate(self.issues, 1):
                    print(f"  {i}. {issue}")
            
            if self.warnings:
                print(f"\n⚠️  发现 {len(self.warnings)} 个警告:")
                for i, warning in enumerate(self.warnings, 1):
                    print(f"  {i}. {warning}")
        
        print("\n" + "="*60)
        
        # 保存报告到文件
        report = {
            "timestamp": str(Path(__file__).stat().st_mtime),
            "issues": self.issues,
            "warnings": self.warnings,
            "status": "healthy" if not self.issues else "issues_found"
        }
        
        report_file = self.project_root / "project_status_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 详细报告已保存到: {report_file}")
    
    def run_all_checks(self):
        """运行所有检查"""
        print("🚀 开始OmniLink项目状态检查...")
        
        self.check_docker_files()
        self.check_main_server_files()
        self.check_slave_server_files()
        self.check_database_files()
        self.check_api_files()
        self.check_ui_files()
        self.check_environment_config()
        self.check_deployment_scripts()
        
        self.generate_report()

def main():
    """主函数"""
    checker = ProjectStatusChecker()
    checker.run_all_checks()

if __name__ == "__main__":
    main() 