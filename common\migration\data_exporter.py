"""
数据导出器类

用于导出系统数据，包括数据库、配置、设备信息等。
"""

import os
import json
import time
import logging
import shutil
import zipfile
from typing import Dict, List, Any, Optional, Set

logger = logging.getLogger(__name__)

class DataExporter:
    """数据导出器类，用于导出系统数据"""
    
    def __init__(self, export_dir: str, temp_dir: Optional[str] = None):
        """
        初始化数据导出器
        
        Args:
            export_dir: 导出目录
            temp_dir: 临时目录，如果不指定则使用系统临时目录
        """
        self.export_dir = export_dir
        self.temp_dir = temp_dir or os.path.join(export_dir, "temp")
        
        # 组件导出路径
        self.component_paths: Dict[str, str] = {}
        
        # 已导出组件
        self.exported_components: Set[str] = set()
        
        # 导出元数据
        self.metadata: Dict[str, Any] = {
            "export_time": time.time(),
            "components": {}
        }
        
    def prepare_export(self) -> bool:
        """
        准备导出环境
        
        Returns:
            是否成功准备
        """
        try:
            # 创建导出目录
            os.makedirs(self.export_dir, exist_ok=True)
            
            # 创建临时目录
            os.makedirs(self.temp_dir, exist_ok=True)
            
            # 创建组件目录
            component_dirs = ["database", "configurations", "devices", "users"]
            for component in component_dirs:
                component_path = os.path.join(self.temp_dir, component)
                os.makedirs(component_path, exist_ok=True)
                self.component_paths[component] = component_path
                
            logger.info(f"导出环境准备完成: {self.export_dir}")
            return True
            
        except Exception as e:
            logger.exception(f"准备导出环境出错: {e}")
            return False
            
    def export_database(self, db_config: Dict[str, Any]) -> bool:
        """
        导出数据库
        
        Args:
            db_config: 数据库配置
            
        Returns:
            是否成功导出
        """
        try:
            logger.info("开始导出数据库")
            component_path = self.component_paths["database"]
            
            # 在实际项目中，这里会根据数据库类型执行不同的导出操作
            # 例如使用mysqldump导出MySQL数据库
            db_type = db_config.get("type", "sqlite")
            db_name = db_config.get("name", "default")
            
            # 模拟导出过程
            time.sleep(1)
            
            # 创建导出的SQL文件
            db_export_file = os.path.join(component_path, f"{db_name}.sql")
            with open(db_export_file, "w") as f:
                f.write(f"-- 模拟的数据库导出内容 ({db_type})\n")
                f.write(f"-- 数据库: {db_name}\n")
                f.write("-- 导出时间: " + time.strftime("%Y-%m-%d %H:%M:%S") + "\n")
                
            # 更新元数据
            self.metadata["components"]["database"] = {
                "type": db_type,
                "name": db_name,
                "export_time": time.time(),
                "file": os.path.basename(db_export_file)
            }
            
            self.exported_components.add("database")
            logger.info(f"数据库导出完成: {db_export_file}")
            return True
            
        except Exception as e:
            logger.exception(f"导出数据库出错: {e}")
            return False
            
    def export_configurations(self, config_dirs: List[str]) -> bool:
        """
        导出配置文件
        
        Args:
            config_dirs: 配置目录列表
            
        Returns:
            是否成功导出
        """
        try:
            logger.info("开始导出配置文件")
            component_path = self.component_paths["configurations"]
            
            # 在实际项目中，这里会复制配置文件到导出目录
            # 此处为示例实现
            for i, config_dir in enumerate(config_dirs):
                target_dir = os.path.join(component_path, f"config_{i}")
                os.makedirs(target_dir, exist_ok=True)
                
                # 模拟复制配置文件
                config_file = os.path.join(target_dir, "config.json")
                with open(config_file, "w") as f:
                    f.write(json.dumps({
                        "name": f"配置 {i}",
                        "source_dir": config_dir,
                        "export_time": time.time()
                    }, indent=2, ensure_ascii=False))
                    
            # 更新元数据
            self.metadata["components"]["configurations"] = {
                "dirs": config_dirs,
                "export_time": time.time(),
                "count": len(config_dirs)
            }
            
            self.exported_components.add("configurations")
            logger.info(f"配置文件导出完成: {len(config_dirs)} 个目录")
            return True
            
        except Exception as e:
            logger.exception(f"导出配置文件出错: {e}")
            return False
            
    def export_devices(self, devices_data: Dict[str, Any]) -> bool:
        """
        导出设备信息
        
        Args:
            devices_data: 设备数据
            
        Returns:
            是否成功导出
        """
        try:
            logger.info("开始导出设备信息")
            component_path = self.component_paths["devices"]
            
            # 导出设备信息到JSON文件
            devices_file = os.path.join(component_path, "devices.json")
            with open(devices_file, "w", encoding="utf-8") as f:
                json.dump(devices_data, f, indent=2, ensure_ascii=False)
                
            # 更新元数据
            self.metadata["components"]["devices"] = {
                "export_time": time.time(),
                "count": len(devices_data.get("devices", [])),
                "file": os.path.basename(devices_file)
            }
            
            self.exported_components.add("devices")
            logger.info(f"设备信息导出完成: {devices_file}")
            return True
            
        except Exception as e:
            logger.exception(f"导出设备信息出错: {e}")
            return False
            
    def export_users(self, users_data: Dict[str, Any]) -> bool:
        """
        导出用户信息
        
        Args:
            users_data: 用户数据
            
        Returns:
            是否成功导出
        """
        try:
            logger.info("开始导出用户信息")
            component_path = self.component_paths["users"]
            
            # 导出用户信息到JSON文件
            users_file = os.path.join(component_path, "users.json")
            with open(users_file, "w", encoding="utf-8") as f:
                json.dump(users_data, f, indent=2, ensure_ascii=False)
                
            # 更新元数据
            self.metadata["components"]["users"] = {
                "export_time": time.time(),
                "count": len(users_data.get("users", [])),
                "file": os.path.basename(users_file)
            }
            
            self.exported_components.add("users")
            logger.info(f"用户信息导出完成: {users_file}")
            return True
            
        except Exception as e:
            logger.exception(f"导出用户信息出错: {e}")
            return False
            
    def create_export_package(self, output_file: Optional[str] = None) -> Optional[str]:
        """
        创建导出包
        
        Args:
            output_file: 输出文件路径，如果不指定则使用默认名称
            
        Returns:
            导出包文件路径，如果失败则返回None
        """
        try:
            if not self.exported_components:
                logger.error("没有任何组件被导出，无法创建导出包")
                return None
                
            # 保存元数据
            metadata_file = os.path.join(self.temp_dir, "metadata.json")
            with open(metadata_file, "w", encoding="utf-8") as f:
                json.dump(self.metadata, f, indent=2, ensure_ascii=False)
                
            # 创建导出包文件名
            if not output_file:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                output_file = os.path.join(self.export_dir, f"export_{timestamp}.zip")
                
            # 创建ZIP文件
            with zipfile.ZipFile(output_file, "w", zipfile.ZIP_DEFLATED) as zipf:
                # 添加元数据
                zipf.write(metadata_file, os.path.basename(metadata_file))
                
                # 添加组件文件
                for component, path in self.component_paths.items():
                    if component in self.exported_components:
                        for root, _, files in os.walk(path):
                            for file in files:
                                file_path = os.path.join(root, file)
                                # 计算ZIP内的相对路径
                                rel_path = os.path.relpath(file_path, self.temp_dir)
                                zipf.write(file_path, rel_path)
                                
            logger.info(f"导出包创建完成: {output_file}")
            return output_file
            
        except Exception as e:
            logger.exception(f"创建导出包出错: {e}")
            return None
            
    def cleanup(self):
        """清理临时文件"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"临时目录已清理: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"清理临时文件出错: {e}")
            
    def get_export_info(self) -> Dict[str, Any]:
        """
        获取导出信息
        
        Returns:
            导出信息字典
        """
        return {
            "export_dir": self.export_dir,
            "temp_dir": self.temp_dir,
            "exported_components": list(self.exported_components),
            "metadata": self.metadata
        } 
