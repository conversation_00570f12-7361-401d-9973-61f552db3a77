#!/usr/bin/env python3
"""
从服务器启动脚本
用于在Docker容器中启动从服务器服务
"""

import asyncio
import logging
import os
import signal
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from slave_server.main import main

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler("/var/log/slave_server.log") if os.path.exists("/var/log") else logging.NullHandler()
        ]
    )

def handle_signal(signum, frame):
    """处理系统信号"""
    logger = logging.getLogger(__name__)
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")
    # 设置事件来通知主循环退出
    asyncio.create_task(shutdown())

async def shutdown():
    """优雅关闭"""
    logger = logging.getLogger(__name__)
    logger.info("Shutting down slave server...")
    
    # 获取当前事件循环中的所有任务
    tasks = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
    
    if tasks:
        logger.info(f"Cancelling {len(tasks)} outstanding tasks...")
        for task in tasks:
            task.cancel()
        
        # 等待所有任务完成取消
        await asyncio.gather(*tasks, return_exceptions=True)
    
    logger.info("Slave server shutdown complete")

if __name__ == "__main__":
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, handle_signal)
    signal.signal(signal.SIGTERM, handle_signal)
    
    logger.info("Starting OmniLink Slave Server...")
    
    try:
        # 运行主程序
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logger.info("Slave server stopped") 