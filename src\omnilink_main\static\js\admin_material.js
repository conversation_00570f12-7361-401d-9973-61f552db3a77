/**
 * 主从服务器管理系统 - Material Design 交互脚本
 */

// MDC组件初始化
function initializeMDC() {
  // 顶部应用栏
  const topAppBar = document.querySelector('.mdc-top-app-bar');
  if (topAppBar) {
    mdc.topAppBar.MDCTopAppBar.attachTo(topAppBar);
  }
  
  // 侧边导航抽屉
  const drawer = document.querySelector('.mdc-drawer');
  if (drawer) {
    const mdcDrawer = mdc.drawer.MDCDrawer.attachTo(drawer);
    
    // 导航按钮点击事件
    const menuButton = document.querySelector('#menu-button');
    if (menuButton) {
      menuButton.addEventListener('click', () => {
        mdcDrawer.open = !mdcDrawer.open;
      });
    }
  }
  
  // 用户菜单
  const userMenu = document.querySelector('#user-menu');
  if (userMenu) {
    const mdcMenu = mdc.menu.MDCMenu.attachTo(userMenu);
    
    const userMenuButton = document.querySelector('#user-menu-button');
    if (userMenuButton) {
      userMenuButton.addEventListener('click', () => {
        mdcMenu.open = !mdcMenu.open;
      });
    }
    
    // 退出按钮事件
    const logoutButton = document.querySelector('#logout-button');
    if (logoutButton) {
      logoutButton.addEventListener('click', () => {
        window.location.href = logoutButton.getAttribute('data-logout-url') || '/auth/logout';
      });
    }
  }
  
  // 初始化卡片的波纹效果
  const cardActions = document.querySelectorAll('.mdc-card__primary-action');
  cardActions.forEach(cardAction => {
    mdc.ripple.MDCRipple.attachTo(cardAction);
  });
  
  // 初始化按钮的波纹效果
  const buttons = document.querySelectorAll('.mdc-button');
  buttons.forEach(button => {
    mdc.ripple.MDCRipple.attachTo(button);
  });
  
  // 初始化图标按钮的波纹效果
  const iconButtons = document.querySelectorAll('.mdc-icon-button');
  iconButtons.forEach(iconButton => {
    const ripple = mdc.ripple.MDCRipple.attachTo(iconButton);
    ripple.unbounded = true;
  });
}

// 仪表盘数据加载与更新
function loadDashboardData() {
  // 定义API端点
  const API_ENDPOINTS = {
    overview: '/api/v1/dashboard/overview',
    events: '/api/v1/dashboard/events',
    alerts: '/api/v1/dashboard/alerts',
    serverHealth: '/api/v1/dashboard/server_health',
    deviceStatus: '/api/v1/dashboard/device_status'
  };
  
  // 加载总览数据
  fetch(API_ENDPOINTS.overview)
    .then(response => response.json())
    .then(data => {
      if (data.code === 0) {
        updateOverviewCards(data.data);
      } else {
        console.error('加载总览数据失败:', data.message);
        // 使用默认数据作为回退
        updateOverviewCardsWithDefaults();
      }
    })
    .catch(error => {
      console.error('加载总览数据错误:', error);
      updateOverviewCardsWithDefaults();
    });
  
  // 加载最近事件
  fetch(API_ENDPOINTS.events)
    .then(response => response.json())
    .then(data => {
      if (data.code === 0) {
        updateRecentEvents(data.data);
      } else {
        console.error('加载事件数据失败:', data.message);
        updateRecentEventsWithDefaults();
      }
    })
    .catch(error => {
      console.error('加载事件数据错误:', error);
      updateRecentEventsWithDefaults();
    });
  
  // 加载活跃告警
  fetch(API_ENDPOINTS.alerts)
    .then(response => response.json())
    .then(data => {
      if (data.code === 0) {
        updateActiveAlerts(data.data);
      } else {
        console.error('加载告警数据失败:', data.message);
        updateActiveAlertsWithDefaults();
      }
    })
    .catch(error => {
      console.error('加载告警数据错误:', error);
      updateActiveAlertsWithDefaults();
    });
  
  // 加载并初始化图表数据
  initializeCharts();
}

// 更新总览卡片数据
function updateOverviewCards(data) {
  // 更新从服务器卡片
  document.getElementById('slaveCount').textContent = data.slaveServers.total || '0';
  document.getElementById('slaveOnline').textContent = data.slaveServers.online || '0';
  
  // 更新设备卡片
  document.getElementById('deviceCount').textContent = data.devices.total || '0';
  document.getElementById('deviceActive').textContent = data.devices.active || '0';
  
  // 更新用户卡片
  document.getElementById('userCount').textContent = data.users.total || '0';
  document.getElementById('userActive').textContent = data.users.active || '0';
  
  // 更新告警卡片
  document.getElementById('alertCount').textContent = data.alerts.total || '0';
  document.getElementById('criticalAlerts').textContent = data.alerts.critical || '0';
}

// 使用默认数据更新总览卡片
function updateOverviewCardsWithDefaults() {
  const defaultData = {
    slaveServers: { total: '12', online: '10' },
    devices: { total: '48', active: '32' },
    users: { total: '25', active: '8' },
    alerts: { total: '3', critical: '1' }
  };
  
  updateOverviewCards(defaultData);
}

// 更新最近事件列表
function updateRecentEvents(events) {
  const eventsTable = document.getElementById('recentEvents');
  if (!eventsTable) return;
  
  eventsTable.innerHTML = '';
  
  if (events.length === 0) {
    const row = document.createElement('tr');
    row.className = 'mdc-data-table__row';
    row.innerHTML = `
      <td class="mdc-data-table__cell" colspan="4">暂无事件</td>
    `;
    eventsTable.appendChild(row);
    return;
  }
  
  events.forEach(event => {
    const row = document.createElement('tr');
    row.className = 'mdc-data-table__row';
    
    let statusClass = '';
    if (event.status === 'success' || event.status === '成功') {
      statusClass = 'status-success';
    } else if (event.status === 'warning' || event.status === '警告') {
      statusClass = 'status-warning';
    } else if (event.status === 'error' || event.status === '错误') {
      statusClass = 'status-error';
    } else if (event.status === 'info' || event.status === '信息') {
      statusClass = 'status-info';
    }
    
    row.innerHTML = `
      <td class="mdc-data-table__cell">${event.time}</td>
      <td class="mdc-data-table__cell">${event.type}</td>
      <td class="mdc-data-table__cell">${event.event}</td>
      <td class="mdc-data-table__cell ${statusClass}">${event.status}</td>
    `;
    
    eventsTable.appendChild(row);
  });
}

// 使用默认数据更新最近事件列表
function updateRecentEventsWithDefaults() {
  const defaultEvents = [
    { time: '2023-09-20 14:25', type: '连接', event: '从服务器 SRV-01 已连接', status: '成功' },
    { time: '2023-09-20 14:20', type: '配置', event: '应用新配置到 SRV-02', status: '成功' },
    { time: '2023-09-20 14:15', type: '设备', event: 'USB设备 USBD-32 已断开', status: '警告' },
    { time: '2023-09-20 14:10', type: '系统', event: '自动备份已完成', status: '成功' },
    { time: '2023-09-20 14:05', type: '用户', event: '用户 admin 登录', status: '成功' }
  ];
  
  updateRecentEvents(defaultEvents);
}

// 更新活跃告警列表
function updateActiveAlerts(alerts) {
  const alertsTable = document.getElementById('activeAlerts');
  if (!alertsTable) return;
  
  alertsTable.innerHTML = '';
  
  if (alerts.length === 0) {
    const row = document.createElement('tr');
    row.className = 'mdc-data-table__row';
    row.innerHTML = `
      <td class="mdc-data-table__cell" colspan="4">暂无告警</td>
    `;
    alertsTable.appendChild(row);
    return;
  }
  
  alerts.forEach(alert => {
    const row = document.createElement('tr');
    row.className = 'mdc-data-table__row';
    
    let levelClass = '';
    if (alert.level === 'critical' || alert.level === '严重') {
      levelClass = 'level-critical';
    } else if (alert.level === 'warning' || alert.level === '警告') {
      levelClass = 'level-warning';
    } else if (alert.level === 'info' || alert.level === '信息') {
      levelClass = 'level-info';
    }
    
    row.innerHTML = `
      <td class="mdc-data-table__cell ${levelClass}">${alert.level}</td>
      <td class="mdc-data-table__cell">${alert.time}</td>
      <td class="mdc-data-table__cell">${alert.source}</td>
      <td class="mdc-data-table__cell">${alert.description}</td>
    `;
    
    alertsTable.appendChild(row);
  });
}

// 使用默认数据更新活跃告警列表
function updateActiveAlertsWithDefaults() {
  const defaultAlerts = [
    { level: '严重', time: '2023-09-20 13:45', source: 'SRV-03', description: '磁盘空间不足 (< 5%)' },
    { level: '警告', time: '2023-09-20 12:30', source: 'SRV-02', description: 'CPU使用率持续高于85%' },
    { level: '信息', time: '2023-09-20 10:15', source: 'SRV-01', description: '多次失败登录尝试' }
  ];
  
  updateActiveAlerts(defaultAlerts);
}

// 初始化图表
function initializeCharts() {
  // 健康状态趋势图
  const healthTrendChart = document.getElementById('healthTrendChart');
  if (healthTrendChart) {
    fetch('/api/v1/dashboard/server_health')
      .then(response => response.json())
      .then(data => {
        if (data.code === 0) {
          renderHealthTrendChart(healthTrendChart, data.data);
        } else {
          console.error('加载服务器健康数据失败:', data.message);
          renderHealthTrendChartWithDefaults(healthTrendChart);
        }
      })
      .catch(error => {
        console.error('加载服务器健康数据错误:', error);
        renderHealthTrendChartWithDefaults(healthTrendChart);
      });
  }
  
  // 设备连接状态图
  const deviceStatusChart = document.getElementById('deviceStatusChart');
  if (deviceStatusChart) {
    fetch('/api/v1/dashboard/device_status')
      .then(response => response.json())
      .then(data => {
        if (data.code === 0) {
          renderDeviceStatusChart(deviceStatusChart, data.data);
        } else {
          console.error('加载设备状态数据失败:', data.message);
          renderDeviceStatusChartWithDefaults(deviceStatusChart);
        }
      })
      .catch(error => {
        console.error('加载设备状态数据错误:', error);
        renderDeviceStatusChartWithDefaults(deviceStatusChart);
      });
  }
}

// 渲染健康状态趋势图
function renderHealthTrendChart(canvas, data) {
  new Chart(canvas.getContext('2d'), {
    type: 'line',
    data: {
      labels: data.labels,
      datasets: data.datasets
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          max: 100
        }
      }
    }
  });
}

// 使用默认数据渲染健康状态趋势图
function renderHealthTrendChartWithDefaults(canvas) {
  const defaultData = {
    labels: ['00:00', '06:00', '12:00', '18:00', '00:00'],
    datasets: [
      {
        label: 'CPU使用率',
        data: [25, 30, 45, 40, 35],
        borderColor: '#1976D2',
        backgroundColor: 'rgba(25, 118, 210, 0.1)',
        tension: 0.3
      },
      {
        label: '内存使用率',
        data: [40, 45, 55, 60, 50],
        borderColor: '#7B1FA2',
        backgroundColor: 'rgba(123, 31, 162, 0.1)',
        tension: 0.3
      }
    ]
  };
  
  renderHealthTrendChart(canvas, defaultData);
}

// 渲染设备连接状态图
function renderDeviceStatusChart(canvas, data) {
  new Chart(canvas.getContext('2d'), {
    type: 'doughnut',
    data: {
      labels: data.labels,
      datasets: [{
        data: data.values,
        backgroundColor: data.colors || [
          '#4CAF50',
          '#2196F3',
          '#9E9E9E',
          '#F44336'
        ]
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'right',
        }
      }
    }
  });
}

// 使用默认数据渲染设备连接状态图
function renderDeviceStatusChartWithDefaults(canvas) {
  const defaultData = {
    labels: ['已连接', '可用', '离线', '错误'],
    values: [32, 16, 8, 4],
    colors: [
      '#4CAF50',
      '#2196F3',
      '#9E9E9E',
      '#F44336'
    ]
  };
  
  renderDeviceStatusChart(canvas, defaultData);
}

// 自动刷新定时器
let refreshTimer = null;

// 设置自动刷新（默认60秒）
function setupAutoRefresh(interval = 60000) {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  
  refreshTimer = setInterval(() => {
    loadDashboardData();
  }, interval);
  
  // 手动刷新按钮
  const refreshButton = document.getElementById('refresh-button');
  if (refreshButton) {
    refreshButton.addEventListener('click', () => {
      loadDashboardData();
    });
  }
}

// 响应主题切换
function setupThemeToggle() {
  const themeToggle = document.getElementById('theme-toggle');
  if (themeToggle) {
    themeToggle.addEventListener('click', () => {
      document.body.classList.toggle('mdc-theme--dark');
      
      // 保存主题偏好到本地存储
      const isDarkMode = document.body.classList.contains('mdc-theme--dark');
      localStorage.setItem('darkMode', isDarkMode);
      
      // 重新渲染图表以适应新主题
      initializeCharts();
    });
  }
  
  // 初始化主题
  const savedDarkMode = localStorage.getItem('darkMode') === 'true';
  if (savedDarkMode) {
    document.body.classList.add('mdc-theme--dark');
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  // 初始化Material组件
  initializeMDC();
  
  // 加载仪表盘数据
  loadDashboardData();
  
  // 设置自动刷新
  setupAutoRefresh();
  
  // 设置主题切换
  setupThemeToggle();
}); 