from sqlalchemy import (
    Column, Integer, String, TIMESTAMP, ForeignKey
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from common.database.base_class import Base

class Organization(Base):
    __tablename__ = 'organizations'
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True)
    parent_id = Column(Integer, ForeignKey('organizations.id'), nullable=True)
    created_at = Column(TIMESTAMP, server_default=func.now())
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now())
    
    parent = relationship('Organization', remote_side=[id], back_populates='children')
    children = relationship('Organization', back_populates='parent')
    
    users = relationship('User', back_populates='organization')
    slave_servers = relationship('SlaveServer', back_populates='organization') 