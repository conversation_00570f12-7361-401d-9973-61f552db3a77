/* 通用样式 */
:root {
    --primary-color: #4285f4;
    --secondary-color: #34a853;
    --tertiary-color: #fbbc05;
    --danger-color: #ea4335;
    --dark-color: #212121;
    --light-color: #f5f5f5;
    --gray-color: #9e9e9e;
    --white-color: #ffffff;
    --shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    --border-radius: 4px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', 'Noto Sans SC', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
}

a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* 布局 */
header {
    background-color: var(--primary-color);
    color: var(--white-color);
    padding: 1rem 2rem;
    box-shadow: var(--shadow);
}

header h1 {
    margin-bottom: 0.5rem;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 1.5rem;
}

nav ul li a {
    color: var(--white-color);
}

main {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

section {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

section h2 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    border-bottom: 1px solid var(--gray-color);
    padding-bottom: 0.5rem;
}

footer {
    background-color: var(--dark-color);
    color: var(--white-color);
    text-align: center;
    padding: 1rem;
    margin-top: 2rem;
}

/* 卡片 */
.stat-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.card {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    text-align: center;
    border-top: 3px solid var(--primary-color);
}

.card h3 {
    margin-bottom: 1rem;
}

.card .number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.card .status {
    font-size: 1.5rem;
    font-weight: bold;
}

.card .status.online {
    color: var(--secondary-color);
}

.card .status.offline {
    color: var(--danger-color);
}

.card .status.good {
    color: var(--secondary-color);
}

.card .status.warning {
    color: var(--tertiary-color);
}

.card .status.error {
    color: var(--danger-color);
}

/* 表格 */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

th, td {
    border: 1px solid var(--gray-color);
    padding: 0.75rem;
    text-align: left;
}

th {
    background-color: var(--primary-color);
    color: var(--white-color);
}

tr:nth-child(even) {
    background-color: var(--light-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .stat-cards {
        grid-template-columns: 1fr;
    }
} 