@echo off
chcp 65001 >nul
echo ================================
echo Playwright MCP 双缓冲启动脚本
echo ================================
echo.

echo [1/3] 启动主要 Playwright MCP 服务器 (端口 8931)...
start "Playwright-Primary" cmd /k "npx @playwright/mcp@0.0.28 --port 8931 --headless"
timeout /t 3 /nobreak >nul

echo [2/3] 启动备用 Playwright MCP 服务器 (端口 8932)...
start "Playwright-Secondary" cmd /k "npx @playwright/mcp@0.0.28 --port 8932 --headless"
timeout /t 3 /nobreak >nul

echo [3/3] 启动 MCP 桥接服务器 (端口 12307)...
start "MCP-Bridge" cmd /k "node -e \"const http = require('http'); const server = http.createServer((req, res) => { res.writeHead(200, {'Content-Type': 'text/event-stream', 'Access-Control-Allow-Origin': '*'}); res.write('data: {\\\"type\\\":\\\"connection\\\",\\\"status\\\":\\\"connected\\\"}\\n\\n'); setInterval(() => { res.write('data: {\\\"type\\\":\\\"heartbeat\\\",\\\"timestamp\\\":' + Date.now() + '}\\n\\n'); }, 30000); }); server.listen(12307, () => console.log('MCP Bridge running on port 12307'));\""

echo.
echo ✅ 所有服务已启动！
echo.
echo 服务状态:
echo - 主要 Playwright MCP: http://localhost:8931
echo - 备用 Playwright MCP: http://localhost:8932  
echo - MCP 桥接服务: http://localhost:12307
echo - Chrome MCP (外部): http://localhost:12306
echo.
echo 按任意键退出...
pause >nul
