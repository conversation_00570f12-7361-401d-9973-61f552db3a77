import logging
from passlib.context import CryptContext

logger = logging.getLogger(__name__)

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verifies a plain password against a hashed password.
    """
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error(f"Error during password verification: {e}", exc_info=True)
        return False

def get_password_hash(password: str) -> str:
    """
    Hashes a plain password.
    """
    try:
        return pwd_context.hash(password)
    except Exception as e:
        logger.error(f"Error during password hashing: {e}", exc_info=True)
        raise 
