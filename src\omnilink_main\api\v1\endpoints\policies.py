from typing import List, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from common.models.user import User
from common.schemas.policy_schema import PolicyRule, PolicyRuleCreate, PolicyRuleUpdate
from src.omnilink_main.services import policy_service
from src.omnilink_main.dependencies.db import get_db
from src.omnilink_main.dependencies.auth import get_current_active_user, PermissionChecker

router = APIRouter()

@router.get("/", response_model=List[PolicyRule], dependencies=[Depends(PermissionChecker("policies.view"))])
async def read_policy_rules(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user)
) -> List[PolicyRule]:
    """
    Retrieve policy rules.
    """
    return await policy_service.get_multi(db, skip=skip, limit=limit)

@router.post("/", response_model=PolicyRule, status_code=status.HTTP_201_CREATED, dependencies=[Depends(PermissionChecker("policies.create"))])
async def create_policy_rule(
    *,
    db: AsyncSession = Depends(get_db),
    policy_in: PolicyRuleCreate,
    current_user: User = Depends(get_current_active_user)
) -> PolicyRule:
    """
    Create new policy rule.
    """
    policy = await policy_service.create(db, obj_in=policy_in)
    return policy

@router.get("/{id}", response_model=PolicyRule, dependencies=[Depends(PermissionChecker("policies.view"))])
async def read_policy_rule(
    *,
    db: AsyncSession = Depends(get_db),
    id: int,
    current_user: User = Depends(get_current_active_user)
) -> PolicyRule:
    """
    Get policy rule by ID.
    """
    policy = await policy_service.get(db, id=id)
    if not policy:
        raise HTTPException(status_code=404, detail="Policy rule not found")
    return policy

@router.put("/{id}", response_model=PolicyRule, dependencies=[Depends(PermissionChecker("policies.edit"))])
async def update_policy_rule(
    *,
    db: AsyncSession = Depends(get_db),
    id: int,
    policy_in: PolicyRuleUpdate,
    current_user: User = Depends(get_current_active_user)
) -> PolicyRule:
    """
    Update a policy rule.
    """
    policy = await policy_service.get(db, id=id)
    if not policy:
        raise HTTPException(status_code=404, detail="Policy rule not found")
    policy = await policy_service.update(db, db_obj=policy, obj_in=policy_in)
    return policy

@router.delete("/{id}", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(PermissionChecker("policies.delete"))])
async def delete_policy_rule(
    *,
    db: AsyncSession = Depends(get_db),
    id: int,
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete a policy rule.
    """
    policy = await policy_service.get(db, id=id)
    if not policy:
        raise HTTPException(status_code=404, detail="Policy rule not found")
    await policy_service.remove(db, id=id)
    return