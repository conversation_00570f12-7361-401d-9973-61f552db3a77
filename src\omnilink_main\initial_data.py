# This file will contain logic to create initial database data.

import asyncio
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from src.omnilink_main.core.config import settings
from src.omnilink_main.core.database import db_engine, AsyncSessionLocal, Base
from common.schemas.user_schema import UserCreate
from common.schemas.role_schema import RoleCreate, RoleUpdate
from common.models.user import User
from common.models.role import Role, user_role_association
from src.omnilink_main.core.security import get_password_hash
from common.models.slave_server import SlaveServer
import secrets

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Predefined roles with comprehensive permissions
PREDEFINED_ROLES = [
    {
        "name": "SuperAdmin",
        "description": "完全系统管理员权限，拥有所有系统功能的访问权限",
        "permissions": [
            "admin.*",
            "user.*", 
            "device.*",
            "slave.*",
            "role.*",
            "audit.*",
            "system.*"
        ]
    },
    {
        "name": "DeviceAdmin", 
        "description": "设备管理员，可以管理设备和从服务器",
        "permissions": [
            "device.view",
            "device.connect", 
            "device.disconnect",
            "device.manage",
            "slave.view",
            "slave.manage"
        ]
    },
    {
        "name": "User",
        "description": "普通用户，可以查看和连接被授权的设备",
        "permissions": [
            "device.view",
            "device.connect",
            "device.disconnect",
            "user.profile.view",
            "user.profile.edit"
        ]
    },
    {
        "name": "Auditor",
        "description": "审计员，可以查看系统日志和审计信息",
        "permissions": [
            "audit.view",
            "device.view",
            "user.view",
            "slave.view"
        ]
    }
]

# Generate secure API key for default slave
DEFAULT_SLAVE_API_KEY = secrets.token_urlsafe(32)

async def create_roles(session: AsyncSession):
    """Creates predefined roles if they don't exist."""
    logger.info("检查并创建系统角色...")
    for role_data in PREDEFINED_ROLES:
        result = await session.execute(select(Role).filter(Role.name == role_data["name"]))
        existing_role = result.scalars().first()
        
        if not existing_role:
            logger.info(f"创建角色: {role_data['name']}")
            role = Role(
                name=role_data["name"],
                description=role_data["description"],
                permissions=role_data["permissions"],
                is_system_role=True
            )
            session.add(role)
        else:
            # Update existing role permissions if they differ
            if set(existing_role.permissions) != set(role_data["permissions"]):
                logger.info(f"更新角色权限: {role_data['name']}")
                existing_role.permissions = role_data["permissions"]
                existing_role.description = role_data["description"]
                
    await session.commit()
    logger.info("系统角色检查/创建完成")


async def create_initial_superuser(session: AsyncSession):
    """Creates the initial superuser using credentials from settings."""
    # Use credentials from OED.md: firefly/bro2fhz12
    admin_username = "firefly"
    admin_email = "<EMAIL>"
    admin_password = "bro2fhz12"  # Production credential from OED.md
    
    # Check if user already exists
    result = await session.execute(select(User).filter(User.username == admin_username))
    existing_user = result.scalars().first()

    if not existing_user:
        logger.info(f"创建初始超级管理员用户: {admin_username}")
        hashed_password = get_password_hash(admin_password)
        
        # Fetch the SuperAdmin role
        admin_role_result = await session.execute(select(Role).filter(Role.name == "SuperAdmin"))
        admin_role = admin_role_result.scalars().first()
        
        if not admin_role:
             logger.error("SuperAdmin角色未找到，无法创建超级管理员用户")
             return

        new_user = User(
            username=admin_username,
            email=admin_email,
            hashed_password=hashed_password,
            is_active=True,
            is_superuser=True
        )
        new_user.roles.append(admin_role)
        session.add(new_user)
        await session.commit()
        logger.info(f"初始超级管理员用户 '{admin_username}' 创建成功")
    else:
        logger.info(f"超级管理员用户 '{admin_username}' 已存在")


async def create_default_slave_server(session: AsyncSession):
    """Creates a default slave server for testing if it doesn't exist."""
    slave_id = "slave-server-01"
    result = await session.execute(select(SlaveServer).filter(SlaveServer.server_id == slave_id))
    existing_slave = result.scalars().first()

    if not existing_slave:
        logger.info(f"创建默认从服务器: {slave_id}")
        new_slave = SlaveServer(
            server_id=slave_id,
            name="默认从服务器",
            description="系统自动创建的默认从服务器，用于开发和测试",
            status="offline",
            api_key=DEFAULT_SLAVE_API_KEY
        )
        session.add(new_slave)
        await session.commit()
        logger.info(f"默认从服务器 '{slave_id}' 创建成功，API密钥: {DEFAULT_SLAVE_API_KEY}")
    else:
        logger.info(f"默认从服务器 '{slave_id}' 已存在")


async def init_initial_data():
    """
    Main function to initialize data. It's called during application startup.
    It assumes the database and tables are already created.
    """
    logger.info("开始初始数据填充检查...")
    
    try:
        async with AsyncSessionLocal() as session:
            await create_roles(session)
            await create_initial_superuser(session)
            await create_default_slave_server(session)
        
        logger.info("初始数据填充检查完成")
    except Exception as e:
        logger.error(f"初始数据填充失败: {e}")
        raise
