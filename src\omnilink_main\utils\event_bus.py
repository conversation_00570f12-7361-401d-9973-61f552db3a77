"""
事件总线模块

提供事件发布/订阅机制，用于系统内不同模块之间的解耦通信。
事件总线允许一个模块发布事件，其他多个模块可以订阅并处理这些事件。
"""

import logging
import threading
import asyncio
import inspect
from typing import Dict, List, Any, Callable, Set, Union, Optional

# 配置日志
logger = logging.getLogger('utils.event_bus')

class EventBus:
    """事件总线类，提供事件发布/订阅功能"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(EventBus, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self):
        """初始化事件总线"""
        if self._initialized:
            return
            
        # 订阅者字典 {事件名: [回调函数列表]}
        self._subscribers: Dict[str, List[Callable]] = {}
        
        # 已注册的事件类型集合
        self._event_types: Set[str] = set()
        
        # 锁，用于线程安全
        self._bus_lock = threading.RLock()
        
        # 标记为已初始化
        self._initialized = True
        
        logger.debug("事件总线初始化完成")
    
    def register_event(self, event_type: str, description: Optional[str] = None) -> None:
        """注册事件类型
        
        参数:
            event_type: 事件类型名称
            description: 事件描述
        """
        with self._bus_lock:
            if event_type not in self._event_types:
                self._event_types.add(event_type)
                self._subscribers[event_type] = []
                logger.debug(f"已注册事件类型: {event_type}{' - ' + description if description else ''}")
    
    def subscribe(self, event_type: str, callback: Callable) -> None:
        """订阅事件
        
        参数:
            event_type: 事件类型名称
            callback: 事件处理回调函数
        """
        with self._bus_lock:
            # 如果事件类型不存在，则注册它
            if event_type not in self._event_types:
                self.register_event(event_type)
            
            # 添加回调函数到订阅者列表
            if callback not in self._subscribers[event_type]:
                self._subscribers[event_type].append(callback)
                logger.debug(f"添加订阅者: {event_type} -> {callback.__qualname__}")
    
    def unsubscribe(self, event_type: str, callback: Callable) -> None:
        """取消订阅事件
        
        参数:
            event_type: 事件类型名称
            callback: 事件处理回调函数
        """
        with self._bus_lock:
            if event_type in self._subscribers and callback in self._subscribers[event_type]:
                self._subscribers[event_type].remove(callback)
                logger.debug(f"移除订阅者: {event_type} -> {callback.__qualname__}")
    
    def publish(self, event_type: str, event_data: Any = None) -> None:
        """发布事件（同步）
        
        参数:
            event_type: 事件类型名称
            event_data: 事件数据
        """
        subscribers = []
        
        # 获取订阅者列表（使用锁来保证线程安全）
        with self._bus_lock:
            if event_type in self._subscribers:
                subscribers = self._subscribers[event_type].copy()
        
        # 如果没有订阅者，直接返回
        if not subscribers:
            logger.debug(f"事件 {event_type} 没有订阅者")
            return
        
        # 调用所有订阅者的回调函数
        for callback in subscribers:
            try:
                # 检查回调是否为协程函数
                if inspect.iscoroutinefunction(callback):
                    # 协程函数，创建任务
                    self._ensure_event_loop_and_run(callback, event_type, event_data)
                else:
                    # 普通函数，直接调用
                    callback(event_type, event_data)
            except Exception as e:
                logger.error(f"调用事件处理函数时出错: {event_type} -> {callback.__qualname__}: {str(e)}")
    
    async def publish_async(self, event_type: str, event_data: Any = None) -> None:
        """发布事件（异步）
        
        参数:
            event_type: 事件类型名称
            event_data: 事件数据
        """
        subscribers = []
        
        # 获取订阅者列表
        with self._bus_lock:
            if event_type in self._subscribers:
                subscribers = self._subscribers[event_type].copy()
        
        # 如果没有订阅者，直接返回
        if not subscribers:
            logger.debug(f"事件 {event_type} 没有订阅者")
            return
        
        # 创建所有回调的任务
        tasks = []
        
        for callback in subscribers:
            try:
                if inspect.iscoroutinefunction(callback):
                    # 协程函数，创建任务
                    task = asyncio.create_task(callback(event_type, event_data))
                    tasks.append(task)
                else:
                    # 普通函数，直接调用
                    callback(event_type, event_data)
            except Exception as e:
                logger.error(f"调用事件处理函数时出错: {event_type} -> {callback.__qualname__}: {str(e)}")
        
        # 等待所有异步任务完成
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    def _ensure_event_loop_and_run(self, callback: Callable, event_type: str, event_data: Any) -> None:
        """确保有事件循环并运行协程
        
        参数:
            callback: 协程函数
            event_type: 事件类型
            event_data: 事件数据
        """
        try:
            # 获取当前线程的事件循环
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                # 当前线程没有事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # 如果事件循环正在运行，创建任务
            if loop.is_running():
                asyncio.run_coroutine_threadsafe(callback(event_type, event_data), loop)
            else:
                # 否则，直接运行协程
                loop.run_until_complete(callback(event_type, event_data))
        except Exception as e:
            logger.error(f"运行异步事件处理函数时出错: {event_type} -> {callback.__qualname__}: {str(e)}")
    
    def get_subscribers(self, event_type: str) -> List[Callable]:
        """获取指定事件类型的所有订阅者
        
        参数:
            event_type: 事件类型名称
            
        返回:
            List[Callable]: 订阅者列表
        """
        with self._bus_lock:
            if event_type in self._subscribers:
                return self._subscribers[event_type].copy()
            return []
    
    def get_event_types(self) -> Set[str]:
        """获取所有已注册的事件类型
        
        返回:
            Set[str]: 事件类型集合
        """
        with self._bus_lock:
            return self._event_types.copy()
    
    def clear_all_subscriptions(self) -> None:
        """清除所有订阅关系"""
        with self._bus_lock:
            for event_type in self._subscribers:
                self._subscribers[event_type] = []
            logger.debug("已清除所有订阅关系") 
