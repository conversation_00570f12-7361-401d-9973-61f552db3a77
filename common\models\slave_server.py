from sqlalchemy import (
    Column, Integer, String, TIMESTAMP, Foreign<PERSON>ey, Text
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from common.database.base_class import Base

class SlaveServer(Base):
    __tablename__ = 'slave_servers'
    id = Column(Integer, primary_key=True, index=True)
    server_id = Column(String(100), unique=True, index=True, nullable=False)
    api_key = Column(String(255), unique=True, index=True, nullable=False)
    name = Column(String(100))
    description = Column(Text)
    ip_address = Column(String(50))
    port = Column(Integer)
    status = Column(String(20), nullable=False)
    version = Column(String(20))
    last_seen = Column(TIMESTAMP)
    organization_id = Column(Integer, ForeignKey('organizations.id'))
    created_at = Column(TIMESTAMP, server_default=func.now())
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now())

    organization = relationship('Organization', back_populates='slave_servers')
    devices = relationship('Device', back_populates='slave_server') 