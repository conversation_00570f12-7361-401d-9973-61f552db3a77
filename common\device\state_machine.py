"""
Device State Machine - Advanced state management for devices.

This module provides a flexible state machine implementation for managing
device states and transitions, with support for validation rules, event
handling, and transition history.
"""

import asyncio
import logging
import time
from datetime import datetime
from enum import Enum, auto
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Type, Union
from dataclasses import dataclass, field

from common.device.models import DeviceConnectionState, DeviceShareState
from common.models.device import Device

logger = logging.getLogger(__name__)


class TransitionResult(Enum):
    """Result of a state transition attempt."""
    SUCCESS = auto()
    INVALID_TRANSITION = auto()
    VALIDATION_FAILED = auto()
    ACTION_FAILED = auto()
    ALREADY_IN_STATE = auto()
    CANCELLED = auto()


@dataclass
class Transition:
    """Represents a transition between states."""
    from_state: Union[DeviceConnectionState, DeviceShareState]
    to_state: Union[DeviceConnectionState, DeviceShareState]
    timestamp: float = field(default_factory=time.time)
    result: TransitionResult = TransitionResult.SUCCESS
    reason: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "from_state": self.from_state.name if isinstance(self.from_state, Enum) else self.from_state,
            "to_state": self.to_state.name if isinstance(self.to_state, Enum) else self.to_state,
            "timestamp": self.timestamp,
            "result": self.result.name,
            "reason": self.reason,
            "metadata": self.metadata
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Transition':
        """Create from dictionary representation."""
        # Convert state strings to enum values if needed
        from_state = data.get("from_state")
        to_state = data.get("to_state")
        
        # Handle conversion from string to enum for from_state
        if isinstance(from_state, str):
            try:
                if from_state in [s.name for s in DeviceConnectionState]:
                    from_state = DeviceConnectionState[from_state]
                elif from_state in [s.name for s in DeviceShareState]:
                    from_state = DeviceShareState[from_state]
            except (KeyError, ValueError):
                logger.warning(f"Unknown state name: {from_state}")
        
        # Handle conversion from string to enum for to_state
        if isinstance(to_state, str):
            try:
                if to_state in [s.name for s in DeviceConnectionState]:
                    to_state = DeviceConnectionState[to_state]
                elif to_state in [s.name for s in DeviceShareState]:
                    to_state = DeviceShareState[to_state]
            except (KeyError, ValueError):
                logger.warning(f"Unknown state name: {to_state}")
        
        # Convert result string to enum
        result = data.get("result", TransitionResult.SUCCESS.name)
        if isinstance(result, str):
            try:
                result = TransitionResult[result]
            except (KeyError, ValueError):
                logger.warning(f"Unknown result name: {result}")
                result = TransitionResult.SUCCESS
        
        return cls(
            from_state=from_state,
            to_state=to_state,
            timestamp=data.get("timestamp", time.time()),
            result=result,
            reason=data.get("reason", ""),
            metadata=data.get("metadata", {})
        )


class TransitionValidator:
    """Interface for validating state transitions."""
    
    def validate(self, device: Device, from_state: Union[DeviceConnectionState, DeviceShareState], 
                to_state: Union[DeviceConnectionState, DeviceShareState], 
                metadata: Dict[str, Any] = None) -> Tuple[bool, str]:
        """
        Validate a state transition.
        
        Args:
            device: Device being transitioned
            from_state: Current state
            to_state: Target state
            metadata: Additional data for validation
            
        Returns:
            Tuple of (is_valid, reason)
        """
        # 实现基础验证逻辑
        if not device:
            return False, "设备对象不能为空"
        
        if not device.device_uuid:
            return False, "设备UUID不能为空"
        
        # 检查设备是否处于可操作状态
        if hasattr(device, 'status') and device.status == DeviceStatus.ERROR:
            return False, "设备处于错误状态，无法进行状态转换"
        
        # 检查状态转换的合理性
        if from_state == to_state:
            return False, "源状态和目标状态不能相同"
        
        # 连接状态特定验证
        if isinstance(from_state, DeviceConnectionState) and isinstance(to_state, DeviceConnectionState):
            # 检查连接状态转换的合理性
            if from_state == DeviceConnectionState.DISCONNECTED and to_state == DeviceConnectionState.SHARED:
                return False, "设备必须先连接才能共享"
            
            if from_state == DeviceConnectionState.ERROR and to_state not in [DeviceConnectionState.DISCONNECTED, DeviceConnectionState.CONNECTING]:
                return False, "错误状态的设备只能断开连接或重新连接"
        
        # 共享状态特定验证
        if isinstance(from_state, DeviceShareState) and isinstance(to_state, DeviceShareState):
            # 检查共享状态转换的合理性
            if from_state == DeviceShareState.NOT_SHARED and to_state == DeviceShareState.SHARED:
                # 检查设备是否已连接
                if hasattr(device, 'state') and device.state.connection_state != DeviceConnectionState.CONNECTED:
                    return False, "设备必须处于连接状态才能共享"
        
        # 检查元数据中的特殊要求
        if metadata:
            # 检查是否需要管理员权限
            if metadata.get('require_admin', False):
                user_role = metadata.get('user_role', '')
                if user_role not in ['super_admin', 'company_admin']:
                    return False, "此操作需要管理员权限"
            
            # 检查设备是否被其他用户占用
            if metadata.get('check_exclusive', False):
                current_user = metadata.get('current_user', '')
                device_owner = getattr(device, 'current_user', '')
                if device_owner and device_owner != current_user:
                    return False, f"设备正被用户 {device_owner} 使用"
        
        return True, "验证通过"


class TransitionAction:
    """Interface for actions to execute during state transitions."""
    
    async def execute(self, device: Device, from_state: Union[DeviceConnectionState, DeviceShareState], 
                     to_state: Union[DeviceConnectionState, DeviceShareState], 
                     metadata: Dict[str, Any] = None) -> Tuple[bool, str]:
        """
        Execute an action during a state transition.
        
        Args:
            device: Device being transitioned
            from_state: Current state
            to_state: Target state
            metadata: Additional data for the action
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # 根据状态转换类型执行相应操作
            if isinstance(from_state, DeviceConnectionState) and isinstance(to_state, DeviceConnectionState):
                return await self._execute_connection_action(device, from_state, to_state, metadata)
            elif isinstance(from_state, DeviceShareState) and isinstance(to_state, DeviceShareState):
                return await self._execute_share_action(device, from_state, to_state, metadata)
            else:
                return False, "不支持的状态转换类型"
        except Exception as e:
            logger.error(f"执行状态转换操作时发生错误: {e}")
            return False, f"操作失败: {str(e)}"
    
    async def _execute_connection_action(self, device: Device, from_state: DeviceConnectionState, 
                                       to_state: DeviceConnectionState, metadata: Dict[str, Any] = None) -> Tuple[bool, str]:
        """执行连接状态转换操作"""
        if to_state == DeviceConnectionState.CONNECTING:
            # 开始连接设备
            logger.info(f"开始连接设备 {device.device_uuid}")
            # 这里可以调用实际的设备连接逻辑
            return True, "开始连接设备"
        
        elif to_state == DeviceConnectionState.CONNECTED:
            # 设备连接成功
            logger.info(f"设备 {device.device_uuid} 连接成功")
            # 更新设备的最后连接时间
            if hasattr(device, 'last_connected'):
                device.last_connected = time.time()
            return True, "设备连接成功"
        
        elif to_state == DeviceConnectionState.DISCONNECTED:
            # 断开设备连接
            logger.info(f"断开设备 {device.device_uuid} 的连接")
            # 清理连接相关资源
            if hasattr(device, 'current_user'):
                device.current_user = None
            return True, "设备连接已断开"
        
        elif to_state == DeviceConnectionState.ERROR:
            # 设备连接出错
            error_msg = metadata.get('error_message', '未知错误') if metadata else '未知错误'
            logger.error(f"设备 {device.device_uuid} 连接出错: {error_msg}")
            return True, f"设备连接出错: {error_msg}"
        
        return True, "连接状态转换完成"
    
    async def _execute_share_action(self, device: Device, from_state: DeviceShareState, 
                                  to_state: DeviceShareState, metadata: Dict[str, Any] = None) -> Tuple[bool, str]:
        """执行共享状态转换操作"""
        if to_state == DeviceShareState.SHARING:
            # 开始共享设备
            logger.info(f"开始共享设备 {device.device_uuid}")
            # 这里可以调用实际的设备共享逻辑
            return True, "开始共享设备"
        
        elif to_state == DeviceShareState.SHARED:
            # 设备共享成功
            user_id = metadata.get('user_id') if metadata else None
            logger.info(f"设备 {device.device_uuid} 共享给用户 {user_id}")
            # 记录共享用户
            if hasattr(device, 'shared_users') and user_id:
                if not hasattr(device, 'shared_users') or device.shared_users is None:
                    device.shared_users = []
                if user_id not in device.shared_users:
                    device.shared_users.append(user_id)
            return True, "设备共享成功"
        
        elif to_state == DeviceShareState.NOT_SHARED:
            # 停止共享设备
            logger.info(f"停止共享设备 {device.device_uuid}")
            # 清理共享相关信息
            if hasattr(device, 'shared_users'):
                device.shared_users = []
            return True, "设备共享已停止"
        
        elif to_state == DeviceShareState.SHARE_ERROR:
            # 设备共享出错
            error_msg = metadata.get('error_message', '未知错误') if metadata else '未知错误'
            logger.error(f"设备 {device.device_uuid} 共享出错: {error_msg}")
            return True, f"设备共享出错: {error_msg}"
        
        return True, "共享状态转换完成"


@dataclass
class TransitionRule:
    """Rule for a state transition."""
    from_state: Union[DeviceConnectionState, DeviceShareState]
    to_state: Union[DeviceConnectionState, DeviceShareState]
    validators: List[TransitionValidator] = field(default_factory=list)
    pre_actions: List[TransitionAction] = field(default_factory=list)
    post_actions: List[TransitionAction] = field(default_factory=list)
    description: str = ""
    
    def add_validator(self, validator: TransitionValidator) -> None:
        """Add a validator to this rule."""
        self.validators.append(validator)
    
    def add_pre_action(self, action: TransitionAction) -> None:
        """Add a pre-transition action to this rule."""
        self.pre_actions.append(action)
    
    def add_post_action(self, action: TransitionAction) -> None:
        """Add a post-transition action to this rule."""
        self.post_actions.append(action)


class StateMachine:
    """
    A flexible state machine for managing device states.
    
    This class provides a framework for managing state transitions
    with validation rules, pre and post transition actions, and
    transition history tracking.
    """
    
    def __init__(self, state_type: Union[Type[DeviceConnectionState], Type[DeviceShareState]]):
        """
        Initialize a state machine.
        
        Args:
            state_type: The type of state to manage (DeviceConnectionState or DeviceShareState)
        """
        self.state_type = state_type
        self.rules: List[TransitionRule] = []
        self.transitions_history: Dict[str, List[Transition]] = {}  # device_uuid -> transitions
        self.history_max_size = 100  # Maximum number of transitions to keep per device
        self.transition_listeners: List[Callable[[str, Transition], None]] = []
    
    def add_rule(self, rule: TransitionRule) -> None:
        """Add a transition rule."""
        self.rules.append(rule)
    
    def add_transition_listener(self, listener: Callable[[str, Transition], None]) -> None:
        """Add a listener for transition events."""
        self.transition_listeners.append(listener)
    
    def remove_transition_listener(self, listener: Callable[[str, Transition], None]) -> None:
        """Remove a transition listener."""
        if listener in self.transition_listeners:
            self.transition_listeners.remove(listener)
    
    def _notify_listeners(self, device_uuid: str, transition: Transition) -> None:
        """Notify all listeners of a transition."""
        for listener in self.transition_listeners:
            try:
                listener(device_uuid, transition)
            except Exception as e:
                logger.error(f"Error in transition listener: {e}")
    
    def _find_rule(self, from_state: Union[DeviceConnectionState, DeviceShareState], 
                  to_state: Union[DeviceConnectionState, DeviceShareState]) -> Optional[TransitionRule]:
        """Find a rule for a transition between states."""
        for rule in self.rules:
            if rule.from_state == from_state and rule.to_state == to_state:
                return rule
        return None
    
    def _record_transition(self, device_uuid: str, transition: Transition) -> None:
        """Record a transition in the history."""
        if device_uuid not in self.transitions_history:
            self.transitions_history[device_uuid] = []
        
        history = self.transitions_history[device_uuid]
        history.append(transition)
        
        # Trim history if needed
        if len(history) > self.history_max_size:
            self.transitions_history[device_uuid] = history[-self.history_max_size:]
    
    def get_transition_history(self, device_uuid: str) -> List[Transition]:
        """Get the transition history for a device."""
        return self.transitions_history.get(device_uuid, [])
    
    def clear_transition_history(self, device_uuid: str) -> None:
        """Clear the transition history for a device."""
        if device_uuid in self.transitions_history:
            del self.transitions_history[device_uuid]
    
    async def can_transition(self, device: Device, 
                           to_state: Union[DeviceConnectionState, DeviceShareState], 
                           metadata: Dict[str, Any] = None) -> Tuple[bool, str]:
        """
        Check if a transition is valid.
        
        Args:
            device: Device to transition
            to_state: Target state
            metadata: Additional data for validation
            
        Returns:
            Tuple of (can_transition, reason)
        """
        # Get current state based on state type
        if self.state_type == DeviceConnectionState:
            current_state = device.state.connection_state
        else:
            current_state = device.state.share_state
        
        # If already in the target state, return True
        if current_state == to_state:
            return True, "Already in target state"
        
        # Find applicable rule
        rule = self._find_rule(current_state, to_state)
        if not rule:
            return False, f"No rule for transition from {current_state} to {to_state}"
        
        # Validate transition
        for validator in rule.validators:
            valid, reason = validator.validate(device, current_state, to_state, metadata)
            if not valid:
                return False, reason
        
        return True, ""
    
    async def transition(self, device: Device, 
                       to_state: Union[DeviceConnectionState, DeviceShareState], 
                       metadata: Dict[str, Any] = None, 
                       reason: str = "") -> Tuple[TransitionResult, str]:
        """
        Transition a device to a new state.
        
        Args:
            device: Device to transition
            to_state: Target state
            metadata: Additional data for the transition
            reason: Reason for the transition
            
        Returns:
            Tuple of (result, message)
        """
        metadata = metadata or {}
        device_uuid = device.device_uuid
        
        # Get current state based on state type
        if self.state_type == DeviceConnectionState:
            current_state = device.state.connection_state
        else:
            current_state = device.state.share_state
        
        # If already in the target state, return early
        if current_state == to_state:
            transition = Transition(
                from_state=current_state,
                to_state=to_state,
                result=TransitionResult.ALREADY_IN_STATE,
                reason="Device already in target state",
                metadata=metadata
            )
            self._record_transition(device_uuid, transition)
            self._notify_listeners(device_uuid, transition)
            return TransitionResult.ALREADY_IN_STATE, "Device already in target state"
        
        # Find applicable rule
        rule = self._find_rule(current_state, to_state)
        if not rule:
            transition = Transition(
                from_state=current_state,
                to_state=to_state,
                result=TransitionResult.INVALID_TRANSITION,
                reason=f"No rule for transition from {current_state} to {to_state}",
                metadata=metadata
            )
            self._record_transition(device_uuid, transition)
            self._notify_listeners(device_uuid, transition)
            return TransitionResult.INVALID_TRANSITION, f"No rule for transition from {current_state} to {to_state}"
        
        # Validate transition
        for validator in rule.validators:
            valid, validation_reason = validator.validate(device, current_state, to_state, metadata)
            if not valid:
                transition = Transition(
                    from_state=current_state,
                    to_state=to_state,
                    result=TransitionResult.VALIDATION_FAILED,
                    reason=validation_reason,
                    metadata=metadata
                )
                self._record_transition(device_uuid, transition)
                self._notify_listeners(device_uuid, transition)
                return TransitionResult.VALIDATION_FAILED, validation_reason
        
        # Execute pre-actions
        for action in rule.pre_actions:
            success, message = await action.execute(device, current_state, to_state, metadata)
            if not success:
                transition = Transition(
                    from_state=current_state,
                    to_state=to_state,
                    result=TransitionResult.ACTION_FAILED,
                    reason=f"Pre-action failed: {message}",
                    metadata=metadata
                )
                self._record_transition(device_uuid, transition)
                self._notify_listeners(device_uuid, transition)
                return TransitionResult.ACTION_FAILED, f"Pre-action failed: {message}"
        
        # Perform state update
        if self.state_type == DeviceConnectionState:
            device.state.update_connection_state(to_state, reason)
        else:
            device.state.update_share_state(to_state, metadata.get("user_id") if metadata else None)
        
        # Execute post-actions
        for action in rule.post_actions:
            try:
                await action.execute(device, current_state, to_state, metadata)
            except Exception as e:
                logger.error(f"Error in post-action: {e}")
                # We don't fail the transition for post-action failures
        
        # Record successful transition
        transition = Transition(
            from_state=current_state,
            to_state=to_state,
            result=TransitionResult.SUCCESS,
            reason=reason,
            metadata=metadata
        )
        self._record_transition(device_uuid, transition)
        self._notify_listeners(device_uuid, transition)
        
        return TransitionResult.SUCCESS, ""


class DeviceConnectionStateMachine(StateMachine):
    """
    State machine for device connection states.
    
    This class provides a specialized state machine for managing
    device connection states with pre-defined rules.
    """
    
    def __init__(self):
        """Initialize the connection state machine with standard rules."""
        super().__init__(DeviceConnectionState)
        self._setup_default_rules()
    
    def _setup_default_rules(self) -> None:
        """Set up default transition rules."""
        # Rule: DISCONNECTED -> CONNECTING
        self.add_rule(TransitionRule(
            from_state=DeviceConnectionState.DISCONNECTED,
            to_state=DeviceConnectionState.CONNECTING,
            description="Start connecting to a disconnected device"
        ))
        
        # Rule: CONNECTING -> CONNECTED
        self.add_rule(TransitionRule(
            from_state=DeviceConnectionState.CONNECTING,
            to_state=DeviceConnectionState.CONNECTED,
            description="Successfully connected to a device"
        ))
        
        # Rule: CONNECTING -> DISCONNECTED
        self.add_rule(TransitionRule(
            from_state=DeviceConnectionState.CONNECTING,
            to_state=DeviceConnectionState.DISCONNECTED,
            description="Connection attempt failed"
        ))
        
        # Rule: CONNECTING -> ERROR
        self.add_rule(TransitionRule(
            from_state=DeviceConnectionState.CONNECTING,
            to_state=DeviceConnectionState.ERROR,
            description="Connection attempt encountered an error"
        ))
        
        # Rule: CONNECTED -> DISCONNECTING
        self.add_rule(TransitionRule(
            from_state=DeviceConnectionState.CONNECTED,
            to_state=DeviceConnectionState.DISCONNECTING,
            description="Start disconnecting a connected device"
        ))
        
        # Rule: DISCONNECTING -> DISCONNECTED
        self.add_rule(TransitionRule(
            from_state=DeviceConnectionState.DISCONNECTING,
            to_state=DeviceConnectionState.DISCONNECTED,
            description="Successfully disconnected a device"
        ))
        
        # Rule: DISCONNECTING -> ERROR
        self.add_rule(TransitionRule(
            from_state=DeviceConnectionState.DISCONNECTING,
            to_state=DeviceConnectionState.ERROR,
            description="Disconnection attempt encountered an error"
        ))
        
        # Rule: CONNECTED -> ERROR
        self.add_rule(TransitionRule(
            from_state=DeviceConnectionState.CONNECTED,
            to_state=DeviceConnectionState.ERROR,
            description="Device connection encountered an error"
        ))
        
        # Rule: ERROR -> DISCONNECTED
        self.add_rule(TransitionRule(
            from_state=DeviceConnectionState.ERROR,
            to_state=DeviceConnectionState.DISCONNECTED,
            description="Reset device from error state"
        ))
        
        # Rule: CONNECTED -> DISCONNECTED (for emergency disconnect)
        self.add_rule(TransitionRule(
            from_state=DeviceConnectionState.CONNECTED,
            to_state=DeviceConnectionState.DISCONNECTED,
            description="Emergency disconnect of a connected device"
        ))
        
        # Rule: SUSPENDED -> CONNECTED
        self.add_rule(TransitionRule(
            from_state=DeviceConnectionState.SUSPENDED,
            to_state=DeviceConnectionState.CONNECTED,
            description="Resume a suspended device"
        ))
        
        # Rule: CONNECTED -> SUSPENDED
        self.add_rule(TransitionRule(
            from_state=DeviceConnectionState.CONNECTED,
            to_state=DeviceConnectionState.SUSPENDED,
            description="Suspend a connected device"
        ))


class DeviceShareStateMachine(StateMachine):
    """
    State machine for device sharing states.
    
    This class provides a specialized state machine for managing
    device sharing states with pre-defined rules.
    """
    
    def __init__(self):
        """Initialize the share state machine with standard rules."""
        super().__init__(DeviceShareState)
        self._setup_default_rules()
    
    def _setup_default_rules(self) -> None:
        """Set up default transition rules."""
        # Rule: NOT_SHARED -> SHARED
        self.add_rule(TransitionRule(
            from_state=DeviceShareState.NOT_SHARED,
            to_state=DeviceShareState.SHARED,
            description="Share a device"
        ))
        
        # Rule: SHARED -> NOT_SHARED
        self.add_rule(TransitionRule(
            from_state=DeviceShareState.SHARED,
            to_state=DeviceShareState.NOT_SHARED,
            description="Unshare a device"
        ))
        
        # Rule: SHARED -> SHARED_AND_USED
        self.add_rule(TransitionRule(
            from_state=DeviceShareState.SHARED,
            to_state=DeviceShareState.SHARED_AND_USED,
            description="User starts using a shared device"
        ))
        
        # Rule: SHARED_AND_USED -> SHARED
        self.add_rule(TransitionRule(
            from_state=DeviceShareState.SHARED_AND_USED,
            to_state=DeviceShareState.SHARED,
            description="User stops using a shared device"
        ))
        
        # Rule: SHARED_AND_USED -> NOT_SHARED
        self.add_rule(TransitionRule(
            from_state=DeviceShareState.SHARED_AND_USED,
            to_state=DeviceShareState.NOT_SHARED,
            description="Unshare a device that is currently in use"
        ))
        
        # Rule: NOT_SHARED -> PENDING
        self.add_rule(TransitionRule(
            from_state=DeviceShareState.NOT_SHARED,
            to_state=DeviceShareState.PENDING,
            description="Device sharing is pending approval"
        ))
        
        # Rule: PENDING -> SHARED
        self.add_rule(TransitionRule(
            from_state=DeviceShareState.PENDING,
            to_state=DeviceShareState.SHARED,
            description="Device sharing request approved"
        ))
        
        # Rule: PENDING -> NOT_SHARED
        self.add_rule(TransitionRule(
            from_state=DeviceShareState.PENDING,
            to_state=DeviceShareState.NOT_SHARED,
            description="Device sharing request rejected"
        ))
        
        # Rule: ANY -> ERROR
        for state in [s for s in DeviceShareState if s != DeviceShareState.ERROR]:
            self.add_rule(TransitionRule(
                from_state=state,
                to_state=DeviceShareState.ERROR,
                description=f"Error occurred while device was in {state.name} state"
            ))
        
        # Rule: ERROR -> NOT_SHARED
        self.add_rule(TransitionRule(
            from_state=DeviceShareState.ERROR,
            to_state=DeviceShareState.NOT_SHARED,
            description="Reset device sharing from error state"
        ))


# Singleton instances
connection_state_machine = DeviceConnectionStateMachine()
share_state_machine = DeviceShareStateMachine() 
