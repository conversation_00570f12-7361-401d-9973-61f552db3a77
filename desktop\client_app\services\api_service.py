import requests
from desktop.client_app.core.settings import settings

class ApiService:
    """
    Service for communicating with the main_server API.
    """

    def __init__(self):
        self.base_url = settings.API_BASE_URL
        self.token = None

    def set_base_url(self, base_url: str):
        """
        Sets the base URL for the API service.
        Removes trailing slashes to ensure proper URL joining.
        """
        self.base_url = base_url.rstrip('/')
        print(f"API base URL set to: {self.base_url}")

    def login(self, username, password):
        """Attempts to log in and get a JWT token."""
        if not self.base_url:
            return False, "服务器地址不能为空。"

        try:
            # Note: The main_server uses form data for the token endpoint
            response = requests.post(
                f"{self.base_url}/api/v1/login/access-token",
                data={"username": username, "password": password}
            )
            response.raise_for_status()  # Raise an exception for bad status codes
            self.token = response.json().get("access_token")
            print("Login successful!")
            return True, None
        except requests.exceptions.RequestException as e:
            print(f"Login failed: {e}")
            error_message = "Connection error"
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_message = e.response.json().get("detail", "Unknown error")
                except requests.exceptions.JSONDecodeError:
                    error_message = e.response.text
            return False, error_message

    def get_slave_servers(self):
        """Retrieves the list of slave servers from the main server."""
        if not self.token:
            return None, "Not authenticated"

        headers = {"Authorization": f"Bearer {self.token}"}
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/slave-servers/",
                headers=headers
            )
            response.raise_for_status()
            return response.json(), None
        except requests.exceptions.RequestException as e:
            print(f"Failed to get slave servers: {e}")
            error_message = "Connection error"
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_message = e.response.json().get("detail", "Unknown error")
                except requests.exceptions.JSONDecodeError:
                    error_message = e.response.text
            return None, error_message


api_service = ApiService()