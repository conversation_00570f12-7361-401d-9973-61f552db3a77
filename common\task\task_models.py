"""
任务模型定义

定义分布式任务系统中使用的数据模型，包括任务、任务状态、任务优先级等。
"""

from enum import Enum, auto
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime
import uuid
import json

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"       # 等待调度
    SCHEDULED = "scheduled"   # 已调度，等待执行
    RUNNING = "running"       # 正在执行
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"         # 执行失败
    CANCELLED = "cancelled"   # 已取消
    TIMEOUT = "timeout"       # 已超时

class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 0
    NORMAL = 50
    HIGH = 100
    CRITICAL = 200

@dataclass
class TaskDependency:
    """任务依赖"""
    task_id: str  # 依赖任务的ID
    required_status: TaskStatus = TaskStatus.COMPLETED  # 依赖任务需要达到的状态

@dataclass
class TaskResult:
    """任务执行结果"""
    success: bool = False
    data: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0  # 执行时间(秒)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "success": self.success,
            "data": self.data,
            "error": self.error,
            "execution_time": self.execution_time
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TaskResult":
        """从字典创建对象"""
        return cls(
            success=data.get("success", False),
            data=data.get("data"),
            error=data.get("error"),
            execution_time=data.get("execution_time", 0.0)
        )

    def __str__(self) -> str:
        """字符串表示"""
        if self.success:
            return f"Success: {self.execution_time:.2f}s"
        else:
            return f"Failed: {self.error}, {self.execution_time:.2f}s"

@dataclass
class Task:
    """分布式任务"""
    name: str  # 任务名称
    task_type: str  # 任务类型
    parameters: Dict[str, Any] = field(default_factory=dict)  # 任务参数
    priority: TaskPriority = TaskPriority.NORMAL  # 任务优先级
    dependencies: List[TaskDependency] = field(default_factory=list)  # 任务依赖
    timeout: Optional[int] = None  # 超时时间(秒)，None表示不超时
    retry_count: int = 0  # 重试次数
    max_retries: int = 3  # 最大重试次数
    retry_delay: int = 60  # 重试延迟(秒)
    
    # 自动生成的字段
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))  # 任务ID
    status: TaskStatus = TaskStatus.PENDING  # 任务状态
    created_at: datetime = field(default_factory=datetime.now)  # 创建时间
    scheduled_at: Optional[datetime] = None  # 调度时间
    started_at: Optional[datetime] = None  # 开始时间
    completed_at: Optional[datetime] = None  # 完成时间
    result: Optional[TaskResult] = None  # 任务结果
    worker_id: Optional[str] = None  # 执行任务的worker ID
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "name": self.name,
            "task_type": self.task_type,
            "parameters": self.parameters,
            "priority": self.priority.value,
            "dependencies": [
                {
                    "task_id": dep.task_id,
                    "required_status": dep.required_status.value
                } for dep in self.dependencies
            ],
            "timeout": self.timeout,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "status": self.status.value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "scheduled_at": self.scheduled_at.isoformat() if self.scheduled_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "result": self.result.to_dict() if self.result else None,
            "worker_id": self.worker_id
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Task":
        """从字典创建对象"""
        task = cls(
            name=data.get("name", ""),
            task_type=data.get("task_type", ""),
            parameters=data.get("parameters", {}),
            priority=TaskPriority(data.get("priority", TaskPriority.NORMAL.value)),
            timeout=data.get("timeout"),
            retry_count=data.get("retry_count", 0),
            max_retries=data.get("max_retries", 3),
            retry_delay=data.get("retry_delay", 60)
        )
        
        # 设置ID和时间戳
        task.task_id = data.get("task_id", task.task_id)
        task.status = TaskStatus(data.get("status", TaskStatus.PENDING.value))
        
        # 解析时间戳
        for field_name in ["created_at", "scheduled_at", "started_at", "completed_at"]:
            timestamp = data.get(field_name)
            if timestamp:
                try:
                    setattr(task, field_name, datetime.fromisoformat(timestamp))
                except ValueError:
                    pass
                    
        # 解析依赖
        dependencies = data.get("dependencies", [])
        task.dependencies = [
            TaskDependency(
                dep.get("task_id", ""),
                TaskStatus(dep.get("required_status", TaskStatus.COMPLETED.value))
            ) for dep in dependencies
        ]
        
        # 解析结果
        result_data = data.get("result")
        if result_data:
            task.result = TaskResult.from_dict(result_data)
            
        task.worker_id = data.get("worker_id")
        
        return task
        
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict())
        
    @classmethod
    def from_json(cls, json_str: str) -> "Task":
        """从JSON字符串创建对象"""
        data = json.loads(json_str)
        return cls.from_dict(data)
        
    def should_retry(self) -> bool:
        """判断是否应该重试"""
        return (
            self.status == TaskStatus.FAILED and 
            self.retry_count < self.max_retries
        )
        
    def can_execute(self, task_statuses: Dict[str, TaskStatus]) -> bool:
        """
        判断任务是否可以执行
        
        参数:
            task_statuses: 任务ID到任务状态的映射
            
        返回:
            是否可以执行
        """
        # 首先检查自身状态
        if self.status != TaskStatus.SCHEDULED:
            return False
            
        # 检查依赖是否满足
        for dep in self.dependencies:
            dep_status = task_statuses.get(dep.task_id)
            if not dep_status or dep_status != dep.required_status:
                return False
                
        return True 
