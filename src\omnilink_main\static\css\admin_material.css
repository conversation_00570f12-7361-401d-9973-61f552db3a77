/* 主从服务器管理系统 - Material Design 3 样式表 */

/* ===== 全局变量与主题 ===== */
:root {
  /* Material Design 3 主题色 */
  --md-sys-color-primary: #1976d2;
  --md-sys-color-on-primary: #ffffff;
  --md-sys-color-primary-container: #d5e3ff;
  --md-sys-color-on-primary-container: #001c3d;
  
  --md-sys-color-secondary: #018786;
  --md-sys-color-on-secondary: #ffffff;
  --md-sys-color-secondary-container: #b4f0ef;
  --md-sys-color-on-secondary-container: #002020;
  
  --md-sys-color-tertiary: #6750A4;
  --md-sys-color-on-tertiary: #ffffff;
  --md-sys-color-tertiary-container: #e9ddff;
  --md-sys-color-on-tertiary-container: #22005d;
  
  --md-sys-color-error: #b3261e;
  --md-sys-color-on-error: #ffffff;
  --md-sys-color-error-container: #f9dedc;
  --md-sys-color-on-error-container: #410e0b;
  
  --md-sys-color-background: #f8f9fa;
  --md-sys-color-on-background: #1d1b20;
  --md-sys-color-surface: #ffffff;
  --md-sys-color-on-surface: #1d1b20;
  --md-sys-color-surface-variant: #e7e0ec;
  --md-sys-color-on-surface-variant: #49454f;
  
  --md-sys-color-outline: #79747e;
  --md-sys-color-outline-variant: #c9c5d0;
  
  /* 卡片阴影 */
  --md-sys-elevation-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --md-sys-elevation-2: 0 3px 6px rgba(0,0,0,0.15), 0 2px 4px rgba(0,0,0,0.12);
  --md-sys-elevation-3: 0 10px 20px rgba(0,0,0,0.15), 0 3px 6px rgba(0,0,0,0.10);
  --md-sys-elevation-4: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
  --md-sys-elevation-5: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);

  /* 尺寸和间距 */
  --md-sys-spacing-unit: 8px;
  --md-sys-border-radius-small: 4px;
  --md-sys-border-radius-medium: 8px;
  --md-sys-border-radius-large: 16px;
  --md-sys-border-radius-extra-large: 28px;
  
  /* 状态卡片颜色 */
  --server-card-color: var(--md-sys-color-primary);
  --device-card-color: #2e7d32;
  --user-card-color: #00838f;
  --alert-card-color: #e65100;
  
  /* 状态颜色 */
  --status-success: #2e7d32;
  --status-warning: #f57c00;
  --status-error: var(--md-sys-color-error);
  --status-info: #0277bd;
  
  /* 动画时间 */
  --md-sys-motion-duration-short: 100ms;
  --md-sys-motion-duration-medium: 250ms;
  --md-sys-motion-duration-long: 500ms;
  --md-sys-motion-easing-standard: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-emphasized: cubic-bezier(0.2, 0, 0, 1);
}

/* 暗色主题变量 */
[data-theme="dark"] {
  --md-sys-color-primary: #80b3ff;
  --md-sys-color-on-primary: #002c66;
  --md-sys-color-primary-container: #00408f;
  --md-sys-color-on-primary-container: #d5e3ff;

  --md-sys-color-secondary: #6fdbda;
  --md-sys-color-on-secondary: #003737;
  --md-sys-color-secondary-container: #004f4f;
  --md-sys-color-on-secondary-container: #b4f0ef;

  --md-sys-color-tertiary: #d0bcff;
  --md-sys-color-on-tertiary: #371e73;
  --md-sys-color-tertiary-container: #4f378b;
  --md-sys-color-on-tertiary-container: #e9ddff;

  --md-sys-color-error: #f2b8b5;
  --md-sys-color-on-error: #601410;
  --md-sys-color-error-container: #8c1d18;
  --md-sys-color-on-error-container: #f9dedc;

  --md-sys-color-background: #1d1b20;
  --md-sys-color-on-background: #e6e1e5;
  --md-sys-color-surface: #121212;
  --md-sys-color-on-surface: #e6e1e5;
  --md-sys-color-surface-variant: #49454f;
  --md-sys-color-on-surface-variant: #cac4d0;

  --md-sys-color-outline: #938f99;
  --md-sys-color-outline-variant: #49454f;
}

/* ===== 基础排版样式 ===== */
body {
  margin: 0;
  padding: 0;
  background-color: var(--md-sys-color-background);
  color: var(--md-sys-color-on-background);
  font-family: Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-standard);
}

.mdc-typography--headline1 {
  font-size: 2.5rem;
  font-weight: 300;
  letter-spacing: -0.0156em;
  margin: 0 0 24px 0;
}

.mdc-typography--headline2 {
  font-size: 2rem;
  font-weight: 300;
  letter-spacing: -0.0083em;
  margin: 0 0 16px 0;
}

.mdc-typography--headline3 {
  font-size: 1.75rem;
  font-weight: 400;
  letter-spacing: 0;
  margin: 0 0 16px 0;
}

.mdc-typography--headline4 {
  font-size: 1.5rem;
  font-weight: 400;
  letter-spacing: 0.0073em;
  margin: 0 0 16px 0;
  color: var(--md-sys-color-on-surface);
}

.mdc-typography--headline5 {
  font-size: 1.25rem;
  font-weight: 400;
  letter-spacing: 0em;
  margin: 0 0 8px 0;
  color: var(--md-sys-color-on-surface);
}

.mdc-typography--headline6 {
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.0125em;
  margin: 0 0 8px 0;
  color: var(--md-sys-color-on-surface);
}

.mdc-typography--subtitle1 {
  font-size: 1rem;
  font-weight: 400;
  letter-spacing: 0.009em;
}

.mdc-typography--subtitle2 {
  font-size: 0.875rem;
  font-weight: 500;
  letter-spacing: 0.0071em;
}

.mdc-typography--body1 {
  font-size: 1rem;
  font-weight: 400;
  letter-spacing: 0.0179em;
  line-height: 1.5;
}

.mdc-typography--body2 {
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: 0.0179em;
  line-height: 1.5;
}

.mdc-typography--caption {
  font-size: 0.75rem;
  font-weight: 400;
  letter-spacing: 0.033em;
}

.mdc-typography--button {
  font-size: 0.875rem;
  font-weight: 500;
  letter-spacing: 0.0892em;
  text-transform: uppercase;
}

/* ===== 布局组件 ===== */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.app-content {
  display: flex;
  flex: 1;
}

.mdc-drawer-app-content {
  display: flex;
  flex: 1;
}

.main-content {
  flex: 1;
  overflow: auto;
  background-color: var(--md-sys-color-background);
  transition: background-color var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-standard);
}

.page-content {
  padding: var(--md-sys-spacing-unit) * 3;
  max-width: 1600px;
  margin: 0 auto;
  animation: fade-in var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-standard);
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* ===== 顶部应用栏 ===== */
.mdc-top-app-bar {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  box-shadow: var(--md-sys-elevation-2);
  transition: background-color var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-standard);
}

.mdc-top-app-bar__title {
  font-weight: 500;
}

.mdc-top-app-bar__action-item {
  color: var(--md-sys-color-on-primary);
}

/* ===== 侧边导航抽屉 ===== */
.mdc-drawer {
  background-color: var(--md-sys-color-surface);
  border-right: 1px solid var(--md-sys-color-outline-variant);
  width: 256px;
  transition: background-color var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-standard);
}

.mdc-drawer .mdc-list-item {
  height: 48px;
  border-radius: 0 var(--md-sys-border-radius-large) var(--md-sys-border-radius-large) 0;
  margin: 4px 8px 4px 0;
  padding-left: 16px;
  transition: background-color var(--md-sys-motion-duration-short) var(--md-sys-motion-easing-standard);
}

.mdc-drawer .mdc-list-item--activated {
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
  font-weight: 500;
}

.mdc-drawer .mdc-list-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .mdc-drawer .mdc-list-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.mdc-drawer .mdc-list-group__subheader {
  color: var(--md-sys-color-on-surface-variant);
  font-size: 14px;
  font-weight: 500;
  padding: 16px 16px 8px;
  margin: 0;
}

.mdc-list-divider {
  margin: 8px 0;
  border-color: var(--md-sys-color-outline-variant);
}

/* ===== 卡片样式 ===== */
.mdc-card {
  background-color: var(--md-sys-color-surface);
  border-radius: var(--md-sys-border-radius-medium);
  overflow: hidden;
  transition: box-shadow var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-standard),
              transform var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-standard),
              background-color var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-standard);
  margin-bottom: var(--md-sys-spacing-unit) * 2;
  box-shadow: var(--md-sys-elevation-1);
}

.mdc-card:hover {
  box-shadow: var(--md-sys-elevation-2);
}

.mdc-card__primary-action {
  padding: var(--md-sys-spacing-unit) * 2;
}

/* ===== 仪表盘卡片 ===== */
.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--md-sys-spacing-unit) * 2;
  margin-bottom: var(--md-sys-spacing-unit) * 3;
}

.dashboard-card {
  border-radius: var(--md-sys-border-radius-medium);
  overflow: hidden;
  transition: transform var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-standard);
}

.dashboard-card:hover {
  transform: translateY(-4px);
}

.dashboard-card .mdc-card__primary-action {
  height: 100%;
  padding: var(--md-sys-spacing-unit) * 2;
}

.card-content {
  display: flex;
  align-items: center;
}

.card-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  margin-right: 16px;
  transition: background-color var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-standard);
}

.card-icon {
  font-size: 32px;
  color: white;
}

.card-data {
  flex: 1;
}

.card-numbers {
  display: flex;
  align-items: baseline;
  margin-top: 8px;
}

.primary-value {
  font-size: 30px;
  font-weight: 400;
  margin-right: 16px;
  color: var(--md-sys-color-on-surface);
}

.secondary-value {
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
}

/* 各类卡片颜色 */
.server-card .card-icon-container {
  background-color: var(--server-card-color);
}

.device-card .card-icon-container {
  background-color: var(--device-card-color);
}

.user-card .card-icon-container {
  background-color: var(--user-card-color);
}

.alert-card .card-icon-container {
  background-color: var(--alert-card-color);
}

/* ===== 图表区域 ===== */
.dashboard-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: var(--md-sys-spacing-unit) * 2;
  margin-bottom: var(--md-sys-spacing-unit) * 3;
}

.chart-card {
  border-radius: var(--md-sys-border-radius-medium);
  overflow: hidden;
}

.chart-card .mdc-card__primary-action {
  padding: var(--md-sys-spacing-unit) * 2;
}

.chart-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--md-sys-spacing-unit) * 2;
}

.chart-header .material-icons {
  margin-right: var(--md-sys-spacing-unit);
  color: var(--md-sys-color-primary);
}

.chart-container {
  height: 250px;
  transition: height var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-standard);
}

/* ===== 表格区域 ===== */
.dashboard-tables {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: var(--md-sys-spacing-unit) * 2;
  margin-bottom: var(--md-sys-spacing-unit) * 3;
}

.table-card {
  border-radius: var(--md-sys-border-radius-medium);
  overflow: hidden;
}

.table-card .mdc-card__primary-action {
  padding: var(--md-sys-spacing-unit) * 2;
}

.table-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--md-sys-spacing-unit) * 2;
}

.table-header .material-icons {
  margin-right: var(--md-sys-spacing-unit);
  color: var(--md-sys-color-primary);
}

.table-container {
  overflow-x: auto;
}

/* ===== 数据表格 ===== */
.mdc-data-table__table {
  width: 100%;
  border-collapse: collapse;
}

.mdc-data-table__header-row {
  height: 56px;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.mdc-data-table__header-cell {
  color: var(--md-sys-color-on-surface-variant);
  font-size: 0.875rem;
  font-weight: 500;
  text-align: left;
  padding: 0 16px;
}

.mdc-data-table__row {
  height: 52px;
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
  transition: background-color var(--md-sys-motion-duration-short) var(--md-sys-motion-easing-standard);
}

.mdc-data-table__row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

[data-theme="dark"] .mdc-data-table__row:hover {
  background-color: rgba(255, 255, 255, 0.04);
}

.mdc-data-table__cell {
  color: var(--md-sys-color-on-surface);
  font-size: 0.875rem;
  padding: 0 16px;
}

/* ===== 状态标识 ===== */
.status-success {
  color: var(--status-success);
}

.status-warning {
  color: var(--status-warning);
}

.status-error {
  color: var(--status-error);
}

.status-info {
  color: var(--status-info);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge-success {
  background-color: rgba(46, 125, 50, 0.1);
  color: var(--status-success);
}

.status-badge-warning {
  background-color: rgba(245, 124, 0, 0.1);
  color: var(--status-warning);
}

.status-badge-error {
  background-color: rgba(179, 38, 30, 0.1);
  color: var(--status-error);
}

.status-badge-info {
  background-color: rgba(2, 119, 189, 0.1);
  color: var(--status-info);
}

.status-badge .material-icons {
  font-size: 16px;
  margin-right: 4px;
}

/* ===== 设备管理工具栏 ===== */
.device-toolbar {
  margin-bottom: var(--md-sys-spacing-unit) * 2;
}

.device-toolbar-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--md-sys-spacing-unit) * 2;
  padding: var(--md-sys-spacing-unit) * 2;
}

.view-toggle-group {
  display: flex;
  gap: 8px;
}

.search-field {
  flex: 1;
  min-width: 200px;
}

.batch-operations-toolbar {
  margin-bottom: var(--md-sys-spacing-unit) * 2;
  background-color: var(--md-sys-color-primary-container);
}

.batch-operations-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: var(--md-sys-spacing-unit) * 2;
}

.batch-operations-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-count {
  font-weight: 500;
  color: var(--md-sys-color-on-primary-container);
}

/* ===== 响应式设计 ===== */
@media (max-width: 599px) {
  .dashboard-cards,
  .dashboard-charts, 
  .dashboard-tables {
    grid-template-columns: 1fr;
  }
  
  .page-content {
    padding: var(--md-sys-spacing-unit) * 2;
  }
  
  .chart-container {
    height: 200px;
  }
  
  .device-toolbar-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .view-toggle-group {
    width: 100%;
    justify-content: space-between;
  }
  
  .batch-operations-content {
    flex-direction: column;
    gap: var(--md-sys-spacing-unit);
  }
  
  .batch-operations-buttons {
    width: 100%;
    justify-content: space-between;
  }
}

@media (min-width: 600px) and (max-width: 959px) {
  .dashboard-charts, 
  .dashboard-tables {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    height: 220px;
  }
}

/* ===== 滚动条美化 ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background-color: rgba(255, 255, 255, 0.05);
}

/* ===== 动画效果 ===== */
.animate-fade-in {
  animation: fade-in var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-standard);
}

.animate-scale-in {
  animation: scale-in var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-emphasized);
}

@keyframes scale-in {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.animate-slide-in-right {
  animation: slide-in-right var(--md-sys-motion-duration-medium) var(--md-sys-motion-easing-emphasized);
}

@keyframes slide-in-right {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* ===== 主题切换过渡 ===== */
body, 
.mdc-top-app-bar, 
.mdc-drawer, 
.main-content,
.mdc-card,
.mdc-card__primary-action,
.mdc-data-table__row,
.mdc-data-table__header-cell,
.mdc-data-table__cell {
  transition: background-color var(--md-sys-motion-duration-long) var(--md-sys-motion-easing-standard),
              color var(--md-sys-motion-duration-long) var(--md-sys-motion-easing-standard);
} 