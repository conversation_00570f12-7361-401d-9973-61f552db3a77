from fastapi import APIRouter, Depends, status, HTTPException
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from src.omnilink_main.dependencies.db import get_db
from common.schemas.api_schema import APIResponse
from common.schemas.token_schema import Token, RefreshToken, TokenRequestForm
from src.omnilink_main.services.auth_service import auth_service_instance
from src.omnilink_main.dependencies.auth import get_current_token, get_current_user
from common.models.user import User

router = APIRouter()

class RefreshTokenRequest(BaseModel):
    refresh_token: str

@router.post("/login", response_model=APIResponse[Token])
async def login(
    db: AsyncSession = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
):
    """
    标准OAuth2登录，获取访问令牌和刷新令牌
    """
    try:
        user = await auth_service_instance.authenticate_user(
            db, username=form_data.username, password=form_data.password
        )
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        access_token = auth_service_instance.create_access_token(user)
        refresh_token = auth_service_instance.create_refresh_token(user)

        return APIResponse(
            success=True,
            message="登录成功",
            data=Token(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type="bearer"
            )
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录过程中发生错误: {str(e)}"
        )

@router.post("/refresh", response_model=APIResponse[dict])
async def refresh_token(
    refresh_request: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    使用刷新令牌获取新的访问令牌
    """
    try:
        new_access_token = await auth_service_instance.refresh_access_token(
            refresh_token=refresh_request.refresh_token
        )
        
        return APIResponse(
            success=True,
            message="令牌刷新成功",
            data={
                "access_token": new_access_token,
                "token_type": "bearer"
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"令牌刷新过程中发生错误: {str(e)}"
        )

@router.post("/logout", response_model=APIResponse[dict])
async def logout(token: str = Depends(get_current_token)):
    """
    用户登出，将访问令牌加入黑名单
    """
    try:
        await auth_service_instance.logout(token=token)
        return APIResponse(
            success=True,
            message="登出成功",
            data={}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登出过程中发生错误: {str(e)}"
        )

@router.get("/me", response_model=APIResponse[dict])
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户信息
    """
    try:
        user_permissions = auth_service_instance.get_user_permissions(current_user)
        
        return APIResponse(
            success=True,
            message="获取用户信息成功",
            data={
                "id": current_user.id,
                "username": current_user.username,
                "email": current_user.email,
                "is_active": current_user.is_active,
                "is_superuser": current_user.is_superuser,
                "roles": [{"id": role.id, "name": role.name, "description": role.description} for role in current_user.roles],
                "permissions": list(user_permissions),
                "last_login_at": current_user.last_login_at.isoformat() if current_user.last_login_at else None
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户信息时发生错误: {str(e)}"
        )
