from datetime import datetime, timedelta, timezone
from typing import Any, Dict
from jose import jwt, J<PERSON><PERSON><PERSON>r
from src.omnilink_main.core.config import settings

class J<PERSON><PERSON><PERSON>ler:
    """
    A utility class for creating and decoding JWT tokens.
    """

    @staticmethod
    def create_access_token(subject: Any) -> str:
        """
        Creates a new access token.

        Args:
            subject: The subject of the token (e.g., user ID or email).

        Returns:
            The encoded JWT access token.
        """
        expire = datetime.now(timezone.utc) + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode = {"exp": expire, "sub": str(subject)}
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
        return encoded_jwt

    @staticmethod
    def decode_token(token: str) -> Dict[str, Any] | None:
        """
        Decodes a JWT token.

        Args:
            token: The encoded JWT token.

        Returns:
            The decoded payload as a dictionary if valid, otherwise None.
        """
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
            return payload
        except JW<PERSON>rror:
            return None
