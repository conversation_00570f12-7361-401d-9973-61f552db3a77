from fastapi import APIRouter

from src.omnilink_main.api.v1.endpoints import auth, users, policies, roles, slave_servers, devices, websocket, status, dashboard

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=['auth'])
api_router.include_router(users.router, prefix="/users", tags=['users'])
api_router.include_router(policies.router, prefix="/policies", tags=['policies'])
api_router.include_router(roles.router, prefix="/roles", tags=['roles'])
api_router.include_router(status.router, prefix="/status", tags=['status'])
api_router.include_router(slave_servers.router, prefix="/slaves", tags=['slaves'])
api_router.include_router(devices.router, prefix="/devices", tags=['devices'])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=['dashboard'])
api_router.include_router(websocket.router)
