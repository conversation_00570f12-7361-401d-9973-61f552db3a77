<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分组管理 - 统一管理控制台</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- jsTree -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jstree/3.3.12/themes/default/style.min.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin_console.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边导航栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <!-- 导航内容与index.html相同 -->
                <div class="position-sticky pt-3">
                    <div class="px-3 py-4 text-white">
                        <h5>主从服务器管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.index') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('admin_console.group_management') }}">
                                <i class="fas fa-server me-2"></i>分组管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.migration_wizard') }}">
                                <i class="fas fa-exchange-alt me-2"></i>迁移向导
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.alerts') }}">
                                <i class="fas fa-bell me-2"></i>告警设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.health_monitor') }}">
                                <i class="fas fa-heartbeat me-2"></i>健康监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" id="helpBtn">
                                <i class="fas fa-question-circle me-2"></i>帮助
                            </a>
                        </li>
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>系统</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin_console.system_settings') }}">
                                <i class="fas fa-cog me-2"></i>系统设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>退出
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">分组管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshBtn">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" id="createGroupBtn">
                                <i class="fas fa-plus"></i> 创建分组
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- 分组树形视图 -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-sitemap me-2"></i>分组结构</span>
                                    <div>
                                        <button class="btn btn-sm btn-outline-secondary" id="expandAllBtn" title="展开全部">
                                            <i class="fas fa-expand-arrows-alt"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary ms-1" id="collapseAllBtn" title="折叠全部">
                                            <i class="fas fa-compress-arrows-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="input-group mb-3">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="groupSearch" placeholder="搜索分组...">
                                </div>
                                <div id="groupTree" style="max-height: 600px; overflow-y: auto;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 分组详情与管理 -->
                    <div class="col-md-8">
                        <div class="card" id="groupDetailsCard">
                            <div class="card-header">
                                <i class="fas fa-info-circle me-2"></i>分组详情
                            </div>
                            <div class="card-body">
                                <div class="text-center py-5" id="groupDetailsPlaceholder">
                                    <i class="fas fa-server fa-3x mb-3 text-muted"></i>
                                    <p class="lead">请从左侧选择一个分组查看详情</p>
                                </div>
                                <div id="groupDetailsContent" style="display: none;">
                                    <ul class="nav nav-tabs" id="groupTabs" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="info-tab" data-bs-toggle="tab" data-bs-target="#info" type="button" role="tab" aria-controls="info" aria-selected="true">基本信息</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="members-tab" data-bs-toggle="tab" data-bs-target="#members" type="button" role="tab" aria-controls="members" aria-selected="false">成员管理</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">分组设置</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="health-tab" data-bs-toggle="tab" data-bs-target="#health" type="button" role="tab" aria-controls="health" aria-selected="false">健康状况</button>
                                        </li>
                                    </ul>
                                    <div class="tab-content p-3 border border-top-0 rounded-bottom" id="groupTabContent">
                                        <!-- 基本信息面板 -->
                                        <div class="tab-pane fade show active" id="info" role="tabpanel" aria-labelledby="info-tab">
                                            <form id="groupInfoForm">
                                                <div class="row mb-3">
                                                    <div class="col-md-6">
                                                        <label for="groupName" class="form-label">分组名称</label>
                                                        <input type="text" class="form-control" id="groupName" required>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label for="groupParent" class="form-label">父分组</label>
                                                        <select class="form-select" id="groupParent">
                                                            <option value="">无（根分组）</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="groupDescription" class="form-label">描述</label>
                                                    <textarea class="form-control" id="groupDescription" rows="2"></textarea>
                                                </div>
                                                <div class="row mb-3">
                                                    <div class="col-md-6">
                                                        <label for="groupTags" class="form-label">标签（用逗号分隔）</label>
                                                        <input type="text" class="form-control" id="groupTags" placeholder="标签1,标签2,标签3">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label for="groupPartition" class="form-label">所属分区</label>
                                                        <select class="form-select" id="groupPartition">
                                                            <option value="">-- 选择分区 --</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">创建时间</label>
                                                    <p class="form-control-plaintext" id="groupCreatedTime">-</p>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">最后更新时间</label>
                                                    <p class="form-control-plaintext" id="groupUpdatedTime">-</p>
                                                </div>
                                                <div class="d-flex justify-content-end">
                                                    <button type="submit" class="btn btn-primary" id="saveGroupInfoBtn">保存更改</button>
                                                </div>
                                            </form>
                                        </div>
                                        <!-- 成员管理面板 -->
                                        <div class="tab-pane fade" id="members" role="tabpanel" aria-labelledby="members-tab">
                                            <div class="d-flex justify-content-between mb-3">
                                                <h5>成员列表</h5>
                                                <button type="button" class="btn btn-sm btn-primary" id="addMemberBtn">
                                                    <i class="fas fa-plus"></i> 添加成员
                                                </button>
                                            </div>
                                            <div class="table-responsive">
                                                <table class="table table-hover table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>服务器ID</th>
                                                            <th>状态</th>
                                                            <th>IP地址</th>
                                                            <th>负载</th>
                                                            <th>最后更新</th>
                                                            <th>操作</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="membersList">
                                                        <tr>
                                                            <td colspan="6" class="text-center">无成员</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <!-- 分组设置面板 -->
                                        <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                                            <form id="groupSettingsForm">
                                                <h5 class="mb-3">分组配置</h5>
                                                <div class="mb-3">
                                                    <label for="minMembers" class="form-label">最小成员数</label>
                                                    <input type="number" class="form-control" id="minMembers" min="0" value="1">
                                                    <div class="form-text">设置此分组应维持的最小成员数量，低于此值将触发告警</div>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="maxMembers" class="form-label">最大成员数</label>
                                                    <input type="number" class="form-control" id="maxMembers" min="1" value="10">
                                                    <div class="form-text">设置此分组可容纳的最大成员数量</div>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="healthCheckInterval" class="form-label">健康检查间隔（秒）</label>
                                                    <input type="number" class="form-control" id="healthCheckInterval" min="5" value="30">
                                                </div>
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="enableFailover" checked>
                                                        <label class="form-check-label" for="enableFailover">启用故障转移</label>
                                                    </div>
                                                    <div class="form-text">当成员不健康时自动从分组中移除</div>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="enableLoadBalance" checked>
                                                        <label class="form-check-label" for="enableLoadBalance">启用负载均衡</label>
                                                    </div>
                                                    <div class="form-text">根据负载自动分配请求到各成员</div>
                                                </div>
                                                <div class="d-flex justify-content-end">
                                                    <button type="submit" class="btn btn-primary" id="saveGroupSettingsBtn">保存设置</button>
                                                </div>
                                            </form>
                                        </div>
                                        <!-- 健康状况面板 -->
                                        <div class="tab-pane fade" id="health" role="tabpanel" aria-labelledby="health-tab">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="card mb-3">
                                                        <div class="card-header">整体健康状态</div>
                                                        <div class="card-body">
                                                            <div class="d-flex align-items-center mb-3">
                                                                <div id="healthStatusIndicator" class="me-3">
                                                                    <i class="fas fa-circle text-success fa-2x"></i>
                                                                </div>
                                                                <div>
                                                                    <h5 class="mb-0" id="healthStatusText">健康</h5>
                                                                    <small class="text-muted" id="healthStatusTime">最后更新: 刚刚</small>
                                                                </div>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label class="form-label">健康分数</label>
                                                                <div class="progress">
                                                                    <div id="healthScoreBar" class="progress-bar bg-success" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">100%</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card mb-3">
                                                        <div class="card-header">统计信息</div>
                                                        <div class="card-body">
                                                            <ul class="list-group list-group-flush">
                                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                    健康成员数
                                                                    <span class="badge bg-success rounded-pill" id="healthyMemberCount">0</span>
                                                                </li>
                                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                    警告成员数
                                                                    <span class="badge bg-warning rounded-pill" id="warningMemberCount">0</span>
                                                                </li>
                                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                    故障成员数
                                                                    <span class="badge bg-danger rounded-pill" id="criticalMemberCount">0</span>
                                                                </li>
                                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                    当前平均负载
                                                                    <span id="avgLoadText">0%</span>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card">
                                                <div class="card-header">历史健康趋势</div>
                                                <div class="card-body">
                                                    <canvas id="healthTrendChart" height="200"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 创建分组模态框 -->
    <div class="modal fade" id="createGroupModal" tabindex="-1" aria-labelledby="createGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createGroupModalLabel">创建新分组</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createGroupForm">
                        <div class="mb-3">
                            <label for="newGroupName" class="form-label">分组名称</label>
                            <input type="text" class="form-control" id="newGroupName" required>
                        </div>
                        <div class="mb-3">
                            <label for="newGroupParent" class="form-label">父分组</label>
                            <select class="form-select" id="newGroupParent">
                                <option value="">无（根分组）</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="newGroupDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="newGroupDescription" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="newGroupPartition" class="form-label">所属分区</label>
                            <select class="form-select" id="newGroupPartition">
                                <option value="">-- 选择分区 --</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="submitCreateGroupBtn">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加成员模态框 -->
    <div class="modal fade" id="addMemberModal" tabindex="-1" aria-labelledby="addMemberModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addMemberModalLabel">添加成员到分组</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">当前分组</label>
                        <input type="text" class="form-control" id="currentGroupDisplay" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="memberSearch" class="form-label">搜索服务器</label>
                        <input type="text" class="form-control" id="memberSearch" placeholder="输入服务器ID或IP地址">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">可用服务器</label>
                        <div class="list-group" id="availableServersList" style="max-height: 200px; overflow-y: auto;">
                            <div class="list-group-item text-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                正在加载服务器列表...
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="submitAddMemberBtn">添加所选成员</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- jsTree -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jstree/3.3.12/jstree.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/admin_console.js') }}"></script>
</body>
</html> 