#!/usr/bin/env python3
"""
OmniLink专用客户端主程序
负责USB设备的发现、连接和管理，支持后台运行
"""

import sys
import os
import logging
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QSystemTrayIcon, QMenu, QAction, QMessageBox
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, QSettings
from PyQt5.QtGui import QIcon, QPixmap

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from desktop.client_app.core.config import ClientConfig
from desktop.client_app.services.auth_service import AuthService
from desktop.client_app.services.device_service import DeviceService
from desktop.client_app.services.virtualhere_service import VirtualHereService
from desktop.client_app.ui.main_window import MainWindow
from desktop.client_app.ui.login_dialog import LoginDialog
from desktop.client_app.utils.logger import setup_logger

# 配置日志
logger = setup_logger("OmniLinkClient")

class DeviceMonitorThread(QThread):
    """设备监控后台线程"""
    device_status_changed = pyqtSignal(dict)
    connection_status_changed = pyqtSignal(str, str)  # device_id, status
    
    def __init__(self, device_service, auth_service):
        super().__init__()
        self.device_service = device_service
        self.auth_service = auth_service
        self.running = True
        
    def run(self):
        """后台监控设备状态变化"""
        while self.running:
            try:
                if self.auth_service.is_authenticated():
                    # 获取最新设备列表
                    devices = self.device_service.get_available_devices()
                    self.device_status_changed.emit(devices)
                    
                    # 检查连接状态
                    active_connections = self.device_service.get_active_connections()
                    for device_id, status in active_connections.items():
                        self.connection_status_changed.emit(device_id, status)
                        
                self.msleep(5000)  # 每5秒检查一次
            except Exception as e:
                logger.error(f"设备监控线程错误: {e}")
                self.msleep(10000)  # 出错时延长检查间隔
    
    def stop(self):
        """停止监控线程"""
        self.running = False
        self.quit()
        self.wait()

class OmniLinkClient(QApplication):
    """OmniLink专用客户端主应用"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # 应用基本设置
        self.setApplicationName("OmniLink Client")
        self.setApplicationVersion("1.0.0")
        self.setQuitOnLastWindowClosed(False)  # 关闭窗口不退出应用
        
        # 初始化配置
        self.config = ClientConfig()
        self.settings = QSettings("OmniLink", "Client")
        
        # 初始化服务
        self.auth_service = AuthService(self.config.api_base_url)
        self.device_service = DeviceService(self.config.api_base_url, self.auth_service)
        self.vh_service = VirtualHereService()
        
        # 初始化UI组件
        self.main_window = None
        self.login_dialog = None
        self.setup_system_tray()
        
        # 初始化设备监控线程
        self.device_monitor = DeviceMonitorThread(self.device_service, self.auth_service)
        self.device_monitor.device_status_changed.connect(self.on_device_status_changed)
        self.device_monitor.connection_status_changed.connect(self.on_connection_status_changed)
        
        # 连接状态管理
        self.active_connections = {}  # device_id -> connection_info
        
        logger.info("OmniLink客户端初始化完成")
    
    def setup_system_tray(self):
        """设置系统托盘"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            QMessageBox.critical(None, "系统托盘",
                               "系统托盘不可用，应用将无法在后台运行。")
            return
        
        # 创建托盘图标
        self.tray_icon = QSystemTrayIcon(self)
        self.tray_icon.setIcon(QIcon("desktop/resources/icons/app_icon.ico"))
        self.tray_icon.setToolTip("OmniLink - USB设备远程连接")
        
        # 创建托盘菜单
        tray_menu = QMenu()
        
        # 显示主窗口
        show_action = QAction("显示主窗口", self)
        show_action.triggered.connect(self.show_main_window)
        tray_menu.addAction(show_action)
        
        tray_menu.addSeparator()
        
        # 设备连接状态子菜单
        self.connections_menu = QMenu("设备连接", self)
        tray_menu.addMenu(self.connections_menu)
        self.update_connections_menu()
        
        tray_menu.addSeparator()
        
        # 登录/登出
        self.auth_action = QAction("登录", self)
        self.auth_action.triggered.connect(self.toggle_auth)
        tray_menu.addAction(self.auth_action)
        
        # 设置
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.show_settings)
        tray_menu.addAction(settings_action)
        
        tray_menu.addSeparator()
        
        # 退出
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self.quit_application)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.tray_icon_activated)
        self.tray_icon.show()
    
    def tray_icon_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show_main_window()
    
    def show_main_window(self):
        """显示主窗口"""
        if not self.auth_service.is_authenticated():
            self.show_login()
            return
            
        if not self.main_window:
            self.main_window = MainWindow(
                self.device_service, 
                self.vh_service, 
                self.auth_service
            )
            self.main_window.device_connected.connect(self.on_device_connected)
            self.main_window.device_disconnected.connect(self.on_device_disconnected)
        
        self.main_window.show()
        self.main_window.raise_()
        self.main_window.activateWindow()
    
    def show_login(self):
        """显示登录对话框"""
        if not self.login_dialog:
            self.login_dialog = LoginDialog(self.auth_service)
            self.login_dialog.login_successful.connect(self.on_login_successful)
        
        self.login_dialog.show()
        self.login_dialog.raise_()
        self.login_dialog.activateWindow()
    
    def on_login_successful(self):
        """登录成功处理"""
        logger.info("用户登录成功")
        self.auth_action.setText("登出")
        
        # 开始设备监控
        if not self.device_monitor.isRunning():
            self.device_monitor.start()
        
        # 显示主窗口
        self.show_main_window()
        
        # 显示登录成功通知
        self.tray_icon.showMessage(
            "OmniLink",
            "登录成功，您可以开始使用USB设备了",
            QSystemTrayIcon.Information,
            3000
        )
    
    def toggle_auth(self):
        """切换登录/登出状态"""
        if self.auth_service.is_authenticated():
            self.logout()
        else:
            self.show_login()
    
    def logout(self):
        """用户登出"""
        # 断开所有设备连接
        for device_id in list(self.active_connections.keys()):
            self.disconnect_device(device_id)
        
        # 停止设备监控
        if self.device_monitor.isRunning():
            self.device_monitor.stop()
        
        # 执行登出
        self.auth_service.logout()
        self.auth_action.setText("登录")
        
        # 关闭主窗口
        if self.main_window:
            self.main_window.close()
            self.main_window = None
        
        logger.info("用户已登出")
        self.tray_icon.showMessage(
            "OmniLink",
            "已登出，所有设备连接已断开",
            QSystemTrayIcon.Information,
            3000
        )
    
    def on_device_connected(self, device_id, connection_info):
        """设备连接成功"""
        self.active_connections[device_id] = connection_info
        self.update_connections_menu()
        
        device_name = connection_info.get('name', device_id)
        logger.info(f"设备连接成功: {device_name}")
        
        self.tray_icon.showMessage(
            "设备连接",
            f"已连接到设备: {device_name}",
            QSystemTrayIcon.Information,
            3000
        )
    
    def on_device_disconnected(self, device_id):
        """设备断开连接"""
        connection_info = self.active_connections.pop(device_id, {})
        self.update_connections_menu()
        
        device_name = connection_info.get('name', device_id)
        logger.info(f"设备断开连接: {device_name}")
        
        self.tray_icon.showMessage(
            "设备断开",
            f"已断开设备: {device_name}",
            QSystemTrayIcon.Information,
            3000
        )
    
    def disconnect_device(self, device_id):
        """断开指定设备"""
        try:
            connection_info = self.active_connections.get(device_id)
            if connection_info:
                # 通过VirtualHere断开连接
                self.vh_service.disconnect_device(connection_info['vh_address'])
                
                # 通知服务器
                self.device_service.disconnect_device(device_id)
                
                self.on_device_disconnected(device_id)
        except Exception as e:
            logger.error(f"断开设备失败 {device_id}: {e}")
    
    def update_connections_menu(self):
        """更新连接菜单"""
        self.connections_menu.clear()
        
        if not self.active_connections:
            no_connections = QAction("无活动连接", self)
            no_connections.setEnabled(False)
            self.connections_menu.addAction(no_connections)
        else:
            for device_id, connection_info in self.active_connections.items():
                device_name = connection_info.get('name', device_id)
                device_action = QAction(f"📱 {device_name}", self)
                
                # 创建设备子菜单
                device_menu = QMenu(self)
                
                # 断开连接动作
                disconnect_action = QAction("断开连接", self)
                disconnect_action.triggered.connect(
                    lambda checked, did=device_id: self.disconnect_device(did)
                )
                device_menu.addAction(disconnect_action)
                
                # 设备信息动作
                info_action = QAction("设备信息", self)
                info_action.triggered.connect(
                    lambda checked, info=connection_info: self.show_device_info(info)
                )
                device_menu.addAction(info_action)
                
                device_action.setMenu(device_menu)
                self.connections_menu.addAction(device_action)
    
    def show_device_info(self, connection_info):
        """显示设备信息"""
        device_name = connection_info.get('name', '未知设备')
        device_type = connection_info.get('type', '未知类型')
        vh_address = connection_info.get('vh_address', '未知地址')
        
        QMessageBox.information(
            None,
            "设备信息",
            f"设备名称: {device_name}\n"
            f"设备类型: {device_type}\n"
            f"VirtualHere地址: {vh_address}"
        )
    
    def on_device_status_changed(self, devices):
        """设备状态变化处理"""
        if self.main_window:
            self.main_window.update_device_list(devices)
    
    def on_connection_status_changed(self, device_id, status):
        """连接状态变化处理"""
        if status == "disconnected" and device_id in self.active_connections:
            self.on_device_disconnected(device_id)
    
    def show_settings(self):
        """显示设置对话框"""
        # TODO: 实现设置对话框
        QMessageBox.information(None, "设置", "设置功能正在开发中...")
    
    def quit_application(self):
        """退出应用程序"""
        # 断开所有连接
        for device_id in list(self.active_connections.keys()):
            self.disconnect_device(device_id)
        
        # 停止监控线程
        if self.device_monitor.isRunning():
            self.device_monitor.stop()
        
        # 退出应用
        logger.info("OmniLink客户端正在退出...")
        self.quit()

def main():
    """主函数"""
    # 确保只有一个实例运行
    app = OmniLinkClient(sys.argv)
    
    # 检查是否已有实例运行
    if app.isRunning():
        logger.warning("OmniLink客户端已在运行")
        QMessageBox.warning(None, "警告", "OmniLink客户端已在运行，请检查系统托盘。")
        return 1
    
    # 自动登录检查
    if app.settings.value("auto_login", False, type=bool):
        saved_token = app.settings.value("auth_token", "")
        if saved_token and app.auth_service.validate_token(saved_token):
            app.auth_service.set_token(saved_token)
            app.on_login_successful()
        else:
            app.show_login()
    else:
        app.show_login()
    
    # 运行应用
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())