"""
迁移配置类

定义迁移过程中需要的配置参数，包括源服务器、目标服务器、迁移内容等。
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Set


class MigrationConfig:
    """迁移配置类，定义迁移过程中需要的配置参数"""
    
    def __init__(self, 
                 source_server: str = None, 
                 target_server: str = None,
                 migration_name: str = None,
                 config_path: str = None):
        """
        初始化迁移配置
        
        Args:
            source_server: 源服务器地址，格式为 "ip:port"
            target_server: 目标服务器地址，格式为 "ip:port"
            migration_name: 迁移任务名称，默认为自动生成的时间戳名称
            config_path: 配置文件路径，如果提供则从文件加载配置
        """
        self.source_server = source_server
        self.target_server = target_server
        self.migration_name = migration_name or f"migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.config_path = config_path
        
        # 迁移组件集合
        self.components: Set[str] = set()
        
        # 迁移选项
        self.options: Dict[str, Any] = {
            "verify_after_migration": True,     # 迁移后验证
            "backup_before_migration": True,    # 迁移前备份
            "auto_rollback_on_failure": True,   # 失败时自动回滚
            "timeout_seconds": 3600,            # 超时时间（秒）
            "parallel_transfer": True,          # 并行传输
            "transfer_chunk_size": 1024 * 1024, # 传输块大小（字节）
            "keep_source_after_migration": True, # 迁移后保留源数据
            "notification_email": None,         # 通知邮箱
            "dry_run": False                    # 演习模式（不实际执行）
        }
        
        # 如果提供了配置文件路径，则从文件加载配置
        if config_path and os.path.exists(config_path):
            self.load_from_file(config_path)
            
    def add_component(self, component: str) -> None:
        """
        添加要迁移的组件
        
        Args:
            component: 组件名称，可以是 "database", "configurations", "devices" 等
        """
        self.components.add(component)
        
    def remove_component(self, component: str) -> None:
        """
        移除要迁移的组件
        
        Args:
            component: 组件名称
        """
        if component in self.components:
            self.components.remove(component)
            
    def set_option(self, key: str, value: Any) -> None:
        """
        设置迁移选项
        
        Args:
            key: 选项名称
            value: 选项值
        """
        if key in self.options:
            self.options[key] = value
        else:
            raise ValueError(f"未知的迁移选项: {key}")
            
    def get_option(self, key: str, default: Any = None) -> Any:
        """
        获取迁移选项
        
        Args:
            key: 选项名称
            default: 默认值，如果选项不存在
            
        Returns:
            选项值
        """
        return self.options.get(key, default)
        
    def save_to_file(self, path: str = None) -> None:
        """
        保存配置到文件
        
        Args:
            path: 文件路径，如果为 None 则使用初始化时提供的路径
        """
        save_path = path or self.config_path
        if not save_path:
            raise ValueError("未指定配置文件路径")
            
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)
        
        config_data = {
            "source_server": self.source_server,
            "target_server": self.target_server,
            "migration_name": self.migration_name,
            "components": list(self.components),
            "options": self.options,
            "created_at": datetime.now().isoformat()
        }
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
            
    def load_from_file(self, path: str = None) -> None:
        """
        从文件加载配置
        
        Args:
            path: 文件路径，如果为 None 则使用初始化时提供的路径
        """
        load_path = path or self.config_path
        if not load_path:
            raise ValueError("未指定配置文件路径")
            
        if not os.path.exists(load_path):
            raise FileNotFoundError(f"配置文件不存在: {load_path}")
            
        with open(load_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            
        self.source_server = config_data.get("source_server")
        self.target_server = config_data.get("target_server")
        self.migration_name = config_data.get("migration_name")
        self.components = set(config_data.get("components", []))
        
        # 更新选项，只更新已知选项
        for key, value in config_data.get("options", {}).items():
            if key in self.options:
                self.options[key] = value
                
    def validate(self) -> List[str]:
        """
        验证配置是否有效
        
        Returns:
            错误信息列表，如果为空则表示配置有效
        """
        errors = []
        
        if not self.source_server:
            errors.append("未指定源服务器")
            
        if not self.target_server:
            errors.append("未指定目标服务器")
            
        if not self.components:
            errors.append("未指定要迁移的组件")
            
        return errors
        
    def __str__(self) -> str:
        """返回配置的字符串表示"""
        return (f"迁移配置[{self.migration_name}]: "
                f"从 {self.source_server} 到 {self.target_server}, "
                f"组件: {', '.join(self.components)}") 
