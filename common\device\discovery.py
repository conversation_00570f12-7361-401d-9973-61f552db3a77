#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
USB设备发现服务

自动发现并监控系统中的USB设备变化，支持设备接入和移除事件通知
"""

import os
import time
import logging
import threading
import asyncio
import platform
from enum import Enum, auto
from typing import Dict, List, Set, Optional, Callable, Any, Tuple

from common.models.device import DeviceInfo, DeviceStatus, DeviceType

# 日志设置
logger = logging.getLogger(__name__)

class DeviceEvent(Enum):
    """设备事件类型枚举"""
    ADDED = auto()      # 设备添加
    REMOVED = auto()    # 设备移除
    CHANGED = auto()    # 设备变化

class DeviceDiscovery:
    """USB设备发现服务"""
    
    def __init__(self, scan_interval: float = 2.0, enable_hotplug: bool = True):
        """初始化设备发现服务
        
        Args:
            scan_interval: 扫描间隔(秒)
            enable_hotplug: 是否启用热插拔监控
        """
        self.scan_interval = scan_interval
        self.enable_hotplug = enable_hotplug
        
        # 内部状态
        self._running = False
        self._scan_task = None
        self._hotplug_monitor = None
        self._last_device_paths: Set[str] = set()
        self._event_listeners: List[Callable[[DeviceEvent, DeviceInfo], None]] = []
        
        # 平台相关信息
        self._system = platform.system().lower()
        
        logger.info(f"设备发现服务初始化，系统: {self._system}, 扫描间隔: {scan_interval}秒, 热插拔: {'启用' if enable_hotplug else '禁用'}")
    
    async def start(self):
        """启动设备发现服务"""
        if self._running:
            logger.warning("设备发现服务已在运行")
            return
        
        self._running = True
        
        # 执行初始扫描
        await self._scan_devices()
        
        # 启动定期扫描任务
        self._scan_task = asyncio.create_task(self._scanning_loop())
        
        # 启动热插拔监控(如果支持)
        if self.enable_hotplug:
            await self._start_hotplug_monitor()
        
        logger.info("设备发现服务已启动")
    
    async def stop(self):
        """停止设备发现服务"""
        if not self._running:
            return
        
        self._running = False
        
        # 停止扫描任务
        if self._scan_task:
            self._scan_task.cancel()
            try:
                await self._scan_task
            except asyncio.CancelledError:
                pass
            self._scan_task = None
        
        # 停止热插拔监控
        if self._hotplug_monitor:
            await self._stop_hotplug_monitor()
        
        logger.info("设备发现服务已停止")
    
    def register_event_listener(self, listener: Callable[[DeviceEvent, DeviceInfo], None]):
        """注册设备事件监听器
        
        Args:
            listener: 回调函数，接收事件类型和设备信息
        """
        if listener not in self._event_listeners:
            self._event_listeners.append(listener)
    
    def unregister_event_listener(self, listener: Callable):
        """注销设备事件监听器
        
        Args:
            listener: 监听器函数
        """
        if listener in self._event_listeners:
            self._event_listeners.remove(listener)
    
    async def _scanning_loop(self):
        """扫描循环"""
        try:
            while self._running:
                await self._scan_devices()
                await asyncio.sleep(self.scan_interval)
        except asyncio.CancelledError:
            logger.info("设备扫描任务已取消")
            raise
        except Exception as e:
            logger.error(f"设备扫描异常: {str(e)}")
            if self._running:
                # 重启扫描任务
                self._scan_task = asyncio.create_task(self._scanning_loop())
    
    async def _scan_devices(self):
        """扫描设备"""
        try:
            # 获取当前设备列表
            current_devices = await self._get_system_devices()
            
            # 当前设备路径集合
            current_paths = {device.hub_path for device in current_devices if device.hub_path}
            
            # 检测新增设备
            new_paths = current_paths - self._last_device_paths
            for device in current_devices:
                if device.hub_path and device.hub_path in new_paths:
                    # 添加到设备管理器
                    if self.device_manager.add_device(device):
                        # 通知新设备事件
                        self._notify_event(DeviceEvent.ADDED, device)
                        logger.info(f"发现新设备: {device.id} - {device.name}")
            
            # 检测移除的设备
            removed_paths = self._last_device_paths - current_paths
            for path in removed_paths:
                # 查找设备
                for device_id, device in list(self.device_manager._devices.items()):
                    if device.hub_path == path:
                        # 通知设备移除事件
                        self._notify_event(DeviceEvent.REMOVED, device)
                        # 从设备管理器中移除
                        self.device_manager.remove_device(device_id)
                        logger.info(f"设备已移除: {device_id} - {device.name}")
            
            # 更新设备路径集合
            self._last_device_paths = current_paths
            
        except Exception as e:
            logger.error(f"扫描设备异常: {str(e)}")
    
    async def _get_system_devices(self) -> List[DeviceInfo]:
        """获取系统USB设备列表
        
        不同操作系统有不同的实现
        
        Returns:
            List[DeviceInfo]: 设备信息列表
        """
        devices = []
        
        try:
            if self._system == "linux":
                devices = await self._get_linux_devices()
            elif self._system == "windows":
                devices = await self._get_windows_devices()
            elif self._system == "darwin":
                devices = await self._get_macos_devices()
            else:
                logger.warning(f"不支持的操作系统: {self._system}")
        except Exception as e:
            logger.error(f"获取系统设备异常: {str(e)}")
        
        return devices
    
    async def _get_linux_devices(self) -> List[DeviceInfo]:
        """获取Linux系统USB设备
        
        通过解析/sys/bus/usb/devices目录获取USB设备信息
        
        Returns:
            List[DeviceInfo]: 设备信息列表
        """
        devices = []
        
        # USB设备信息目录
        usb_path = "/sys/bus/usb/devices"
        
        if not os.path.exists(usb_path):
            logger.warning(f"USB设备路径不存在: {usb_path}")
            return devices
        
        # 遍历USB设备
        for device_dir in os.listdir(usb_path):
            # 过滤非设备目录
            if ":" in device_dir:
                continue
            
            device_path = os.path.join(usb_path, device_dir)
            
            try:
                # 读取厂商ID和产品ID
                vendor_path = os.path.join(device_path, "idVendor")
                product_path = os.path.join(device_path, "idProduct")
                
                if not (os.path.exists(vendor_path) and os.path.exists(product_path)):
                    continue
                
                with open(vendor_path, "r") as f:
                    vendor_id = f.read().strip()
                
                with open(product_path, "r") as f:
                    product_id = f.read().strip()
                
                # 读取设备信息
                manufacturer = ""
                manufacturer_path = os.path.join(device_path, "manufacturer")
                if os.path.exists(manufacturer_path):
                    with open(manufacturer_path, "r") as f:
                        manufacturer = f.read().strip()
                
                product = ""
                product_path = os.path.join(device_path, "product")
                if os.path.exists(product_path):
                    with open(product_path, "r") as f:
                        product = f.read().strip()
                
                serial = ""
                serial_path = os.path.join(device_path, "serial")
                if os.path.exists(serial_path):
                    with open(serial_path, "r") as f:
                        serial = f.read().strip()
                
                # 构造设备名称
                name = product or f"{manufacturer} {product_id}"
                if not name:
                    name = f"USB Device {vendor_id}:{product_id}"
                
                # 创建设备ID
                device_id = f"usb-{vendor_id}-{product_id}"
                if serial:
                    device_id = f"{device_id}-{serial}"
                
                # 创建设备信息
                device = DeviceInfo(
                    id=device_id,
                    name=name,
                    type=DeviceType.USB,
                    vendor_id=vendor_id,
                    product_id=product_id,
                    serial_number=serial,
                    hub_path=device_dir,
                    status=DeviceStatus.AVAILABLE
                )
                
                # 添加到设备列表
                devices.append(device)
                
            except Exception as e:
                logger.error(f"读取USB设备信息异常 {device_dir}: {str(e)}")
        
        return devices
    
    async def _get_windows_devices(self) -> List[DeviceInfo]:
        """获取Windows系统USB设备
        
        通过WMI获取USB设备信息
        
        Returns:
            List[DeviceInfo]: 设备信息列表
        """
        devices = []
        
        try:
            # 导入WMI模块
            import wmi
            
            # 创建WMI连接
            c = wmi.WMI()
            
            # 查询USB设备
            for usb in c.Win32_USBControllerDevice():
                try:
                    # 获取设备实例
                    device = usb.Dependent
                    
                    # 解析设备ID获取厂商ID和产品ID
                    device_id = device.DeviceID
                    if "VID_" not in device_id or "PID_" not in device_id:
                        continue
                    
                    # 提取厂商ID
                    vid_start = device_id.find("VID_") + 4
                    vid_end = device_id.find("&", vid_start)
                    vendor_id = device_id[vid_start:vid_end]
                    
                    # 提取产品ID
                    pid_start = device_id.find("PID_") + 4
                    pid_end = device_id.find("&", pid_start)
                    if pid_end == -1:
                        pid_end = len(device_id)
                    product_id = device_id[pid_start:pid_end]
                    
                    # 提取序列号
                    serial = ""
                    if "SER_" in device_id:
                        ser_start = device_id.find("SER_") + 4
                        ser_end = device_id.find("&", ser_start)
                        if ser_end == -1:
                            ser_end = len(device_id)
                        serial = device_id[ser_start:ser_end]
                    
                    # 创建设备名称
                    name = device.Caption or device.Description or f"USB Device {vendor_id}:{product_id}"
                    
                    # 创建设备ID
                    device_id = f"usb-{vendor_id}-{product_id}"
                    if serial:
                        device_id = f"{device_id}-{serial}"
                    
                    # 创建设备信息
                    device_info = DeviceInfo(
                        id=device_id,
                        name=name,
                        type=DeviceType.USB,
                        vendor_id=vendor_id,
                        product_id=product_id,
                        serial_number=serial,
                        hub_path=device.DeviceID,
                        status=DeviceStatus.AVAILABLE
                    )
                    
                    # 添加到设备列表
                    devices.append(device_info)
                    
                except Exception as e:
                    logger.error(f"解析Windows USB设备信息异常: {str(e)}")
        
        except ImportError:
            logger.error("未安装wmi模块，无法获取Windows USB设备信息")
        except Exception as e:
            logger.error(f"获取Windows USB设备异常: {str(e)}")
        
        return devices
    
    async def _get_macos_devices(self) -> List[DeviceInfo]:
        """获取macOS系统USB设备
        
        通过ioreg命令获取USB设备信息
        
        Returns:
            List[DeviceInfo]: 设备信息列表
        """
        devices = []
        
        try:
            # 执行ioreg命令获取USB设备信息
            process = await asyncio.create_subprocess_exec(
                "ioreg", "-p", "IOUSB", "-l", "-w", "0",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"执行ioreg命令失败: {stderr.decode()}")
                return devices
            
            output = stdout.decode()
            lines = output.splitlines()
            
            current_device = {}
            for line in lines:
                line = line.strip()
                
                # 新设备开始
                if "+-o" in line:
                    # 保存之前的设备
                    if current_device and "idVendor" in current_device and "idProduct" in current_device:
                        try:
                            vendor_id = f"{current_device['idVendor']:04x}"
                            product_id = f"{current_device['idProduct']:04x}"
                            
                            # 创建设备名称
                            name = current_device.get("USB Product Name") or current_device.get("name") or f"USB Device {vendor_id}:{product_id}"
                            
                            # 序列号
                            serial = current_device.get("USB Serial Number", "")
                            
                            # 创建设备ID
                            device_id = f"usb-{vendor_id}-{product_id}"
                            if serial:
                                device_id = f"{device_id}-{serial}"
                            
                            # 创建设备路径
                            path = current_device.get("location", "")
                            
                            # 创建设备信息
                            device = DeviceInfo(
                                id=device_id,
                                name=name,
                                type=DeviceType.USB,
                                vendor_id=vendor_id,
                                product_id=product_id,
                                serial_number=serial,
                                hub_path=path,
                                status=DeviceStatus.AVAILABLE
                            )
                            
                            # 添加到设备列表
                            devices.append(device)
                            
                        except Exception as e:
                            logger.error(f"解析macOS USB设备信息异常: {str(e)}")
                    
                    # 重置当前设备
                    current_device = {}
                
                # 解析设备属性
                if "=" in line:
                    key, value = line.split("=", 1)
                    key = key.strip().strip('"')
                    value = value.strip().strip('"').strip(',')
                    
                    # 尝试转换数值
                    try:
                        if value.startswith("0x"):
                            value = int(value, 16)
                        elif value.isdigit():
                            value = int(value)
                    except ValueError:
                        pass
                    
                    current_device[key] = value
            
            # 处理最后一个设备
            if current_device and "idVendor" in current_device and "idProduct" in current_device:
                try:
                    vendor_id = f"{current_device['idVendor']:04x}"
                    product_id = f"{current_device['idProduct']:04x}"
                    
                    # 创建设备名称
                    name = current_device.get("USB Product Name") or current_device.get("name") or f"USB Device {vendor_id}:{product_id}"
                    
                    # 序列号
                    serial = current_device.get("USB Serial Number", "")
                    
                    # 创建设备ID
                    device_id = f"usb-{vendor_id}-{product_id}"
                    if serial:
                        device_id = f"{device_id}-{serial}"
                    
                    # 创建设备路径
                    path = current_device.get("location", "")
                    
                    # 创建设备信息
                    device = DeviceInfo(
                        id=device_id,
                        name=name,
                        type=DeviceType.USB,
                        vendor_id=vendor_id,
                        product_id=product_id,
                        serial_number=serial,
                        hub_path=path,
                        status=DeviceStatus.AVAILABLE
                    )
                    
                    # 添加到设备列表
                    devices.append(device)
                    
                except Exception as e:
                    logger.error(f"解析macOS USB设备信息异常: {str(e)}")
            
        except Exception as e:
            logger.error(f"获取macOS USB设备异常: {str(e)}")
        
        return devices
    
    async def _start_hotplug_monitor(self):
        """启动热插拔监控
        
        不同操作系统有不同的实现
        """
        try:
            if self._system == "linux":
                await self._start_linux_hotplug_monitor()
            elif self._system == "windows":
                await self._start_windows_hotplug_monitor()
            elif self._system == "darwin":
                await self._start_macos_hotplug_monitor()
            else:
                logger.warning(f"不支持的操作系统热插拔监控: {self._system}")
        except Exception as e:
            logger.error(f"启动热插拔监控异常: {str(e)}")
    
    async def _stop_hotplug_monitor(self):
        """停止热插拔监控"""
        try:
            if self._hotplug_monitor:
                # 不同操作系统有不同的实现
                if self._system == "linux":
                    await self._stop_linux_hotplug_monitor()
                elif self._system == "windows":
                    await self._stop_windows_hotplug_monitor()
                elif self._system == "darwin":
                    await self._stop_macos_hotplug_monitor()
                
                self._hotplug_monitor = None
        except Exception as e:
            logger.error(f"停止热插拔监控异常: {str(e)}")
    
    async def _start_linux_hotplug_monitor(self):
        """启动Linux热插拔监控"""
        try:
            # 确保pyudev模块已安装
            import pyudev
            
            # 创建上下文
            context = pyudev.Context()
            
            # 创建监视器
            monitor = pyudev.Monitor.from_netlink(context)
            monitor.filter_by(subsystem='usb', device_type='usb_device')
            
            # 创建监听处理线程
            def monitor_thread():
                # 启动监听
                observer = pyudev.MonitorObserver(monitor, self._linux_device_event)
                observer.start()
                
                # 保存观察者
                self._hotplug_monitor = observer
                
                logger.info("Linux USB热插拔监控已启动")
            
            # 启动线程
            threading.Thread(target=monitor_thread, daemon=True).start()
            
        except ImportError:
            logger.error("未安装pyudev模块，无法启动Linux热插拔监控")
    
    def _linux_device_event(self, action, device):
        """Linux设备事件处理"""
        try:
            # 只处理USB设备
            if device.subsystem != 'usb' or device.device_type != 'usb_device':
                return
            
            # 读取设备信息
            vendor_id = device.get('ID_VENDOR_ID', '')
            product_id = device.get('ID_MODEL_ID', '')
            
            if not vendor_id or not product_id:
                return
            
            # 创建设备ID
            serial = device.get('ID_SERIAL_SHORT', '')
            device_id = f"usb-{vendor_id}-{product_id}"
            if serial:
                device_id = f"{device_id}-{serial}"
            
            # 设备名称
            name = device.get('ID_MODEL', '') or f"USB Device {vendor_id}:{product_id}"
            
            # 设备路径
            path = device.device_path.split('/')[-1]
            
            if action == 'add':
                # 创建设备信息
                device_info = DeviceInfo(
                    id=device_id,
                    name=name,
                    type=DeviceType.USB,
                    vendor_id=vendor_id,
                    product_id=product_id,
                    serial_number=serial,
                    hub_path=path,
                    status=DeviceStatus.AVAILABLE
                )
                
                # 添加到设备管理器
                if self.device_manager.add_device(device_info):
                    # 触发事件通知
                    self._notify_event(DeviceEvent.ADDED, device_info)
                    logger.info(f"热插拔检测到新设备: {device_id} - {name}")
                
                # 更新设备路径集合
                self._last_device_paths.add(path)
                
            elif action == 'remove':
                # 查找设备
                removed = False
                for dev_id, dev in list(self.device_manager._devices.items()):
                    if dev.hub_path == path:
                        # 触发事件通知
                        self._notify_event(DeviceEvent.REMOVED, dev)
                        # 从设备管理器中移除
                        self.device_manager.remove_device(dev_id)
                        logger.info(f"热插拔检测到设备移除: {dev_id} - {dev.name}")
                        removed = True
                
                # 如果设备已被移除，更新设备路径集合
                if removed and path in self._last_device_paths:
                    self._last_device_paths.remove(path)
            
        except Exception as e:
            logger.error(f"处理Linux设备事件异常: {str(e)}")
    
    async def _stop_linux_hotplug_monitor(self):
        """停止Linux热插拔监控"""
        if self._hotplug_monitor:
            self._hotplug_monitor.stop()
            logger.info("Linux USB热插拔监控已停止")
    
    async def _start_windows_hotplug_monitor(self):
        """启动Windows热插拔监控"""
        # Windows的热插拔监控较为复杂，需要使用WMI事件通知
        # 这里简化为通过定期扫描检测变化
        logger.info("Windows不支持真正的热插拔监控，将使用定期扫描代替")
    
    async def _stop_windows_hotplug_monitor(self):
        """停止Windows热插拔监控"""
        # Windows使用定期扫描，无需特殊停止操作
        pass
    
    async def _start_macos_hotplug_monitor(self):
        """启动macOS热插拔监控"""
        # macOS的热插拔监控较为复杂，需要使用IOKit框架
        # 这里简化为通过定期扫描检测变化
        logger.info("macOS不支持真正的热插拔监控，将使用定期扫描代替")
    
    async def _stop_macos_hotplug_monitor(self):
        """停止macOS热插拔监控"""
        # macOS使用定期扫描，无需特殊停止操作
        pass
    
    def _notify_event(self, event: DeviceEvent, device: DeviceInfo):
        """通知设备事件
        
        Args:
            event: 事件类型
            device: 设备信息
        """
        for listener in self._event_listeners:
            try:
                listener(event, device)
            except Exception as e:
                logger.error(f"设备事件监听器异常: {e}")

# 全局单例实例
_instance = None

def get_device_discovery(scan_interval: float = 2.0, enable_hotplug: bool = True) -> DeviceDiscovery:
    """获取设备发现服务实例（单例模式）
    
    Args:
        scan_interval: 扫描间隔(秒)
        enable_hotplug: 是否启用热插拔监控
        
    Returns:
        DeviceDiscovery: 设备发现服务实例
    """
    global _instance
    if _instance is None:
        _instance = DeviceDiscovery(
            scan_interval=scan_interval,
            enable_hotplug=enable_hotplug
        )
    return _instance 
