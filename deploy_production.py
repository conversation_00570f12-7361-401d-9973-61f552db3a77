#!/usr/bin/env python3
"""
OmniLink生产级部署脚本
支持主从服务器的完整部署和配置
"""

import os
import sys
import subprocess
import time
import json
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('deployment.log')
    ]
)
logger = logging.getLogger(__name__)

class ProductionDeployer:
    """生产级部署器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.project_root = Path(__file__).parent
        self.config = self.load_config(config_file)
        self.services = ['postgres-db', 'redis', 'main-server', 'slave-server']
        
    def load_config(self, config_file: Optional[str]) -> Dict:
        """加载配置文件"""
        default_config = {
            "environment": "production",
            "build_timeout": 600,
            "health_check_timeout": 120,
            "backup_enabled": True,
            "monitoring_enabled": True,
            "ssl_enabled": False,
            "domain": "localhost",
            "admin_user": "firefly",
            "admin_password": "bro2fhz12"
        }
        
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
                logger.info(f"配置文件已加载: {config_file}")
            except Exception as e:
                logger.warning(f"配置文件加载失败，使用默认配置: {e}")
        
        return default_config

    def run_command(self, command: str, cwd: Optional[str] = None, timeout: int = 300) -> bool:
        """执行命令"""
        try:
            logger.info(f"执行命令: {command}")
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd or self.project_root,
                timeout=timeout,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info(f"命令执行成功: {command}")
                if result.stdout:
                    logger.debug(f"输出: {result.stdout}")
                return True
            else:
                logger.error(f"命令执行失败: {command}")
                logger.error(f"错误输出: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"命令执行超时: {command}")
            return False
        except Exception as e:
            logger.error(f"命令执行异常: {command} - {e}")
            return False

    def check_prerequisites(self) -> bool:
        """检查部署前提条件"""
        logger.info("检查部署前提条件...")
        
        checks = [
            ("Docker", "docker --version"),
            ("Docker Compose", "docker-compose --version"),
            ("Git", "git --version"),
        ]
        
        for name, command in checks:
            if not self.run_command(command, timeout=30):
                logger.error(f"前提条件检查失败: {name}")
                return False
            logger.info(f"✅ {name} 可用")
        
        # 检查必要文件
        required_files = [
            "docker-compose.yaml",
            "app.env",
            "deployment/dockerfiles/Dockerfile.main",
            "deployment/dockerfiles/Dockerfile.slave"
        ]
        
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                logger.error(f"必要文件缺失: {file_path}")
                return False
            logger.info(f"✅ {file_path} 存在")
        
        return True

    def backup_existing_data(self) -> bool:
        """备份现有数据"""
        if not self.config.get("backup_enabled", True):
            logger.info("备份功能已禁用，跳过备份")
            return True
            
        logger.info("备份现有数据...")
        
        backup_dir = self.project_root / "backups" / f"backup_{int(time.time())}"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份数据库
        if self.run_command("docker-compose ps postgres-db", timeout=30):
            logger.info("备份PostgreSQL数据库...")
            backup_cmd = f"""
            docker-compose exec -T postgres-db pg_dump -U omnilink omnilink_db > {backup_dir}/database.sql
            """
            if not self.run_command(backup_cmd, timeout=300):
                logger.warning("数据库备份失败，但继续部署")
        
        # 备份配置文件
        config_files = ["app.env", "docker-compose.yaml"]
        for config_file in config_files:
            src = self.project_root / config_file
            dst = backup_dir / config_file
            if src.exists():
                try:
                    import shutil
                    shutil.copy2(src, dst)
                    logger.info(f"配置文件已备份: {config_file}")
                except Exception as e:
                    logger.warning(f"配置文件备份失败: {config_file} - {e}")
        
        logger.info(f"备份完成，位置: {backup_dir}")
        return True

    def build_images(self) -> bool:
        """构建Docker镜像"""
        logger.info("构建Docker镜像...")
        
        # 清理旧镜像（可选）
        if self.config.get("clean_build", False):
            logger.info("清理旧镜像...")
            self.run_command("docker-compose down --rmi all", timeout=300)
        
        # 构建镜像
        build_cmd = "docker-compose build --no-cache"
        if not self.run_command(build_cmd, timeout=self.config.get("build_timeout", 600)):
            logger.error("Docker镜像构建失败")
            return False
        
        logger.info("✅ Docker镜像构建完成")
        return True

    def start_services(self) -> bool:
        """启动服务"""
        logger.info("启动服务...")
        
        # 按顺序启动服务
        service_order = ['postgres-db', 'redis', 'main-server', 'slave-server']
        
        for service in service_order:
            logger.info(f"启动服务: {service}")
            
            start_cmd = f"docker-compose up -d {service}"
            if not self.run_command(start_cmd, timeout=120):
                logger.error(f"服务启动失败: {service}")
                return False
            
            # 等待服务健康检查
            if not self.wait_for_service_health(service):
                logger.error(f"服务健康检查失败: {service}")
                return False
            
            logger.info(f"✅ 服务启动成功: {service}")
            time.sleep(5)  # 服务间启动间隔
        
        return True

    def wait_for_service_health(self, service: str) -> bool:
        """等待服务健康检查通过"""
        logger.info(f"等待服务健康检查: {service}")
        
        timeout = self.config.get("health_check_timeout", 120)
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # 检查容器状态
            check_cmd = f"docker-compose ps {service}"
            if self.run_command(check_cmd, timeout=30):
                
                # 特殊的健康检查
                if service == "postgres-db":
                    health_cmd = "docker-compose exec -T postgres-db pg_isready -U omnilink"
                elif service == "redis":
                    health_cmd = "docker-compose exec -T redis redis-cli ping"
                elif service == "main-server":
                    health_cmd = "curl -f http://localhost:8000/health || exit 1"
                elif service == "slave-server":
                    health_cmd = "curl -f http://localhost:8001/health -H 'Authorization: Bearer dev-api-key-12345' || exit 1"
                else:
                    # 默认检查：容器运行即可
                    return True
                
                if self.run_command(health_cmd, timeout=10):
                    logger.info(f"✅ {service} 健康检查通过")
                    return True
            
            time.sleep(5)
        
        logger.error(f"❌ {service} 健康检查超时")
        return False

    def run_tests(self) -> bool:
        """运行部署后测试"""
        logger.info("运行部署后测试...")
        
        # 运行通信测试
        test_cmd = "python test_main_slave_communication.py"
        if not self.run_command(test_cmd, timeout=300):
            logger.warning("通信测试失败，但部署继续")
            return False
        
        logger.info("✅ 部署后测试通过")
        return True

    def show_deployment_info(self):
        """显示部署信息"""
        logger.info("部署完成！")
        
        info = f"""
{'='*60}
OmniLink 部署信息
{'='*60}

服务访问地址:
  - 主服务器 (Web管理界面): http://localhost:8000
  - 主服务器 API文档: http://localhost:8000/docs
  - 从服务器 API: http://localhost:8001
  - PostgreSQL: localhost:5433
  - Redis: localhost:6380

管理员账户:
  - 用户名: {self.config['admin_user']}
  - 密码: {self.config['admin_password']}

常用命令:
  - 查看服务状态: docker-compose ps
  - 查看日志: docker-compose logs -f [service_name]
  - 停止服务: docker-compose down
  - 重启服务: docker-compose restart [service_name]

配置文件:
  - 环境配置: app.env
  - Docker配置: docker-compose.yaml

日志文件:
  - 部署日志: deployment.log

{'='*60}
        """
        
        print(info)
        
        # 保存部署信息到文件
        with open("deployment_info.txt", "w", encoding="utf-8") as f:
            f.write(info)

    def deploy(self) -> bool:
        """执行完整部署"""
        logger.info("开始OmniLink生产级部署...")
        
        steps = [
            ("检查前提条件", self.check_prerequisites),
            ("备份现有数据", self.backup_existing_data),
            ("构建Docker镜像", self.build_images),
            ("启动服务", self.start_services),
            ("运行测试", self.run_tests),
        ]
        
        for step_name, step_func in steps:
            logger.info(f"\n🔄 执行步骤: {step_name}")
            try:
                if not step_func():
                    logger.error(f"❌ 步骤失败: {step_name}")
                    return False
                logger.info(f"✅ 步骤完成: {step_name}")
            except Exception as e:
                logger.error(f"❌ 步骤异常: {step_name} - {e}")
                return False
        
        self.show_deployment_info()
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OmniLink生产级部署脚本")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--no-backup", action="store_true", help="跳过备份")
    parser.add_argument("--clean-build", action="store_true", help="清理构建")
    
    args = parser.parse_args()
    
    # 创建部署器
    deployer = ProductionDeployer(args.config)
    
    # 应用命令行参数
    if args.no_backup:
        deployer.config["backup_enabled"] = False
    if args.clean_build:
        deployer.config["clean_build"] = True
    
    # 执行部署
    success = deployer.deploy()
    
    if success:
        logger.info("🎉 部署成功完成！")
        sys.exit(0)
    else:
        logger.error("💥 部署失败！")
        sys.exit(1)

if __name__ == "__main__":
    main() 