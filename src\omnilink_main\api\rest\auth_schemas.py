from typing import Optional, List, Any # 确保 Any 被导入
from pydantic import BaseModel, Field

# 根据 nex.md 阶段六 -> 3. 设备访问控制系统 -> [待完成] 由专用客户端发来的登录请求... 任务描述
# 和之前已有的模型结构进行整合和增强

class ClientNetworkInfo(BaseModel):
    """客户端上报的网络信息"""
    client_reported_public_ip: Optional[str] = Field(None, description="客户端获取的公网IP地址")
    public_ip_method: Optional[str] = Field(None, description="客户端获取公网IP的方法 (e.g., HTTP, STUN)")
    public_ip_status: Optional[str] = Field(None, description="客户端获取公网IP的状态 (e.g., SUCCESS, FAILED, UNAVAILABLE)")
    lan_ips: List[str] = Field(default_factory=list, description="客户端获取的内网IP地址列表")
    mac_addresses: List[str] = Field(default_factory=list, description="客户端所有活动网卡的MAC地址列表") # 根据nex.md描述，MAC地址在network_info中
    vpn_detected: Optional[bool] = Field(None, description="客户端是否检测到VPN")
    proxy_detected: Optional[bool] = Field(None, description="客户端是否检测到代理")

class ClientDeviceInfo(BaseModel):
    """客户端上报的设备信息"""
    device_id: Optional[str] = Field(None, description="客户端设备的唯一指纹/ID (Machine ID)")
    user_agent: Optional[str] = Field(None, description="客户端的用户代理字符串")
    # 其他可能的设备信息可以后续添加，如操作系统版本、客户端应用版本等

class ClientLoginRequest(BaseModel):
    """客户端登录请求体"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    network_info: Optional[ClientNetworkInfo] = Field(None, description="客户端网络信息")
    device_info: Optional[ClientDeviceInfo] = Field(None, description="客户端设备信息")
    client_version: Optional[str] = Field(None, description="客户端版本号") # nex.md的ClientLoginRequest示例中有这个，予以保留

class UserInfo(BaseModel):
    """登录成功后返回的用户基本信息"""
    username: str
    user_id: str # Typically UUID or DB ID
    # Add other non-sensitive user info if needed

class LoginSuccessResponse(BaseModel):
    """登录成功响应体"""
    access_token: str
    token_type: str = "bearer"
    expires_at: str # ISO 8601 format timestamp
    session_salt: str # Added: Base64 encoded session salt
    user_info: Optional[UserInfo] = None # Optional detailed user info

class LoginFailureResponse(BaseModel):
    """登录失败或通用错误响应体"""
    detail: str

# --- New Schema for Action Confirmation ---
# class ConfirmActionRequest(BaseModel):
#     """Request body for confirming a sensitive action"""
#     confirmation_token: str = Field(..., description="The confirmation token received from the initial request (HTTP 428).")
#     password: str = Field(..., description="The current user's password for verification.")
# 
# --- End New Schema ---

class GenericResponse(BaseModel): # 添加一个通用的响应模型
    """通用API响应模型"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[Any] = None # 允许data为任何类型 
