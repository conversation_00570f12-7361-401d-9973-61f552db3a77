#!/usr/bin/env python3
import subprocess
import sys
import time
from pathlib import Path

def run_cmd(command, timeout=300):
    try:
        print(f"执行: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=timeout, cwd=Path(__file__).parent)
        print(f"返回码: {result.returncode}")
        if result.stdout:
            print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        print(f"异常: {e}")
        return False, "", str(e)

def main():
    print("OmniLink Docker 部署测试")
    print("=" * 50)
    
    # 检查Docker
    print("\n1. 检查Docker...")
    success, stdout, stderr = run_cmd("docker --version")
    if not success:
        print("Docker不可用，请启动Docker Desktop")
        return
    print(f"Docker版本: {stdout.strip()}")
    
    # 检查配置
    print("\n2. 检查配置文件...")
    files = ["docker-compose.yaml", "app.env"]
    for f in files:
        if not Path(f).exists():
            print(f"缺失文件: {f}")
            return
    print("配置文件完整")
    
    # 构建镜像
    print("\n3. 构建镜像...")
    run_cmd("docker-compose down --remove-orphans")
    success, _, stderr = run_cmd("docker-compose build --no-cache", 900)
    if not success:
        print(f"构建失败: {stderr}")
        return
    print("镜像构建成功")
    
    # 启动服务
    print("\n4. 启动服务...")
    run_cmd("docker-compose up -d postgres-db redis")
    time.sleep(30)
    run_cmd("docker-compose up -d main-server")
    time.sleep(20)
    run_cmd("docker-compose up -d slave-server")
    time.sleep(15)
    
    # 检查状态
    print("\n5. 检查状态...")
    success, stdout, _ = run_cmd("docker-compose ps")
    if success:
        print("容器状态:")
        print(stdout)
    
    # 健康检查
    print("\n6. 健康检查...")
    success, _, _ = run_cmd('curl -f http://localhost:8000/health')
    print(f"主服务器: {'✅' if success else '❌'}")
    
    success, _, _ = run_cmd('curl -f -H "Authorization: Bearer dev-api-key-12345" http://localhost:8001/health')
    print(f"从服务器: {'✅' if success else '❌'}")
    
    print("\n部署完成！")
    print("访问: http://localhost:8000/docs")
    print("账户: firefly / bro2fhz12")

if __name__ == "__main__":
    main() 